# GitHub docs "About code owners":
# https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

*                                 @eneco-online/dxp-foundation-frontend

# Explicitly leaving ownership empty, so it doesn't require any PR approvals.
# This is especially useful for generated files

/libs/sitecore/types
/libs/dc/src/hooks
/libs/dc/src/models
/libs/dc/src/services
/libs/dc-be/src/client
/mocks/dc/handlers.ts
/mocks/dc-be/handlers.ts
/mocks/sitecore

# CI/CD (pipeline configurations, templates & Dockerfiles)

/cicd                                                             @eneco-online/dxp-foundation-devops

# Contains scripts that should also be reviewed by non-devops cq frontend members
/cicd/**/*.js                                                     @eneco-online/dxp-foundation-frontend

# Sparky

/libs/sparky                                                      @eneco-online/dxp-foundation-sparky
/libs/custom-components                                           @eneco-online/dxp-foundation-sparky
/e2e/nl/internal/sparky                                           @eneco-online/dxp-foundation-sparky

# SelfService NL

/containers/nl/eneco/selfservice                                  @eneco-online/dxp-cx
/apps/cookiewall/nl                                               @eneco-online/dxp-cx
/apps/dashboard/nl                                                @eneco-online/dxp-cx
/apps/invoices/shared                                             @eneco-online/dxp-cx
/apps/orders/nl                                                   @eneco-online/dxp-cx
/apps/profile/nl/multilabel                                       @eneco-online/dxp-cx
/apps/advancepayment/nl                                           @eneco-online/dxp-cx
/apps/mandate/nl                                                  @eneco-online/dxp-cx
/apps/maintenance/nl                                              @eneco-online/dxp-cx
/apps/meterreadings/nl                                            @eneco-online/dxp-cx
/apps/chat                                                        @eneco-online/dxp-cx
/apps/payment                                                     @eneco-online/dxp-cx
/apps/relocation                                                  @eneco-online/dxp-cx
/apps/usage/nl/multilabel                                         @eneco-online/dxp-cx
/apps/usagecap/shared                                             @eneco-online/dxp-cx


/mocks/dc/customers/accounts                                      @eneco-online/dxp-cx
/mocks/dc/customers/gdpr                                          @eneco-online/dxp-cx
/mocks/dc/customers/payments/freeDeposit                          @eneco-online/dxp-cx
/mocks/dc/customers/payments/payInvoice                           @eneco-online/dxp-cx
/mocks/dc/customers/products                                      @eneco-online/dxp-cx
/mocks/sitecore/apps/cookiewall/nl                                @eneco-online/dxp-cx
/mocks/sitecore/apps/maintenance                                  @eneco-online/dxp-cx
/mocks/sitecore/apps/meterreadings                                @eneco-online/dxp-cx
/mocks/sitecore/apps/orders                                       @eneco-online/dxp-cx
/mocks/sitecore/apps/relocation                                   @eneco-online/dxp-cx
/mocks/sitecore/apps/profile/nl/multilabel                        @eneco-online/dxp-cx
/mocks/sitecore/apps/profile/shared/multilabel                    @eneco-online/dxp-cx
/mocks/sitecore/apps/profile/be/eneco/native                      @eneco-online/dxp-app
/mocks/sitecore/apps/usage/nl                                     @eneco-online/dxp-cx
/mocks/sitecore/apps/usagecap                                     @eneco-online/dxp-cx
/mocks/sitecore/containers/nl/eneco/selfservice.ts                @eneco-online/dxp-cx

# Selfservice shared

/apps/profile/shared/multilabel                                   @eneco-online/dxp-cx
/apps/products/shared/multilabel                                  @eneco-online/dxp-cx
/libs/sitecore/components/content/src/FormsContactForm/           @eneco-online/dxp-cx

/mocks/dc/customers/address                                       @eneco-online/dxp-cx @eneco-online/dxp-sales
/mocks/dc/customers/accounts/financialoverview                    @eneco-online/dxp-cx
/mocks/dc/customers/profile                                       @eneco-online/dxp-cx
/mocks/dc/userAccounts                                            @eneco-online/dxp-cx
/mocks/dc/reading                                                 @eneco-online/dxp-cx
/mocks/dc/customers/accounts/reading                              @eneco-online/dxp-cx
/mocks/dc/products                                                @eneco-online/dxp-cx
/mocks/sitecore/apps/advancepayment                               @eneco-online/dxp-cx
/mocks/sitecore/apps/dashboard                                    @eneco-online/dxp-cx
/mocks/sitecore/apps/invoices                                     @eneco-online/dxp-cx
/mocks/sitecore/apps/products                                     @eneco-online/dxp-cx
/mocks/sitecore/apps/payment                                      @eneco-online/dxp-cx
/mocks/sitecore/apps/dynamicPricing                               @eneco-online/dxp-cx @eneco-online/dxp-foundation-frontend @eneco-online/b2c-new-propositions

/mocks/sitecore/components/                                       @eneco-online/dxp-cx @eneco-online/dxp-sales @eneco-online/dxp-propositions
/libs/layouts/src/selfservice.tsx                                 @eneco-online/dxp-cx


# Payment Arrangement NL

/apps/paymentarrangement                                          @eneco-online/dxp-cx
/mocks/sitecore/apps/paymentarrangement                           @eneco-online/dxp-cx

# Access Management NL

/apps/accessmanagement/nl/                                        @eneco-online/dxp-cx
/mocks/sitecore/apps/accessmanagement/nl                          @eneco-online/dxp-cx

# Access Management shared

/libs/layouts/src/accessmanagement.tsx                            @eneco-online/dxp-cx

# Sales

/containers/nl/eneco/flows                                        @eneco-online/dxp-sales
/apps/customerreview/shared/multilabel                            @eneco-online/dxp-sales
/apps/flows                                                       @eneco-online/dxp-sales
/apps/flows/nl/eneco/energyFlow                                   @eneco-online/dxp-sales
/apps/flows/nl/eneco/urgentEnergyFlow                             @eneco-online/dxp-sales
/apps/flows/nl/eneco/decarbonisationLeadFlow                      @eneco-online/dxp-sales
/apps/unsubscribe                                                 @eneco-online/dxp-sales
/libs/eneco/flows                                                 @eneco-online/dxp-sales
/libs/eneco/flows/src/types/context                               @eneco-online/dxp-sales @eneco-online/dxp-propositions
/mocks/sitecore/apps/flows                                        @eneco-online/dxp-sales
/mocks/sitecore/apps/flows/energyFlow                             @eneco-online/dxp-sales
/mocks/sitecore/apps/flows/urgentEnergyFlow                       @eneco-online/dxp-sales
/mocks/sitecore/apps/flows/decarbonisationLeadFlow                @eneco-online/dxp-sales
/mocks/sitecore/apps/unsubscribe                                  @eneco-online/dxp-sales
/mocks/dc/preferences                                             @eneco-online/dxp-sales
/mocks/dc/averagereviewscore                                      @eneco-online/dxp-sales
/mocks/sitecore/components/shared/customerReview.ts               @eneco-online/dxp-sales
/mocks/sitecore/containers/*/*/flows.ts                           @eneco-online/dxp-sales
/libs/sitecore/components/forms/src/GenericForm/RevolutionForm.tsx @eneco-online/dxp-sales

# Oxxio

/containers/nl/oxxio/main                                         @eneco-online/dxp-oxxio @eneco-online/dxp-cx
/containers/nl/oxxio/flows                                        @eneco-online/dxp-oxxio @eneco-online/dxp-sales
/containers/nl/oxxio/selfservice                                  @eneco-online/dxp-cx
/app/flows/nl/oxxio                                               @eneco-online/dxp-oxxio @eneco-online/dxp-sales
/mocks/sitecore/containers/nl/oxxio/selfservice.ts                @eneco-online/dxp-cx
/mocks/sitecore/containers/nl/oxxio/main.ts                       @eneco-online/dxp-oxxio @eneco-online/dxp-cx
/mocks/sitecore/apps/content/nl/oxxio                             @eneco-online/dxp-oxxio
/mocks/sitecore/containers/nl/oxxio/flows.ts                      @eneco-online/dxp-oxxio @eneco-online/dxp-sales

# WoonEnergie

/containers/nl/woonenergie/main                                   @eneco-online/dxp-cx
/containers/nl/woonenergie/selfservice                            @eneco-online/dxp-cx
/mocks/sitecore/containers/nl/woonenergie                         @eneco-online/dxp-cx
/mocks/sitecore/containers/nl/woonenergie/main.ts                 @eneco-online/dxp-cx

# Belgium

/containers/be/eneco/main                                         @eneco-online/dxp-belgium
/containers/be/eneco/selfservice                                  @eneco-online/dxp-belgium
/apps/myzone/be                                                   @eneco-online/dxp-belgium
/apps/header/be                                                   @eneco-online/dxp-belgium
/mocks/sitecore/containers/be/eneco/main.ts                       @eneco-online/dxp-belgium
/mocks/sitecore/containers/be/eneco/selfservice.ts                @eneco-online/dxp-belgium
/mocks/sitecore/components/be                                     @eneco-online/dxp-belgium
/libs/next-be/                                                    @eneco-online/dxp-belgium
/libs/dc-be                                                       @eneco-online/dxp-belgium
/libs/auth-be                                                     @eneco-online/dxp-belgium
/mocks/dc-be                                                      @eneco-online/dxp-belgium
/types-be                                                         @eneco-online/dxp-belgium
/apps/gtm/be                                                      @eneco-online/dxp-belgium
/libs/sitecore/components/content/src/SmartHomeBattery            @eneco-online/dxp-belgium

# Insights App

/containers/be/eneco/insights                                     @eneco-online/dxp-app
/apps/usage/be/eneco/native                                       @eneco-online/dxp-app
/apps/accessmanagement/be/eneco/native                            @eneco-online/dxp-app
/apps/energyprofile/be/eneco/native                               @eneco-online/dxp-app
/apps/dashboard/be/eneco/native                                   @eneco-online/dxp-app
/apps/profile/be/eneco/native                                     @eneco-online/dxp-app
/apps/hems/be/eneco/native                                        @eneco-online/dxp-app
/apps/launchpad/be/eneco/native                                   @eneco-online/dxp-app
/apps/dongle/be/eneco/native                                      @eneco-online/dxp-app
/apps/mandate/be/eneco/native                                     @eneco-online/dxp-app
/apps/profile/be/eneco/native                                     @eneco-online/dxp-app
/libs/native-auth                                                 @eneco-online/dxp-app
/libs/native-in-app-browser                                       @eneco-online/dxp-app
/libs/native-components                                           @eneco-online/dxp-app
/libs/sitecore/native-components                                  @eneco-online/dxp-app
/containers/be/eneco/insights                                     @eneco-online/dxp-app
/mocks/dc/banner                                                  @eneco-online/dxp-app
/mocks/dc/customers/accounts/externalmandate                      @eneco-online/dxp-app
/mocks/dc/customers/accounts/dashboard                            @eneco-online/dxp-app
/mocks/dc/customers/accounts/motivations                          @eneco-online/dxp-app
/mocks/dc/hems                                                    @eneco-online/dxp-app
/mocks/dc/launchpad                                               @eneco-online/dxp-app
/mocks/dc/subscriptions                                           @eneco-online/dxp-app
/mocks/dc/p1DongleUsage                                           @eneco-online/dxp-app
/mocks/sitecore/apps/dashboard/be/eneco/native                    @eneco-online/dxp-app
/mocks/sitecore/apps/launchpad                                    @eneco-online/dxp-app
/mocks/sitecore/apps/dongle                                       @eneco-online/dxp-app
/mocks/sitecore/apps/usage/be/eneco/native                        @eneco-online/dxp-app
/mocks/sitecore/apps/hems/be/eneco/native                         @eneco-online/dxp-app
/mocks/sitecore/apps/profile/be/eneco/native                      @eneco-online/dxp-app
/mocks/sitecore/apps/mandate/be/eneco/native                      @eneco-online/dxp-app
/mocks/sitecore/containers/be/eneco/insights                      @eneco-online/dxp-app

# Calculator

/apps/calculator                                                  @eneco-online/dxp-propositions

# Inspiration Advice Filter

/apps/inspirationadvicefilter                                               @eneco-online/dxp-propositions
/mocks/sitecore/apps/inspirationadvicefilter                                @eneco-online/dxp-propositions

# Shop Window

/apps/shopwindow                                                  @eneco-online/dxp-propositions
/mocks/sitecore/apps/shopwindow                                   @eneco-online/dxp-propositions

# Flows

/mocks/dc/appointmentsLeads                                       @eneco-online/dxp-propositions
/apps/flows/nl/eneco/ketelComfortFlow                             @eneco-online/dxp-propositions
/mocks/sitecore/apps/flows/ketelComfortFlow                       @eneco-online/dxp-propositions
/apps/flows/nl/eneco/serviceGemakFlow                             @eneco-online/dxp-propositions
/mocks/sitecore/apps/flows/serviceGemakFlow                       @eneco-online/dxp-propositions
/apps/appointments/shared/multilabel                              @eneco-online/dxp-propositions
/mocks/sitecore/apps/appointments                                 @eneco-online/dxp-propositions
/apps/appointmentsV2/shared/multilabel                            @eneco-online/dxp-propositions
/mocks/sitecore/apps/appointmentsV2                               @eneco-online/dxp-propositions

# B2C New Propositions

/apps/dynamicpricingtariffs/shared/multilabel                     @eneco-online/b2c-new-propositions

# Shared

/containers/nl/eneco/main                                         @eneco-online/dxp-propositions @eneco-online/dxp-cx @eneco-online/dxp-sales
/mocks/sitecore/containers/nl/eneco/main.ts                       @eneco-online/dxp-propositions @eneco-online/dxp-cx @eneco-online/dxp-sales

# Eligibilityhub

/containers/nl/internal/eligibilityhub                            @eneco-online/data

# Calculationtool

/apps/calculationtool                                             @eneco-online/dxp-sales
