import React, { FC, useContext, useEffect, useState, useMemo } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { SubmitHandler, useForm, useWatch, Controller } from 'react-hook-form';

import { maskHouseNumber } from '@common/maskInput/maskHouseNumber';
import PostalCodeInput from '@components/PostalCodeInput/PostalCodeInput';
import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { useAddressPublicApi, useAddressFormWatcher } from '@hooks/address';
import { useFormatter } from '@i18n';
import { Placeholder, useContent } from '@sitecore/common';
import { Button, Form, Heading, InputSelect, NavLink, Stack, Text, TextLink, InputText } from '@sparky';
import { ChevronLeftIcon, InfoIcon } from '@sparky/icons';
import { TrackViewComponent } from '@tracking';

import { RenderingContentType } from '../registrationFlow.types';
import { canRegisterAddressSchema } from './canRegisterAddressSchema';
import { Action } from '../registrationFlow.constants';
import { RegistrationMachine } from '../registrationFlow.machine';

interface Props {
  trackingStart: () => void;
}

type FormValues = {
  houseNumber: string;
  houseNumberSuffix: string;
  postalCode: string;
};

export const CanRegisterAddress: FC<Props> = ({ trackingStart }) => {
  const { send, state } = useContext(RegistrationMachine);
  const { fields, f } = useContent<RenderingContentType>();
  const { address } = useFormatter();
  const [formHasReceivedFocus, setFormHasReceivedFocus] = useState(false);
  const [isSuffixRequired, setIsSuffixRequired] = useState(false);
  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    resetField,
  } = useForm<FormValues>({
    defaultValues: {
      postalCode: state.context.postalCode,
      houseNumber: String(state.context.houseNumber || ''),
      houseNumberSuffix: state.context.houseNumberSuffix || '',
    },
    resolver: yupResolver(canRegisterAddressSchema(isSuffixRequired)),
  });
  const { houseNumberSuffix: houseNumberSuffixValue, postalCode, houseNumber } = useWatch({ control });

  const { parameters, houseNumberSuffix, isValidInput, emptySuffixString } = useAddressFormWatcher({
    houseNumberSuffix: houseNumberSuffixValue,
    postalCode,
    houseNumber,
  });

  const { data, error, isValidating, houseNumberSuffixOptions } = useAddressPublicApi(parameters);

  const isCurrentAddressValidated = useMemo(() => {
    if (!data && !error) return false;
    if (!isValidInput || !postalCode || !houseNumber) return false;

    // there can be a null option in the suffix options, so null can be valid even if suffix is required
    const isHouseSuffixMissing = isSuffixRequired && !houseNumberSuffixValue;

    if (isHouseSuffixMissing) return false;

    return postalCode === parameters?.postalCode && houseNumber === parameters?.houseNumber;
  }, [data, error, isValidInput, postalCode, houseNumber, isSuffixRequired, houseNumberSuffixValue, parameters]);

  useEffect(() => {
    setIsSuffixRequired(
      !!houseNumberSuffixOptions.length && !houseNumberSuffixOptions.some(suffix => suffix.value === emptySuffixString),
    );
  }, [houseNumberSuffixOptions, emptySuffixString]);

  // If houseNumberSuffixOptions exists set value of input to empty string to trigger validation
  // (since input is nullable, null avoids validation).
  useEffect(() => {
    if (isValidating || !parameters) return;

    resetField('houseNumberSuffix', {
      defaultValue: houseNumberSuffixOptions.length ? houseNumberSuffix : '',
    });
  }, [houseNumberSuffixOptions, isValidating, parameters, houseNumberSuffix, resetField]);

  const submitForm: SubmitHandler<FormValues> = ({ postalCode, houseNumber, houseNumberSuffix }) => {
    if (!isCurrentAddressValidated) return;
    send({
      type: Action.CAN_REGISTER_CONTINUE_ADDRESS,
      postalCode,
      houseNumber: Number(houseNumber),
      houseNumberSuffix,
    });
  };

  const getFullAddressStatus = () => {
    const copy =
      data && isValidInput
        ? address.medium({ ...data, houseNumberSuffix })
        : isValidating
          ? f('canRegisterAddress.isLoadingAddressText.value')
          : error
            ? f('canRegisterAddress.addressUnknownText.value')
            : null;

    // return copy;
    if (copy) {
      return (
        <Text as="p" size="BodyM" color="textPrimary">
          {copy}
        </Text>
      );
    }
    return null;
  };

  const formErrorMessages: { [key: string]: string } = {
    postalCodeRequired: f('canRegisterAddress.postalCodeFormField.value.requiredMessage'),
    postalCodeInvalid: f('canRegisterAddress.postalCodeFormField.value.validationMessage'),
    houseNumberRequired: f('canRegisterAddress.houseNumberFormField.value.requiredMessage'),
    houseNumberSuffixInvalid: f('canRegisterAddress.houseNumberSuffixFormField.value.validationMessage'),
  };

  const handleOnFocus = () => {
    if (formHasReceivedFocus) return;
    trackingStart();
    setFormHasReceivedFocus(true);
  };

  return (
    <Stack gap="4">
      <Form onSubmit={handleSubmit(submitForm)} onFocus={handleOnFocus}>
        <Stack gap="6">
          <NavLink
            href={f('canRegisterAddress.backLink.value.href')}
            variant="secondary"
            leftIcon={<ChevronLeftIcon />}>
            {f('canRegisterAddress.backLink.value.text')}
          </NavLink>
          <Heading as="h1" size="XS">
            {f('canRegisterAddress.title.value')}
          </Heading>
          <Stack gap="3">
            <Text>{f('canRegisterAddress.introductionText.value')}</Text>

            {fields.canRegisterAddress?.modalDialog?.value?.triggerText && (
              <TrackedDialog
                trigger={
                  <TextLink rightIcon={<InfoIcon size="small" />}>
                    {f('canRegisterAddress.modalDialog.value.triggerText')}
                  </TextLink>
                }
                title={f('canRegisterAddress.modalDialog.value.title')}>
                <TrackViewComponent type="dialog" label={f('canRegisterAddress.modalDialog.value.title')}>
                  <Stack alignX="start" gap="4">
                    <RichText html={f('canRegisterAddress.modalDialog.value.content')} />
                  </Stack>
                </TrackViewComponent>
              </TrackedDialog>
            )}
          </Stack>
          <Stack gap="2">
            <Stack direction="row" gap="2">
              <Stack.Item grow>
                <Controller
                  control={control}
                  name="postalCode"
                  render={({ field: { onChange, value, name } }) => (
                    <PostalCodeInput
                      error={errors?.postalCode?.message && formErrorMessages[errors.postalCode.message]}
                      label={f('canRegisterAddress.postalCodeFormField.value.label')}
                      name={name}
                      onChange={onChange}
                      placeholder={f('canRegisterAddress.postalCodeFormField.value.placeholder')}
                      value={value}
                    />
                  )}
                />
              </Stack.Item>
              <Stack.Item grow>
                <Controller
                  control={control}
                  name="houseNumber"
                  render={({ field: { onChange, name, value } }) => (
                    <InputText
                      error={errors?.houseNumber?.message && formErrorMessages[errors.houseNumber.message]}
                      label={f('canRegisterAddress.houseNumberFormField.value.label')}
                      name={name}
                      placeholder={f('canRegisterAddress.houseNumberFormField.value.placeholder')}
                      onChange={event => onChange(maskHouseNumber(event))}
                      value={value}
                    />
                  )}
                />
              </Stack.Item>
              <Stack.Item grow>
                <InputSelect
                  {...register('houseNumberSuffix')}
                  error={errors?.houseNumberSuffix?.message && formErrorMessages[errors.houseNumberSuffix.message]}
                  isDisabled={!houseNumberSuffixOptions.length}
                  options={houseNumberSuffixOptions}
                  placeholder={' '}
                  label={f('canRegisterAddress.houseNumberSuffixFormField.value.label')}
                />
              </Stack.Item>
            </Stack>
            {getFullAddressStatus()}
          </Stack>
          <Button type="submit" isLoading={isValidating}>
            {f('canRegisterAddress.continueButtonText.value')}
          </Button>
        </Stack>
      </Form>
      <Placeholder name="jss-registration-bottom" />
    </Stack>
  );
};
