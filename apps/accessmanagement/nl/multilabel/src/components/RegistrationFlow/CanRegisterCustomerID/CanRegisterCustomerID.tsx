import React, { FC, useContext } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';

import { getStaticPrefix } from '@common/env';
import CustomerIdInput from '@components/CustomerIdInput/CustomerIdInput';
import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import useDC from '@dc/useDC';
import { useContent } from '@sitecore/common';
import { Button, Form, Heading, NavLink, Stack, Text, TextLink, Image } from '@sparky';
import { ChevronLeftIcon, InfoIcon } from '@sparky/icons';
import { TrackViewComponent } from '@tracking';

import { RenderingContentType } from '../registrationFlow.types';
import { canRegisterCustomerIDSchema } from './canRegisterCustomerIDSchema';
import { Action, State } from '../registrationFlow.constants';
import { RegistrationMachine } from '../registrationFlow.machine';

const resolver = yupResolver(canRegisterCustomerIDSchema);

type FormValues = {
  customerId: number;
};

export const CanRegisterCustomerID: FC = () => {
  const { label } = useDC();
  const { f } = useContent<RenderingContentType>();
  const { send, state } = useContext(RegistrationMachine);
  const isValidatingCustomer = state.value === State.VALIDATING_CUSTOMER;
  const { isInvalidCombination } = state.context;
  const {
    formState: { errors },
    handleSubmit,
    control,
  } = useForm<FormValues>({
    defaultValues: {
      customerId: state.context.customerId,
    },
    resolver,
  });
  const handleBack = () => send({ type: Action.BACK });

  const submitForm: SubmitHandler<FormValues> = ({ customerId }) => {
    send({ type: Action.CAN_REGISTER_CONTINUE_CUSTOMER_ID, customerId });
  };

  const formErrorMessages: { [key: string]: string } = {
    customerIdRequired: f('canRegisterCustomerID.customerIdFormField.value.requiredMessage'),
    customerIdInvalid: f('canRegisterCustomerID.customerIdFormField.value.validationMessage'),
  };

  return (
    <Form onSubmit={handleSubmit(submitForm)}>
      <Stack gap="6">
        <NavLink variant="secondary" onClick={handleBack} leftIcon={<ChevronLeftIcon />}>
          {f('canRegisterCustomerID.backLinkText.value')}
        </NavLink>
        <Heading as="h1" size="XS">
          {f('canRegisterCustomerID.title.value')}
        </Heading>
        {isInvalidCombination && (
          <TrackedNotificationBox
            label={f('canRegisterCustomerID.errorNotificationField.value.content')}
            isAlert={true}
            text={<RichText html={f('canRegisterCustomerID.errorNotificationField.value.content')} />}
            variant="error"
          />
        )}
        <Stack gap="3">
          <Text>{f('canRegisterCustomerID.introductionText.value')}</Text>
          <TrackedDialog
            trigger={
              <TextLink rightIcon={<InfoIcon size="small" />}>
                {f('canRegisterCustomerID.modalDialog.value.triggerText')}
              </TextLink>
            }
            title={f('canRegisterCustomerID.modalDialog.value.title')}>
            <TrackViewComponent type="dialog" label={f('canRegisterCustomerID.modalDialog.value.title')}>
              <Stack gap="4">
                <Stack.Item>
                  <RichText html={f('canRegisterCustomerID.inEmailModalDescription.value')} />
                </Stack.Item>
                <Stack.Item>
                  <Image
                    src={`${getStaticPrefix()}/images/${label}/registration/id_in_email.svg`}
                    alt={f('canRegisterCustomerID.inEmailModalLabelText.value')}
                  />
                </Stack.Item>
                <Stack.Item>
                  <RichText html={f('canRegisterCustomerID.onBankStatementModalDescription.value')} />
                </Stack.Item>
                <Stack.Item>
                  <Image
                    src={`${getStaticPrefix()}/images/${label}/registration/id_on_bank_statement.svg`}
                    alt={f('canRegisterCustomerID.onBankStatementModalLabelText.value')}
                  />
                </Stack.Item>
                <Stack.Item>
                  <RichText html={f('canRegisterCustomerID.onYearnoteModalDescription.value')} />
                </Stack.Item>
                <Stack.Item>
                  <Image
                    src={`${getStaticPrefix()}/images/${label}/registration/id_on_yearnote.svg`}
                    alt={f('canRegisterCustomerID.onYearnoteModalLabelText.value')}
                  />
                </Stack.Item>
              </Stack>
            </TrackViewComponent>
          </TrackedDialog>
        </Stack>
        <Controller
          control={control}
          name="customerId"
          render={({ field: { onChange, value, name } }) => (
            <CustomerIdInput
              error={errors?.customerId?.message && formErrorMessages[errors.customerId.message]}
              label={f('canRegisterCustomerID.customerIdFormField.value.label')}
              name={name}
              onChange={onChange}
              value={value || ''}
            />
          )}
        />
        <Button type="submit" isLoading={isValidatingCustomer}>
          {f('canRegisterCustomerID.continueButtonText.value')}
        </Button>
      </Stack>
    </Form>
  );
};
