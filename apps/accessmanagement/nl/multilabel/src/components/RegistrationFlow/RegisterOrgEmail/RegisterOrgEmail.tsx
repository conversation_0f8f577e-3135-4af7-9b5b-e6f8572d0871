import React, { FC, useContext, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { SubmitHandler, useForm } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import { useContent } from '@sitecore/common';
import { Button, Form, Heading, InputEmail, Stack, Text, TextLink, NavLink } from '@sparky';
import { ChevronLeftIcon, InfoIcon, PlusIcon, MinusIcon } from '@sparky/icons';
import { TrackViewComponent } from '@tracking';

import { RenderingContentType } from '../registrationFlow.types';
import { registerOrgEmailSchema } from './registerOrgEmailSchema';
import { Action } from '../registrationFlow.constants';
import { RegistrationMachine } from '../registrationFlow.machine';

const resolver = yupResolver(registerOrgEmailSchema);

type FormValues = {
  emailAddress: string;
  primaryEmailOverride: string;
};

export const RegisterOrgEmail: FC = () => {
  const { f } = useContent<RenderingContentType>();
  const { send, state } = useContext(RegistrationMachine);
  const [showPrimaryEmailOverride, setShowPrimaryEmailOverride] = useState<boolean | null>(
    state.context.primaryEmailOverride ? true : null,
  );
  const { hasError } = state.context;
  const {
    formState: { errors },
    handleSubmit,
    register,
    setValue,
  } = useForm<FormValues>({
    defaultValues: {
      emailAddress: state.context.emailAddress,
      primaryEmailOverride: state.context.primaryEmailOverride || '',
    },
    resolver,
  });
  const handleBack = () => send({ type: Action.BACK });

  const submitForm: SubmitHandler<FormValues> = ({ emailAddress, primaryEmailOverride }) => {
    send({ type: Action.CUSTOMER_SET_EMAIL, emailAddress, primaryEmailOverride });
  };

  const handleClickSetPrimairyEmailOverride = (setOveride: boolean) => {
    setShowPrimaryEmailOverride(setOveride);

    if (setOveride === false) {
      setValue('primaryEmailOverride', '');
    }
  };

  const formErrorMessages: { [key: string]: string } = {
    emailInvalid: f('registerOrgEmail.emailFormField.value.validationMessage'),
    emailRequired: f('registerOrgEmail.emailFormField.value.requiredMessage'),
    emailOverrideInvalid: f('registerOrgEmail.emailFormField.value.validationMessage'),
  };

  return (
    <Form onSubmit={handleSubmit(submitForm)}>
      <Stack gap="6">
        <NavLink variant="secondary" onClick={handleBack} leftIcon={<ChevronLeftIcon />}>
          {f('registerOrgEmail.backLinkText.value')}
        </NavLink>
        <Heading as="h1" size="XS">
          {f('registerOrgEmail.title.value')}
        </Heading>
        {hasError && (
          <TrackedNotificationBox
            label={f('registerOrgEmail.technicalErrorNotificationField.value.title')}
            title={f('registerOrgEmail.technicalErrorNotificationField.value.title')}
            isAlert
            text={<RichText html={f('registerOrgEmail.technicalErrorNotificationField.value.content')} />}
            variant="error"
          />
        )}
        <Stack gap="3">
          <RichText
            html={
              showPrimaryEmailOverride
                ? f('registerOrgEmail.overrideDescription.value')
                : f('registerOrgEmail.introductionDescription.value')
            }
          />
          <TrackedDialog
            trigger={
              <TextLink rightIcon={<InfoIcon size="small" />}>
                {f('registerOrgEmail.modalDialog.value.triggerText')}
              </TextLink>
            }
            title={
              showPrimaryEmailOverride
                ? f('registerOrgEmail.overrideEmailForLoginCodeModalDialog.value.title')
                : f('registerOrgEmail.modalDialog.value.title')
            }>
            <TrackViewComponent type="dialog" label={f('registerOrgEmail.modalDialog.value.title')}>
              <Stack gap="4">
                <RichText
                  html={
                    showPrimaryEmailOverride
                      ? f('registerOrgEmail.overrideEmailForLoginCodeModalDialog.value.content')
                      : f('registerOrgEmail.modalDialog.value.content')
                  }
                />
              </Stack>
            </TrackViewComponent>
          </TrackedDialog>
        </Stack>
        <Stack gap="3">
          <InputEmail
            {...register('emailAddress')}
            label={
              showPrimaryEmailOverride
                ? f('registerOrgEmail.overrideEmailForUsernameLabel.value')
                : f('registerOrgEmail.emailFormField.value.label')
            }
            autoComplete="email"
            error={errors?.emailAddress?.message && formErrorMessages[errors.emailAddress.message]}
            placeholder={f('registerOrgEmail.emailFormField.value.placeholder')}
            hint={showPrimaryEmailOverride ? f('registerOrgEmail.overrideEmailForUsernameHintLabel.value') : ''}
            defaultValue={state.context.emailAddress}
          />
          {!showPrimaryEmailOverride && (
            <Text>
              <TextLink
                emphasis="medium"
                onClick={() => {
                  handleClickSetPrimairyEmailOverride(true);
                }}>
                <PlusIcon color="iconPrimary" />
                {f('registerOrgEmail.emailOverrideLinkText.value')}
              </TextLink>
            </Text>
          )}
        </Stack>

        {showPrimaryEmailOverride && (
          <Stack gap="2">
            <InputEmail
              {...register('primaryEmailOverride')}
              label={f('registerOrgEmail.overrideEmailForLoginCodeLabel.value')}
              autoComplete="email"
              error={errors?.primaryEmailOverride?.message && formErrorMessages[errors.primaryEmailOverride.message]}
              placeholder={f('registerOrgEmail.emailFormField.value.placeholder')}
              hint={f('registerOrgEmail.overrideEmailForLoginCodeHintLabel.value')}
              defaultValue={state.context.primaryEmailOverride || ''}
            />
            <Text>
              <TextLink
                emphasis="medium"
                onClick={() => {
                  handleClickSetPrimairyEmailOverride(false);
                }}>
                <MinusIcon color="iconPrimary" />
                {f('registerOrgEmail.emailOverrideCloseLinkText.value')}
              </TextLink>
            </Text>
          </Stack>
        )}

        <Stack gap="2">
          <Button type="submit">{f('registerOrgEmail.confirmButtonText.value')}</Button>
        </Stack>
      </Stack>
    </Form>
  );
};
