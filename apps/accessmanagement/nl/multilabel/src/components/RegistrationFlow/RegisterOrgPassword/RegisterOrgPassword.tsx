import React, { FC, useContext } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { SubmitHandler, useForm } from 'react-hook-form';

import { useContent } from '@sitecore/common';
import { Button, Form, Heading, Stack, Text, NavLink, InputPassword } from '@sparky';
import { ChevronLeftIcon } from '@sparky/icons';

import { RenderingContentType } from '../registrationFlow.types';
import { registerOrgPasswordSchema } from './registerOrgPasswordSchema';
import { Action, State } from '../registrationFlow.constants';
import { RegistrationMachine } from '../registrationFlow.machine';

const resolver = yupResolver(registerOrgPasswordSchema);

type FormValues = {
  password: string;
};

export const RegisterOrgPassword: FC = () => {
  const { f } = useContent<RenderingContentType>();
  const { send, state } = useContext(RegistrationMachine);
  const isSettingNewCredentials = state.value === State.SETTING_NEW_CREDENTIALS;
  const {
    formState: { errors },
    handleSubmit,
    register,
  } = useForm<FormValues>({
    resolver,
  });
  const handleBack = () => send({ type: Action.BACK });

  const submitForm: SubmitHandler<FormValues> = ({ password }) => {
    send({ type: Action.CUSTOMER_SET_PASSWORD, password });
  };

  const formErrorMessages: { [key: string]: string } = {
    passwordInvalid: f('registerOrgPassword.passwordFormField.value.validationMessage'),
    passwordRequired: f('registerOrgPassword.passwordFormField.value.requiredMessage'),
  };

  return (
    <Form onSubmit={handleSubmit(submitForm)}>
      <Stack gap="6">
        <NavLink variant="secondary" onClick={handleBack} leftIcon={<ChevronLeftIcon />}>
          E-mailadres
        </NavLink>
        <Heading as="h1" size="XS">
          {f('registerOrgPassword.title.value')}
        </Heading>
        <Text>{f('registerOrgPassword.introductionText.value')}</Text>
        <InputPassword
          {...register('password')}
          label={f('registerOrgPassword.passwordFormField.value.label')}
          autoComplete="new-password"
          hint={f('registerOrgPassword.passwordFormField.value.hint')}
          error={errors?.password?.message && formErrorMessages[errors.password.message]}
          isReadOnly={isSettingNewCredentials}
        />
        <Button type="submit" isLoading={isSettingNewCredentials}>
          {f('registerOrgPassword.confirmButtonText.value')}
        </Button>
      </Stack>
    </Form>
  );
};
