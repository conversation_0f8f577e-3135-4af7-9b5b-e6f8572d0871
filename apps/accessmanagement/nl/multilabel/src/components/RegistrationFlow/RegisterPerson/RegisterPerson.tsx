import React, { FC, useContext } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { SubmitHandler, useForm } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import { useContent } from '@sitecore/common';
import { Button, Form, Heading, InputEmail, Stack, Text, NavLink, TextLink, InputPassword } from '@sparky';
import { ChevronLeftIcon, InfoIcon } from '@sparky/icons';
import { TrackViewComponent } from '@tracking';

import { RenderingContentType } from '../registrationFlow.types';
import { registerPersonSchema } from './registerPersonSchema';
import { Action, State } from '../registrationFlow.constants';
import { RegistrationMachine } from '../registrationFlow.machine';

const resolver = yupResolver(registerPersonSchema);

type FormValues = {
  emailAddress: string;
  password: string;
};

const ERROR_KEYS = {
  CONFLICT: 'conflictErrorNotification',
  TECHNICAL: 'technicalErrorNotificationField',
} as const;

export const RegisterPerson: FC = () => {
  const { f } = useContent<RenderingContentType>();
  const { send, state } = useContext(RegistrationMachine);
  const isSettingNewCredentials = state.value === State.SETTING_NEW_CREDENTIALS;
  const { hasError, errorStatusCode } = state.context;
  const {
    formState: { errors },
    handleSubmit,
    register,
  } = useForm<FormValues>({
    defaultValues: {
      emailAddress: state.context.emailAddress,
    },
    resolver,
  });
  const handleBack = () => send({ type: Action.BACK });

  const submitForm: SubmitHandler<FormValues> = ({ emailAddress, password }) => {
    send({ type: Action.CUSTOMER_SET_CREDENTIALS, emailAddress, password });
  };

  const formErrorMessages: { [key: string]: string } = {
    emailRequired: f('registerPerson.emailFormField.value.requiredMessage'),
    emailInvalid: f('registerPerson.emailFormField.value.validationMessage'),
    passwordRequired: f('registerPerson.passwordFormField.value.requiredMessage'),
    passwordInvalid: f('registerPerson.passwordFormField.value.validationMessage'),
  };

  const errorKey = errorStatusCode === 409 ? ERROR_KEYS.CONFLICT : ERROR_KEYS.TECHNICAL;

  return (
    <Form onSubmit={handleSubmit(submitForm)}>
      <Stack gap="6">
        <NavLink variant="secondary" onClick={handleBack} leftIcon={<ChevronLeftIcon />}>
          {f('registerPerson.backLinkText.value')}
        </NavLink>
        <Heading as="h1" size="XS">
          {f('registerPerson.title.value')}
        </Heading>
        {hasError && (
          <TrackedNotificationBox
            label={f(`registerPerson.${errorKey}.value.title`)}
            title={f(`registerPerson.${errorKey}.value.title`)}
            isAlert
            text={<RichText html={f(`registerPerson.${errorKey}.value.content`)} />}
            variant="error"
          />
        )}
        <Stack gap="3">
          <Text>{f('registerPerson.introductionText.value')}</Text>
          <TrackedDialog
            trigger={
              <TextLink rightIcon={<InfoIcon size="small" />}>
                {f('registerPerson.modalDialog.value.triggerText')}
              </TextLink>
            }
            title={f('registerPerson.modalDialog.value.title')}>
            <TrackViewComponent type="dialog" label={f('registerPerson.modalDialog.value.title')}>
              <Stack gap="4">
                <RichText html={f('registerPerson.modalDialog.value.content')} />
              </Stack>
            </TrackViewComponent>
          </TrackedDialog>
        </Stack>
        <InputEmail
          {...register('emailAddress')}
          label={f('registerPerson.emailFormField.value.label')}
          autoComplete="email"
          error={errors?.emailAddress?.message && formErrorMessages[errors.emailAddress.message]}
          isReadOnly={isSettingNewCredentials}
          placeholder={f('registerPerson.emailFormField.value.placeholder')}
          defaultValue={state.context.emailAddress}
        />
        <InputPassword
          {...register('password')}
          label={f('registerPerson.passwordFormField.value.label')}
          autoComplete="new-password"
          hint={f('registerPerson.passwordFormField.value.hint')}
          error={errors?.password?.message && formErrorMessages[errors.password.message]}
          isReadOnly={isSettingNewCredentials}
        />
        <Button type="submit" isLoading={isSettingNewCredentials}>
          {f('registerPerson.continueButtonText.value')}
        </Button>
      </Stack>
    </Form>
  );
};
