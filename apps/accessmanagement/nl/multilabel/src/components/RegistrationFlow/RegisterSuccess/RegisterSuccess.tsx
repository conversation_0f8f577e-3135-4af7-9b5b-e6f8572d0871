import React, { <PERSON> } from 'react';

import { useContent } from '@sitecore/common';
import { Stack, ButtonLink, Text, Heading } from '@sparky';

import { RenderingContentType } from '../registrationFlow.types';

export const RegisterSuccess: FC = () => {
  const { f } = useContent<RenderingContentType>();
  return (
    <Stack gap="6">
      <Heading as="h1" size="XS">
        {f('registerSuccess.title.value')}
      </Heading>
      <Text>{f('registerSuccess.introductionText.value')}</Text>
      <ButtonLink href={f('registerSuccess.link.value.href')}>{f('registerSuccess.link.value.text')}</ButtonLink>
    </Stack>
  );
};
