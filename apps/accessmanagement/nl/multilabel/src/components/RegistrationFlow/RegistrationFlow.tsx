import React, { FC, useEffect, useMemo } from 'react';

import Logger from '@common/log';
import ComponentError from '@components/ui/ComponentError/ComponentError';
import useDC from '@dc/useDC';
import { useRouter } from '@dxp-next';
import { useActorRef, useSelector } from '@eneco-packages/xstate-custom/xstate-react';
import { useContent } from '@sitecore/common';
import { useTracking } from '@tracking';

import { CanRegisterAddress } from './CanRegisterAddress/CanRegisterAddress';
import { CanRegisterCustomerID } from './CanRegisterCustomerID/CanRegisterCustomerID';
import { Layout } from './Layout';
import { RegisterOrgEmail } from './RegisterOrgEmail/RegisterOrgEmail';
import { RegisterOrgPassword } from './RegisterOrgPassword/RegisterOrgPassword';
import { RegisterPerson } from './RegisterPerson/RegisterPerson';
import { RegisterSuccess } from './RegisterSuccess/RegisterSuccess';
import { flowVariants, FunnelSteps, PageViews, State } from './registrationFlow.constants';
import { RegistrationMachine, registrationMachine } from './registrationFlow.machine';
import { FlowVariant, RegistrationMachineState, RenderingContentType } from './registrationFlow.types';

const RegistrationFlow: FC = () => {
  const { componentName } = useContent<RenderingContentType>();

  // use Sitecore component name to decide flow variant because this component is shared by AccountCreate and AccountReset flows
  const flowVariant = flowVariants.find(flowVariant => flowVariant === componentName);

  if (!flowVariant) {
    Logger.error(
      'aX4j9Z2',
      `Registration flow is only supported with the following component names: ${flowVariants.join(', ')}`,
    );

    return <ComponentError />;
  }

  return <RegistrationFlowComponent flowVariant={flowVariant} />;
};

const RegistrationFlowComponent: FC<{ flowVariant: FlowVariant }> = ({ flowVariant }) => {
  const { label, businessUnit } = useDC();
  const { activePath } = useRouter();

  const { trackFunnelStart, trackFunnelStep, trackFunnelCompleted, trackPageView } = useTracking(flowVariant);

  const machine = useMemo(
    () => registrationMachine({ businessUnit, flowVariant, label }),
    [businessUnit, flowVariant, label],
  );
  const service = useActorRef(machine);
  const state: RegistrationMachineState = useSelector(service, state => state);
  const { send } = service;

  useEffect(() => {
    const currentPage = PageViews[state.value as keyof typeof PageViews];

    const getPreviousPage = () => {
      switch (true) {
        case state.value === State.CAN_REGISTER_ADDRESS:
          return '';
        case state.history?.value === State.VALIDATING_CUSTOMER:
          return PageViews.CAN_REGISTER_CUSTOMER_ID;
        case state.value === State.REGISTER_SUCCESS && state.context.customerType === 'person':
          return PageViews.REGISTER_PERSON;
        case state.value === State.REGISTER_SUCCESS && state.context.customerType === 'organisation':
          return PageViews.REGISTER_ORG_PASSWORD;
        default:
          return PageViews[state.history?.value as keyof typeof PageViews];
      }
    };

    if (currentPage) {
      const pageName = `${activePath}${currentPage}/`;
      const previousPage = getPreviousPage()
        ? `${window.location.origin}${activePath}${getPreviousPage()}/`
        : `${window.location.origin}${activePath}`;

      trackPageView({
        pageName,
        previousPage,
      });
    }

    const funnelStep = FunnelSteps[state.value as keyof typeof FunnelSteps];
    if (funnelStep && state.value !== State.REGISTER_SUCCESS && state.value !== State.CAN_REGISTER_ADDRESS) {
      trackFunnelStep({ step: funnelStep });
    }
    if (funnelStep && state.value === State.REGISTER_SUCCESS) trackFunnelCompleted({ step: funnelStep });
  }, [
    state.value,
    state.history?.value,
    state.context.customerType,
    activePath,
    trackFunnelStep,
    trackFunnelCompleted,
    trackPageView,
  ]);

  const customerType = state.context.customerType;

  const registrationStep = () => {
    switch (state.value) {
      case State.CAN_REGISTER_ADDRESS:
        return (
          <CanRegisterAddress trackingStart={() => trackFunnelStart({ step: FunnelSteps.CAN_REGISTER_ADDRESS })} />
        );
      case State.CAN_REGISTER_CUSTOMER_ID:
      case State.VALIDATING_CUSTOMER:
        return <CanRegisterCustomerID />;
      case State.REGISTER_ORG_EMAIL:
        return <RegisterOrgEmail />;
      case State.REGISTER_ORG_PASSWORD:
        return <RegisterOrgPassword />;
      case State.REGISTER_PERSON:
        return <RegisterPerson />;
      case State.SETTING_NEW_CREDENTIALS:
        switch (customerType) {
          case 'organisation':
            return <RegisterOrgPassword />;
          case 'person':
          default:
            return <RegisterPerson />;
        }
      case State.REGISTER_SUCCESS:
        return <RegisterSuccess />;
      default:
        return null;
    }
  };

  return (
    <RegistrationMachine.Provider value={{ state, send }}>
      <Layout>{registrationStep()}</Layout>
    </RegistrationMachine.Provider>
  );
};

export default RegistrationFlow;
