import { render, screen, within } from '@testing-library/react';
import userEvent, { UserEvent } from '@testing-library/user-event';

import * as UserAccountPublicService from '@dc/services/UserAccountsPublicService';
import { emptySuffixString } from '@hooks/address';
import { TestAppProviders } from '@jest-tools/TestAppProviders';
import registrationAccountCreateMock from '@mocks/sitecore/apps/accessmanagement/nl/eneco/registrationAccountCreate';
import ContentCard from '@sitecore/components/content/ContentCard/ContentCard';

import RegistrationFlow from '../RegistrationFlow';
import { FlowVariant } from '../registrationFlow.types';

const { fields: registrationMock, placeholders } = registrationAccountCreateMock('eneco');

const contentCardMock = placeholders['jss-registration-bottom'][0].fields;

describe('RegistrationFlow', () => {
  let registerSpy: jest.SpyInstance;
  let user: UserEvent;

  beforeEach(() => {
    user = userEvent.setup();

    render(
      <TestAppProviders
        rendering={registrationAccountCreateMock('eneco')}
        components={{
          ContentCard: ContentCard,
        }}>
        <RegistrationFlow />
      </TestAppProviders>,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
    if (registerSpy) registerSpy.mockRestore();
  });

  describe('personal account', () => {
    it('should let a regular user (person) create new login credentials', async () => {
      // First step of the form, verify customer exists
      expect(await screen.findByText(registrationMock.canRegisterAddress.title.value)).toBeVisible();

      expect(await screen.findByText(contentCardMock.content.value)).toBeVisible();

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      await user.type(postalCodeInput, '1234AB');

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      await user.type(houseNumberInput, '56');

      expect(await screen.findByText(/Amstelveenseweg 56, Amsterdam/)).toBeVisible();

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      // Second step : enter customer ID
      const customerIdInput = await screen.findByRole('textbox', {
        name: registrationMock.canRegisterCustomerID.customerIdFormField.value.label,
      });

      expect(screen.queryByText(contentCardMock.content.value)).not.toBeInTheDocument();

      await user.type(customerIdInput, '12345');

      const continueButtonCustomerId = screen.getByRole('button', {
        name: registrationMock.canRegisterCustomerID.continueButtonText.value,
      });
      await user.click(continueButtonCustomerId);

      // third step of the form, enter e-mail and password to create account
      const emailInput = await screen.findByLabelText(registrationMock.registerPerson.emailFormField.value.label);

      expect(screen.queryByText(contentCardMock.content.value)).not.toBeInTheDocument();

      await user.type(emailInput, '<EMAIL>');

      const passwordInput = screen.getByLabelText(registrationMock.registerPerson.passwordFormField.value.label);
      await user.type(passwordInput, 'aaaaaaA1');

      const confirmButton = screen.getByRole('button', {
        name: registrationMock.registerPerson.continueButtonText.value,
      });
      await user.click(confirmButton);

      // fourth step of the form, show success message
      expect(await screen.findByText(registrationMock.registerSuccess.title.value)).toBeVisible();

      expect(screen.queryByText(contentCardMock.content.value)).not.toBeInTheDocument();

      const toLoginButton = screen.getByRole('link', { name: registrationMock.registerSuccess.link.value.text });
      expect(toLoginButton).toBeVisible();
    });

    it('should save address data when user continues to second step (analytics purposes)', async () => {
      const spy = jest.spyOn(UserAccountPublicService, 'registerAccountCreationInitiatedEvent');

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      const postalCode = '1234AB';
      await user.type(postalCodeInput, postalCode);

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      const houseNumber = 56;
      await user.type(houseNumberInput, houseNumber.toString());

      expect(await screen.findByText(/Amstelveenseweg 56, Amsterdam/)).toBeVisible();

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          requestBody: expect.objectContaining({
            data: expect.objectContaining({
              address: expect.objectContaining({ houseNumber, houseNumberSuffix: '', postalCode }),
            }),
          }),
        }),
      );
    });

    it('should allow the user to continue to second step with an address that was not found', async () => {
      // the address might be not found but is tied to the customer
      // First step of the form, verify customer exists
      expect(await screen.findByText(registrationMock.canRegisterAddress.title.value)).toBeVisible();

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      await user.type(postalCodeInput, '1234AB');

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      await user.type(houseNumberInput, '5');

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      expect(await screen.findByText(/Adres niet gevonden/)).toBeVisible();
      await user.click(continueButtonAddress);
      const nextScreen = screen.findByRole('textbox', {
        name: registrationMock.canRegisterCustomerID.customerIdFormField.value.label,
      });
      expect(await nextScreen).toBeInTheDocument();
    });

    it('should not allow the user to continue to second step with an address missing the suffix (when its required)', async () => {
      // First step of the form, verify customer exists
      expect(await screen.findByText(registrationMock.canRegisterAddress.title.value)).toBeVisible();

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      await user.type(postalCodeInput, '1234AB');

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      await user.type(houseNumberInput, '57');

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      expect(await screen.findByText(/Amsterdamseweg 57, Amstelveen/)).toBeVisible();

      await user.click(continueButtonAddress);
      expect(
        await screen.findByText(registrationMock.canRegisterAddress.houseNumberSuffixFormField.value.validationMessage),
      ).toBeVisible();
    });
    it('should accept a null suffix if such an option exists', async () => {
      // First step of the form, verify customer exists
      expect(await screen.findByText(registrationMock.canRegisterAddress.title.value)).toBeVisible();

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      await user.type(postalCodeInput, '1234AB');

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      await user.type(houseNumberInput, '55');

      expect(await screen.findByText(/Amsterdamseweg 55, Amstelveen/)).toBeVisible();

      const selectElement = screen.getByRole('combobox', {
        name: registrationMock.canRegisterAddress.houseNumberSuffixFormField.value.label,
      });
      const options = within(selectElement)
        .getAllByRole('option')
        .map(option => (option as HTMLOptionElement).value);
      const nullValue = emptySuffixString;
      const nullOptionIndex = options.findIndex(option => option === nullValue);
      expect(nullOptionIndex).toBeGreaterThan(-1);

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      expect(
        await screen.findByRole('textbox', {
          name: registrationMock.canRegisterCustomerID.customerIdFormField.value.label,
        }),
      ).toBeVisible();
    });

    it('should display username conflict error when the user tries to register with an existing email address', async () => {
      registerSpy = jest.spyOn(UserAccountPublicService, 'register').mockRejectedValue({
        status: 409,
      });

      const postalCodeInput = screen.getByLabelText('Postcode');
      const postalCode = '1234AB';
      await user.type(postalCodeInput, postalCode);

      const houseNumberInput = screen.getByLabelText('Huisnummer');
      const houseNumber = 56;
      await user.type(houseNumberInput, houseNumber.toString());

      await screen.findByText(/Amstelveenseweg 56, Amsterdam/);

      const continueButtonAddress = screen.getByRole('button', { name: 'Ga verder' });
      await user.click(continueButtonAddress);

      const customerIdInput = await screen.findByRole('textbox', { name: 'Klantnummer' });
      const customerId = '12345';
      await user.type(customerIdInput, customerId);

      const continueButtonCustomerId = screen.getByRole('button', { name: 'Ga verder' });
      await user.click(continueButtonCustomerId);

      const emailInput = await screen.findByLabelText('E-mailadres');
      const emailAddress = '<EMAIL>';
      await user.type(emailInput, emailAddress);

      const passwordInput = screen.getByLabelText('Wachtwoord');
      const password = 'aaaaaaA1.';
      await user.type(passwordInput, password);

      const confirmButton = screen.getByRole('button', { name: 'Bevestigen' });
      await user.click(confirmButton);

      const errorNotification = await screen.findByText(
        registrationMock.registerPerson.conflictErrorNotification.value.title,
      );
      expect(errorNotification).toBeVisible();
    });
    it('registers the account on submit', async () => {
      const spy = jest.spyOn(UserAccountPublicService, 'register');

      const postalCodeInput = screen.getByLabelText('Postcode');
      const postalCode = '1234AB';
      await user.type(postalCodeInput, postalCode);

      const houseNumberInput = screen.getByLabelText('Huisnummer');
      const houseNumber = 56;
      await user.type(houseNumberInput, houseNumber.toString());

      await screen.findByText(/Amstelveenseweg 56, Amsterdam/);

      const continueButtonAddress = screen.getByRole('button', { name: 'Ga verder' });
      await user.click(continueButtonAddress);

      const customerIdInput = await screen.findByRole('textbox', { name: 'Klantnummer' });
      const customerId = '12345';
      await user.type(customerIdInput, customerId);

      const continueButtonCustomerId = screen.getByRole('button', { name: 'Ga verder' });
      await user.click(continueButtonCustomerId);

      const emailInput = await screen.findByLabelText('E-mailadres');
      const emailAddress = '<EMAIL>';
      await user.type(emailInput, emailAddress);

      const passwordInput = screen.getByLabelText('Wachtwoord');
      const password = 'aaaaaaA1';
      await user.type(passwordInput, password);

      const confirmButton = screen.getByRole('button', { name: 'Bevestigen' });
      await user.click(confirmButton);

      const source: FlowVariant = 'RegistrationAccountCreate';

      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          requestBody: expect.objectContaining({
            data: expect.objectContaining({
              emailAddress,
              password,
              source,
              address: expect.objectContaining({ houseNumber, houseNumberSuffix: '', postalCode }),
            }),
          }),
        }),
      );
    });
  });
  describe('organisation account', () => {
    it('should let a organisational user ("MKB") create new login credentials with a single email address', async () => {
      // First step of the form, verify customer exists
      expect(await screen.findByText(registrationMock.canRegisterAddress.title.value)).toBeVisible();

      expect(await screen.findByText(contentCardMock.content.value)).toBeVisible();

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      await user.type(postalCodeInput, '1234AB');

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      await user.type(houseNumberInput, '56');

      expect(await screen.findByText(/Amstelveenseweg 56, Amsterdam/)).toBeVisible();

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      const customerIdInput = await screen.findByRole('textbox', {
        name: registrationMock.canRegisterCustomerID.customerIdFormField.value.label,
      });
      await user.type(customerIdInput, '100'); // 1 = mocked as organisation/"MKB" account

      const continueButtonCustomerId = screen.getByRole('button', {
        name: registrationMock.canRegisterCustomerID.continueButtonText.value,
      });
      await user.click(continueButtonCustomerId);

      // Second step of the form, enter e-mail and password to create account
      const emailInput = await screen.findByLabelText(registrationMock.registerOrgEmail.emailFormField.value.label);

      expect(screen.queryByText(contentCardMock.content.value)).not.toBeInTheDocument();

      await user.type(emailInput, '<EMAIL>');

      const continueButtonEmailStep = screen.getByRole('button', {
        name: registrationMock.registerOrgEmail.confirmButtonText.value,
      });
      await user.click(continueButtonEmailStep);

      const passwordInput = await screen.findByLabelText(
        registrationMock.registerOrgPassword.passwordFormField.value.label,
      );
      await user.type(passwordInput, 'aaaaaaA1');

      const confirmButton = screen.getByRole('button', {
        name: registrationMock.registerOrgPassword.confirmButtonText.value,
      });
      await user.click(confirmButton);

      // Third step of the form, show success message
      expect(await screen.findByText(registrationMock.registerSuccess.title.value)).toBeVisible();

      // after first step should not contain the banner
      expect(screen.queryByText('Altijd je verbruik bij de hand? Registreer je via de app.')).not.toBeInTheDocument();

      const toLoginButton = screen.getByRole('link', { name: registrationMock.registerSuccess.link.value.text });
      expect(toLoginButton).toBeVisible();
    });

    it('should let a organisational user ("MKB") create new login credentials with two email addresses', async () => {
      // First step of the form, verify customer exists
      expect(await screen.findByText(registrationMock.canRegisterAddress.title.value)).toBeVisible();

      expect(await screen.findByText(contentCardMock.content.value)).toBeVisible();

      const postalCodeInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.postalCodeFormField.value.label,
      );
      await user.type(postalCodeInput, '1234AB');

      const houseNumberInput = screen.getByLabelText(
        registrationMock.canRegisterAddress.houseNumberFormField.value.label,
      );
      await user.type(houseNumberInput, '56');

      expect(await screen.findByText(/Amstelveenseweg 56, Amsterdam/)).toBeInTheDocument();

      const continueButtonAddress = screen.getByRole('button', {
        name: registrationMock.canRegisterAddress.continueButtonText.value,
      });
      await user.click(continueButtonAddress);

      const customerIdInput = await screen.findByRole('textbox', {
        name: registrationMock.canRegisterCustomerID.customerIdFormField.value.label,
      });
      await user.type(customerIdInput, '100'); // 1 = mocked as organisation/"MKB" account

      const continueButtonCustomerId = screen.getByRole('button', {
        name: registrationMock.canRegisterCustomerID.continueButtonText.value,
      });
      await user.click(continueButtonCustomerId);

      // Second step of the form, enter email addresses to create account
      const emailInput = await screen.findByLabelText(registrationMock.registerOrgEmail.emailFormField.value.label);
      const showSecondEmailFieldButton = screen.getByRole('button', {
        name: registrationMock.registerOrgEmail.emailOverrideLinkText.value,
      });
      await user.click(showSecondEmailFieldButton);
      const secondEmailInput = await screen.findByLabelText(
        registrationMock.registerOrgEmail.overrideEmailForLoginCodeLabel.value,
      );
      await user.type(emailInput, '<EMAIL>');
      await user.type(secondEmailInput, '<EMAIL>');

      const continueButtonEmailStep = screen.getByRole('button', {
        name: registrationMock.registerOrgEmail.confirmButtonText.value,
      });
      await user.click(continueButtonEmailStep);

      const passwordInput = await screen.findByLabelText(
        registrationMock.registerOrgPassword.passwordFormField.value.label,
      );
      await user.type(passwordInput, 'aaaaaaA1');

      const confirmButton = screen.getByRole('button', {
        name: registrationMock.registerOrgPassword.confirmButtonText.value,
      });
      await user.click(confirmButton);

      // Third step of the form, show success message
      expect(await screen.findByText(/Je account is aangemaakt/)).toBeVisible();
      const toLoginButton = screen.getByRole('link', { name: registrationMock.registerSuccess.link.value.text });
      expect(toLoginButton).toBeVisible();
    });

    it('registers the account on submit', async () => {
      const spy = jest.spyOn(UserAccountPublicService, 'register');

      const postalCodeInput = screen.getByLabelText('Postcode');
      const postalCode = '1234AB';
      await user.type(postalCodeInput, postalCode);

      const houseNumberInput = screen.getByLabelText('Huisnummer');
      const houseNumber = 56;
      await user.type(houseNumberInput, houseNumber.toString());

      await screen.findByText(/Amstelveenseweg 56, Amsterdam/);

      const continueButtonAddress = screen.getByRole('button', { name: 'Ga verder' });
      await user.click(continueButtonAddress);

      const customerIdInput = await screen.findByRole('textbox', { name: 'Klantnummer' });
      const customerId = '100';
      await user.type(customerIdInput, customerId);

      const continueButtonCustomerId = screen.getByRole('button', { name: 'Ga verder' });
      await user.click(continueButtonCustomerId);

      const emailInput = await screen.findByLabelText(registrationMock.registerOrgEmail.emailFormField.value.label);
      const showSecondEmailFieldButton = screen.getByRole('button', {
        name: registrationMock.registerOrgEmail.emailOverrideLinkText.value,
      });
      await user.click(showSecondEmailFieldButton);
      const secondEmailInput = await screen.findByLabelText(
        registrationMock.registerOrgEmail.overrideEmailForLoginCodeLabel.value,
      );
      const emailAddress = '<EMAIL>';
      const emailAddress2 = '<EMAIL>';
      await user.type(emailInput, emailAddress);
      await user.type(secondEmailInput, emailAddress2);

      const continueButtonEmailStep = screen.getByRole('button', {
        name: registrationMock.registerOrgEmail.confirmButtonText.value,
      });
      await user.click(continueButtonEmailStep);

      const passwordInput = await screen.findByLabelText(
        registrationMock.registerOrgPassword.passwordFormField.value.label,
      );
      const password = 'aaaaaaA1';
      await user.type(passwordInput, password);

      const confirmButton = screen.getByRole('button', {
        name: registrationMock.registerOrgPassword.confirmButtonText.value,
      });
      await user.click(confirmButton);

      const source: FlowVariant = 'RegistrationAccountCreate';

      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          requestBody: expect.objectContaining({
            data: expect.objectContaining({
              emailAddress,
              password,
              primaryEmailOverride: emailAddress2,
              source,
              address: expect.objectContaining({ houseNumber, houseNumberSuffix: '', postalCode }),
            }),
          }),
        }),
      );
    });
  });
});
