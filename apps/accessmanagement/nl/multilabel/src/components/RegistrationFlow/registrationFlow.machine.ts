import { createContext } from 'react';

import { ApiError } from '@dc/client/ApiError';
import {
  canRegisterWithCustomerId,
  register,
  registerPasswordResetInitiatedEvent,
  registerAccountCreationInitiatedEvent,
} from '@dc/services';
import { createMachine, assign, fromPromise } from '@eneco-packages/xstate-custom/xstate';
import { emptySuffixString } from '@hooks/address';

import { Action, Service, State } from './registrationFlow.constants';
import { Context, Event, RegistrationMachineActor, RegistrationMachineState } from './registrationFlow.types';

export const registrationMachine = ({
  businessUnit,
  flowVariant,
  label,
}: Pick<Context, 'businessUnit' | 'flowVariant' | 'label'>) =>
  createMachine({
    id: 'registrationMachine',
    context: { businessUnit, flowVariant, label },
    initial: State.CAN_REGISTER_ADDRESS,
    types: {
      context: {} as Context,
      events: {} as Event,
    },
    states: {
      [State.CAN_REGISTER_ADDRESS]: {
        on: {
          [Action.CAN_REGISTER_CONTINUE_ADDRESS]: {
            actions: [
              assign({
                postalCode: ({ event }) => event.postalCode,
                houseNumber: ({ event }) => event.houseNumber,
                houseNumberSuffix: ({ event }) => event.houseNumberSuffix,
              }),
              ({ context, event }) => {
                const addressSaveEventHandlers = {
                  RegistrationAccountReset: registerPasswordResetInitiatedEvent,
                  RegistrationAccountCreate: registerAccountCreationInitiatedEvent,
                };
                const payload = {
                  label,
                  businessUnit: context.businessUnit,
                  requestBody: {
                    data: {
                      address: {
                        postalCode: event.postalCode,
                        houseNumber: event.houseNumber,
                        houseNumberSuffix: event.houseNumberSuffix,
                      },
                    },
                  },
                };
                void addressSaveEventHandlers[context.flowVariant](payload);
              },
            ],
            target: State.CAN_REGISTER_CUSTOMER_ID,
          },
        },
      },
      [State.CAN_REGISTER_CUSTOMER_ID]: {
        on: {
          [Action.CAN_REGISTER_CONTINUE_CUSTOMER_ID]: {
            actions: assign({
              customerId: ({ event }) => event.customerId,
            }),
            target: State.VALIDATING_CUSTOMER,
          },
          [Action.BACK]: {
            target: State.CAN_REGISTER_ADDRESS,
            actions: assign({
              isInvalidCombination: () => false,
            }),
          },
        },
      },
      [State.VALIDATING_CUSTOMER]: {
        invoke: {
          id: Service.CAN_REGISTER_WITH_CUSTOMER_ID,
          src: Service.CAN_REGISTER_WITH_CUSTOMER_ID,
          input: ({ context }) => ({ ...context }),
          onDone: [
            {
              actions: assign({
                customerType: ({ event }) => event.output,
              }),
              guard: ({ event }) => event.output === 'organisation',
              target: State.REGISTER_ORG_EMAIL,
            },
            {
              actions: assign({
                customerType: ({ event }) => event.output,
              }),
              guard: ({ event }) => event.output === 'person',
              target: State.REGISTER_PERSON,
            },
          ],
          onError: {
            actions: assign({
              isInvalidCombination: () => true,
            }),
            target: State.CAN_REGISTER_CUSTOMER_ID,
          },
        },
      },
      [State.REGISTER_ORG_EMAIL]: {
        on: {
          [Action.BACK]: {
            target: State.CAN_REGISTER_CUSTOMER_ID,
            actions: assign({
              hasError: () => false,
            }),
          },
          [Action.CUSTOMER_SET_EMAIL]: {
            actions: assign({
              emailAddress: ({ event }) => event.emailAddress,
              primaryEmailOverride: ({ event }) => event.primaryEmailOverride,
            }),
            target: State.REGISTER_ORG_PASSWORD,
          },
        },
      },
      [State.REGISTER_ORG_PASSWORD]: {
        on: {
          [Action.BACK]: {
            target: State.REGISTER_ORG_EMAIL,
          },
          [Action.CUSTOMER_SET_PASSWORD]: {
            actions: assign({
              password: ({ event }) => event.password,
            }),
            target: State.SETTING_NEW_CREDENTIALS,
          },
        },
      },
      [State.REGISTER_PERSON]: {
        on: {
          [Action.BACK]: {
            target: State.CAN_REGISTER_CUSTOMER_ID,
            actions: assign({
              hasError: () => false,
            }),
          },
          [Action.CUSTOMER_SET_CREDENTIALS]: {
            actions: assign({
              emailAddress: ({ event }) => event.emailAddress,
              password: ({ event }) => event.password,
            }),
            target: State.SETTING_NEW_CREDENTIALS,
          },
        },
      },
      [State.SETTING_NEW_CREDENTIALS]: {
        entry: assign({
          hasError: () => false,
        }),
        invoke: {
          id: Service.REGISTER_WITH_CUSTOMER_ID,
          src: Service.REGISTER_WITH_CUSTOMER_ID,
          input: ({ context }) => ({ ...context }),
          onDone: {
            target: State.REGISTER_SUCCESS,
          },
          onError: [
            {
              actions: assign({
                hasError: () => true,
                errorStatusCode: ({ event }) => {
                  const errorEvent = event?.error as ApiError;
                  return errorEvent?.status;
                },
              }),
              guard: ({ context }) => context.customerType === 'organisation',
              target: State.REGISTER_ORG_EMAIL,
            },
            {
              actions: assign({
                hasError: () => true,
                errorStatusCode: ({ event }) => {
                  const errorEvent = event?.error as ApiError;
                  return errorEvent?.status;
                },
              }),
              guard: ({ context }) => context.customerType === 'person',
              target: State.REGISTER_PERSON,
            },
          ],
        },
      },
      [State.REGISTER_SUCCESS]: {
        type: 'final',
      },
    },
  }).provide({
    actors: {
      [Service.CAN_REGISTER_WITH_CUSTOMER_ID]: fromPromise(async ({ input }: { input: Context }) => {
        const { label, customerId, postalCode, houseNumber, houseNumberSuffix } = input;
        if (!customerId) throw new Error('Expected `customerId` not to be undefined');
        if (!postalCode) throw new Error('Expected `postalCode` not to be undefined');
        if (!houseNumber) throw new Error('Expected `houseNumber` not to be undefined');
        const sendNullSuffix = !houseNumberSuffix || houseNumberSuffix === emptySuffixString;
        const address = {
          postalCode,
          houseNumber,
          houseNumberSuffix: sendNullSuffix ? null : houseNumberSuffix,
        };
        const response = await canRegisterWithCustomerId({
          label,
          businessUnit: input.businessUnit,
          customerId,
          requestBody: { data: { address } },
        });
        return response.data?.customerType;
      }),
      [Service.REGISTER_WITH_CUSTOMER_ID]: fromPromise(async ({ input }: { input: Context }): Promise<true> => {
        const {
          label,
          customerId,
          postalCode,
          houseNumber,
          houseNumberSuffix,
          emailAddress,
          password,
          primaryEmailOverride,
        } = input;
        if (!customerId) throw new Error('Expected `customerId` not to be undefined');
        if (!postalCode) throw new Error('Expected `postalCode` not to be undefined');
        if (!houseNumber) throw new Error('Expected `houseNumber` not to be undefined');
        if (!emailAddress) throw new Error('Expected `emailAddress` not to be undefined');
        if (!password) throw new Error('Expected `password` not to be undefined');
        const address = { postalCode, houseNumber, houseNumberSuffix };
        const data = {
          address,
          emailAddress,
          password,
          source: input.flowVariant,
          ...(primaryEmailOverride && { primaryEmailOverride }),
        };

        await register({
          label,
          businessUnit: input.businessUnit,
          customerId,
          requestBody: { data },
        });
        return true;
      }),
    },
  });

export const RegistrationMachine = createContext(
  {} as { state: RegistrationMachineState; send: RegistrationMachineActor['send'] },
);
