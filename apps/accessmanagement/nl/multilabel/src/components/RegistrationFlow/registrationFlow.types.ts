import { Actor, MachineContext } from '@eneco-packages/xstate-custom/xstate';
import {
  DC_Repositories_Base_Enumerations_BusinessUnit,
  DC_Repositories_Base_Enumerations_Label,
} from '@monorepo-types/dc';
import { RegistrationAccountCreateRendering } from '@sitecore/types/RegistrationAccountCreate';
import { RegistrationAccountResetRendering } from '@sitecore/types/RegistrationAccountReset';

import { Action, flowVariants } from './registrationFlow.constants';
import { registrationMachine } from './registrationFlow.machine';

export type RegistrationMachineActor = Actor<ReturnType<typeof registrationMachine>>;

export type FlowVariant = (typeof flowVariants)[number];

export type Context = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  flowVariant: FlowVariant;
  label: DC_Repositories_Base_Enumerations_Label;
  customerId?: number;
  surname?: string;
  postalCode?: string;
  houseNumber?: number;
  houseNumberSuffix?: string | null;
  emailAddress?: string;
  primaryEmailOverride?: string | null;
  password?: string;
  isInvalidCombination?: boolean;
  customerType?: 'person' | 'organisation';
  hasError?: boolean;
  errorStatusCode?: number | null;
};

export type RegistrationMachineState = MachineContext;

export type Event =
  | {
      type: Action.CAN_REGISTER_CONTINUE_ADDRESS;
      postalCode: string;
      houseNumber: number;
      houseNumberSuffix: string | null;
    }
  | {
      type: Action.CAN_REGISTER_CONTINUE_CUSTOMER_ID;
      customerId: number;
    }
  | {
      type: Action.BACK;
    }
  | {
      type: Action.CUSTOMER_SET_CREDENTIALS;
      emailAddress: string;
      password: string;
    }
  | {
      type: Action.CUSTOMER_SET_EMAIL;
      emailAddress: string;
      primaryEmailOverride?: string | null;
    }
  | {
      type: Action.CUSTOMER_SET_PASSWORD;
      password: string;
    }
  | { type: Action.CUSTOMER_REGISTERED };

export type RenderingContentType = (RegistrationAccountCreateRendering | RegistrationAccountResetRendering) & {
  placeholders: {
    'jss-registration-bottom': [];
  };
};
