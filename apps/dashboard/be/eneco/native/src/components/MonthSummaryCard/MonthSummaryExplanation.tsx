import { FC } from 'react';

import { getStaticPrefix } from '@common/env';
import { DC_Domain_Models_Usages_Insights_BudgetStatus } from '@monorepo-types/dc';
import { useContent } from '@sitecore/common';
import { MonthSummaryCardRendering } from '@sitecore/types/MonthSummaryCard';
import { Box, Stack, Text, Image, Heading } from '@sparky';

import { toMonthSummaryColors } from './monthSummaryCardHelpers';
import { MonthSummaryGraphSet } from '../MonthSummaryCard/MonthSummaryGraphSet';

interface Props {
  status: DC_Domain_Models_Usages_Insights_BudgetStatus;
  progressedCost: number;
  progressedDays: number;
}

const MonthSummaryExplanation: FC<Props> = ({ status, progressedCost, progressedDays }) => {
  const { fields } = useContent<MonthSummaryCardRendering>();

  return (
    <Box paddingX="6">
      <Stack as="div" direction="column" gap="6" alignX="center">
        <Stack alignX="center">
          {status !== 'unknown' && <Text weight="bold">{fields[`${status}Title`].value}</Text>}
        </Stack>

        <Heading as="h1" size="S">
          {fields.explanationTitle.value}
        </Heading>

        <MonthSummaryGraphSet
          status={toMonthSummaryColors(status)}
          progressedDays={progressedDays}
          progressedCost={progressedCost}
        />

        <Text size="BodyS">{fields.explanationEstimateText.value}</Text>

        <Stack gap="2">
          <Heading as="h2" size="2XS">
            {fields.explanationSubTitle.value}
          </Heading>
          <Text size="BodyS">{fields.explanationUsageText.value}</Text>
        </Stack>

        <Image
          maxWidth="100%"
          height="auto"
          src={`${getStaticPrefix()}/images/eneco/month-summary.jpg`}
          alt={'Usage Graph'}
        />
      </Stack>
    </Box>
  );
};

export default MonthSummaryExplanation;
