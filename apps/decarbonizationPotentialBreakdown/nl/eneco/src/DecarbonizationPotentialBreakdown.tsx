import { useEffect, useState } from 'react';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import { SplitView as SplitViewComponent } from '@components/SplitView/SplitView';
import { config, ContentContainer, HeroContainer, RebrandedImageContainer } from '@custom-components/Hero';
import { Relative } from '@custom-components/Section';
import { useLinkComponent } from '@link';
import { useContent, mapImage, wrap } from '@sitecore/common';
import { DecarbonizationPotentialBreakdownRendering } from '@sitecore/types/DecarbonizationPotentialBreakdown';
import { PageGrid, Box, Stretch, Grid, Stack, Image, Heading, Text, ButtonLink } from '@sparky';

import { MetricsSection, HeroCard } from './components';
import { MetricsNames } from './types';

const NUMBER_OF_FLOORS_LIMIT = 20;

const DecarbonizationPotentialBreakdownNlEneco = () => {
  const Link = useLinkComponent();
  const [metricsData, setMetricsData] = useState<Record<MetricsNames, string | undefined>>({
    [MetricsNames.gasConsumptionReductionPercentage]: undefined,
    [MetricsNames.electricityConsumptionReductionPercentage]: undefined,
    [MetricsNames.costSavingsEur]: undefined,
    [MetricsNames.costSavingsPct]: undefined,
    [MetricsNames.investmentEur]: undefined,
    [MetricsNames.paybackPeriod]: undefined,
  });
  const { fields } = useContent<DecarbonizationPotentialBreakdownRendering>();
  const imageProps = mapImage(fields?.content?.image);
  const notFoundImageProps = mapImage(fields?.noResultsOutcome.image);
  const { searchParams } = useApplication();

  const metricsUnavailable = () => {
    const numberOfFloors = searchParams.get('numberOfFloors') || 0;
    if (Number(numberOfFloors) > NUMBER_OF_FLOORS_LIMIT) return false;
    return true;
  };

  const showMetricsBreakdown = !metricsUnavailable();

  useEffect(() => {
    const metricsList: Record<MetricsNames, string | undefined> = {
      [MetricsNames.gasConsumptionReductionPercentage]: undefined,
      [MetricsNames.electricityConsumptionReductionPercentage]: undefined,
      [MetricsNames.costSavingsEur]: undefined,
      [MetricsNames.costSavingsPct]: undefined,
      [MetricsNames.investmentEur]: undefined,
      [MetricsNames.paybackPeriod]: undefined,
    };

    for (const m of Object.values(MetricsNames)) {
      if (searchParams.get(m)) {
        metricsList[m] = searchParams.get(m) || undefined;
      }
    }

    setMetricsData(metricsList as Record<MetricsNames, string | undefined>);
  }, [searchParams]);

  const buttonLink = fields.content?.buttonLink;

  const heroOutput = (
    <Box borderRadius="m" paddingTop={{ initial: '2', xl: '10' }} paddingX={{ md: '2', xl: '0' }}>
      <HeroContainer isVariableWidth={false} isMinMargin={false}>
        {imageProps.src ? (
          <RebrandedImageContainer>
            <Image
              alt={imageProps.alt}
              height={{
                initial: config.minHeight.initial,
                sm: config.minHeight.sm,
                md: config.minHeight.md,
                lg: '100%',
              }}
              hasLazyLoad={false}
              objectFit="cover"
              sources={imageProps.sources}
              src={imageProps.src}
              width="100%"
              borderRadius={{
                initial: ['square', 'square', 'square', 'square'],
                lg: ['rounded', 'rounded', 'rounded', 'rounded'],
              }}
            />
          </RebrandedImageContainer>
        ) : null}

        <ContentContainer
          alignY="justify"
          hasBottomPlaceholder={false}
          hasRightPlaceholder={false}
          isVariableHeight={false}>
          <Stack.Item>
            <PageGrid>
              <PageGrid.Item gridColumn="1/-1">
                <Box paddingTop={{ initial: '0', lg: '16' }} paddingBottom={{ initial: '8', lg: '16' }}>
                  <Grid gridTemplateColumns={{ initial: '1fr', lg: '1fr 1fr' }} columnGap={{ lg: '8', xl: '16' }}>
                    <Grid.Item
                      order={{
                        initial: 2,
                        lg: 1,
                      }}>
                      <Box paddingTop={{ initial: '2', lg: '0' }} paddingBottom={{ initial: '2', lg: '0' }}>
                        <HeroCard costSavings={metricsData[MetricsNames.costSavingsEur] || ''} {...fields.content} />
                      </Box>
                    </Grid.Item>
                    <Grid.Item
                      order={{
                        initial: 1,
                        lg: 2,
                      }}></Grid.Item>
                  </Grid>
                </Box>
              </PageGrid.Item>
            </PageGrid>
          </Stack.Item>
        </ContentContainer>
      </HeroContainer>
    </Box>
  );

  const metricsOutput = (
    <Relative>
      <Box paddingY={{ initial: '10', md: '16' }} paddingBottom={{ initial: '10', md: '16' }}>
        <PageGrid>
          <Stretch width>
            <PageGrid.Item gridColumn="1/-1">
              <MetricsSection
                metrics={fields?.calculationsOutcome?.itemsList?.value?.enum}
                metricsData={metricsData}
                title={fields?.calculationsOutcome?.title?.value}
                description={fields?.calculationsOutcome?.description?.value}
              />
            </PageGrid.Item>
          </Stretch>
        </PageGrid>
      </Box>
    </Relative>
  );

  const noResultsOutput = (
    <Relative>
      <Box paddingY={{ initial: '10', md: '16' }} paddingBottom={{ initial: '10', md: '16' }}>
        <PageGrid>
          <Stretch width>
            <PageGrid.Item gridColumn="1/-1">
              <SplitViewComponent columnArrangement={'8-4'} columnAlignment="centered">
                <SplitViewComponent.Item>
                  <Stretch height>
                    <PageGrid.Item gridColumn={{ initial: '1/-1', md: '1/-4' }}>
                      <Stack gap="6">
                        <Heading as="h1" size={{ initial: '2XL', md: 'XL' }}>
                          {wrap(fields.noResultsOutcome?.title)}
                        </Heading>
                        <Text size={{ initial: 'BodyM', md: 'BodyL' }} as="div">
                          <RichText html={fields.noResultsOutcome?.description?.value} />
                        </Text>
                        {((buttonLink?.value?.text && buttonLink.value.href) || buttonLink?.editable) && (
                          <Stack.Item grow={false}>
                            <Stretch width={{ initial: true, md: false }}>
                              <Link
                                editable={buttonLink.editable}
                                linkType={buttonLink.value.linktype}
                                linkValue={buttonLink.value}>
                                <ButtonLink target={buttonLink.value.target}>{buttonLink.value.text}</ButtonLink>
                              </Link>
                            </Stretch>
                          </Stack.Item>
                        )}
                      </Stack>
                    </PageGrid.Item>
                  </Stretch>
                </SplitViewComponent.Item>
                <SplitViewComponent.Item>
                  <Stretch height>
                    <Stack gap={6}>
                      {notFoundImageProps.src ? (
                        <Image
                          alt={notFoundImageProps.alt}
                          hasLazyLoad={false}
                          objectFit="cover"
                          sources={notFoundImageProps.sources}
                          src={notFoundImageProps.src}
                          width="100%"
                          borderRadius={{
                            initial: ['square', 'square', 'square', 'square'],
                            lg: ['rounded', 'rounded', 'rounded', 'rounded'],
                          }}
                        />
                      ) : null}
                    </Stack>
                  </Stretch>
                </SplitViewComponent.Item>
              </SplitViewComponent>
            </PageGrid.Item>
          </Stretch>
        </PageGrid>
      </Box>
    </Relative>
  );

  return showMetricsBreakdown ? (
    <>
      {heroOutput}
      {metricsOutput}
    </>
  ) : (
    noResultsOutput
  );
};
export default DecarbonizationPotentialBreakdownNlEneco;
