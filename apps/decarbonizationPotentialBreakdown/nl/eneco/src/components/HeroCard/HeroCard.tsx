import RichText from '@components/RichText/RichText';
import { Checklist } from '@components/ui/Checklist/Checklist';
import BrandBox from '@custom-components/BrandBox/BrandBox';
import { useFormatter } from '@i18n';
import { useLinkComponent } from '@link';
import { wrap } from '@sitecore/common';
import { Fields } from '@sitecore/types/DecarbonizationPotentialBreakdown';
import { Stack, ButtonLink, Grid, Card, Box, Text, Stretch, Bleed } from '@sparky';
import { useMediaQuery } from '@sparky/hooks';

interface UspFields {
  content: {
    value: {
      value: string;
    };
  };
}

interface Usp {
  name: string;
  fields: UspFields;
}
type Props = Fields['content'] & { uspList?: Usp[]; costSavings: string };

const HeroCard = ({ title, buttonLink, uspList, uspTitle, costSavings }: Props) => {
  const Link = useLinkComponent();
  const { currency } = useFormatter();

  const isDesktop = useMediaQuery('lg');

  const ContentBlock: React.FC<Pick<Props, 'uspList' | 'buttonLink' | 'uspTitle'>> = ({
    uspTitle,
    buttonLink,
    uspList,
  }) => (
    <Box borderRadius="m" padding={{ initial: '0', lg: '8' }} paddingTop={{ initial: '6', lg: '12' }}>
      <Stack gap="6">
        {(uspTitle.value || uspTitle.editable) && (
          <Text size={{ initial: 'BodyM', lg: 'BodyL' }}>{wrap(uspTitle, <RichText html={uspTitle.value} />)}</Text>
        )}

        {uspList?.length && (
          <Checklist>
            {uspList.map((usp: Usp) => {
              return (
                <Checklist.Item alignY="start" key={usp.name}>
                  {usp.fields.content.value.value}
                </Checklist.Item>
              );
            })}
          </Checklist>
        )}
        {((buttonLink?.value?.href && buttonLink.value.text) || buttonLink.editable) && (
          <Stack direction="row" gap="3">
            <Stack.Item grow={false}>
              <Stretch width={{ initial: true, lg: false }}>
                <Link editable={buttonLink.editable} linkType={buttonLink.value.linktype} linkValue={buttonLink.value}>
                  <ButtonLink target={buttonLink.value.target}>{buttonLink.value.text}</ButtonLink>
                </Link>
              </Stretch>
            </Stack.Item>
          </Stack>
        )}
      </Stack>
    </Box>
  );

  const costSavingsFormatted = currency.euroNoFractionDigits(costSavings);
  const costSavingsFromFormatted = currency.euroNoFractionDigits(+costSavings * 0.9);

  return (
    <Grid columns={{ initial: '1', lg: '12' }}>
      <Grid.Item gridColumn={{ initial: '1/-1', lg: '1/11' }} gridRow={{ lg: '1 / -1' }} order={{ lg: 2 }}>
        <BrandBox
          title={`${title.value} ${costSavingsFromFormatted} - ${costSavingsFormatted}`}
          borderRadius="all"
          padding={{ initial: '6', lg: '8' }}
          size={{ initial: 'M', lg: 'L' }}
        />
      </Grid.Item>

      <Grid.Item gridColumn={{ initial: '1/-1', lg: '1/13' }}>
        <Bleed top={{ initial: '0', lg: '6' }}>
          <Box paddingLeft={{ initial: '0', lg: '8' }}>
            {isDesktop ? (
              <Card corners="rounded" overflow="hidden">
                <ContentBlock uspTitle={uspTitle} buttonLink={buttonLink} uspList={uspList} />
              </Card>
            ) : (
              <ContentBlock uspTitle={uspTitle} buttonLink={buttonLink} uspList={uspList} />
            )}
          </Box>
        </Bleed>
      </Grid.Item>
    </Grid>
  );
};
export default HeroCard;
