import { FC } from 'react';

import RichText from '@components/RichText/RichText';
import { useFormatter } from '@i18n';
import { getIllustration } from '@sitecore/common';
import { NameLabelListField } from '@sitecore/types/DecarbonizationPotentialBreakdown';
import { Illustrations } from '@sitecore/types/manual/Illustrations';
import { Box, Grid, Heading, Stack, Text } from '@sparky';

import { MetricsNames } from '../../types';

interface Props {
  metrics: NameLabelListField['value']['enum'];
  title: string;
  description: string;
  metricsData: Record<MetricsNames, string | undefined>;
}

const getIllustrationName = (name: string): Illustrations | undefined => {
  if (name === 'savings') return 'Savings';
  if (name === 'energyCostReduction') return 'Electricity';
  if (name === 'electricitySaving') return 'Smartlamp';
  if (name === 'investmentCost') return 'LabelEuro';
  if (name === 'gasSaving') return 'Leaves';
  if (name === 'paybackPeriod') return 'Calendar';
};

const getPositivePercentage = (fraction: number) => {
  const percentage = Math.abs(fraction) * 100;
  return Math.round(percentage);
};

const getRange = (value: number, increment: number) => {
  if (increment > 0) {
    const high = value * (increment + 1);
    return [value, high];
  } else {
    const low = value * (increment + 1);
    return [low, value];
  }
};

const convertFractionToPercentageRange = (fraction: number, rangeSizeMultiplier: number) => {
  const percentage = getPositivePercentage(fraction);
  return getRange(percentage, rangeSizeMultiplier);
};

export const MetricsSection: FC<Props> = ({ metrics, metricsData, title, description }) => {
  const { number, currency } = useFormatter();

  const getMetricValue = (metric: string) => {
    if (metric === 'savings' && metricsData.costSavingsEur) {
      const value = Number(metricsData.costSavingsEur);
      // convert savings to a range where the low is 10% decreased from the original value
      const [low, high] = getRange(value, -0.1);

      return value === 0 ? '0' : `${currency.euroNoFractionDigits(low)} - ${currency.euroNoFractionDigits(high)}`;
    }
    if (metric === 'energyCostReduction' && metricsData.costSavingsPct) {
      const costReduction = Number(metricsData.costSavingsPct);
      // converting cost reduction to a range where the lower 10% decreased from the original value
      const [low, high] = convertFractionToPercentageRange(costReduction, -0.1);
      return costReduction === 0 ? '0 %' : `${number.noFractionDigits(low)} - ${number.noFractionDigits(high)} %`;
    }
    if (metric === 'electricitySaving' && metricsData.electricityConsumptionReductionPercentage) {
      const electricitySaving = Number(metricsData.electricityConsumptionReductionPercentage);
      // converting electricity cost savings to a range where the low is 10% decreased from the original value
      const [low, high] = convertFractionToPercentageRange(electricitySaving, -0.1);

      return electricitySaving === 0 ? '0 %' : `${number.noFractionDigits(low)} - ${number.noFractionDigits(high)} %`;
    }
    if (metric === 'investmentCost' && metricsData.investmentEur) {
      const investmentCost = Number(metricsData.investmentEur);
      // converting investment costs to a range where the high is 10% increased from the original value
      const [low, high] = getRange(investmentCost, 0.1);

      return investmentCost === 0
        ? '0'
        : `${currency.euroNoFractionDigits(low)} - ${currency.euroNoFractionDigits(high)}`;
    }
    if (metric === 'gasSaving' && metricsData.gasConsumptionReductionPercentage) {
      const gasSavings = Number(metricsData.gasConsumptionReductionPercentage);
      // converting gas savings to a range where the low is 10% decreased from the original value
      const [low, high] = convertFractionToPercentageRange(gasSavings, -0.1);

      return gasSavings === 0 ? `0 %` : `${number.noFractionDigits(low)} - ${number.noFractionDigits(high)} %`;
    }
    if (metric === 'paybackPeriod' && metricsData.paybackPeriod) {
      const paybackPeriod = Math.round(+metricsData.paybackPeriod);

      if (paybackPeriod === 0) return `0 jaar`;
      // payback period represented in 2 years range
      return `${number.noFractionDigits(paybackPeriod)} - ${number.noFractionDigits(paybackPeriod + 2)} jaar`;
    }
  };

  const renderMetricValue = (value: string) => {
    return (
      <Stack.Item>
        <Stack direction="row" alignY="center">
          <Stack.Item>
            <Heading size={'S'} as="h4">
              {value}
            </Heading>
          </Stack.Item>
        </Stack>
      </Stack.Item>
    );
  };

  return (
    <Stack.Item>
      <Stack gap={10}>
        <Stack.Item>
          <Box paddingBottom="6">
            <Heading as="h2" size={{ initial: 'S', lg: 'M' }}>
              {title}
            </Heading>

            <Text color={'textPrimary'}>
              <RichText html={description} />
            </Text>
          </Box>
        </Stack.Item>
        <Stack.Item>
          <Grid as="div" columns={{ initial: 1, md: 2, lg: 3 }} flow="row" gap="10" gridTemplateRows="1fr 1fr">
            {metrics.map(({ label, name }) => {
              const Illustration = name ? getIllustration(getIllustrationName(name) as Illustrations) : undefined;
              const value = getMetricValue(name);
              return value ? (
                <Grid.Item key={name}>
                  <Stack gap="3" direction={{ initial: 'row' }}>
                    <Stack.Item>
                      {Illustration && <Illustration color="iconPrimary" size={{ initial: 'medium', lg: 'small' }} />}
                    </Stack.Item>
                    <Stack.Item>
                      <Heading size={'3XS'} color="textLowEmphasis" as="h3">
                        {label}
                      </Heading>
                      {renderMetricValue(value)}
                    </Stack.Item>
                  </Stack>
                </Grid.Item>
              ) : null;
            })}
          </Grid>
        </Stack.Item>
      </Stack>
    </Stack.Item>
  );
};

export default MetricsSection;
