import { FC } from 'react';

import RichText from '@components/RichText/RichText';
import { useFormatter } from '@i18n';
import { getIllustration } from '@sitecore/common';
import { NameLabelListField } from '@sitecore/types/DecarbonizationPotentialBreakdown';
import { Illustrations } from '@sitecore/types/manual/Illustrations';
import { Box, Grid, Heading, Stack, Text } from '@sparky';

import { MetricsNames } from '../../types';

interface Props {
  metrics: NameLabelListField['value']['enum'];
  title: string;
  description: string;
  metricsData: Record<MetricsNames, string | undefined>;
}

const getIllustrationName = (name: string): Illustrations | undefined => {
  if (name === 'savings') return 'Savings';
  if (name === 'energyCostReduction') return 'Electricity';
  if (name === 'electricitySaving') return 'Smartlamp';
  if (name === 'investmentCost') return 'LabelEuro';
  if (name === 'gasSaving') return 'Leaves';
  if (name === 'paybackPeriod') return 'Calendar';
};

export const MetricsSection: FC<Props> = ({ metrics, metricsData, title, description }) => {
  const { number, currency } = useFormatter();

  const getMetricValue = (metric: string) => {
    if (metric === 'savings' && metricsData.costSavingsEur)
      return `${currency.euroNoFractionDigits(metricsData.costSavingsEur)}`;
    if (metric === 'energyCostReduction' && metricsData.costSavingsPct)
      return `${number.twoFractionDigits(Number(metricsData.costSavingsPct) * 100)} %`;
    if (metric === 'electricitySaving' && metricsData.electricityConsumptionReductionPercentage)
      return `${number.twoFractionDigits(Number(metricsData.electricityConsumptionReductionPercentage) * 100)} %`;
    if (metric === 'investmentCost' && metricsData.investmentEur)
      return `${currency.euroNoFractionDigits(metricsData.investmentEur)}`;
    if (metric === 'gasSaving' && metricsData.gasConsumptionReductionPercentage)
      return `${number.twoFractionDigits(Number(metricsData.gasConsumptionReductionPercentage) * 100)} %`;
    if (metric === 'paybackPeriod' && metricsData.paybackPeriod)
      return `${number.twoFractionDigits(+metricsData.paybackPeriod)} jaar`;
  };

  const renderMetricValue = (value: string) => {
    return (
      <Stack.Item>
        <Stack direction="row" alignY="center">
          <Stack.Item>
            <Heading size={'S'} as="h4">
              {value}
            </Heading>
          </Stack.Item>
        </Stack>
      </Stack.Item>
    );
  };

  return (
    <Stack.Item>
      <Stack gap={10}>
        <Stack.Item>
          <Box paddingBottom="6">
            <Heading as="h2" size={{ initial: 'S', lg: 'M' }}>
              {title}
            </Heading>

            <Text color={'textPrimary'}>
              <RichText html={description} />
            </Text>
          </Box>
        </Stack.Item>
        <Stack.Item>
          <Grid as="div" columns={{ initial: 1, md: 2, lg: 3 }} flow="row" gap="10" gridTemplateRows="1fr 1fr">
            {metrics.map(({ label, name }) => {
              const Illustration = name ? getIllustration(getIllustrationName(name) as Illustrations) : undefined;
              const value = getMetricValue(name);
              return value ? (
                <Grid.Item key={name}>
                  <Stack gap="3" direction={{ initial: 'row' }}>
                    <Stack.Item>
                      {Illustration && <Illustration color="iconPrimary" size={{ initial: 'medium', lg: 'small' }} />}
                    </Stack.Item>
                    <Stack.Item>
                      <Heading size={'3XS'} color="textLowEmphasis" as="h3">
                        {label}
                      </Heading>
                      {renderMetricValue(value)}
                    </Stack.Item>
                  </Stack>
                </Grid.Item>
              ) : null;
            })}
          </Grid>
        </Stack.Item>
      </Stack>
    </Stack.Item>
  );
};

export default MetricsSection;
