import { GenericRadioChoiceStep } from '@eneco/flows2/src/components';
import { FlowRoute, FlowRouteCategory, FlowActions } from '@eneco/flows2/types';

import {
  BuildingInstallmentsStep,
  BusinessTypeStep,
  BuildingAreaStep,
  BuildingFloorsStep,
  SummaryStep,
  CompanyDetailsStep,
} from './steps';
import { DecarbonisationLeadFlowContext } from './types';

export const initialContext: DecarbonisationLeadFlowContext = {};

export const routes: FlowRoute<DecarbonisationLeadFlowContext>[] = [
  {
    id: 'BUSINESS_CATEGORY',
    path: '/branche/',
    category: FlowRouteCategory.BuildingDetails,
    component: BusinessTypeStep,
  },
  {
    id: 'BUILDING_AREA',
    path: '/oppervlakte/',
    category: FlowRouteCategory.BuildingDetails,
    component: BuildingAreaStep,
  },
  {
    id: 'BUILDING_FLOORS',
    path: '/verdiepingen/',
    category: FlowRouteCategory.BuildingDetails,
    component: BuildingFloorsStep,
  },
  {
    id: 'BUILDING_AGE',
    path: '/bouwjaar/',
    category: FlowRouteCategory.BuildingDetails,
    component: GenericRadioChoiceStep,
  },
  {
    id: 'BUILDING_HEATING',
    path: '/verwarming/',
    category: FlowRouteCategory.BuildingDetails,
    component: GenericRadioChoiceStep,
  },
  {
    id: 'BUILDING_INSTALLMENTS',
    path: '/investeringen/',
    category: FlowRouteCategory.BuildingDetails,
    component: BuildingInstallmentsStep,
  },
  {
    id: 'DETAILS',
    path: '/gegevens/',
    category: FlowRouteCategory.Details,
    component: CompanyDetailsStep,
  },
  {
    id: 'SUMMARY',
    path: '/controle/',
    category: FlowRouteCategory.Summary,
    component: SummaryStep,
  },
];

export const actions: FlowActions<DecarbonisationLeadFlowContext> = {};
