import { assign, createMachine, EventObject, raise } from 'xstate';

import { ApiError } from '@dc/client/ApiError';
import { chamberOfCommerceSearch } from '@dc/services';
import { MachineConfig } from '@eneco/flows2/src/types';
import {
  Customers_ChamberOfCommerce_ChamberOfCommerceSearchResponseModel,
  RequestModels_Customers_ChamberOfCommerce_ChamberOfCommerceSearchRequestModel,
  ResponseModels_Customers_ChamberOfCommerce_ChamberOfCommerceSearchResponseModel,
} from '@monorepo-types/dc';

interface Values {
  companyName: string;
}

export interface ChamberOfCommerceSearchMachineContext {
  results?: Customers_ChamberOfCommerce_ChamberOfCommerceSearchResponseModel['results'];

  errorMessage?: string;

  lastCallData?: {
    cachedResponse?: Customers_ChamberOfCommerce_ChamberOfCommerceSearchResponseModel;
    eventValues: Values;
  };
}

type ChamberOfCommerceSearchMachinesState = {
  value: 'IDLE' | 'FETCHING' | 'SUCCESS' | 'ERROR' | 'ERROR_NOT_FOUND';
  context: ChamberOfCommerceSearchMachineContext;
};

export interface ChamberOfCommerceSearchMachineEvent extends EventObject {
  type: 'GET_COMPANY_OPTIONS' | 'IDLE' | 'NOT_FOUND';
  config: MachineConfig;
  values: Values;
}

const chamberOfCommerceSearchService = (
  config: MachineConfig,
  values: Values,
): Promise<{
  data?: ResponseModels_Customers_ChamberOfCommerce_ChamberOfCommerceSearchResponseModel;
  values?: Values;
}> =>
  new Promise((resolve, reject) => {
    const requestBody: RequestModels_Customers_ChamberOfCommerce_ChamberOfCommerceSearchRequestModel = {
      data: {
        name: values.companyName || '',
        page: 1,
        amount: 10,
      },
    };

    return chamberOfCommerceSearch({
      businessUnit: config.businessUnit,
      label: config.label,
      requestBody,
    })
      .then(response => {
        resolve({ data: response, values });
      })
      .catch((error: ApiError) => {
        reject({ error: error?.message });
      });
  });

export const chamberOfCommerceSearchMachine = createMachine<
  ChamberOfCommerceSearchMachineContext,
  ChamberOfCommerceSearchMachineEvent,
  ChamberOfCommerceSearchMachinesState
>(
  {
    id: 'chamberOfCommerceSearch',
    predictableActionArguments: true,
    initial: 'IDLE',
    context: {},
    on: {
      GET_COMPANY_OPTIONS: { target: 'FETCHING' },
      IDLE: { target: 'IDLE' },
    },
    states: {
      IDLE: {},
      FETCHING: {
        // @ts-ignore Check types for raise action
        invoke: {
          src: 'get',
          onDone: {
            target: ['SUCCESS'],
            actions: [
              assign((_, event) => ({
                results: event?.data?.data?.data?.results,
                lastCallData: {
                  eventValues: {
                    companyName: event?.data?.values?.companyName,
                  },
                  cachedResponse: event?.data?.data,
                },
              })),
            ],
          },
          onError: {
            target: ['ERROR'],
            actions: [
              raise((_, event) => {
                switch (event.data?.error) {
                  case 'Not Found':
                    return { type: 'NOT_FOUND' };

                  default: {
                    return { type: '' };
                  }
                }
              }),
              assign({ errorMessage: (_, event) => event.data?.error }),
            ],
          },
        },
      },
      SUCCESS: {
        entry: ['clearErrorMessage'],
        after: {
          500: { target: 'IDLE' },
        },
      },
      ERROR: {
        on: {
          NOT_FOUND: { target: 'ERROR_NOT_FOUND' },
        },
      },
      ERROR_NOT_FOUND: {},
    },
  },
  {
    actions: {
      clearErrorMessage: assign({ errorMessage: undefined }),
    },
    services: {
      get: (context, event) => {
        const { config, values } = event;
        const { lastCallData } = context;

        const flowContextKeys: Array<keyof Values> = ['companyName'];
        const shouldFetch = !flowContextKeys.every(key => values[key] === lastCallData?.eventValues?.[key]);

        return shouldFetch
          ? chamberOfCommerceSearchService(config, values)
          : new Promise(resolve => resolve({ ...lastCallData?.cachedResponse }));
      },
    },
  },
);
