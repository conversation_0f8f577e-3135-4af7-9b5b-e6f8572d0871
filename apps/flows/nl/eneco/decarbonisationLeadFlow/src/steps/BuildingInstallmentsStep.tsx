import React, { FC } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm, SubmitHandler, FieldValues } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import useDC from '@dc/useDC';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { basketMachine } from '@eneco/flows2/src/machines/basketMachine';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useRedirect } from '@hooks/redirect';
import {
  DC_Products_Client_Models_DecarbPotential,
  DC_Products_Client_Models_ConstructionPeriod,
  DC_Products_Client_Models_CurrentHeatingSystem,
  DC_Products_Client_Models_FloorAreaM2,
  DC_Products_Client_Models_BuildingCategory,
  DC_Products_Client_Models_BuildingSubCategory,
} from '@monorepo-types/dc';
import { getIllustration, useLayoutData, usePlaceholderContent } from '@sitecore/common';
import { DecarbonizationLeadFlowAdditionalSuppliesStepRendering } from '@sitecore/types/DecarbonizationLeadFlowAdditionalSuppliesStep';
import { Illustrations } from '@sitecore/types/manual/Illustrations';
import { CheckboxGroup, CheckTile } from '@sparky';
import { useMachine } from 'packages/xstate-v5/xstate-react';

import { useGetDecarbonizationPotential } from '../hooks';
import { DecarbonisationLeadFlowContext, Installments } from '../types';

interface FormFields {
  installments: Installments[];
}

const installmentsFieldName = 'installments';

const unknownOption = Installments.UNKNOWN;

export const BuildingInstallmentsStep: FC = () => {
  const { useFlowSelector } = useFlowHooks<DecarbonisationLeadFlowContext>();
  const { get: getDecarbonizationPotential, isLoading } = useGetDecarbonizationPotential();
  const [, sendToBasketMachine] = useMachine(basketMachine);
  const { businessUnit, label } = useDC();
  const flowContext = useFlowSelector(state => state.context);
  const layoutData = useLayoutData();
  const redirect = useRedirect();
  const data = usePlaceholderContent<{
    DecarbonizationLeadFlowAdditionalSuppliesStep: DecarbonizationLeadFlowAdditionalSuppliesStepRendering;
  }>();

  const { DecarbonizationLeadFlowAdditionalSuppliesStep: { fields: textualData } = {} } = data;

  const requiredMessage = textualData?.choice?.additionalSuppliesRadioGroupField?.value?.requiredMessage;
  const businessTypeStepSchema = yup.object().shape({
    [installmentsFieldName]: yup.array(yup.string()).required(requiredMessage).min(1, requiredMessage),
  });

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    defaultValues: {
      [installmentsFieldName]: flowContext[installmentsFieldName] || undefined,
    },
    mode: 'onSubmit',
    resolver: yupResolver(businessTypeStepSchema),
  });

  const getDecarbPotentialBreakdownMetrics = (data: DC_Products_Client_Models_DecarbPotential[]) => {
    const {
      gasConsumptionReductionPercentage,
      electricityConsumptionReductionPercentage,
      costSavingsEur,
      costSavingsPct,
      investmentEur,
      paybackPeriod,
    } = data[0] || {};
    return {
      gasConsumptionReductionPercentage,
      electricityConsumptionReductionPercentage,
      costSavingsEur,
      costSavingsPct,
      investmentEur,
      paybackPeriod,
      numberOfFloors: flowContext.numberOfFloors,
    };
  };

  const updateBasketMachine: SubmitHandler<FormFields> = async ({ installments }) => {
    const {
      buildingCategory,
      buildingSubcategory,
      currentHeatingSystem,
      constructionPeriod,
      floorAreaM2,
      numberOfFloors,
    } = flowContext;

    const decarbPotential = await getDecarbonizationPotential({
      buildingCategory: buildingCategory as DC_Products_Client_Models_BuildingCategory,
      buildingSubCategory: buildingSubcategory as DC_Products_Client_Models_BuildingSubCategory,
      floorAreaM2: floorAreaM2 as DC_Products_Client_Models_FloorAreaM2,
      numberOfFloors: numberOfFloors || 1,
      constructionPeriod: constructionPeriod as DC_Products_Client_Models_ConstructionPeriod,
      currentHeatingSystem: currentHeatingSystem as DC_Products_Client_Models_CurrentHeatingSystem,
      solarPanelsInstalled: installments.includes(Installments.SOLAR_PANELS),
      roofIsolationInstalledWithinLast10Years: installments.includes(Installments.ROOF_ISOLATION),
    });
    const decarbonizationPotentialMetrics = getDecarbPotentialBreakdownMetrics(decarbPotential?.data || []);

    let redirectPath = `${layoutData.route.fields?.frontEndRootPath?.value}resultaat`;
    const paybackPeriodIsTooQuick =
      decarbonizationPotentialMetrics.paybackPeriod && decarbonizationPotentialMetrics.paybackPeriod < 4;

    if (Object.keys(decarbonizationPotentialMetrics).length === 0 || paybackPeriodIsTooQuick) {
      redirectPath = `${layoutData.route.fields?.frontEndRootPath?.value}meer-informatie`;
    }

    await sendToBasketMachine({
      type: 'UPDATE_BASKET',
      config: {
        businessUnit,
        label,
      },
      id: flowContext.basketId,
      values: {
        ...flowContext,
        // @ts-ignore basket machine does not respect the extended flow context type
        installments,
        ...decarbonizationPotentialMetrics,
        path: '/gegevens/',
      },
    });

    redirect({
      queryParams: decarbonizationPotentialMetrics,
      link: redirectPath,
    });
  };

  const genericFieldArrayValue = flowContext[installmentsFieldName] || [];
  const options = textualData?.choice?.additionalSuppliesRadioGroupField?.value?.options;

  const onOptionCheckboxChange = (
    name: string,
    value = genericFieldArrayValue as string[],
    onChange: (...event: unknown[]) => void,
  ) => {
    if (name === unknownOption && !value.includes(name)) {
      return onChange([name]);
    }

    if (name !== unknownOption && value.includes(unknownOption)) {
      value = value.filter(val => val !== unknownOption);
    }

    return value.includes(name) ? onChange(value.filter(val => val !== name)) : onChange([...value, name]);
  };

  return (
    <Layout.Content
      variant="A"
      handleSubmit={handleSubmit}
      onSuccessfulSubmit={updateBasketMachine as SubmitHandler<FieldValues>}>
      <Header>
        <Header.Title>{textualData?.content?.title?.value}</Header.Title>
        {textualData?.content?.text?.value && <RichText html={textualData?.content?.text?.value} />}
      </Header>

      {options && (
        <Controller
          control={control}
          name={installmentsFieldName}
          render={({ field: { value, onChange } }) => (
            <CheckboxGroup label="" error={errors?.[installmentsFieldName]?.message}>
              {options.map(({ name, label }) => {
                const Illustration =
                  name && name !== unknownOption ? getIllustration(name as Illustrations) : undefined;
                return (
                  <CheckTile
                    key={name}
                    name={name}
                    icon={Illustration && <Illustration size="small" color="currentColor" />}
                    isChecked={value?.includes(name as Installments)}
                    onChange={() => onOptionCheckboxChange(name, value, onChange)}>
                    {label}
                  </CheckTile>
                );
              })}
            </CheckboxGroup>
          )}
        />
      )}

      <NextButton isLoading={isSubmitSuccessful || isLoading}>{textualData?.content?.nextStepText?.value}</NextButton>
    </Layout.Content>
  );
};
