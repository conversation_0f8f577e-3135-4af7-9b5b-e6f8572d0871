import React, { FC, useEffect, useMemo } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { DC_Products_Client_Models_BuildingType } from '@monorepo-types/dc';
import { usePlaceholderContent } from '@sitecore/common';
import { DecarbonizationLeadFlowBusinessCategoriesStepRendering } from '@sitecore/types/DecarbonizationLeadFlowBusinessCategoriesStep';
import { RadioGroup, RadioTile, Heading } from '@sparky';

import {
  DecarbonisationLeadFlowContext,
  BusinessCategoriesValues,
  BusinessCategoriesKeys,
  BusinessCategories,
} from '../types';

enum BusinessMainCategory {
  AUTO = 'autobedrijf',
  RETAIL = 'detailhandel',
  HEALTHCARE = 'gezondheidszorg',
  HORECA = 'horeca',
  OFFICE = 'kantoor',
  EDUCATION = 'onderwijs_primair',
  OTHER = 'overig',
  RECREATION = 'recreatie',
}

const categories: Record<BusinessMainCategory, { value: BusinessCategoriesKeys }[]> = {
  [BusinessMainCategory.AUTO]: [
    { value: BusinessCategoriesKeys.CarRepairShop },
    { value: BusinessCategoriesKeys.CarShowroom },
  ],
  [BusinessMainCategory.RETAIL]: [
    { value: BusinessCategoriesKeys.RetailWithCooling },
    { value: BusinessCategoriesKeys.RetailWithoutCooling },
  ],
  [BusinessMainCategory.HEALTHCARE]: [
    { value: BusinessCategoriesKeys.HealthcareMeeting },
    { value: BusinessCategoriesKeys.HealthcarePractice },
    { value: BusinessCategoriesKeys.HealthcareHome },
  ],
  [BusinessMainCategory.HORECA]: [
    { value: BusinessCategoriesKeys.HospitalityCafe },
    { value: BusinessCategoriesKeys.HospitalitySnackBar },
    { value: BusinessCategoriesKeys.HospitalityHotelsMotels },
    { value: BusinessCategoriesKeys.HospitalityRestaurant },
  ],
  [BusinessMainCategory.OFFICE]: [
    { value: BusinessCategoriesKeys.OfficeGovernment },
    { value: BusinessCategoriesKeys.OfficeOther },
  ],
  [BusinessMainCategory.EDUCATION]: [{ value: BusinessCategoriesKeys.EducationPrimary }],
  [BusinessMainCategory.OTHER]: [
    { value: BusinessCategoriesKeys.OtherBeautyCare },
    { value: BusinessCategoriesKeys.OtherReligion },
  ],
  [BusinessMainCategory.RECREATION]: [
    { value: BusinessCategoriesKeys.RecreationIndoorSports },
    { value: BusinessCategoriesKeys.RecreationOutdoorSports },
    { value: BusinessCategoriesKeys.RecreationClub },
    { value: BusinessCategoriesKeys.RecreationPool },
  ],
};

const getBusinessType = (
  category: BusinessMainCategory,
  subcategory: BusinessCategoriesKeys | undefined,
): BusinessCategoriesValues | undefined => {
  const key = subcategory || category;
  return Object.keys(BusinessCategories).includes(key)
    ? BusinessCategoriesValues[key as BusinessCategoriesKeys]
    : undefined;
};

const businessTypeField = 'businessType';
const businessTypeSubcategoryField = 'businessTypeSubcategory';

interface FormFields {
  businessType: BusinessMainCategory;
  businessTypeSubcategory: BusinessCategoriesKeys;
}

export const BusinessTypeStep: FC = () => {
  const { useFlowActorRef, useFlowSelector } = useFlowHooks<DecarbonisationLeadFlowContext>();
  const sendToFlowMachine = useFlowActorRef().send;
  const flowContext = useFlowSelector(state => state.context);

  const data = usePlaceholderContent<{
    DecarbonizationLeadFlowBusinessCategoriesStep: DecarbonizationLeadFlowBusinessCategoriesStepRendering;
  }>();

  const { DecarbonizationLeadFlowBusinessCategoriesStep: { fields: textualData } = {} } = data;

  const businessTypeStepSchema = yup.object().shape({
    [businessTypeField]: yup.string().required(textualData?.choice?.businessCategories?.value?.requiredMessage),
    [businessTypeSubcategoryField]: yup.string().when(businessTypeField, {
      // make sure the subcategory is checked when exists
      is: (businessType: BusinessMainCategory) => businessType && categories[businessType]?.length > 1,
      then: schema => schema.required(textualData?.choice?.businessCategories?.value?.requiredMessage),
      otherwise: schema => schema.notRequired(),
    }),
  });

  const {
    handleSubmit,
    control,
    getValues,
    watch,
    resetField,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(businessTypeStepSchema),
  });

  const selectedBusinessType = watch(businessTypeField);

  useEffect(() => {
    // cleanup subcategory value on category change
    if (selectedBusinessType) {
      resetField(businessTypeSubcategoryField);
    }
  }, [resetField, selectedBusinessType]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      const formValues = getValues();

      const businessType = getBusinessType(formValues[businessTypeField], formValues[businessTypeSubcategoryField]);

      const categoryTextualData = textualData?.choice?.businessCategories?.value?.options.find(
        item => item.name === formValues[businessTypeField] && item.label,
      );
      const subcategoryTextualData = textualData?.choice?.businessCategories?.value?.options.find(
        item => item.name === formValues[businessTypeSubcategoryField] && item.label,
      );

      const categoryTextLabel = categoryTextualData?.label || '';
      const subcategoryTextLabel = subcategoryTextualData ? `, ${subcategoryTextualData.label}` : '';
      const buildingTypeLabel = `${categoryTextLabel}${subcategoryTextLabel}`;

      sendToFlowMachine({
        type: 'NEXT_PAGE',
        values: {
          ...flowContext,
          buildingType: businessType as DC_Products_Client_Models_BuildingType,
          buildingTypeLabel,
        },
      });
      return;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitSuccessful]);

  const subcategoriesLabelsMap = useMemo(() => {
    if (textualData?.choice?.businessCategories?.value?.options) {
      return textualData?.choice?.businessCategories?.value?.options?.reduce(
        (agr: { [x: string]: string }, item: { name: string; label: string }) => {
          if (!categories[item.name as BusinessMainCategory]) {
            agr[item.name] = item.label;
          }
          return agr;
        },
        {},
      );
    }
    return {};
  }, [textualData]);

  const categoriesRadioGroup = useMemo(() => {
    return textualData?.choice?.businessCategories?.value?.options?.reduce(
      (agr: React.JSX.Element[], item: { name: string; label: string }) => {
        if (categories[item.name as BusinessMainCategory]) {
          agr.push(
            <RadioTile key={item.name} value={item.name}>
              {item.label}
            </RadioTile>,
          );
        }
        return agr;
      },
      [],
    );
  }, [textualData]);

  const showSubcategories = selectedBusinessType && categories[selectedBusinessType as BusinessMainCategory].length > 1;

  const subcategoriesRadioGroup = selectedBusinessType
    ? categories[selectedBusinessType as BusinessMainCategory].map(({ value }) => (
        <RadioTile key={value} value={value}>
          {subcategoriesLabelsMap[value]}
        </RadioTile>
      ))
    : null;

  const subcategoriesTitle = textualData?.content?.subcategorySectionTitle?.value.replace(
    '%1$',
    selectedBusinessType?.toLowerCase() ?? '',
  );

  return (
    <Layout.Content variant="A" handleSubmit={handleSubmit}>
      <Header>
        <Header.Title>{textualData?.content?.title?.value}</Header.Title>
        {textualData?.content?.text?.value && <RichText html={textualData?.content?.text?.value} />}
      </Header>
      <Controller
        control={control}
        name={businessTypeField}
        render={({ field: { onChange, value, name } }) => (
          <RadioGroup
            aria-labelledby={name}
            name={name}
            direction="column"
            value={value}
            onValueChange={onChange}
            error={errors?.[name]?.message}>
            {categoriesRadioGroup}
          </RadioGroup>
        )}
      />

      {showSubcategories && (
        <>
          <Heading as={'h2'} size={'XS'} children={subcategoriesTitle} />
          <Controller
            control={control}
            name={businessTypeSubcategoryField}
            rules={{ required: textualData?.choice?.businessCategories?.value?.requiredMessage }}
            render={({ field: { onChange, value, name } }) => (
              <RadioGroup
                aria-labelledby={name}
                direction="column"
                name={name}
                value={value}
                onValueChange={onChange}
                error={errors?.[name]?.message}>
                {subcategoriesRadioGroup}
              </RadioGroup>
            )}
          />
        </>
      )}

      <NextButton isLoading={isSubmitSuccessful}>{textualData?.content?.nextStepText?.value}</NextButton>
    </Layout.Content>
  );
};
