import React, { FC, useEffect } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useMachine } from '@xstate/react';
import { useForm, Controller } from 'react-hook-form';
import { useDebounce } from 'react-use';
import * as yup from 'yup';

import { capitalizeFirstLetter } from '@common/string';
import { emailValidation } from '@common/validation/emailRegex';
import RichText from '@components/RichText/RichText';
import useDC from '@dc/useDC';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { formatInitials } from '@eneco/flows2/helpers';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { usePlaceholderContent } from '@sitecore/common';
import { DecarbonizationLeadFlowCompanyDetailsStepRendering } from '@sitecore/types/DecarbonizationLeadFlowCompanyDetailsStep';
import {
  <PERSON>,
  Grid,
  Heading,
  InputCombobox,
  InputEmail,
  InputTelephone,
  InputText,
  NotificationBox,
  RadioButton,
  RadioGroup,
  RadioTile,
  Stack,
} from '@sparky';

import { chamberOfCommerceSearchMachine } from '../machines';
import { DecarbonisationLeadFlowContext } from '../types';

interface FormFields {
  companyName: string;
  chamberOfCommerceNumber: string;
  salutation: string;
  initials: string;
  surnamePreposition: string;
  surname: string;
  phoneNumber: string;
  emailAddress: string;
  isCustomer: string;
}

export const CompanyDetailsStep: FC = () => {
  const { useFlowActorRef, useFlowSelector } = useFlowHooks<DecarbonisationLeadFlowContext>();
  const sendToFlowMachine = useFlowActorRef().send;

  const flowContext = useFlowSelector(state => state.context);

  const { businessUnit, label } = useDC();

  const { companyName, salutation, initials, surnamePreposition, surname, phoneNumber, emailAddress, isCustomer } =
    flowContext;

  const [chamberOfCommerceSearchState, sendTochamberOfCommerceSearchMachine] =
    useMachine(chamberOfCommerceSearchMachine);
  const { context: { results } = {} } = chamberOfCommerceSearchState;
  const companyNameOptions = results?.map(result => result?.name ?? '') || [];

  const { DecarbonizationLeadFlowCompanyDetailsStep: { fields: textualData } = {} } = usePlaceholderContent<{
    DecarbonizationLeadFlowCompanyDetailsStep: DecarbonizationLeadFlowCompanyDetailsStepRendering;
  }>();

  const formSchema = yup.object({
    companyName: yup.string().required(textualData?.companyDetails?.companyNameFormField?.value?.requiredMessage),
    chamberOfCommerceNumber: yup
      .string()
      .required(textualData?.companyDetails?.chamberOfCommerceNumberFormField?.value?.requiredMessage),
    salutation: yup.string().required(textualData?.personalDetails?.salutationRadioGroupField?.value?.requiredMessage),
    initials: yup
      .string()
      .required(textualData?.personalDetails?.initialsFormField?.value?.requiredMessage)
      .test(
        'initials max',
        textualData?.personalDetails?.initialsFormField?.value?.validationMaxValueMessage ?? '',
        (value = '') => {
          return value.toUpperCase().replace(/[^A-Z]/g, '').length <= 6;
        },
      ),
    surnamePreposition: yup.string(),
    surname: yup.string().required(textualData?.personalDetails?.surnameFormField?.value.requiredMessage),
    phoneNumber: yup
      .string()
      .required(textualData?.contactDetails?.phoneNumberFormField?.value?.requiredMessage)
      .matches(
        /^(?:^0[0-57-9][0-9]{2}[-\s]?[0-9]{6}$)|(?:^0[0-57-9][0-9][-\s]?[0-9]{7}$)|(?:^06[-\s]?[0-9]{8}$)$/,
        textualData?.contactDetails?.phoneNumberFormField?.value?.validationMessage,
      ),
    emailAddress: emailValidation({
      emailMessage: textualData?.contactDetails?.emailAddressFormField?.value?.validationMessage,
      requiredMessage: textualData?.contactDetails?.emailAddressFormField?.value?.requiredMessage,
    }),
    isCustomer: yup
      .string()
      .required(textualData?.isBusinessCustomer?.isBusinessCustomerRadioGroupField?.value?.requiredMessage),
  });

  const {
    handleSubmit,
    control,
    getValues,
    watch,
    setValue,
    register,
    trigger,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onTouched',
    defaultValues: {
      companyName,
      salutation,
      initials,
      surnamePreposition,
      surname,
      phoneNumber,
      emailAddress,
      isCustomer,
    },
    resolver: yupResolver(formSchema),
  });

  const watchCompanyName = watch('companyName');

  const selectedChamberOfCommerceNumber =
    results?.find(result => result.name === watchCompanyName)?.chamberOfCommerceNumber ?? '';

  useDebounce(
    () => {
      const isCompanyNameInOptions = companyNameOptions.includes(watchCompanyName);
      if (watchCompanyName && !isCompanyNameInOptions) {
        sendTochamberOfCommerceSearchMachine({
          type: 'GET_COMPANY_OPTIONS',
          config: { businessUnit, label },
          values: {
            companyName: watchCompanyName,
          },
        });
      }
    },
    500,
    [watchCompanyName],
  );

  useEffect(() => {
    if (selectedChamberOfCommerceNumber) {
      setValue('chamberOfCommerceNumber', selectedChamberOfCommerceNumber);
      trigger('companyName');
    }
  }, [selectedChamberOfCommerceNumber, setValue, trigger]);

  useEffect(() => {
    if (isSubmitSuccessful) {
      const formData = getValues();

      sendToFlowMachine({
        type: 'NEXT_PAGE',
        values: {
          companyName: formData.companyName,
          chamberOfCommerceNumber: formData.chamberOfCommerceNumber,
          salutation: formData.salutation,
          initials: formData.initials,
          surnamePreposition: formData.surnamePreposition || undefined,
          surname: formData.surname,
          phoneNumber: formData.phoneNumber,
          emailAddress: formData.emailAddress,
          isCustomer: formData.isCustomer,
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitSuccessful]);

  const kvkNumberOutput = selectedChamberOfCommerceNumber && (
    <Heading as="h3" size="2XS">
      {textualData?.companyDetails?.chamberOfCommerceNumberFormField?.value?.label}: {selectedChamberOfCommerceNumber}
    </Heading>
  );

  const generateInputElement = (value: string, label: string): JSX.Element => {
    return (
      <Stack.Item grow>
        <Stack>
          <RadioTile value={value} key={value}>
            {label}
          </RadioTile>
        </Stack>
      </Stack.Item>
    );
  };

  return (
    <Layout.Content variant="A" handleSubmit={handleSubmit}>
      <Header>
        <Heading as="h1" size="L">
          {textualData?.content?.title?.value}
        </Heading>
        {textualData?.content?.text?.value && <Header.Text>{textualData.content.text.value}</Header.Text>}
      </Header>
      {textualData?.content?.notification?.value?.content && (
        <NotificationBox
          isAlert={false}
          variant={textualData?.content?.notification?.value?.variant}
          title={textualData?.content?.notification?.value?.title}
          text={<RichText html={textualData?.content?.notification?.value?.content} />}
        />
      )}

      <Box paddingBottom={6}>
        <Stack direction="column" gap={6}>
          <InputCombobox
            {...register('companyName')}
            defaultValue={companyName}
            onChange={value => {
              const newValue = value.trim();
              if (newValue !== watchCompanyName) {
                setValue('companyName', newValue);
              }
            }}
            label={textualData?.companyDetails?.companyNameFormField?.value?.label ?? ''}
            placeholder={textualData?.companyDetails?.companyNameFormField?.value?.placeholder}
            hint={textualData?.companyDetails?.companyNameFormField?.value?.hint}
            error={errors.companyName?.message}
            options={
              chamberOfCommerceSearchState.matches('FETCHING')
                ? [textualData?.content?.companySearchLoadingText?.value ?? '']
                : companyNameOptions
            }
          />
          {kvkNumberOutput}
        </Stack>
      </Box>
      <Box paddingBottom={10}>
        <Stack direction="column" gap={6}>
          <Controller
            control={control}
            name="salutation"
            render={({ field: { onChange, value, name } }) => (
              <RadioGroup
                label={textualData?.personalDetails?.salutationRadioGroupField?.value?.label ?? ''}
                name={name}
                direction="row"
                value={value}
                onValueChange={onChange}
                error={errors.salutation?.message}>
                {textualData?.personalDetails?.salutationRadioGroupField?.value?.options?.map(option => (
                  <RadioButton value={option.name} key={option.name}>
                    {option.label}
                  </RadioButton>
                ))}
              </RadioGroup>
            )}
          />
          <Grid columns="2" columnGap="3">
            <InputText
              {...register('initials', {
                onBlur: (event: React.ChangeEvent<HTMLInputElement>) => {
                  setValue('initials', formatInitials(event.target.value));
                },
              })}
              label={textualData?.personalDetails?.initialsFormField?.value?.label ?? ''}
              placeholder={textualData?.personalDetails?.initialsFormField?.value?.placeholder}
              hint={textualData?.personalDetails?.initialsFormField?.value?.hint}
              error={errors.initials?.message}
            />
            <InputText
              {...register('surnamePreposition')}
              label={textualData?.personalDetails?.surnamePrepositionFormField?.value?.label ?? ''}
              placeholder={textualData?.personalDetails?.surnamePrepositionFormField?.value?.placeholder}
              hint={textualData?.personalDetails?.surnamePrepositionFormField?.value?.hint}
              error={errors.surnamePreposition?.message}
            />
          </Grid>
          <InputText
            {...register('surname', {
              onBlur: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('surname', capitalizeFirstLetter(event?.target?.value?.trim()));
              },
            })}
            label={textualData?.personalDetails?.surnameFormField?.value?.label ?? ''}
            placeholder={textualData?.personalDetails?.surnameFormField?.value?.placeholder}
            hint={textualData?.personalDetails?.surnameFormField?.value?.hint}
            error={errors.surname?.message}
          />

          <InputTelephone
            {...register('phoneNumber')}
            label={textualData?.contactDetails?.phoneNumberFormField?.value?.label ?? ''}
            placeholder={textualData?.contactDetails?.phoneNumberFormField?.value?.placeholder}
            hint={textualData?.contactDetails?.phoneNumberFormField?.value?.hint}
            autoComplete="tel"
            error={errors.phoneNumber?.message}
          />
          <Stack.Item>
            <InputEmail
              {...register('emailAddress')}
              label={textualData?.contactDetails?.emailAddressFormField?.value?.label ?? ''}
              placeholder={textualData?.contactDetails?.emailAddressFormField?.value?.placeholder}
              hint={textualData?.contactDetails?.emailAddressFormField?.value?.hint}
              autoComplete="email"
              error={errors.emailAddress?.message}
            />
          </Stack.Item>
          <Heading as="h3" size="XS">
            {textualData?.isBusinessCustomer.isBusinessCustomerRadioGroupField?.value?.label}
          </Heading>
          <Controller
            control={control}
            name="isCustomer"
            render={({ field: { onChange, value, name } }) => (
              <RadioGroup
                label=""
                name={name}
                direction="row"
                value={value}
                onValueChange={onChange}
                error={errors.isCustomer?.message}>
                {textualData?.isBusinessCustomer.isBusinessCustomerRadioGroupField?.value?.options.map(option =>
                  generateInputElement(option.name, option.label),
                )}
              </RadioGroup>
            )}
          />
        </Stack>
      </Box>

      <NextButton isLoading={isSubmitSuccessful}>{textualData?.content?.nextStepText?.value}</NextButton>
    </Layout.Content>
  );
};
