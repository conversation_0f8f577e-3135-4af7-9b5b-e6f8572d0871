import React, { BaseSyntheticEvent, FC, useRef, useEffect } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, SubmitHandler } from 'react-hook-form';
import * as yup from 'yup';

import { Recaptcha, RecaptchaRef } from '@components/Recaptcha/Recaptcha';
import RichText from '@components/RichText/RichText';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import { useFormsPublicV2PostFormV2 } from '@dc/hooks';
import useDC from '@dc/useDC';
import { Header, Layout, NextButton, Overview } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { RequestModels_Forms_V2_FormsRequestModelV2 } from '@monorepo-types/dc';
import { usePlaceholderContent } from '@sitecore/common';
import { DecarbonizationLeadFlowSummaryStepRendering } from '@sitecore/types/DecarbonizationLeadFlowSummaryStep';
import { Heading, Stack, Text } from '@sparky';

import { DecarbonisationLeadFlowContext } from '../types';

interface FormFields {
  recaptchaToken?: string;
}

const decarbonizationLeadDetails: (keyof DecarbonisationLeadFlowContext)[] = [
  'companyName',
  'chamberOfCommerceNumber',
  'salutation',
  'initials',
  'surnamePreposition',
  'surname',
  'phoneNumber',
  'emailAddress',
  'buildingType',
  'floorAreaM2',
  'numberOfFloors',
  'constructionPeriod',
  'currentHeatingSystem',
  'installments',
  'gasConsumptionReductionPercentage',
  'electricityConsumptionReductionPercentage',
  'costSavingsEur',
  'costSavingsPct',
  'investmentEur',
  'paybackPeriod',
];

export const SummaryStep: FC = () => {
  const { useFlowActorRef, useFlowSelector } = useFlowHooks<DecarbonisationLeadFlowContext>();
  const sendToFlowMachine = useFlowActorRef().send;
  const recaptchaRef = useRef<RecaptchaRef>(null);
  const { label, businessUnit } = useDC();

  const { isSuccess, isError, isLoading, send } = useFormsPublicV2PostFormV2();
  const flowContext = useFlowSelector(state => state.context);
  const data = usePlaceholderContent<{
    DecarbonizationLeadFlowSummaryStep: DecarbonizationLeadFlowSummaryStepRendering;
  }>();

  const { DecarbonizationLeadFlowSummaryStep: { fields: textualData } = {} } = data;
  const senderId = textualData?.settings?.senderIdOption?.value || '';
  const submitForm: SubmitHandler<FormFields> = formValues => {
    const formFields = decarbonizationLeadDetails.reduce(
      (agr: Array<{ key: string; value: string }>, item: keyof DecarbonisationLeadFlowContext) => {
        if (flowContext[item]) {
          agr.push({
            key: item,
            value: `${flowContext[item]}`,
          });
        }
        return agr;
      },
      [] as Array<{ key: string; value: string }>,
    );

    const requestBody: RequestModels_Forms_V2_FormsRequestModelV2 = {
      data: {
        destinationCode: senderId,
        urlOrigin: window.location.href,
        emailAddress: flowContext.emailAddress,
        houseNumber: 1,
        surnamePreposition: flowContext.surnamePreposition,
        surname: flowContext.surname,
        formFields,
      },
    };

    send({
      label,
      formId: senderId,
      businessUnit,
      requestBody,
      recaptchaToken: formValues.recaptchaToken,
    });
  };

  const formSchema = yup.object({
    recaptchaToken: yup.string().required(),
  });

  const {
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
  });
  useEffect(() => {
    if (isSuccess) {
      sendToFlowMachine({
        type: 'NEXT_PAGE',
      });
    }
  }, [isSuccess, sendToFlowMachine]);

  const onSubmit = async (event?: BaseSyntheticEvent) => {
    event?.preventDefault();
    setValue('recaptchaToken', await recaptchaRef.current?.verify());
    return handleSubmit(submitForm)();
  };
  const { phoneNumber, surnamePreposition, surname, initials, emailAddress } = flowContext;

  return (
    <Layout.Content variant="B" handleSubmit={() => onSubmit}>
      <Header>
        <Heading as="h1" size="M">
          {textualData?.content?.title?.value}
        </Heading>
        {textualData?.content?.text?.value && <RichText html={textualData?.content?.text?.value} />}
      </Header>
      <Stack.Item>
        <Overview>
          <Overview.Header>{textualData?.personalDetailsOverview?.title?.value}</Overview.Header>
          <Overview.Details
            type="profile"
            title={textualData?.personalDetailsOverview?.personalDetailsLabel?.value ?? ''}
            editButton={{
              label: textualData?.edit?.editButtonLabel?.value ?? '',
              onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_BUILDINGDETAILS' }),
            }}>
            <Text as="p">{flowContext.companyName}</Text>
            <Text as="p">
              {textualData?.personalDetailsOverview?.nameLabel?.value}:{' '}
              {`${initials} ${surnamePreposition ? surnamePreposition + ' ' : ''}${surname}`}
            </Text>
            <Text as="p">
              {textualData?.personalDetailsOverview?.phoneNumberLabel?.value}: {phoneNumber}
            </Text>
            <Text as="p">
              {textualData?.personalDetailsOverview?.emailAddressLabel?.value}: {emailAddress}
            </Text>
          </Overview.Details>
          <Overview.Details type="company" title={textualData?.companyDetails?.sectionTitle?.value ?? ''}>
            <Text as="p">{flowContext.companyName}</Text>
            <Text as="p">
              {textualData?.companyDetails?.companyTypeLabel?.value}: {flowContext.buildingTypeLabel}
            </Text>
          </Overview.Details>{' '}
          <Overview.Details type="contact" title={textualData?.isBusinessCustomer?.sectionTitle?.value ?? ''}>
            <Text as="p">{flowContext.isCustomer === 'true' ? 'Ja' : 'Nee'}</Text>
          </Overview.Details>
        </Overview>
      </Stack.Item>
      <Stack.Item>
        <Stack gap={2}>
          <Heading as="h2" size="XS">
            {textualData?.disclaimer?.disclaimerTitle?.value}
          </Heading>
          <Text size="BodyM">
            <RichText html={textualData?.disclaimer?.disclaimerContent?.value} />
          </Text>
        </Stack>
      </Stack.Item>
      <Text size="BodyS">
        <Recaptcha hasError={!!errors.recaptchaToken} ref={recaptchaRef} />
      </Text>
      <NextButton isLoading={isLoading}>{textualData?.content?.nextStepText?.value}</NextButton>
      {isError && textualData?.genericError?.fetchOfferErrorNotification && (
        <TrackedNotificationBox
          isAlert
          label={textualData.genericError?.fetchOfferErrorNotification?.value?.title}
          text={<RichText html={textualData.genericError?.fetchOfferErrorNotification?.value?.content} />}
          title={textualData.genericError?.fetchOfferErrorNotification?.value?.title}
          variant={textualData.genericError?.fetchOfferErrorNotification?.value?.variant}
        />
      )}
    </Layout.Content>
  );
};
