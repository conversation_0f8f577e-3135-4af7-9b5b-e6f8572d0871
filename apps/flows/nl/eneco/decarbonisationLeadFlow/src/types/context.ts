import { FlowContext } from '@eneco/flows2/src/types';
import {
  DC_Products_Client_Models_FloorAreaM2,
  DC_Products_Client_Models_BuildingType,
  DC_Products_Client_Models_ConstructionPeriod,
  DC_Products_Client_Models_CurrentHeatingSystem,
} from '@monorepo-types/dc';

import { Installments } from './types';

export interface DecarbonisationLeadFlowContext extends FlowContext {
  buildingType?: DC_Products_Client_Models_BuildingType;
  buildingTypeLabel?: string;
  floorAreaM2?: DC_Products_Client_Models_FloorAreaM2;
  numberOfFloors?: number;
  constructionPeriod?: DC_Products_Client_Models_ConstructionPeriod;
  currentHeatingSystem?: DC_Products_Client_Models_CurrentHeatingSystem;
  installments?: [Installments] | [];
  companyName?: string;
  chamberOfCommerceNumber?: string;
  salutation?: string;
  initials?: string;
  surnamePreposition?: string;
  surname?: string;
  phoneNumber?: string;
  emailAddress?: string;
  isCustomer?: string;
  gasConsumptionReductionPercentage?: number;
  electricityConsumptionReductionPercentage?: number;
  costSavingsEur?: number;
  costSavingsPct?: number;
  investmentEur?: number;
  paybackPeriod?: number;
}
