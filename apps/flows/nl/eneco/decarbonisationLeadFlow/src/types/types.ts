export enum BusinessCategoriesKeys {
  CarRepairShop = 'autobedrijf_autoschadeherstelbedrijven',
  CarShowroom = 'autobedrijf_showroom_en_garage',
  RetailWithCooling = 'detailhandel_met_koeling',
  RetailWithoutCooling = 'detailhandel_zonder_koeling',
  HealthcareMeeting = 'gezondheidszorg_bijeenkomst',
  HealthcarePractice = 'gezondheidszorg_praktijk',
  HealthcareHome = 'gezondheidszorg_tehuis',
  HospitalityCafe = 'horeca_cafe',
  HospitalitySnackBar = 'horeca_cafetaria',
  HospitalityHotelsMotels = 'horeca_hotels_motels',
  HospitalityRestaurant = 'horeca_restaurant',
  OfficeGovernment = 'kantoor_overheid',
  OfficeOther = 'kantoor_overig',
  EducationPrimary = 'onderwijs_primair',
  OtherBeautyCare = 'overig_haar_en_schoonheidsverzorging',
  OtherReligion = 'overig_religie',
  RecreationIndoorSports = 'recreatie_binnensport',
  RecreationOutdoorSports = 'recreatie_buitensport',
  RecreationClub = 'recreatie_vereniging',
  RecreationPool = 'recreatie_zwembad',
}

export enum BusinessCategoriesValues {
  autobedrijf_autoschadeherstelbedrijven = 'CarRepairShop',
  autobedrijf_showroom_en_garage = 'CarShowroom',
  detailhandel_met_koeling = 'RetailWithCooling',
  detailhandel_zonder_koeling = 'RetailWithoutCooling',
  gezondheidszorg_bijeenkomst = 'HealthcareMeeting',
  gezondheidszorg_praktijk = 'HealthcarePractice',
  gezondheidszorg_tehuis = 'HealthcareHome',
  horeca_cafe = 'HospitalityCafe',
  horeca_cafetaria = 'HospitalitySnackBar',
  horeca_hotels_motels = 'HospitalityHotelsMotels',
  horeca_restaurant = 'HospitalityRestaurant',
  kantoor_overheid = 'OfficeGovernment',
  kantoor_overig = 'OfficeOther',
  onderwijs_primair = 'EducationPrimary',
  overig_haar_en_schoonheidsverzorging = 'OtherBeautyCare',
  overig_religie = 'OtherReligion',
  recreatie_binnensport = 'RecreationIndoorSports',
  recreatie_buitensport = 'RecreationOutdoorSports',
  recreatie_vereniging = 'RecreationClub',
  recreatie_zwembad = 'RecreationPool',
}

export const BusinessCategories = {
  [BusinessCategoriesKeys.CarRepairShop]: BusinessCategoriesValues.autobedrijf_autoschadeherstelbedrijven,
  [BusinessCategoriesKeys.CarShowroom]: BusinessCategoriesValues.autobedrijf_showroom_en_garage,
  [BusinessCategoriesKeys.RetailWithCooling]: BusinessCategoriesValues.detailhandel_met_koeling,
  [BusinessCategoriesKeys.RetailWithoutCooling]: BusinessCategoriesValues.detailhandel_zonder_koeling,
  [BusinessCategoriesKeys.HealthcareMeeting]: BusinessCategoriesValues.gezondheidszorg_bijeenkomst,
  [BusinessCategoriesKeys.HealthcarePractice]: BusinessCategoriesValues.gezondheidszorg_praktijk,
  [BusinessCategoriesKeys.HealthcareHome]: BusinessCategoriesValues.gezondheidszorg_tehuis,
  [BusinessCategoriesKeys.HospitalityCafe]: BusinessCategoriesValues.horeca_cafe,
  [BusinessCategoriesKeys.HospitalitySnackBar]: BusinessCategoriesValues.horeca_cafetaria,
  [BusinessCategoriesKeys.HospitalityHotelsMotels]: BusinessCategoriesValues.horeca_hotels_motels,
  [BusinessCategoriesKeys.HospitalityRestaurant]: BusinessCategoriesValues.horeca_restaurant,
  [BusinessCategoriesKeys.OfficeGovernment]: BusinessCategoriesValues.kantoor_overheid,
  [BusinessCategoriesKeys.OfficeOther]: BusinessCategoriesValues.kantoor_overig,
  [BusinessCategoriesKeys.EducationPrimary]: BusinessCategoriesValues.onderwijs_primair,
  [BusinessCategoriesKeys.OtherBeautyCare]: BusinessCategoriesValues.overig_haar_en_schoonheidsverzorging,
  [BusinessCategoriesKeys.OtherReligion]: BusinessCategoriesValues.overig_religie,
  [BusinessCategoriesKeys.RecreationIndoorSports]: BusinessCategoriesValues.recreatie_binnensport,
  [BusinessCategoriesKeys.RecreationOutdoorSports]: BusinessCategoriesValues.recreatie_buitensport,
  [BusinessCategoriesKeys.RecreationClub]: BusinessCategoriesValues.recreatie_vereniging,
  [BusinessCategoriesKeys.RecreationPool]: BusinessCategoriesValues.recreatie_zwembad,
};

export enum Installments {
  SOLAR_PANELS = 'Solar panels',
  ROOF_ISOLATION = 'Roof isolation',
  UNKNOWN = 'Unknown',
}
