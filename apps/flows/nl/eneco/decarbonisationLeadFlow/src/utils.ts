import { DC_Products_Client_Models_ConstructionPeriod } from '@monorepo-types/dc';

export const getConstructionPeriodValue = (data: string) => {
  let value = '';
  if (data === 'Voor 1920') {
    value = 'Before1920';
  }
  if (data === '1920 - 1975') {
    value = 'Between1920And1975';
  }
  if (data === '1975 - 2010') {
    value = 'Between1975And2010';
  }
  if (data === 'Na 2010') {
    value = 'After2010';
  }

  return value as DC_Products_Client_Models_ConstructionPeriod;
};
