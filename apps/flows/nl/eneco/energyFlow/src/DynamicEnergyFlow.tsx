import { ExperimentProvider } from '@dxp-experiments';
import { Layout } from '@eneco/flows2';
import { createFlowProvider } from '@eneco/flows2/utils';

import { FlowConsumerContextEnergy } from './context';
import { routes, actions, initialContext } from './DynamicEnergyFlow.config';

const DynamicEnergyFlow = () => {
  const { Provider, Navigation, Router } = createFlowProvider<FlowConsumerContextEnergy>({
    routes,
    actions,
    initialContext,
  });
  return (
    <ExperimentProvider>
      <Provider>
        <Layout>
          <Layout.Header>
            <Navigation sitecoreComponentName="DynamicEnergyFlowNavigation" />
          </Layout.Header>
          <Router />
        </Layout>
      </Provider>
    </ExperimentProvider>
  );
};

export default DynamicEnergyFlow;
