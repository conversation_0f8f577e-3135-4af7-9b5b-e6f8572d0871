import { FlowRoute, FlowRouteCategory, FlowActions } from '@eneco/flows2/types';

import { setUsageKnown, setUsageEstimatedValues, resetUsageValues, resetSolarPanelsOutput } from './actions';
import { FlowConsumerContextEnergy } from './context';
import {
  shouldSkipWhenNoElectricity,
  shouldSkipWhenNoGasUpsellProductAvailable,
  shouldSkipWhenNoRentalDeviceCrossSellProductAvailable,
  shouldSkipWhenNoGas,
  shouldSkipWhenUsageIsKnown,
  shouldSkipWhenUsageNotKnown,
  shouldSkipWhenNoSolarPanels,
  shouldSkipWhenOnlyWarmth,
  shouldSkipWhenOnlyGeothermalHeating,
} from './skippingRules';
import {
  AddressDetailsStep,
  BuildingAgeStep,
  BuildingTypeStep,
  DeliveryDateStep,
  SustainableChoiceStep,
  ContactDetailsStep,
  CrossSellRentalDeviceStep,
  EnergyChoiceStep,
  MeterTypeStep,
  MovingStep,
  EnergyFlowOfferStep,
  PersonalDetailsStep,
  ResidentsStep,
  SolarPanelsStep,
  SolarPanelsRedeliveryStep,
  SummaryStep,
  UsageStep,
  UsageKnownStep,
} from './steps';
import { AddressStep } from './steps/AddressStep/AddressStep';

export const initialContext: FlowConsumerContextEnergy = {};

export const routes: FlowRoute<FlowConsumerContextEnergy>[] = [
  {
    id: 'ADDRESS',
    path: '/adres/',
    category: FlowRouteCategory.Calculation,
    component: AddressStep,
    skippingFields: ['postalCode', 'houseNumber'],
  },

  {
    id: 'ENERGY_CHOICE',
    path: '/energiekeuze/',
    category: FlowRouteCategory.Calculation,
    component: EnergyChoiceStep,
    actions: ['resetUsageValues'],
  },

  {
    id: 'USAGE_KNOWN',
    path: '/verbruik-bekend/',
    category: FlowRouteCategory.Calculation,
    component: UsageKnownStep,
    skippingRules: [shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'USAGE',
    path: '/verbruik/',
    category: FlowRouteCategory.Calculation,
    component: UsageStep,
    actions: ['setUsageKnown'],
    skippingRules: [shouldSkipWhenUsageNotKnown, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'RESIDENTS',
    path: '/inschatting/aantal-bewoners/',
    category: FlowRouteCategory.Calculation,
    component: ResidentsStep,
    skippingRules: [shouldSkipWhenUsageIsKnown, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'BUILDING_TYPE',
    path: '/inschatting/type-woning/',
    category: FlowRouteCategory.Calculation,
    component: BuildingTypeStep,
    skippingRules: [shouldSkipWhenUsageIsKnown, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'BUILDING_AGE',
    path: '/inschatting/bouwjaar-woning/',
    category: FlowRouteCategory.Calculation,
    component: BuildingAgeStep,
    actions: ['resetUsageValues', 'setUsageEstimatedValues'],
    skippingRules: [shouldSkipWhenUsageIsKnown, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'METER_TYPE',
    path: '/inschatting/metertype/',
    category: FlowRouteCategory.Calculation,
    component: MeterTypeStep,
    actions: ['resetUsageValues', 'setUsageEstimatedValues'],
    skippingRules: [shouldSkipWhenUsageIsKnown, shouldSkipWhenNoElectricity],
  },

  {
    id: 'SOLAR_PANELS',
    path: '/zonnepanelen/',
    category: FlowRouteCategory.Calculation,
    component: SolarPanelsStep,
    actions: ['resetSolarPanelsOutput'],
    skippingRules: [shouldSkipWhenNoElectricity],
  },

  {
    id: 'SOLAR_PANELS_REDELIVERY',
    path: '/zonnepanelen/verbruik/',
    category: FlowRouteCategory.Calculation,
    component: SolarPanelsRedeliveryStep,
    skippingRules: [shouldSkipWhenNoSolarPanels],
  },

  {
    id: 'MOVING',
    path: '/je-gegevens/verhuizen/',
    category: FlowRouteCategory.Calculation,
    component: MovingStep,
    skippingRules: [],
  },

  {
    id: 'OFFER',
    path: '/productkeuze/looptijd/',
    category: FlowRouteCategory.Overview,
    component: EnergyFlowOfferStep,
    skippingRules: [shouldSkipWhenOnlyWarmth, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'CO2UPSELL',
    path: '/productkeuze/co2-gas/',
    category: FlowRouteCategory.Overview,
    component: SustainableChoiceStep,
    skippingRules: [shouldSkipWhenNoGas, shouldSkipWhenNoGasUpsellProductAvailable],
  },

  {
    id: 'CROSS_SELL_RENTAL_DEVICE',
    path: '/productkeuze/huurapparaat/',
    category: FlowRouteCategory.Overview,
    component: CrossSellRentalDeviceStep,
    skippingRules: [shouldSkipWhenNoRentalDeviceCrossSellProductAvailable],
  },

  {
    id: 'DELIVERY_DATE',
    path: '/je-gegevens/start-leverdatum/',
    category: FlowRouteCategory.Details,
    component: DeliveryDateStep,
  },

  {
    id: 'ADDRESS_DETAILS',
    path: '/je-gegevens/adresgegevens/',
    category: FlowRouteCategory.Details,
    component: AddressDetailsStep,
    skippingRules: [shouldSkipWhenOnlyWarmth, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'PERSONAL_DETAILS',
    path: '/je-gegevens/persoonlijke-gegevens/',
    category: FlowRouteCategory.Details,
    component: PersonalDetailsStep,
  },

  {
    id: 'CONTACT_DETAILS',
    path: '/je-gegevens/contactgegevens/',
    category: FlowRouteCategory.Details,
    component: ContactDetailsStep,
  },

  {
    id: 'SUMMARY',
    path: '/je-gegevens/controle-keuze/',
    category: FlowRouteCategory.Summary,
    component: SummaryStep,
  },
];

export const actions: FlowActions<FlowConsumerContextEnergy> = {
  setUsageKnown,
  setUsageEstimatedValues,
  resetUsageValues,
  resetSolarPanelsOutput,
};
