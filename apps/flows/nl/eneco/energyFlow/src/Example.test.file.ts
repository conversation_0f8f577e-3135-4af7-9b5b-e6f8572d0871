import { FlowRoute, FlowRouteCategory, FlowActions } from '@eneco/flows2/types';

import { setUsageKnown, setUsageEstimatedValues, resetUsageValues, resetSolarPanelsOutput } from './actions';
import { FlowConsumerContextEnergy } from './context';
import { shouldSkipWhenOnlyWarmth, shouldSkipWhenOnlyGeothermalHeating } from './skippingRules';
import { shouldSkipIfTestVariantIsActive } from './skippingRules/shouldSkipIfTestVariantIsActive';
import { EnergyFlowOfferStep, OverviewStep } from './steps';

// The initialContext here defines the default context values for the flow under test.
// These values will override the original initialContext.
export const initialContext: FlowConsumerContextEnergy = {
  gb_variant: 'expendable_offercard',
};

// Routes defined here specify steps that should behave differently.
// These replace the corresponding steps in the default flow.
// To skip a step, add a skippingRule.
export const routes: FlowRoute<FlowConsumerContextEnergy>[] = [
  {
    id: 'OFFER',
    path: '/productkeuze/looptijd/',
    category: FlowRouteCategory.Overview,
    component: EnergyFlowOfferStep,
    skippingRules: [shouldSkipWhenOnlyWarmth, shouldSkipWhenOnlyGeothermalHeating],
  },

  {
    id: 'OVERVIEW',
    path: '/overzicht/',
    category: FlowRouteCategory.Overview,
    component: OverviewStep,
    skippingRules: [shouldSkipIfTestVariantIsActive],
  },
];

export const actions: FlowActions<FlowConsumerContextEnergy> = {
  setUsageKnown,
  setUsageEstimatedValues,
  resetUsageValues,
  resetSolarPanelsOutput,
};
