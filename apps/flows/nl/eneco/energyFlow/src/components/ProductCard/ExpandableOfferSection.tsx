import { FC, useEffect } from 'react';

import { useForm, Controller, useWatch } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { FlowContext } from '@eneco/flows/src/types';
import { useFormatter } from '@i18n';
import { DC_Domain_Models_Products_DiscountType } from '@monorepo-types/dc';
import { Fields } from '@sitecore/types/OfferStep';
import { Expandable, Box, Stack, NotificationBox, TabSwitch, Divider, TextLink, Text } from '@sparky';

import { constructTextualDataKey, getProductTypeLabel } from '../../helpers';
import { useProductItemContent } from '../../hooks/sitecore/useProductItemContent';
import { DetailsType, Offer, Product } from '../../types';
import { OfferType } from '../../types/enums';
import { typeTheme } from '../PriceDetails/PriceDetails';
import { PriceDetailsOverview } from '../PriceDetailsOverview/PriceDetailsOverview';

const NEW_ENERGY_3_YEARS_DISCOUNT_TYPE = 'newEnergy3Years' as DC_Domain_Models_Products_DiscountType;
const EXTEND_CONTRACT_DISCOUNT_TYPE = 'extend3Years' as DC_Domain_Models_Products_DiscountType;

const ExpandableOfferSection: FC<{
  offer: Offer;
  textualData?: Fields;
  flowContext?: FlowContext;
  isOpen: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setCostPeriod: React.Dispatch<React.SetStateAction<string>>;
}> = ({ offer, textualData, flowContext, isOpen, setOpen, setCostPeriod }) => {
  const { discountType } = offer || {};
  const { currency } = useFormatter();
  const formMethods = useForm<{ costPeriod: string }>({
    mode: 'onBlur',
    defaultValues: {
      costPeriod: 'costsMonthly',
    },
  });
  const { control } = formMethods;
  const { costPeriod = 'costsMonthly' } = useWatch({ control });

  type CostPeriod = 'costsMonthly' | 'costsYearly';

  const periodLabel: Record<CostPeriod, string> = {
    costsMonthly: 'per maand',
    costsYearly: 'per jaar',
  };

  useEffect(() => {
    setCostPeriod(costPeriod);
  }, [costPeriod, setCostPeriod]);

  const textualDataKey = constructTextualDataKey(offer?.contractSpan, flowContext?.energyType);
  const { getProductDetails } = useProductItemContent(textualData?.productData?.productDataList);

  const label = periodLabel[(costPeriod ?? 'costsMonthly') as 'costsMonthly' | 'costsYearly'];
  const getCostPerPeriod = (data: Offer | Product) =>
    costPeriod === 'costsMonthly' ? data.costsMonthly : data.costsYearly;

  const productDetails = getProductDetails(textualDataKey);

  const tariffDescriptionText = productDetails?.tariffDescription;
  const tariffSplitExplanationText = productDetails?.tariffSplitExplanation;

  const shouldShowVariablePricingNotification =
    discountType?.includes(OfferType.Variable) &&
    !!textualData?.productData?.variableProductNotification?.value?.content;

  return (
    <Expandable isOpen={isOpen} setOpen={setOpen}>
      <Box paddingRight="6">
        <Expandable.Trigger isTransparent aria-labelledby={`${offer.type}-tariff-details`}>
          <Box id={`${offer.type}-tariff-details`} paddingLeft="6" paddingY="6">
            {textualData?.productData?.tariffsTriggerText?.value}
          </Box>
        </Expandable.Trigger>
      </Box>

      <Expandable.Content>
        <Box padding="6" paddingTop="0">
          <Stack gap="6">
            {shouldShowVariablePricingNotification && (
              <NotificationBox
                isAlert={false}
                variant={textualData?.productData?.variableProductNotification?.value?.variant}
                title={textualData?.productData?.variableProductNotification?.value?.title}
                text={<RichText html={textualData?.productData?.variableProductNotification?.value?.content} />}
              />
            )}
            <Controller
              control={control}
              name="costPeriod"
              render={({ field: { onChange, value, name } }) => (
                <TabSwitch
                  name={name}
                  value={value}
                  onValueChange={val => {
                    onChange(val);
                  }}>
                  <TabSwitch.Item key="costsMonthly" value="costsMonthly">
                    Prijs per maand
                  </TabSwitch.Item>

                  <TabSwitch.Item key="costsYearly" value="costsYearly">
                    Prijs per jaar
                  </TabSwitch.Item>
                </TabSwitch>
              )}
            />
            <Stack gap="4">
              {offer.products.map(product => (
                <Stack key={product.code} direction="row" alignY="start" gap="2">
                  <Stack.Item grow={true}>
                    <Stack direction="row" gap="2">
                      {product.type === 'warmtewisselaar'
                        ? typeTheme[DetailsType.Warmth]?.icon
                        : typeTheme[product.type as DetailsType]?.icon}
                      <Stack>
                        <Text weight="bold">{getProductTypeLabel(product.type, textualData?.unitPriceLabels)}</Text>
                      </Stack>
                    </Stack>
                  </Stack.Item>

                  <Stack.Item shrink={false}>
                    <Stack direction="column" alignY="end" alignX="end">
                      <Stack.Item grow={true}>
                        <Text weight="bold">
                          {currency.euroHighPrecision(getCostPerPeriod(product)?.vatIncluded ?? 0)}
                        </Text>
                      </Stack.Item>
                      <Stack.Item grow={true}>
                        <Text size="BodyS">{label}</Text>
                      </Stack.Item>
                    </Stack>
                  </Stack.Item>
                </Stack>
              ))}
              <Stack.Item grow>
                <Divider />
              </Stack.Item>
              <Stack direction="row" alignY="start" gap="2">
                <Stack.Item grow={true}>
                  <Stack direction="row" gap="2">
                    <Text weight="bold">Totaal</Text>
                  </Stack>
                </Stack.Item>

                <Stack.Item shrink={false}>
                  <Stack direction="column" alignY="end" gap="2">
                    <Stack direction="column" alignY="end" alignX="end">
                      <Stack.Item grow={true}>
                        <Text weight="bold">
                          {currency.euroHighPrecision(getCostPerPeriod(offer)?.vatIncluded ?? 0)}
                        </Text>
                      </Stack.Item>
                      <Stack.Item grow={true}>
                        <Text size="BodyS">{label}</Text>
                      </Stack.Item>
                    </Stack>
                  </Stack>
                </Stack.Item>
              </Stack>
            </Stack>
          </Stack>
          <Stack gap="3">
            <Text size="BodyS">
              <TrackedDialog
                title="Bekijk de prijsopbouw"
                trigger={<TextLink emphasis="high">Bekijk de prijsopbouw</TextLink>}>
                <PriceDetailsOverview offer={offer} flowContext={flowContext ?? {}} textualData={textualData} />
              </TrackedDialog>
            </Text>
            {offer?.contractSpan?.some(span => span.duration === 3 && span.byPeriod === 'Y') &&
              (offer?.discountType === NEW_ENERGY_3_YEARS_DISCOUNT_TYPE ||
                offer?.discountType === EXTEND_CONTRACT_DISCOUNT_TYPE) && (
                <Box paddingTop={{ initial: '3', md: '0' }}>
                  <Text size="BodyXS" color="textLowEmphasis">
                    {tariffSplitExplanationText}
                  </Text>
                </Box>
              )}

            {tariffDescriptionText && (
              <Box paddingTop={{ initial: '3', md: '0' }}>
                <Text size="BodyXS" color="textLowEmphasis">
                  {tariffDescriptionText}
                </Text>
              </Box>
            )}
          </Stack>
        </Box>
      </Expandable.Content>
    </Expandable>
  );
};

export default ExpandableOfferSection;
