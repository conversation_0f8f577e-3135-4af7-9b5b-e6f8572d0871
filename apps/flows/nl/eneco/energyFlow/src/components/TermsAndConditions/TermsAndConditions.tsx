import React, { FC } from 'react';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { FlowContext } from '@eneco/flows/types';
import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';
import { Fields as OxxioSummaryStepFields } from '@sitecore/types/OxxioSummaryStep';
import { Fields as SummaryStepFields } from '@sitecore/types/SummaryStep';
import { Heading, Box, NavLink, Stack, Text, TextLink, Divider } from '@sparky';

import { PriceDetailsOverview as PriceDetailsOverviewEneco, PriceDetailsOverviewOxxio } from '../../components';
import { checkOfferIncludesNonDynamicPricingProduct, checkOfferIncludesDynamicPricingProduct } from '../../helpers';
import { checkOffersIncludeVariablePricingProduct } from '../../helpers/misc';
import { Offer, OfferTermsAndConditions } from '../../types';

type FieldsUnion = Partial<SummaryStepFields | OxxioSummaryStepFields>;

interface Props {
  label: DC_Repositories_Base_Enumerations_Label;
  flowContext: FlowContext;
  textualData?: FieldsUnion;
  offer?: Offer;
  offerTermsAndConditions?: OfferTermsAndConditions[];
}

export const TermsAndConditions: FC<Props> = ({ label, textualData, offer, offerTermsAndConditions, flowContext }) => {
  const isDynamicPricingContract =
    !checkOffersIncludeVariablePricingProduct(offer) &&
    checkOfferIncludesDynamicPricingProduct(offer) &&
    !checkOfferIncludesNonDynamicPricingProduct(offer);
  const isHybridContract =
    !checkOffersIncludeVariablePricingProduct(offer) &&
    checkOfferIncludesDynamicPricingProduct(offer) &&
    checkOfferIncludesNonDynamicPricingProduct(offer);

  const isRegularContract = !isDynamicPricingContract && !isHybridContract;

  return (
    <>
      <Stack.Item>
        <Stack direction="column" gap="4">
          <Heading as="h3" size="XS">
            {textualData?.salesConditions?.conditionsParagraph?.value?.title}
          </Heading>

          <Stack.Item>
            <Text>
              <RichText
                html={textualData?.salesConditions?.conditionsParagraph?.value?.content?.split('{conditions}')[0]}
              />{' '}
              <TrackedDialog
                title={textualData?.salesConditions?.salesConditionsDialog?.value?.title}
                description={<RichText html={textualData?.salesConditions?.salesConditionsDialog?.value?.content} />}
                trigger={
                  <TextLink>{textualData?.salesConditions?.salesConditionsDialog?.value?.triggerText}</TextLink>
                }>
                <Stack direction="column" gap="2">
                  {offerTermsAndConditions?.map(
                    ({ href, title }) =>
                      href &&
                      href !== 'null' &&
                      title && (
                        <NavLink key={href} href={href} target="_blank">
                          {title}
                        </NavLink>
                      ),
                  )}
                </Stack>
              </TrackedDialog>{' '}
              <RichText
                html={
                  textualData?.salesConditions?.conditionsParagraph?.value?.content
                    ?.split('{conditions}')[1]
                    ?.split('{prices}')[0]
                }
              />{' '}
              <TrackedDialog
                title={textualData?.salesConditions?.tariffsDialog?.value?.title}
                description={<RichText html={textualData?.salesConditions?.tariffsDialog?.value?.content} />}
                trigger={<TextLink>{textualData?.salesConditions?.tariffsDialog?.value?.triggerText}</TextLink>}>
                {label === 'oxxio' ? (
                  <>
                    <Box backgroundColor="backgroundSecondary" paddingX="3">
                      <PriceDetailsOverviewOxxio
                        offer={offer}
                        flowContext={flowContext}
                        textualData={textualData as OxxioSummaryStepFields}
                      />
                    </Box>
                    <Divider />
                  </>
                ) : (
                  <PriceDetailsOverviewEneco
                    offer={offer}
                    flowContext={flowContext}
                    textualData={textualData as SummaryStepFields}
                  />
                )}
              </TrackedDialog>{' '}
              <RichText
                html={
                  textualData?.salesConditions?.conditionsParagraph?.value?.content
                    ?.split('{conditions}')[1]
                    ?.split('{prices}')[1]
                }
              />
            </Text>
          </Stack.Item>
        </Stack>
      </Stack.Item>

      <Stack.Item>
        <Stack direction="column" gap="4">
          <Heading as="h3" size="XS">
            {textualData?.salesConditions?.considerationPeriodParagraph?.value?.title}
          </Heading>

          <RichText html={textualData?.salesConditions?.considerationPeriodParagraph?.value?.content} />
        </Stack>
      </Stack.Item>

      {isDynamicPricingContract && (
        <Stack.Item>
          <Stack direction="column" gap="4">
            <Heading as="h3" size="XS">
              {textualData?.salesConditions?.noticePeriodDynamicPricingProductParagraph?.value?.title}
            </Heading>

            <RichText html={textualData?.salesConditions?.noticePeriodDynamicPricingProductParagraph?.value?.content} />
          </Stack>
        </Stack.Item>
      )}

      {isHybridContract && (
        <Stack.Item>
          <Stack direction="column" gap="4">
            <Heading as="h3" size="XS">
              {textualData?.salesConditions?.noticePeriodHybridParagraph?.value?.title}
            </Heading>

            <RichText html={textualData?.salesConditions?.noticePeriodHybridParagraph?.value?.content} />
          </Stack>
        </Stack.Item>
      )}

      {isRegularContract && (
        <Stack.Item>
          <Stack direction="column" gap="4">
            <Heading as="h3" size="XS">
              {textualData?.salesConditions?.noticePeriodParagraph?.value?.title}
            </Heading>

            <RichText html={textualData?.salesConditions?.noticePeriodParagraph?.value?.content} />
          </Stack>
        </Stack.Item>
      )}

      <Stack.Item>
        <Stack direction="column" gap="4">
          <Heading as="h3" size="XS">
            {textualData?.salesConditions?.objectionParagraph?.value?.title}
          </Heading>

          <RichText html={textualData?.salesConditions?.objectionParagraph?.value?.content} />
        </Stack>
      </Stack.Item>

      {isDynamicPricingContract && (
        <Stack.Item>
          <Stack direction="column" gap="4">
            <Heading as="h3" size="XS">
              {textualData?.salesConditions?.variablePricingParagraph?.value?.title}
            </Heading>

            <RichText html={textualData?.salesConditions?.variablePricingParagraph?.value?.content} />
          </Stack>
        </Stack.Item>
      )}

      {(isHybridContract || isDynamicPricingContract) && (
        <Stack.Item>
          <Stack direction="column" gap="4">
            <Heading as="h3" size="XS">
              {textualData?.salesConditions?.variablePricingHybridParagraph?.value?.title}
            </Heading>

            <RichText html={textualData?.salesConditions?.variablePricingHybridParagraph?.value?.content} />
          </Stack>
        </Stack.Item>
      )}
    </>
  );
};
