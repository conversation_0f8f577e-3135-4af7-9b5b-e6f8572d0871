import React, { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, useForm } from 'react-hook-form';
import * as yup from 'yup';

import AddressFinder, {
  defaultValues as addressFinderDefaultValues,
  addressFinderSchema,
} from '@components/AddressFinder/AddressFinder';
import RichText from '@components/RichText/RichText';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { Customers_Address_AddressModel } from '@monorepo-types/dc';
import { usePlaceholderContent } from '@sitecore/common';
import { AddressStepRendering } from '@sitecore/types/AddressStep';
import { Box, Form, Stack, NotificationBox, Checkbox } from '@sparky';

import { FlowConsumerContextEnergy } from '../../context';
import { useFlowNavigation } from '../../hooks/updateContextAndGoToNextStep';

interface FormFields {
  postalCode: string;
  houseNumber: number;
  houseNumberSuffix?: string;
  city: string;
  street: string;
  isMoving?: boolean;
}

const convertIsMovingParamToBoolean = (value?: string | boolean): boolean | undefined => {
  if (typeof value === 'string') {
    return value === 'true';
  }

  return value;
};

export const AddressStep: FC = () => {
  const { useFlowSelector } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const { updateContextAndGoToNextStep } = useFlowNavigation();

  const [isSuffixRequired, setIsSuffixRequired] = useState(false);

  const {
    context: { sc_trk, postalCode, houseNumber, houseNumberSuffix, street, city, isMoving },
  } = flowState;

  const { AddressStep: { fields: textualData } = {} } = usePlaceholderContent<{ AddressStep: AddressStepRendering }>();

  const formSchema = yup.object({
    ...addressFinderSchema(isSuffixRequired).fields,
  });

  const formMethods = useForm<FormFields>({
    mode: 'onBlur',
    defaultValues: {
      ...addressFinderDefaultValues,
      postalCode,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      isMoving: convertIsMovingParamToBoolean(isMoving),
    },
    resolver: yupResolver(formSchema),
  });

  const {
    handleSubmit,
    getValues,
    register,
    formState: { isSubmitSuccessful },
  } = formMethods;

  const [addressFinderData, setAddressFinderData] = useState<Customers_Address_AddressModel>();

  useEffect(() => {
    if (isSubmitSuccessful) {
      const formData = getValues();

      updateContextAndGoToNextStep({
        ...formData,
        postalCode: addressFinderData?.postalCode?.toString(),
        houseNumber: Number(addressFinderData?.houseNumber),
        houseNumberSuffix,
      });
    }
  }, [isSubmitSuccessful]);

  return (
    <Layout.Content variant="A" handleSubmit={handleSubmit}>
      <FormProvider {...formMethods}>
        <Header>
          <Header.Title>{textualData?.content?.title?.value}</Header.Title>
          {textualData?.content?.text?.value && <Header.Text>{textualData.content.text.value}</Header.Text>}
        </Header>

        {textualData?.content?.notification?.value?.content && (
          <NotificationBox
            isAlert={false}
            variant={textualData?.content?.notification?.value?.variant}
            title={textualData?.content?.notification?.value?.title}
            text={<RichText html={textualData?.content?.notification?.value?.content} />}
          />
        )}
        <Stack.Item as="fieldset">
          <Form>
            <AddressFinder
              customLabels={{
                houseNumber: textualData?.address?.houseNumberFormField,
                postalCode: textualData?.address?.postalCodeFormField,
                houseNumberSuffix: textualData?.address?.houseNumberSuffixFormField,
              }}
              isSuffixRequired={isSuffixRequired}
              setIsSuffixRequired={setIsSuffixRequired}
              setAddressFinderData={setAddressFinderData}
            />
          </Form>

          {sc_trk === 'vg_b_moving_step_ene' ||
            (sc_trk === 'vg_b_moving_step_oxx' && (
              <Box paddingTop="4">
                <Checkbox {...register('isMoving')} label="Ik ga verhuizen" />
              </Box>
            ))}
        </Stack.Item>

        <input {...register('city')} type="hidden" />
        <input {...register('street')} type="hidden" />

        <NextButton isLoading={isSubmitSuccessful}>{textualData?.content?.nextStepText?.value}</NextButton>
      </FormProvider>
    </Layout.Content>
  );
};
