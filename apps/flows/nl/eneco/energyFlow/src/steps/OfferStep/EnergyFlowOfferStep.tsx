import React, { FC, useEffect, useMemo, useRef, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { Checklist } from '@components/ui/Checklist/Checklist';
import useDC from '@dc/useDC';
import { Product } from '@eneco/flows/types';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { Fields, OfferStepRendering } from '@sitecore/types/OfferStep';
import {
  Bleed,
  Box,
  Carousel,
  Grid,
  NotificationBox,
  RadioGroup,
  RadioTile,
  Text,
  TextLink,
  VisuallyHidden,
} from '@sparky';
import { useMediaQuery } from '@sparky/hooks';

import { Error, OverviewConsumption, ProductCard, Ribbon } from '../../components';
import ChatActivator from '../../components/ChatActivator/ChatActivator';
import ExpandableOfferSection from '../../components/ProductCard/ExpandableOfferSection';
import { FlowConsumerContextEnergy } from '../../context';
import { touExperimentFeatureToggleKey, touExperimentProductFilter } from '../../growthbook/experiment.helpers';
import {
  checkAvailabilityOfferType,
  checkOfferIncludesDynamicPricingProduct,
  checkOfferIncludesNonDynamicPricingProduct,
  checkOffersIncludesDynamicPricingProduct,
  constructTextualDataKey,
  getCashbackTexts,
  getCrossSellRentalDeviceProductAvailability,
  getOffer,
  getUpsellProductAvailability,
  selectDefaultDiscountCode,
} from '../../helpers';
import { handleNbaFeedback } from '../../helpers/dc';
import { checkIfOfferHasTimeOfUseProduct, checkOfferTypes, filterOffersByType } from '../../helpers/misc';
import { checkOnDiscountType } from '../../helpers/sitecore';
import { useFlowTracking, useGetOffersByExperiment } from '../../hooks';
import { useProductItemContent } from '../../hooks/sitecore/useProductItemContent';
import { useFlowNavigation } from '../../hooks/updateContextAndGoToNextStep';
import { useOfferMachine } from '../../hooks/useOfferMachine';
import { EnergyType, Offer } from '../../types';
import { OfferType } from '../../types/enums';

interface FormFields {
  discountCode?: string;
}

export const checkOffersIncludesNonDynamicPricingProduct = (offers: Offer[]): boolean => {
  return !!offers.find(offer => checkOfferIncludesNonDynamicPricingProduct(offer));
};

const OfferCard: FC<{
  offer: Offer;
  isSME?: boolean;
  energyType?: EnergyType;
  textualData?: Fields;
  flowContext?: FlowConsumerContextEnergy;
  isOpen: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ offer, energyType = EnergyType.Electricity, textualData, flowContext, isOpen, setOpen }) => {
  const { format, currency } = useFormatter();
  const [costPeriod, setCostPeriod] = useState('costsMonthly');

  const textualDataKey = checkOnDiscountType(
    constructTextualDataKey(offer.contractSpan, energyType),
    offer?.discountType,
  );

  const { getProductDetails } = useProductItemContent(textualData?.productData?.productDataList);

  const productDetails = getProductDetails(textualDataKey);

  // Ensures that any product item is not rendered unless it is mapped on Sitecore, even though that's exposed by the api
  if (!productDetails) {
    return null;
  }

  const { title, promotionText, usps } = productDetails;

  const cashbackTexts = getCashbackTexts(
    offer?.costDiscounts?.cashback?.type,
    textualData?.promotionTexts,
    textualData?.giftData,
    offer?.costDiscounts?.cashback?.value,
  );

  const getCostPerPeriod = (data: Offer | Product) =>
    costPeriod === 'costsMonthly' ? data.costsMonthly : data.costsYearly;
  const isDynamicOffer = checkOfferIncludesDynamicPricingProduct(offer);

  const getPeriodLabel = () => {
    if (costPeriod === 'costsMonthly') {
      return isDynamicOffer
        ? textualData?.productData?.perMonthEstimatedLabel?.value
        : textualData?.productData?.perMonthLabel?.value;
    }
    return isDynamicOffer ? 'verwacht per jaar' : 'geschat per jaar';
  };

  return (
    <ProductCard
      id={offer.type ?? ''}
      title={title}
      tag={offer.mostPopularChoice ? promotionText : undefined}
      usps={usps}
      price={{
        value: getCostPerPeriod(offer)?.vatIncluded ?? 0,
        period: getPeriodLabel(),
      }}
      ribbon={
        cashbackTexts ? (
          <Ribbon
            size="small"
            emphasis="high"
            title={format(cashbackTexts.title, {
              [`${offer?.costDiscounts?.cashback?.type}`]: currency.euroNoFractionDigits(
                offer?.costDiscounts?.cashback?.value ?? 0,
              ),
            })}
            text={cashbackTexts.text}
            trigger={cashbackTexts?.trigger}
          />
        ) : null
      }
      touRibbon={
        checkIfOfferHasTimeOfUseProduct(offer) ? (
          <Ribbon
            size="small"
            emphasis="high"
            ribbonTitle={textualData?.promotionTexts?.timeOfUseRibbonContent?.value}
            title={textualData?.content?.timeOfUseInfoDialog?.value?.title || ''}
            text={textualData?.content?.timeOfUseInfoDialog?.value?.content}
            trigger={textualData?.content?.timeOfUseInfoDialog?.value?.triggerText}
            contentContainer="dialog"
            hasInfoIcon={false}
            hasGiftIcon={false}>
            <Checklist>
              {textualData?.content?.timeOfUseInfoDialogUspList?.value?.enum?.map(({ label, name }) => (
                <Checklist.Item alignY="start" key={name} gap={2}>
                  {label}
                </Checklist.Item>
              ))}
            </Checklist>
          </Ribbon>
        ) : null
      }>
      <ExpandableOfferSection
        isOpen={isOpen}
        setOpen={setOpen}
        offer={offer}
        textualData={textualData}
        flowContext={flowContext}
        setCostPeriod={setCostPeriod}
      />
    </ProductCard>
  );
};

export const EnergyFlowOfferStep: FC = () => {
  const { businessUnit, label } = useDC();
  const { useFlowSelector, useFlowActorRef } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const sendToFlowMachine = useFlowActorRef().send;
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const [productFilter, setProductFilter] = useState('');
  const [showCard, setShowCard] = useState(false);
  const cardRef = useRef<HTMLDivElement | null>(null);
  const {
    context: flowContext,
    context: {
      discountCode,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      hasDoubleMeter,
      hasSolarPanels,
      hasUpsellGas,
      hasCrossSellRentalDevice,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      energyType,
      isSME,
      isUsageDetailsEditProhibited,
    },
  } = flowState;

  const { offerState, sendToOfferMachine } = useOfferMachine(businessUnit, label);

  const { context: { offer } = {} } = offerState;

  const { OfferStep: { fields: textualData } = {} } = usePlaceholderContent<{ OfferStep: OfferStepRendering }>();

  const { filterOffers } = useGetOffersByExperiment({
    experimentKey: touExperimentFeatureToggleKey,
    filterPredicate: touExperimentProductFilter,
  });

  const offers = filterOffers({ ...flowContext, hasCrossSellRentalDevice: false }, offer);

  const showProductToggle =
    checkOffersIncludesDynamicPricingProduct(offers) && checkOffersIncludesNonDynamicPricingProduct(offers);

  const availableOffers = useMemo(() => {
    if (!showProductToggle) {
      return offers;
    }
    if (!productFilter) {
      return [];
    }

    if (productFilter) {
      return filterOffersByType(offers, productFilter);
    }
    return offers.filter(offer => offer.discountType?.includes(productFilter));
  }, [offers, productFilter, showProductToggle]);

  const toggleCard = () => {
    setShowCard(true);
  };

  useEffect(() => {
    if (showCard && cardRef.current) {
      const card = cardRef.current;
      card.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
      setShowCard(false);
    }
  }, [showCard]);

  const formSchema = yup.object({
    discountCode: yup.string().required(),
  });

  const {
    handleSubmit,
    control,
    getValues,
    setValue,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
    defaultValues: {
      // For this step we won't be using the "defaultValue" straight away since the earlier
      // selected "discountCode" might no longer be available due to flow context changes
    },
  });

  const { trackViewItem, trackSelectItem, trackAddToCart } = useFlowTracking();

  const { format, address } = useFormatter();

  const [isLoading, setIsLoading] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(true);
  const [isFailed, setIsFailed] = useState(false);

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);
  const [isNotAuthenticated, setIsNotAuthenticated] = useState(false);

  const [isInitialOfferSelection, setInitialOfferSelection] = useState(!discountCode);
  const [isPriceDetailsOpen, setIsPriceDetailsOpen] = useState(false);

  const isLargeBreakpoint = useMediaQuery('lg');
  const isMediumBreakpoint = useMediaQuery('md');
  const isExtraLargeBreakpoint = useMediaQuery('xl');

  const [shouldShowMoreInfoDialog, setShouldShowMoreInfoDialog] = useState(false);
  const shouldShowCarousel = !(
    isLargeBreakpoint ||
    (!isLargeBreakpoint && availableOffers.length < 2) ||
    (isMediumBreakpoint && availableOffers.length < 3) ||
    availableOffers.length === 1
  );
  const shouldShowCarouselIndicators = shouldShowCarousel && (!isExtraLargeBreakpoint || availableOffers.length > 3);
  const shouldAlignCrouselItems = shouldShowCarousel && isExtraLargeBreakpoint && availableOffers.length === 3;

  const selectedDiscountCode = watch('discountCode');
  const selectedDiscountCodeIndex = Math.max(
    availableOffers?.findIndex(offer => offer.type === selectedDiscountCode) ?? 0,
    0,
  );

  useEffect(() => {
    if (!availableOffers.length || availableOffers?.some(offer => offer.type === selectedDiscountCode)) return;
    setValue('discountCode', selectDefaultDiscountCode(availableOffers));
  }, [availableOffers, selectedDiscountCode, setValue]);

  useEffect(() => {
    sendToOfferMachine({
      type: 'GET_OFFER',
      flowContext: { ...flowContext },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps -- lifecycle componentDidMount
  }, []);

  useEffect(() => {
    if (selectedDiscountCode) {
      if (isInitialOfferSelection) setInitialOfferSelection(false);

      const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);
      if (selectedOffer) trackSelectItem(selectedOffer, offer, isInitialOfferSelection);
    }
  }, [selectedDiscountCode]);

  useEffect(() => {
    if (!isSubmitSuccessful) return;

    const isRentalDeviceCrossSellProductAvailable = getCrossSellRentalDeviceProductAvailability(offer);

    const isGasUpsellProductAvailable = getUpsellProductAvailability(
      'gas',
      { ...flowContext, ...{ discountCode: selectedDiscountCode } },
      offer,
    );

    const selectedOffer = getOffer({ ...flowContext, ...{ discountCode: selectedDiscountCode } }, offer);

    if (selectedOffer) trackAddToCart(selectedOffer, offer);
    if (selectedOffer?.nbaContext?.serviceLocationId && selectedOffer?.nbaContext?.contextId) {
      const { serviceLocationId, contextId, actionId, variationId, servingPointId } = selectedOffer.nbaContext;

      handleNbaFeedback(
        businessUnit,
        label,
        'Success',
        serviceLocationId,
        contextId,
        actionId,
        variationId,
        servingPointId,
      );
    }

    updateContextAndGoToNextStep({
      ...getValues(),
      isRentalDeviceCrossSellProductAvailable,
      isGasUpsellProductAvailable,
      hasCrossSellRentalDevice: hasCrossSellRentalDevice && isRentalDeviceCrossSellProductAvailable,
      hasUpsellGas: hasUpsellGas && isGasUpsellProductAvailable,
    });
  }, [isSubmitSuccessful]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('FETCHING'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
      setIsNotAuthenticated(offerState.matches('ERROR_NOT_AUTHENTICATED'));
    }

    if (offerState.matches('SUCCESS')) {
      const selectedOfferType = checkAvailabilityOfferType(offers, discountCode);

      if (selectedOfferType) {
        const selectedOffer = offers.find(offer => offer.type === selectedOfferType);
        const isVariablePricingProduct = selectedOffer?.discountType === OfferType.Variable;
        const isDynamicPricingProduct = checkOfferIncludesDynamicPricingProduct(selectedOffer);
        setValue('discountCode', selectedOfferType);
        setProductFilter(isVariablePricingProduct ? 'variable' : isDynamicPricingProduct ? 'dynamic' : 'fixed');
      }

      setShouldShowMoreInfoDialog(
        !!(
          checkOffersIncludesDynamicPricingProduct(offers) && textualData?.content?.moreInfoDialog?.value?.triggerText
        ),
      );

      offers.forEach(availableOffer => {
        trackViewItem(availableOffer, offer);
        if (availableOffer.nbaContext?.servingPointId) {
          handleNbaFeedback(
            businessUnit,
            label,
            'Served',
            availableOffer.nbaContext.serviceLocationId ?? '',
            availableOffer.nbaContext.contextId ?? '',
            availableOffer.nbaContext.actionId,
            availableOffer.nbaContext.variationId,
            availableOffer.nbaContext.servingPointId ?? '',
          );
          handleNbaFeedback(
            businessUnit,
            label,
            'Viewed',
            availableOffer.nbaContext.serviceLocationId ?? '',
            availableOffer.nbaContext.contextId ?? '',
            availableOffer.nbaContext.actionId,
            availableOffer.nbaContext.variationId,
            availableOffer.nbaContext.servingPointId ?? '',
          );
        }
      });
    }
  }, [offerState]);

  const handleValueChange = (value: string) => {
    setProductFilter(value);
    toggleCard();
  };

  const filteredOptions = checkOfferTypes(textualData?.productData?.contractTypeRadio?.value?.options ?? [], offers);
  const hasSubtitleLayout = filteredOptions[0]?.description;

  const gridLayout = hasSubtitleLayout ? '1fr' : { initial: '1fr', md: '1fr 1fr' };
  const buttonLayout = hasSubtitleLayout
    ? { md: `repeat(${filteredOptions.length}, 1fr)` }
    : filteredOptions.length > 2
      ? '1fr'
      : '1fr 1fr';

  return (
    <Layout.Content variant="C" isLoading={isLoading} handleSubmit={handleSubmit}>
      {isSuccessful && (
        <>
          <Header>
            <Header.Title>{textualData?.content?.title?.value}</Header.Title>
            <Header.Text>{textualData?.content?.text?.value}</Header.Text>
          </Header>

          {textualData?.content?.notification?.value?.content && (
            <NotificationBox
              isAlert={false}
              variant={textualData?.content?.notification?.value?.variant}
              title={textualData?.content?.notification?.value?.title}
              text={<RichText html={textualData?.content?.notification?.value?.content} />}
            />
          )}

          {shouldShowMoreInfoDialog && (
            <TrackedDialog
              title={textualData?.content?.moreInfoDialog?.value?.title}
              trigger={<TextLink emphasis="high">{textualData?.content?.moreInfoDialog?.value?.triggerText}</TextLink>}>
              <RichText html={textualData?.content?.moreInfoDialog?.value?.content} />
            </TrackedDialog>
          )}

          {showProductToggle && (
            <Box paddingBottom="5">
              {textualData?.content?.contractTypeTitle?.value && (
                <Box paddingY="3">
                  <Text weight="bold">{textualData?.content.contractTypeTitle.value}</Text>
                </Box>
              )}
              <Grid gridTemplateColumns={gridLayout} gap="6">
                <VisuallyHidden id="productFilter">
                  {textualData?.productData?.contractTypeRadio?.value?.label}
                </VisuallyHidden>
                <RadioGroup
                  aria-labelledby="productFilter"
                  direction={hasSubtitleLayout ? 'column' : filteredOptions.length > 2 ? 'column' : 'row'}
                  name="Productfilter"
                  value={productFilter}
                  onValueChange={e => handleValueChange(e)}
                  wrap={!hasSubtitleLayout}>
                  <Grid gridTemplateColumns={buttonLayout} gap="3">
                    {filteredOptions?.map(option => (
                      <RadioTile key={option.name} value={option.name} subtitle={option?.description}>
                        {option.label}
                      </RadioTile>
                    ))}
                  </Grid>
                </RadioGroup>
              </Grid>
            </Box>
          )}

          <OverviewConsumption
            layout="B"
            textualData={{
              description: format(textualData?.footer?.footerDescription?.value ?? '', {
                address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
              }),
              redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
              electricityLabel: textualData?.footer?.electricityLabel?.value,
              electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
              electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
              gasLabel: textualData?.footer?.gasLabel?.value,
              warmthLabel: textualData?.footer?.warmthLabel?.value,
              waterLabel: textualData?.footer?.waterLabel?.value,
            }}
            data={{
              hasDoubleMeter,
              hasSolarPanels,
              usageElectricityHigh,
              usageElectricityLow,
              usageGas,
              usageWarmth,
              usageWater,
              solarPanelsOutput,
            }}
            editButton={
              isUsageDetailsEditProhibited
                ? undefined
                : {
                    label: textualData?.footer?.modifyTriggerText?.value ?? '',
                    onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_USAGE' }),
                  }
            }
          />

          {availableOffers.length > 0 ? (
            <>
              <Controller
                control={control}
                name="discountCode"
                render={({ field: { onChange, value, name } }) => (
                  <RadioGroup
                    aria-labelledby="discountCode"
                    name={name}
                    value={value}
                    direction={availableOffers.length >= 2 ? { initial: 'column', md: 'row' } : 'row'}
                    alignY="start"
                    wrap={false}
                    onValueChange={onChange}
                    error={errors['discountCode']?.message}>
                    {shouldShowCarousel ? (
                      <Bleed
                        horizontal={!isLargeBreakpoint ? { initial: 6, md: 10 } : undefined}
                        right={shouldAlignCrouselItems ? 24 : !isLargeBreakpoint ? 6 : 0}>
                        <Carousel
                          key={value}
                          showIndicator={shouldShowCarouselIndicators}
                          scrollBehavior="snap"
                          scrollMarginX="gridGutter"
                          hasCardPadding
                          layout="default"
                          startAt={selectedDiscountCodeIndex}
                          scrollTo={selectedDiscountCodeIndex}>
                          {availableOffers.map(offer => (
                            <Carousel.Item key={offer.type}>
                              <Box ref={cardRef}>
                                <OfferCard
                                  key={offer.type}
                                  isOpen={isPriceDetailsOpen}
                                  setOpen={setIsPriceDetailsOpen}
                                  offer={offer}
                                  textualData={textualData}
                                  flowContext={flowContext}
                                  isSME={isSME}
                                  energyType={energyType}
                                />
                              </Box>
                            </Carousel.Item>
                          ))}
                        </Carousel>
                      </Bleed>
                    ) : availableOffers.length === 1 && !isMediumBreakpoint ? (
                      availableOffers.map(offer => (
                        <OfferCard
                          key={offer.type}
                          isOpen={isPriceDetailsOpen}
                          setOpen={setIsPriceDetailsOpen}
                          offer={offer}
                          textualData={textualData}
                          flowContext={flowContext}
                          isSME={isSME}
                          energyType={energyType}
                        />
                      ))
                    ) : (
                      <Grid gridTemplateColumns="1fr 1fr 1fr" gap="6">
                        {availableOffers.map(offer => (
                          <OfferCard
                            key={offer.type}
                            isOpen={isPriceDetailsOpen}
                            setOpen={setIsPriceDetailsOpen}
                            offer={offer}
                            textualData={textualData}
                            flowContext={flowContext}
                            isSME={isSME}
                            energyType={energyType}
                          />
                        ))}
                      </Grid>
                    )}
                  </RadioGroup>
                )}
              />
              <NextButton isLoading={isSubmitSuccessful} alignX={{ initial: 'justify', md: 'center' }}>
                {textualData?.content?.nextStepText?.value}
              </NextButton>
              {isLargeBreakpoint && (
                <ChatActivator
                  text="Vraag over energiecontracten?"
                  topic="start_salesflow_looptijd"
                  trigger="De chatbot helpt je graag."
                />
              )}
            </>
          ) : null}
        </>
      )}

      {isFailed && (
        <Error
          type={isNotAuthenticated ? 'AUTHENTIFICATION' : 'GENERIC'}
          alignY="center"
          textualData={{
            notification: isNotAuthenticated
              ? textualData?.genericError?.errorNotAuthenticatedNotification
              : isNotAvailable
                ? textualData?.genericError?.errorNotAvailableNotification
                : isNotFound
                  ? textualData?.genericError?.errorNotFoundNotification
                  : textualData?.genericError?.errorNotification,
            buttonLabel: isNotAuthenticated
              ? textualData?.genericError?.loginButtonText
              : !isNotAvailable && !isNotFound
                ? textualData?.genericError?.tryAgainButtonText
                : undefined,
          }}
        />
      )}
    </Layout.Content>
  );
};
