import { shouldSkipWhenUserIsLoggedIn } from '@eneco/flows2/skippingRules';
import { AccountSelectStep } from '@eneco/flows2/src/components';
import { FlowRoute, FlowRouteCategory, FlowActions } from '@eneco/flows2/types';

import { FlowContextThermostat } from './context';
import { LogInStep } from './steps/LogInStep/LogInStep';
import { OfferStep } from './steps/OfferStep/OfferStep';
import { OverviewStep } from './steps/OverviewStep/OverviewStep';
import { SuitabilityStep } from './steps/SuitabilityStep/SuitabilityStep';

export const initialContext: FlowContextThermostat = {};

export const routes: FlowRoute<FlowContextThermostat>[] = [
  {
    id: 'SUITABILITY',
    path: '/geschiktheid/',
    category: FlowRouteCategory.Suitability,
    component: SuitabilityStep,
  },
  {
    id: 'LOGIN',
    path: '/inloggen/',
    category: FlowRouteCategory.Login,
    component: LogInStep,
    skippingRules: [shouldSkipWhenUserIsLoggedIn],
  },
  {
    id: 'ACCOUNTSELECT',
    path: '/account/',
    category: FlowRouteCategory.Login,
    component: AccountSelectStep,
    skippingRules: [shouldSkipWhenUserIsLoggedIn],
  },
  {
    id: 'OFFER',
    path: '/aanbod/',
    category: FlowRouteCategory.Offer,
    component: OfferStep,
  },
  {
    id: 'OVERVIEW',
    path: '/controle/',
    category: FlowRouteCategory.Summary,
    component: OverviewStep,
  },
];

export const actions: FlowActions<FlowContextThermostat> = {};
