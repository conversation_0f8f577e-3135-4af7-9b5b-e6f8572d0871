import { Layout } from '@eneco/flows2';
import { createFlowProvider } from '@eneco/flows2/utils';

import { FlowContextThermostat } from './context';
import { routes, actions, initialContext } from './ThermostatFlow.config';

const { Provider, Navigation, Router } = createFlowProvider<FlowContextThermostat>({
  routes,
  actions,
  initialContext,
});

const Flow = () => {
  return (
    <Provider>
      <Layout>
        <Layout.Header>
          <Navigation sitecoreComponentName="ThermostatFlowNavigation" />
        </Layout.Header>

        <Router />
      </Layout>
    </Provider>
  );
};

export default Flow;
