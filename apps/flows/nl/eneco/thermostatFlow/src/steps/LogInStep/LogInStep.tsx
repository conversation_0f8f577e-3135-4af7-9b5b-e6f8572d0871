import { FC, useEffect } from 'react';

import { signIn } from 'next-auth/react';

import RichText from '@components/RichText/RichText';
import useDC from '@dc/useDC';
import { useSession } from '@dxp-auth';
import { Header, Layout } from '@eneco/flows2';
import { mapImage, usePlaceholderContent } from '@sitecore/common';
import type { LogInStepRendering } from '@sitecore/types/LogInStep';
import { Button, Image, Stack } from '@sparky';
import { ProfileIcon } from '@sparky/icons';

import { useFlowNavigation } from '../../util/updateContextAndGoToNextStep';
import { useAuthenticated } from '../../util/useAuthenticated';

export const LogInStep: FC = () => {
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const { status } = useSession() ?? {};
  const { accountId } = useDC();
  const { isAuthenticated } = useAuthenticated();

  const { LogInStep: { fields: textualData } = {} } = usePlaceholderContent<{
    LogInStep: LogInStepRendering;
  }>();

  useEffect(() => {
    if (isAuthenticated && accountId) {
      updateContextAndGoToNextStep({
        accountId,
      });
    }
  }, [accountId, isAuthenticated]);

  const imageProps = mapImage(textualData?.content?.image);

  function handleLoginClick() {
    signIn('okta');
  }

  return (
    <Layout.Content variant="A" isLoading={status === 'loading'}>
      <Header>
        <Header.Title>{textualData?.content?.title?.value}</Header.Title>
        {textualData?.content?.content?.value && (
          <Header.Text as="div">
            <RichText html={textualData.content.content.value} />
          </Header.Text>
        )}
      </Header>
      {imageProps.src ? (
        <Image src={imageProps.src} alt={imageProps?.alt} aspectRatio="16/9" height="auto" objectFit="cover" />
      ) : null}
      <Stack direction={{ initial: 'column', md: 'row' }}>
        <Button onClick={handleLoginClick}>
          <ProfileIcon /> {textualData?.content?.logInButtonText?.value}
        </Button>
      </Stack>
    </Layout.Content>
  );
};
