import { FC, useEffect } from 'react';

import { useForm } from 'react-hook-form';

import { Header, Layout, Price, NextButton } from '@eneco/flows2';
import { mapImage, usePlaceholderContent } from '@sitecore/common';
import { ThermostatFlowOfferStepRendering } from '@sitecore/types/ThermostatFlowOfferStep';
import { Card, Divider, Grid, Image, Stack, Text, Box, NotificationBox } from '@sparky';

import { useFlowNavigation } from '../../util/updateContextAndGoToNextStep';
import { useAuthenticated } from '../../util/useAuthenticated';
import { useThermostatOffer } from '../../util/useThermostatOffer';

export const OfferStep: FC = () => {
  const { ThermostatFlowOfferStep: { fields: { content, productDetails } = {} } = {} } = usePlaceholderContent<{
    ThermostatFlowOfferStep: ThermostatFlowOfferStepRendering;
  }>();
  const { isAuthenticated } = useAuthenticated();
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const { offer, isLoading, isError } = useThermostatOffer();
  const productImageProps = mapImage(productDetails?.image);

  const {
    handleSubmit,
    formState: { isSubmitSuccessful },
  } = useForm({
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (isSubmitSuccessful) {
      updateContextAndGoToNextStep({
        offerId: offer?.offerId,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitSuccessful]);

  return (
    <>
      {isAuthenticated && (
        <Layout.Content variant="B" isLoading={isLoading} handleSubmit={handleSubmit}>
          <Header>
            <Header.Title>{content?.title?.value}</Header.Title>
            {content?.text?.value && <Header.Text>{content.text.value}</Header.Text>}
          </Header>

          {isError ? (
            <NotificationBox
              isAlert
              title={content?.errorNotification.value?.title}
              text={content?.errorNotification.value?.content}
              variant={content?.errorNotification.value?.variant}
            />
          ) : (
            <>
              <Card>
                <Box padding="6">
                  <Stack gap="6">
                    {productImageProps.src ? (
                      <Image src={productImageProps.src} alt={productImageProps.alt} width="100%" />
                    ) : null}

                    {productDetails?.text?.value && <Text>{productDetails.text.value}</Text>}
                    <Grid columns="2" rowGap="2">
                      <Text size="BodyM" weight="bold">
                        {productDetails?.title?.value}
                      </Text>
                      <Stack>
                        <Stack.Item shrink={false}>
                          <Price suffix="">{offer?.oneOffCost}</Price>
                        </Stack.Item>
                        <Text size="BodyM" align="right" color="textLowEmphasis">
                          {productDetails?.perMonthLabel?.value}
                        </Text>
                      </Stack>
                      <Grid.Item gridColumn="1/-1">
                        <Divider />
                      </Grid.Item>
                      <Text size="BodyM" weight="bold">
                        {productDetails?.discountLabel?.value}
                      </Text>
                      <Stack>
                        <Stack.Item shrink={false}>
                          <Price suffix="">{offer?.discountValue}</Price>
                        </Stack.Item>
                        <Text size="BodyM" align="right" color="textLowEmphasis">
                          {productDetails?.perMonthLabel?.value}
                        </Text>
                      </Stack>
                      <Grid.Item gridColumn="1/-1">
                        <Divider />
                      </Grid.Item>

                      <Text size="BodyM" weight="bold">
                        {productDetails?.fixedCostsLabel?.value}
                      </Text>
                      <Stack>
                        <Stack.Item shrink={false}>
                          <Price suffix="">{offer?.monthlyCost}</Price>
                        </Stack.Item>
                        <Text size="BodyM" align="right" color="textLowEmphasis">
                          {productDetails?.firstYearFreeLabel?.value}
                        </Text>
                      </Stack>
                    </Grid>
                  </Stack>
                </Box>
              </Card>
              <Stack direction={{ initial: 'column', md: 'row' }}>
                <NextButton>{content?.nextStepText?.value}</NextButton>
              </Stack>
            </>
          )}
        </Layout.Content>
      )}
    </>
  );
};
