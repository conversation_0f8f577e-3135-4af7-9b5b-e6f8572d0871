import { FC } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { Checklist } from '@components/ui/Checklist/Checklist';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { usePlaceholderContent } from '@sitecore/common';
import { OptionsStepRendering } from '@sitecore/types/manual/thermostatFlow/optionsStep';
import { Heading, Stack, Box, RadioCard, RadioGroup, Stretch } from '@sparky';

interface FormFields {
  thermostatType: string;
}

export const OptionsStep: FC = () => {
  const { ThermostatFlowOptionsStep: { fields: textualData } = {} } = usePlaceholderContent<{
    ThermostatFlowOptionsStep: OptionsStepRendering;
  }>();

  const { fields } = textualData?.items[0] ?? {};
  const resultCardFields = fields?.items.map(item => item.fields);

  const formSchema = yup.object({
    thermostatType: yup.string(),
  });

  const {
    control,
    formState: { isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onChange',
    resolver: yupResolver(formSchema),
  });

  return (
    <Layout.Content variant="B">
      <Header>
        <Header.Title>{textualData?.title?.value}</Header.Title>

        <Header.Text as="div">
          <RichText html={textualData?.description?.value} />
        </Header.Text>
      </Header>

      {resultCardFields?.map(item => (
        <Controller
          control={control}
          defaultValue="wired"
          name="thermostatType"
          key={item.name.value}
          render={({ field: { onChange, value, name } }) => (
            <RadioGroup
              aria-labelledby="thermostatType"
              name={name}
              value={value}
              direction={{ initial: 'column', lg: 'row' }}
              wrap={false}
              alignY="center"
              onValueChange={onChange}>
              <Stack.Item key={`${item.name.value}`}>
                <Stretch>
                  <RadioCard value={item.name.value} ariaLabelledby={`${item.name.value}-label`}>
                    <Stretch>
                      <Stack direction="column" gap="0">
                        <Stack.Item grow={true}>
                          <Box padding="6">
                            <Stack gap="2">
                              <Stack direction="row" alignX="justify" gap="2">
                                <Stack direction="row" gap="2" alignY="start">
                                  <Stack.Item shrink={false}>
                                    <Box paddingTop="1">
                                      <RadioCard.Indicator />
                                    </Box>
                                  </Stack.Item>
                                  <Stack.Item shrink={false}>
                                    <Heading as="h3" size="S">
                                      {item?.cardTitle?.value}
                                    </Heading>
                                  </Stack.Item>
                                </Stack>
                              </Stack>
                              {item.usps?.value && (
                                <Checklist>
                                  {item.usps?.value?.enum?.map(
                                    usp =>
                                      usp.label && (
                                        <Checklist.Item alignY="start" key={usp.name}>
                                          {usp.label}
                                        </Checklist.Item>
                                      ),
                                  )}
                                </Checklist>
                              )}
                            </Stack>
                          </Box>
                        </Stack.Item>
                      </Stack>
                    </Stretch>
                  </RadioCard>
                </Stretch>
              </Stack.Item>
            </RadioGroup>
          )}
        />
      ))}

      <Stack direction={{ initial: 'column', md: 'row' }}>
        <NextButton isLoading={isSubmitSuccessful}>{textualData?.nextStepText?.value}</NextButton>
      </Stack>
    </Layout.Content>
  );
};
