import { FC } from 'react';

import { useForm } from 'react-hook-form';

import { spaceTextByUpperCase } from '@common/string';
import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { Header, Layout, Overview, NextButton } from '@eneco/flows2';
import { useCustomerProfileAccount } from '@hooks/profile';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { ThermostatFlowOverviewStepRendering } from '@sitecore/types/ThermostatFlowOverviewStep';
import { Box, NavLink, NotificationBox, Stack, Text, TextLink } from '@sparky';

import { useAuthenticated } from '../../util/useAuthenticated';

export const OverviewStep: FC = () => {
  const {
    ThermostatFlowOverviewStep: { fields: { content, notifications, productDetails, personalDetails } = {} } = {},
  } = usePlaceholderContent<{
    ThermostatFlowOverviewStep: ThermostatFlowOverviewStepRendering;
  }>();
  const { isAuthenticated } = useAuthenticated();
  const { currentAccount, data: profile, isLoading: isProfileLoading } = useCustomerProfileAccount();
  const { address } = useFormatter();

  const isOfferError = null;
  const isOrderError = null;

  const offer = {
    oneOffCost: 1.9,
    product: {
      contractStartDate: '2023-01-01T00:00:00Z',
    },
    termsAndConditions: [],
  };

  const {
    handleSubmit,
    formState: { isSubmitSuccessful },
  } = useForm({
    mode: 'onSubmit',
  });

  return (
    <>
      {isAuthenticated && (
        <Layout.Content variant="B" isLoading={isProfileLoading} handleSubmit={handleSubmit}>
          <Header>
            <Header.Title>{content?.title?.value}</Header.Title>
            {content?.text?.value && <Header.Text>{content.text.value}</Header.Text>}
          </Header>
          <Overview>
            <Overview.Header>{personalDetails?.title?.value}</Overview.Header>

            <Overview.Details type="profile" title={personalDetails?.nameLabel?.value ?? ''}>
              <Text as="p">{profile.contact?.name}</Text>
            </Overview.Details>
            <Overview.Details type="address" title={personalDetails?.addressLabel?.value ?? ''}>
              <Text as="p">{address.streetAddress(currentAccount?.address)}</Text>
              <Text as="p">{address.postalCodeCity(currentAccount?.address)}</Text>
            </Overview.Details>
            <Overview.Details type="contact" title={personalDetails?.contactLabel?.value ?? ''}>
              <Text as="p">
                {personalDetails?.emailAddressLabel?.value}: {profile.contact?.emailAddress}
              </Text>
              {profile.contact?.phoneNumber ? (
                <Text as="p">
                  {personalDetails?.phoneNumberLabel?.value}: {profile.contact?.phoneNumber}
                </Text>
              ) : null}
              {profile.contact?.mobilePhoneNumber ? (
                <Text as="p">
                  {personalDetails?.mobilePhoneNumberLabel?.value}: {profile.contact?.mobilePhoneNumber}
                </Text>
              ) : null}
            </Overview.Details>
          </Overview>

          {isOfferError ? (
            <NotificationBox
              isAlert
              title={notifications?.offerErrorNotification?.value?.title}
              text={notifications?.offerErrorNotification?.value?.content}
              variant={notifications?.offerErrorNotification?.value?.variant}
            />
          ) : (
            <>
              <Overview>
                <Overview.Header>{productDetails?.title?.value}</Overview.Header>

                <Overview.Product
                  title={productDetails?.oneOffCostsLabel?.value ?? ''}
                  tag={productDetails?.thermostatInfoLabel?.value ?? ''}
                  type="other"
                  hideIcon
                  costsMonthly={{ value: offer.oneOffCost ?? NaN, period: '' }}
                />
                <Overview.Product
                  title={productDetails?.discountLabel?.value ?? ''}
                  tag={productDetails?.discountInfoLabel?.value}
                  type="other"
                  hideIcon
                  costsMonthly={{ value: offer.oneOffCost ?? NaN, period: '' }}
                />

                <Overview.Product
                  title={productDetails?.fixedCostsLabel?.value ?? ''}
                  subtitle={productDetails?.fixedCostsLabel?.value}
                  tag={productDetails?.firstYearFreeLabel?.value}
                  type="other"
                  hideIcon
                  hideDivider
                  costsMonthly={{ value: 2, period: productDetails?.perMonthLabel?.value }}
                />
              </Overview>

              <Box>
                <RichText html={productDetails?.conditionContent?.value?.split('{conditions}')[0]} />
                <TrackedDialog
                  title={productDetails?.salesConditionsDialog?.value?.title}
                  description={<RichText html={productDetails?.salesConditionsDialog?.value?.content} />}
                  trigger={<TextLink>{productDetails?.salesConditionsDialog?.value?.triggerText}</TextLink>}>
                  <Stack direction="column" gap="2">
                    {offer.termsAndConditions?.map(condition => {
                      const { url, text } = condition ?? {};
                      if (!url || !text) return null;
                      return (
                        <NavLink key={url} href={url ?? ''} target="_blank">
                          {spaceTextByUpperCase(text)}
                        </NavLink>
                      );
                    })}
                  </Stack>
                </TrackedDialog>
                <RichText html={productDetails?.conditionContent?.value?.split('{conditions}')[1]} />
              </Box>

              {isOrderError ? (
                <NotificationBox
                  isAlert
                  title={notifications?.orderErrorNotification?.value?.title}
                  text={notifications?.orderErrorNotification?.value?.content}
                  variant={notifications?.orderErrorNotification?.value?.variant}
                />
              ) : null}
              <Stack direction={{ initial: 'column', md: 'row' }}>
                <NextButton isLoading={isSubmitSuccessful}>{content?.nextStepText?.value}</NextButton>
              </Stack>
            </>
          )}
        </Layout.Content>
      )}
    </>
  );
};
