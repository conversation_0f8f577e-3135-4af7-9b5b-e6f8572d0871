import { FC, PropsWithChildren } from 'react';

import { screen, render } from '@testing-library/react';

import { createFlowProvider } from '@eneco/flows2/utils';
import { TestAppProviders } from '@jest-tools/TestAppProviders';
import mockupData from 'mocks/sitecore/apps/flows/thermostatFlow/suitabilityStep';

import { SuitabilityStep as FlowStep } from './SuitabilityStep';
import { FlowContextThermostat } from '../../context';
import { routes, actions, initialContext } from '../../ThermostatFlow.config';

const { Provider } = createFlowProvider<FlowContextThermostat>({
  routes,
  actions,
  initialContext,
});

const TestComponent: FC<PropsWithChildren<{ rendering: unknown; initialContext: FlowContextThermostat }>> = ({
  rendering,
}) => {
  return (
    <TestAppProviders rendering={rendering}>
      <Provider>
        <FlowStep />
      </Provider>
    </TestAppProviders>
  );
};

describe('SuitabilityStep', () => {
  it('should render flow step correctly', async () => {
    render(<TestComponent rendering={mockupData} initialContext={initialContext} />);
    expect(await screen.findByRole('heading', { name: /Wat geldt voor jou?/i })).toBeVisible();
    expect(await screen.findByText(/Leuk dat je een Slimme thermostaat wilt aanschaffen/i)).toBeVisible();
  });
});
