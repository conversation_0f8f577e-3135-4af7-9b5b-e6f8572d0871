import { FC, useEffect } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { Header, Layout, NextButton } from '@eneco/flows2';
import { useCustomerProfileAccount } from '@hooks/profile';
import { useFormatter } from '@i18n';
import { getIllustration, usePlaceholderContent } from '@sitecore/common';
import { ThermostatFlowSuitabilityStepRendering } from '@sitecore/types/ThermostatFlowSuitabilityStep';
import { Stack, CheckTile, NotificationBox } from '@sparky';

import { Unsuitable } from './Unsuitable';
import { useFlowNavigation } from '../../util/updateContextAndGoToNextStep';
import { useSuitabilityConfig } from '../../util/useSuitabilityConfig';

export interface FormFields {
  hasWifiAccess: boolean;
  [key: string]: boolean;
}

export const SuitabilityStep: FC = () => {
  const { updateContextAndGoToNextStep } = useFlowNavigation();

  const { ThermostatFlowSuitabilityStep: { fields: textualData } = {} } = usePlaceholderContent<{
    ThermostatFlowSuitabilityStep: ThermostatFlowSuitabilityStepRendering;
  }>();

  const { address } = useFormatter();
  const { currentAccount, isLoading } = useCustomerProfileAccount();

  const { cachedInput, formSchema, itemsToRender } = useSuitabilityConfig();
  const addressLine = address.medium(currentAccount?.address);

  const {
    handleSubmit,
    register,
    getValues,
    formState: { errors, isValid, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: { hasWifiAccess: true, ...cachedInput },
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    if (isSubmitSuccessful) {
      const formData = getValues();

      updateContextAndGoToNextStep({
        suitabilityChecks: formData,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitSuccessful, getValues]);

  const { nextStepText, suitableNotification, text, title } = textualData?.content ?? {};

  return (
    <Layout.Content variant="B" isLoading={isLoading} handleSubmit={handleSubmit}>
      <Header>
        <Header.Title>{title?.value}</Header.Title>
        {text?.value && (
          <Header.Text as="div">
            <RichText html={text.value?.replace('{address}', addressLine)} />
          </Header.Text>
        )}
      </Header>

      {itemsToRender.map(
        ({
          fields: {
            checkbox: { name, label, illustration },
          },
        }) => {
          const Illustration = illustration.value ? getIllustration(illustration.value) : undefined;
          return (
            <CheckTile
              icon={Illustration && <Illustration size="small" color="currentColor" />}
              key={name.value}
              error={errors[name.value]?.message}
              {...register(name.value)}>
              {label.value}
            </CheckTile>
          );
        },
      )}

      {isValid ? (
        <>
          <NotificationBox
            isAlert
            variant={suitableNotification?.value.variant}
            title={suitableNotification?.value.title}
            text={suitableNotification?.value.content}
          />
          <Stack direction={{ initial: 'column', md: 'row' }}>
            <NextButton isLoading={isSubmitSuccessful}>{nextStepText?.value}</NextButton>
          </Stack>
        </>
      ) : (
        <Unsuitable getValues={getValues} errors={errors} />
      )}
    </Layout.Content>
  );
};
