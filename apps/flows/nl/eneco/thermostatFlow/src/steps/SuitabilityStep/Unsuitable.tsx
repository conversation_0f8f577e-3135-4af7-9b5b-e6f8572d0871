import { FC, useRef, BaseSyntheticEvent } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FieldErrors, UseFormGetValues, useForm, SubmitHandler } from 'react-hook-form';
import * as yup from 'yup';

import { Recaptcha, RecaptchaRef } from '@components/Recaptcha/Recaptcha';
import RichText from '@components/RichText/RichText';
import { usePlaceholderContent } from '@sitecore/common';
import { ThermostatFlowSuitabilityStepRendering } from '@sitecore/types/ThermostatFlowSuitabilityStep';
import { Stack, NotificationBox, Card, Text, BulletList, Button } from '@sparky';

import { FormFields } from './SuitabilityStep';
import { useSignup } from '../../util/useSignup';

type Props = {
  errors: FieldErrors<FormFields>;
  getValues: UseFormGetValues<FormFields>;
};
interface Form {
  recaptchaToken?: string;
}

export const Unsuitable: FC<Props> = ({ errors, getValues }) => {
  const { ThermostatFlowSuitabilityStep: { fields: textualData } = {} } = usePlaceholderContent<{
    ThermostatFlowSuitabilityStep: ThermostatFlowSuitabilityStepRendering;
  }>();

  const {
    unsuitableNotification,
    multipleReasonsText,
    signupContent,
    emailSignupButtonText,
    emailDestinationCodeText,
    emailErrorNotification,
    emailSuccessNotification,
  } = textualData?.unsuitable ?? {};
  const { signupUser, isSuccess, isError, emailAddress, isLoading } = useSignup(emailDestinationCodeText?.value ?? '');

  const hasSingleError = Object.keys(errors).length === 1;
  const statusNotification = isSuccess ? emailSuccessNotification : isError ? emailErrorNotification : undefined;
  const recaptchaRef = useRef<RecaptchaRef>(null);

  const unsuitableReasons = `unsuitable values: ${Object.keys(errors)
    .map(key => `${key}: ${getValues(key)}`)
    .join(', ')}`;

  const formSchema = yup.object({
    recaptchaToken: yup.string().required(),
  });

  const { handleSubmit, setValue, formState } = useForm<Form>({
    mode: 'onSubmit',
    resolver: yupResolver(formSchema),
  });

  const submitForm: SubmitHandler<Form> = formValues => {
    if (formValues.recaptchaToken) {
      signupUser(unsuitableReasons, formValues.recaptchaToken);
    }
  };

  const onSubmit = async (event?: BaseSyntheticEvent) => {
    event?.preventDefault();
    setValue('recaptchaToken', await recaptchaRef.current?.verify());
    return handleSubmit(submitForm)();
  };

  return (
    <Card>
      <Card.Content>
        <Stack gap="6">
          <NotificationBox
            isAlert
            variant={unsuitableNotification?.value?.variant}
            title={unsuitableNotification?.value?.title}
            text={unsuitableNotification?.value?.content}
          />
          {hasSingleError ? (
            <Text as="p">{Object.values(errors)[0]?.message as string}</Text>
          ) : (
            <>
              <Text as="p">{multipleReasonsText?.value}</Text>

              <BulletList>
                {Object.entries(errors).map(([key, error]) => (
                  <BulletList.Item key={key}>{error?.message as string}</BulletList.Item>
                ))}
              </BulletList>
            </>
          )}
          <RichText html={signupContent?.value?.replace('{email}', emailAddress ?? 'unknown')} />

          {statusNotification ? (
            <NotificationBox
              isAlert
              variant={statusNotification?.value?.variant}
              title={statusNotification?.value?.title}
              text={statusNotification?.value?.content}
            />
          ) : (
            <>
              <Stack.Item>
                <Button onClick={onSubmit} isLoading={isLoading}>
                  {emailSignupButtonText?.value}
                </Button>
              </Stack.Item>
              <Text size="BodyS">
                <Recaptcha hasError={!!formState.errors.recaptchaToken} ref={recaptchaRef} />
              </Text>
            </>
          )}
        </Stack>
      </Card.Content>
    </Card>
  );
};
