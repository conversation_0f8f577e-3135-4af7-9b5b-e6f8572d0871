import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';

import { FlowContextThermostat } from './../context';

export function useFlowNavigation() {
  const { useFlowActorRef } = useFlowHooks<FlowContextThermostat>();
  const sendToFlowMachine = useFlowActorRef().send;

  const updateContextAndGoToNextStep = (values: Partial<FlowContextThermostat>) => {
    sendToFlowMachine({ type: 'UPDATE_VALUES', values });
    sendToFlowMachine({ type: 'NEXT_PAGE' });
  };

  return { updateContextAndGoToNextStep };
}
