import { useEffect } from 'react';

import { useSession } from '@dxp-auth';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';

import { FlowContextThermostat } from './../context';

export function useAuthenticated() {
  const { useFlowActorRef } = useFlowHooks<FlowContextThermostat>();
  const sendToFlowMachine = useFlowActorRef().send;

  const { status } = useSession();
  const isAuthenticated = status === 'authenticated';

  useEffect(() => {
    if (!isAuthenticated) {
      sendToFlowMachine({
        type: 'UPDATE_ROUTE',
        values: {
          path: '/inloggen/',
        },
      });
    }
  }, [isAuthenticated, sendToFlowMachine]);

  return { isAuthenticated };
}
