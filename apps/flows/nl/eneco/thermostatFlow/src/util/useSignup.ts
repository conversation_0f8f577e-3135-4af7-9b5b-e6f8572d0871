import { stripAccolades } from '@common/string';
import { useFormsPublicV2PostFormV2 } from '@dc/hooks';
import { useCustomerProfileAccount } from '@hooks/profile';

/**
 * Generate a signup function based on the destination code. Also returns the emailaddress
 * used for the signup, and the status of the signup.
 */
export const useSignup = (destinationCode: string) => {
  const { send, isSuccess, isError, isLoading } = useFormsPublicV2PostFormV2();
  const { data } = useCustomerProfileAccount();
  const emailAddress = data?.contact?.emailAddress;

  /**
   * Signs the user up to an email list.
   * As the user is logged in, we can infer the required data from the user's profile.
   */
  function signupUser(discontinueReason: string, recaptchaToken: string) {
    send({
      recaptchaToken,
      formId: stripAccolades(destinationCode),
      data: {
        urlOrigin: window.location.href,
        destinationCode: destinationCode,
        initials: data?.person?.initials,
        surname: data?.person?.surname,
        surnamePreposition: data?.person?.surnamePreposition,
        emailAddress,
        formFields: [
          { key: 'salutationFormField', value: data?.person?.salutation },
          { key: 'discontinueReason', value: discontinueReason },
        ],
        //@todo clean up these values when no longer required
        houseNumber: 0,
        houseNumberSuffix: '',
        postalCode: '',
      },
    });
  }

  return { isSuccess, isError, isLoading, signupUser, emailAddress };
};
