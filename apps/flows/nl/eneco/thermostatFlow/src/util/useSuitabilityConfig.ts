import * as yup from 'yup';

import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { usePlaceholderContent } from '@sitecore/common';
import { SuitabilityStepRendering } from '@sitecore/types/manual/thermostatFlow/suitabilityStep';

import { FlowContextThermostat } from './../context';

export function useSuitabilityConfig() {
  const { useFlowSelector } = useFlowHooks<FlowContextThermostat>();
  const flowState = useFlowSelector(state => state);

  const { ThermostatFlowSuitabilityStep: { fields: textualData } = {} } = usePlaceholderContent<{
    ThermostatFlowSuitabilityStep: SuitabilityStepRendering;
  }>();
  const { requiredCheckedItemsList, requiredUncheckedItemsList } = textualData?.formfields ?? {};

  const {
    context: { suitabilityChecks },
  } = flowState;

  const requiredTrueItems = requiredCheckedItemsList?.reduce(
    (
      acc,
      {
        fields: {
          checkbox: { name, requiredMessageText },
        },
      },
    ) => ({ ...acc, [name.value]: yup.boolean().oneOf([true], requiredMessageText.value) }),
    {},
  );

  const requiredFalseItems = requiredUncheckedItemsList?.reduce(
    (
      acc,
      {
        fields: {
          checkbox: { name, requiredMessageText },
        },
      },
    ) => ({ ...acc, [name.value]: yup.boolean().oneOf([false], requiredMessageText.value) }),
    {},
  );
  const formConfig = { ...requiredTrueItems, ...requiredFalseItems };
  // Use context for initial values if they match the form config
  const cachedInput = Object.entries(suitabilityChecks ?? {}).reduce(
    (acc, [key, value]) => {
      if (key in formConfig && typeof value === 'boolean') {
        acc[key] = value;
      }
      return acc;
    },
    {} as Record<string, boolean>,
  );

  const formSchema = yup.object(formConfig);

  const itemsToRender = [...(requiredCheckedItemsList ?? []), ...(requiredUncheckedItemsList ?? [])];

  return { itemsToRender, formSchema, cachedInput };
}
