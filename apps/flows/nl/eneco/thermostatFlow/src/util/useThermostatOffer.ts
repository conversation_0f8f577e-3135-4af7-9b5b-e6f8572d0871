import { useEffect } from 'react';

import logger from '@common/log';
import { useSalesFlowPublicGetSalesOffer } from '@dc/hooks';
import { useCustomerProfileAccount } from '@hooks/profile';

export function useThermostatOffer() {
  const { error: profileError, isLoading: isCustomerAccountLoading } = useCustomerProfileAccount();
  const { send, data, isLoading: isDataLoading, isError: hasOfferError } = useSalesFlowPublicGetSalesOffer();
  const requestBody = {
    data: { deviceType: 'Wired' },
  };

  useEffect(() => {
    if (!isCustomerAccountLoading) {
      send(requestBody);
    }
  }, [isCustomerAccountLoading]);

  const isLoading = isCustomerAccountLoading || isDataLoading;
  const thermostatProduct = data?.offers[0]?.products?.[0];
  const subscriptionProduct = data?.offers[0]?.products?.[1];
  const totalPrice = data?.offers[0]?.total;
  const offerId = data?.offers[0]?.id;

  const discountValue = thermostatProduct?.discount?.amount;
  const oneOffCost = thermostatProduct?.total;

  const monthlyCost = subscriptionProduct?.unitPrice;

  const offer = {
    monthlyCost,
    oneOffCost,
    totalPrice,
    discountValue,
    offerId,
  };

  const hasMissingData = !isLoading && data && !oneOffCost;
  const isError = hasOfferError || !!profileError;

  useEffect(() => {
    if (hasMissingData) {
      logger.error('TxBZKv', 'Missing data in useThermostatOffer', { data });
    }
  }, [data, hasMissingData]);

  return { isLoading, isError, offer };
}
