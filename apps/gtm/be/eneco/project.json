{"name": "gtm-be-eneco", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/gtm/be/eneco/src", "projectType": "library", "tags": ["app:next"], "targets": {"lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/gtm/be/eneco/src/**/*.{ts,tsx}"]}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "apps/gtm/be/eneco/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "runInBand": true}}}, "tsc": {"executor": "./tools/executors/tsc:tsc", "options": {"tsConfig": ["tsconfig.json"]}}}}