import { FC } from 'react';

import { Box, Stack, Stretch, Text } from '@sparky';

export type ChargeModeBadgeProps = {
  icon: JSX.Element | null;
  color: 'textHighlightVarFour' | 'textOnBackgroundVarOne' | 'textPrimary' | 'textOnBackgroundVarSix';
  backgroundColor: string;
  label: string;
};

const ChargeModeBadge: FC<ChargeModeBadgeProps> = ({ icon, color, backgroundColor, label }) => {
  return (
    <Stretch width="false">
      <Box paddingX="2" paddingY="1" backgroundColor={backgroundColor} borderRadius="m">
        <Stack direction="row" alignY="center" gap="1">
          {icon}
          <Text size="BodyXS" color={color} weight="bold">
            {label}
          </Text>
        </Stack>
      </Box>
    </Stretch>
  );
};

export default ChargeModeBadge;
