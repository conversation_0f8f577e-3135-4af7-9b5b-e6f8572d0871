import { addDays, isAfter } from 'date-fns';

import { Hems_DailyChargeSettingsResponseModel } from '@monorepo-types/dc';

export function getFirstInLineDefaultSchedule(
  schedules: Hems_DailyChargeSettingsResponseModel[],
  amountOfFutureDaysToCheck: number,
): string {
  // Use the departure time of the first-in-line schedule in case today's schedule is in the past
  for (let i = 0; i <= amountOfFutureDaysToCheck; i++) {
    const today = addDays(new Date(), i);
    const departureYear = today.getUTCFullYear();
    const departureMonth = today.getUTCMonth() + 1 === 12 ? '01' : String(today.getUTCMonth() + 1).padStart(2, '0');
    const departureDay = today.getUTCDate().toString().padStart(2, '0');
    const departureTime = schedules[today.getUTCDay() % 7].departureTime;

    const departureTimestamp = new Date(`${departureYear}-${departureMonth}-${departureDay}T${departureTime}`);

    if (isAfter(departureTimestamp, new Date())) {
      return departureTimestamp.toISOString();
    }
  }
  return new Date().toISOString();
}
