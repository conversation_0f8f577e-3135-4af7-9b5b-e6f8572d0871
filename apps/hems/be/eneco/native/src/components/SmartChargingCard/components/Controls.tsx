import { FC, useCallback, useEffect, useState } from 'react';

import { Preferences } from '@capacitor/preferences';

import Logger from '@common/log';
import { AppPreferences } from '@common/preferences';
import SmartChargingButton from '@custom-components/native/hems/SmartChargingButton';
import { useHemsStartSchedule, useHemsStopSchedule } from '@dc/hooks';
import { useTranslation } from '@i18n';
import { DC_Domain_Models_HEMS_ChargeMode } from '@monorepo-types/dc';
import { Box, Button, Stack, Stretch } from '@sparky';
import { CloseIcon, PlayIcon } from '@sparky/icons';
import { useTracking } from '@tracking';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useGetLatestSession from '../../../hooks/useGetLatestSession';
import { getChargeModeLatestSession } from '../../../utils/chargeMode';

const { SMARTCHARGING_SWITCH_MODE } = AppPreferences;

const Controls: FC = () => {
  const { t } = useTranslation();
  const { trackViewComponent } = useTracking();

  const { chargeState, smartChargingVehicleId } = useSmartChargingContext();
  const latestSession = useGetLatestSession();
  const { chargingStopped, isBoost } = useChargeScenarios();
  const { send: sendStart } = useHemsStartSchedule();
  const { send: sendStop } = useHemsStopSchedule();

  const [isSwitchingMode, setIsSwitchingMode] = useState<boolean>(false);
  const [chargeMode, setChargeMode] = useState<DC_Domain_Models_HEMS_ChargeMode>('Unknown');
  const apiChargeMode = chargeState?.mode || 'Unknown';

  const sendChargeRequest = useCallback(
    async (mode?: DC_Domain_Models_HEMS_ChargeMode) => {
      if (!smartChargingVehicleId) return;

      if (!chargingStopped) {
        try {
          await sendStop({ deviceId: smartChargingVehicleId });
        } catch (e) {
          Logger.error('MMwudL', 'Something went wrong while trying to stop the charging session: ', e);
        }
      } else {
        let newMode;

        if (mode) {
          newMode = mode;
        } else {
          // In the insights we do this check to check whether the session was a boost
          // or smart session: session.totalCharged === session.smartCharged. TBD if there is a better way
          const chargeModeLastSession = getChargeModeLatestSession(latestSession?.[0]);
          let chargeMode: DC_Domain_Models_HEMS_ChargeMode = 'Smart';
          if (chargeModeLastSession) {
            chargeMode = chargeModeLastSession === 'Combined' ? 'Smart' : chargeModeLastSession;
          }
          newMode = chargeMode;
        }

        try {
          await sendStart({
            deviceId: smartChargingVehicleId,
            requestBody: { chargeMode: newMode },
          });
        } catch (e) {
          Logger.error('itcnqa', 'Something went wrong while trying to resume the charging session: ', e);
        }
      }
    },
    [smartChargingVehicleId, chargingStopped],
  );

  // TODO: Functionality for switching modes and start/stopping is handled in a new US. This should be evaluated then.
  const pollingResult = async () => {
    const { value } = await Preferences.get({ key: SMARTCHARGING_SWITCH_MODE });

    //setAllButtonsDisabled(apiChargeMode === 'Unknown');

    //if (await updateSmartMode(value)) return;

    if (!value) {
      setChargeMode(apiChargeMode ?? 'Unknown');
      return;
    }

    if (apiChargeMode === value) {
      setIsSwitchingMode(false);
      setChargeMode(apiChargeMode);
      await Preferences.remove({ key: SMARTCHARGING_SWITCH_MODE });
    } else {
      setIsSwitchingMode(true);
      setChargeMode((value as DC_Domain_Models_HEMS_ChargeMode) || 'Unknown');
    }
  };

  useEffect(() => {
    pollingResult();
  }, [chargeState]);

  const handleChargeModeChange = useCallback(
    async (mode: DC_Domain_Models_HEMS_ChargeMode) => {
      if (mode !== chargeMode) {
        trackViewComponent({ label: mode, type: 'SmartChargingButtons' });
        setChargeMode(mode);
        await Preferences.set({ key: SMARTCHARGING_SWITCH_MODE, value: mode });
        setIsSwitchingMode(true);
        sendChargeRequest(mode);
      }
    },
    [chargeMode],
  );

  if (!chargeState) {
    return null;
  }

  return (
    <Box paddingTop="1">
      <Stack gap="3">
        {!chargingStopped && (
          <Stack.Item>
            <Stack gap="2" direction="row" alignX="justify">
              <Stack.Item grow>
                <SmartChargingButton
                  label={t('smartButtonLabel')}
                  action="smart"
                  active={chargeMode === 'Smart'}
                  disabled={isSwitchingMode}
                  onClick={() => handleChargeModeChange('Smart')}
                />
              </Stack.Item>
              <Stack.Item grow>
                <SmartChargingButton
                  label={t('boostButtonLabel')}
                  action="boost"
                  active={isBoost}
                  disabled={isSwitchingMode}
                  onClick={() => handleChargeModeChange('Boost')}
                />
              </Stack.Item>
            </Stack>
          </Stack.Item>
        )}
        <Stack.Item>
          <Stretch>
            <Button action="primary" size="compact" onClick={() => sendChargeRequest()} isLoading={isSwitchingMode}>
              <Stack direction="row" alignY="center" gap="2">
                <Stack.Item>
                  {chargingStopped ? (
                    <PlayIcon size="medium" color="iconInverted" />
                  ) : (
                    <CloseIcon size="medium" color="iconInverted" />
                  )}
                </Stack.Item>
                <Stack.Item>
                  <span>{chargingStopped ? t('resumeButtonLabel') : t('stopButtonLabel')}</span>
                </Stack.Item>
              </Stack>
            </Button>
          </Stretch>
        </Stack.Item>
      </Stack>
    </Box>
  );
};

export default Controls;
