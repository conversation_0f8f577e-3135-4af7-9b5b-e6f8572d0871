import { useTranslation } from '@i18n';
import { Box, ButtonOverlay, Popover, Stack, Stretch, Text } from '@sparky';
import { HomeIcon, InfoIcon } from '@sparky/icons';

const SessionAway = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Stack gap="2">
        <ButtonOverlay>
          <Stack direction="row" alignX="justify" alignY="start" gap="2">
            <Stack.Item>
              <Text size="BodyS">{t('chargingData.chargingExternal')}</Text>
            </Stack.Item>
            <Stack.Item>
              <Popover
                side="top"
                size="large"
                title={t('chargingData.awayTipTitle')}
                trigger={
                  <ButtonOverlay.Button>
                    <InfoIcon size="small" />
                  </ButtonOverlay.Button>
                }>
                {t('chargingData.awayTip')}
              </Popover>
            </Stack.Item>
          </Stack>
        </ButtonOverlay>

        <Stretch width={false}>
          <Box backgroundColor="backgroundSecondary" paddingY="1" paddingLeft="1" paddingRight="2" borderRadius="l">
            <Stack direction="row" gap="1" alignY="center">
              {/*TODO 723744 change Home icon here*/}
              <HomeIcon size="small" color="iconPrimary" />
              <Text size="BodyXS" weight="bold">
                {t('modeBadge.away')}
              </Text>
            </Stack>
          </Box>
        </Stretch>
      </Stack>
    </Box>
  );
};

export default SessionAway;
