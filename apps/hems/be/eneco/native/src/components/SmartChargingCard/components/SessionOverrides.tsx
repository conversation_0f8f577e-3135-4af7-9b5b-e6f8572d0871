import { FC } from 'react';

import { useTranslation } from '@i18n';
import { useLinkComponent } from '@link';
import { InsightsNativePaths } from '@native-components/constants/paths';
import { Box, ButtonOverlay, LinkOverlay, Popover, Stack, Text, VisuallyHidden } from '@sparky';
import { ClockIcon, EditIcon, FlagIcon, InfoIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useFormattedTargetTime from '../../../hooks/useFormattedTargetTime';

type OverrideItemProps = {
  icon: React.ReactElement;
  label: string;
  value: string | number;
  url: string;
  linkLabel?: string;
};

const OverrideItem: FC<OverrideItemProps> = ({ icon, label, value, url, linkLabel }) => {
  const Link = useLinkComponent();

  return (
    <Box paddingY="2">
      <LinkOverlay>
        <Stack direction="row" gap="1" alignY="center" alignX="justify">
          <Stack.Item>
            <Stack direction="row" gap="1" alignX="center">
              {icon}
              <Text size="BodyS">
                {label} <Text weight="bold">{value}</Text>
              </Text>
            </Stack>
          </Stack.Item>
          <Stack.Item>
            <Link href={url} linkType="internal">
              <LinkOverlay.Link>
                <EditIcon size="small" />
                {linkLabel && <VisuallyHidden>{linkLabel}</VisuallyHidden>}
              </LinkOverlay.Link>
            </Link>
          </Stack.Item>
        </Stack>
      </LinkOverlay>
    </Box>
  );
};

type TooltipItemProps = {
  icon: React.ReactElement;
  label: string;
  value: string;
  title: string;
  tip: string;
};

const TooltipItem: FC<TooltipItemProps> = ({ icon, label, value, title, tip }) => {
  return (
    <ButtonOverlay>
      <Box paddingY="2">
        <Stack direction="row" gap="1" alignY="center" alignX="justify">
          <Stack.Item>
            <Stack direction="row" gap="1" alignX="center">
              {icon}
              <Text size="BodyS">
                {label} <Text weight="bold">{value}</Text>
              </Text>
            </Stack>
          </Stack.Item>
          <Stack.Item>
            <Popover
              side="top"
              size="large"
              title={title}
              trigger={
                <ButtonOverlay.Button>
                  <InfoIcon size="small" />
                </ButtonOverlay.Button>
              }>
              {tip}
            </Popover>
          </Stack.Item>
        </Stack>
      </Box>
    </ButtonOverlay>
  );
};

const SessionOverrides = () => {
  const { t } = useTranslation();
  const { chargeState } = useSmartChargingContext();
  const { isAway, isSmart, isBoost, chargingStopped } = useChargeScenarios();
  const { targetTime } = useFormattedTargetTime();

  if (!chargeState || isAway) {
    return null;
  }

  return (
    <Box>
      <LinkOverlay>
        <Stack>
          <Stack.Item>
            <OverrideItem
              icon={<FlagIcon size="small" />}
              label={t('chargingData.chargingTargetOverride')}
              value={`${chargeState.targetStateOfCharge}%`}
              url={InsightsNativePaths.EV_OVERRIDE_LOAD_TARGET_PATH}
              linkLabel={t('chargingData.chargingTargetOverrideLinkLabel')}
            />
            {(isSmart || chargingStopped) && chargeState.targetEndDate && (
              <OverrideItem
                icon={<ClockIcon size="small" />}
                label={t('chargingData.timeTargetSmart')}
                value={targetTime(chargeState.targetEndDate)}
                url={InsightsNativePaths.EV_OVERRIDE_DEPARTURE_TIME_PATH}
                linkLabel={t('chargingData.timeTargetLinkLabel')}
              />
            )}
            {isBoost && chargeState.targetEndDate && (
              <TooltipItem
                icon={<ClockIcon size="small" />}
                label={t('chargingData.timeTargetBoost')}
                value={targetTime(chargeState.targetEndDate)}
                title={t('chargingData.boostTipTitle')}
                tip={t('chargingData.boostTip')}
              />
            )}
          </Stack.Item>
        </Stack>
      </LinkOverlay>
    </Box>
  );
};

export default SessionOverrides;
