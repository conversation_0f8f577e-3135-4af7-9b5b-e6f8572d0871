import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { addMinutes, subMinutes, format } from 'date-fns';

import { useHemsChargesettingsOverride, useHemsGetDeviceState, useHemsV2GetSessionDetailsV2 } from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import { smartChargingCurrentLoadingSchedule } from '@mocks/sitecore/apps/hems/be/eneco/native/smartcharging/SmartChargingCurrentLoadingSchedule';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';
import type { Hems_DeviceStateResponseModel } from '@monorepo-types/dc';

import SmartChargingCurrentLoadingSchedule from './SmartChargingCurrentLoadingSchedule';

const { fields } = smartChargingCurrentLoadingSchedule;

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useHemsGetDeviceState: jest.fn() as unknown as typeof useHemsGetDeviceState,
  useHemsChargesettingsOverride: jest.fn().mockReturnValue({
    send: jest.fn(),
    data: {
      departureTime: '2024-09-26T09:29:35.832Z',
      chargeModeSettings: [
        {
          targetStateOfCharge: 80,
          chargeMode: 'Smart',
        },
      ],
    },
    isError: false,
    isSuccess: false,
  }),
  useHemsV2GetSessionDetailsV2: jest.fn().mockReturnValue({
    data: {},
    mutate: jest.fn(),
    isLoading: false,
  }) as unknown as typeof useHemsV2GetSessionDetailsV2,
}));

describe('SmartChargingCurrentLoadingSchedule', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {
          deviceInfo: {
            brand: 'Audi',
            model: 'A3',
            year: 0,
          },
          deviceState: {
            stateOfCharge: 80,
            range: 400,
            lastUpdate: '2024-10-11T09:52:08.110Z',
          },
          chargeState: {
            mode: 'Boost',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
          },
          schedules: [
            {
              day: 'Sunday',
              departureTime: '07:00',
              chargeModeSettings: [
                {
                  targetStateOfCharge: 90,
                  chargeMode: 'Smart',
                },
              ],
            },
          ],
          overrideSchedule: {
            departureTime: '2025-02-04T17:00:00.000Z',
            chargeModeSettings: [
              {
                targetStateOfCharge: 90,
                chargeMode: 'Smart',
              },
            ],
          },
        },
      },
    });
  });

  it('should render', () => {
    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    expect(screen.getByText(fields.pageTitle.value)).toBeInTheDocument();
    expect(screen.getByText(fields.saveOverrideButtonLink.value.text)).toBeInTheDocument();
  });

  it('should render confimation notification on success when changing the schedule', () => {
    (useHemsChargesettingsOverride as jest.MockedFunction<typeof useHemsChargesettingsOverride>).mockReturnValue({
      send: jest.fn(),
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    expect(screen.getByText(fields.successNotification.value.title)).toBeInTheDocument();
  });

  it('should render error notification when there is an error when changing the schedule', () => {
    (useHemsChargesettingsOverride as jest.MockedFunction<typeof useHemsChargesettingsOverride>).mockReturnValue({
      send: jest.fn(),
      isError: true,
      isSuccess: false,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    expect(screen.getByText(fields.errorNotification.value.title)).toBeInTheDocument();
  });

  it('should show error when user selects a departure time within half an hour(Smart mode)', async () => {
    (useHemsGetDeviceState as jest.MockedFunction<typeof useHemsGetDeviceState>).mockReturnValue({
      data: {
        deviceStateInfo: {
          chargeState: {
            mode: 'Smart',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
          },
        },
      } as Hems_DeviceStateResponseModel,
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    const user = userEvent.setup();

    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    const departureTimeInput = screen.getByLabelText<HTMLInputElement>(fields.timeOfDepartureLabel.value);
    expect(departureTimeInput).toBeInTheDocument();

    const button = screen.getByText(fields.saveOverrideButtonLink.value.text);
    expect(button).toBeInTheDocument();

    const targetTime = format(subMinutes(new Date(), 30), 'kk:mm');

    await user.click(departureTimeInput);
    await user.type(departureTimeInput, targetTime, {
      initialSelectionStart: 0,
      initialSelectionEnd: departureTimeInput.value.length,
    });
    expect(departureTimeInput.value).toBe(targetTime);
    await user.click(button);

    const error = screen.getByText(fields.errorDepartureTimeText.value);
    expect(error).toBeInTheDocument();
  });

  it('should validate when user selects a departure time after half an hour (Smart mode)', async () => {
    (useHemsGetDeviceState as jest.MockedFunction<typeof useHemsGetDeviceState>).mockReturnValue({
      data: {
        deviceStateInfo: {
          chargeState: {
            mode: 'Smart',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
          },
        },
      } as Hems_DeviceStateResponseModel,
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    const user = userEvent.setup();

    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    const departureTimeInput = screen.getByLabelText<HTMLInputElement>(fields.timeOfDepartureLabel.value);
    expect(departureTimeInput).toBeInTheDocument();

    const button = screen.getByText(fields.saveOverrideButtonLink.value.text);
    expect(button).toBeInTheDocument();

    const targetTime = format(addMinutes(new Date(), 60), 'kk:mm');

    await user.click(departureTimeInput);
    await user.type(departureTimeInput, targetTime, {
      initialSelectionStart: 0,
      initialSelectionEnd: departureTimeInput.value.length,
    });
    expect(departureTimeInput.value).toBe(targetTime);
    await user.click(button);

    expect(screen.queryByText(fields.errorDepartureTimeText.value)).not.toBeInTheDocument();
  });

  it('should render a the OEM charging limit when maxOemStateOfCharge is defined', () => {
    (useHemsGetDeviceState as jest.MockedFunction<typeof useHemsGetDeviceState>).mockReturnValue({
      data: {
        deviceStateInfo: {
          deviceState: {
            maxOemStateOfCharge: 80,
          },
        },
      } as Hems_DeviceStateResponseModel,
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    expect(screen.getByText(fields.maxChargeLevelRemarkText.value)).toBeInTheDocument();

    const maxOemPercentageElemenent = screen.queryByTestId('max-oem-percentage');
    expect(maxOemPercentageElemenent?.textContent).toEqual('80%');
  });

  it('should not render day & depart input fields when mode is Boost', async () => {
    (useHemsGetDeviceState as jest.MockedFunction<typeof useHemsGetDeviceState>).mockReturnValue({
      data: {
        deviceStateInfo: {
          chargeState: {
            mode: 'Boost',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
          },
        },
      } as Hems_DeviceStateResponseModel,
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingCurrentLoadingSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-current-session-override',
    });

    await expect(screen.findByText(fields.dateOfDepartureLabel.value, {}, { timeout: 10 })).rejects.toThrow();
    await expect(screen.findByText(fields.timeOfDepartureLabel.value, {}, { timeout: 10 })).rejects.toThrow();
  });
});
