import { FC, useCallback, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format, formatISO, isBefore, isAfter, subMinutes } from 'date-fns';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useHemsChargesettingsOverride } from '@dc/hooks';
import useDC from '@dc/useDC';
import { Hems_ChargeSettingsOverrideRequestModel } from '@monorepo-types/dc';
import { useContent } from '@sitecore/common';
import { SmartChargingCurrentLoadingScheduleRendering } from '@sitecore/types/SmartChargingCurrentLoadingSchedule';
import { Stack, Stretch } from '@sparky';

import SmartChargingCurrentLoadingScheduleButtons from './SmartChargingCurrentLoadingScheduleButtons';
import SmartChargingCurrentLoadingScheduleForm from './SmartChargingCurrentLoadingScheduleForm';
import { useSmartChargingContext } from '../../context/SmartChargingContext';
import useChargeScenarios from '../../hooks/useChargeScenarios';

export type FormValues = {
  formDepartureTime: string;
};

const SmartChargingCurrentLoadingSchedulePanel: FC = () => {
  const { businessUnit, label, customerId } = useDC();
  const {
    send,
    isError: isErrorChargeSettingsOverride,
    isSuccess: isSuccessChargeSettingsOverride,
  } = useHemsChargesettingsOverride();

  const { smartChargingVehicleId, schedules, overrideSchedule, deviceState, chargeState } = useSmartChargingContext();
  const { isStoppedOrUnknown, isBoost } = useChargeScenarios();
  const { fields } = useContent<SmartChargingCurrentLoadingScheduleRendering>();

  const [departureDay, setDepartureDay] = useState<string>(formatISO(new Date(), { representation: 'date' }));
  const [departureTime, setDepartureTime] = useState<string>('07:30');

  const [targetSoc, setTargetSoc] = useState<number>(80);
  const [hasPostError, setHasPostError] = useState(false);
  const [hasPostSuccess, setHasPostSuccess] = useState(false);

  useEffect(() => {
    function setFormValues() {
      if (overrideSchedule?.chargeModeSettings && overrideSchedule.departureTime) {
        const overrideDepartureDay = formatISO(overrideSchedule.departureTime, { representation: 'date' });
        const now = formatISO(new Date(), { representation: 'date' });
        if (isBefore(overrideDepartureDay, now)) {
          setDepartureDay(now);
        } else {
          setDepartureDay(overrideDepartureDay);
        }
        setDepartureTime(format(overrideSchedule.departureTime, 'HH:mm'));
        setTargetSoc(overrideSchedule.chargeModeSettings[0].targetStateOfCharge);
      } else if (schedules?.[0]?.chargeModeSettings) {
        setDepartureTime(schedules[0].departureTime);
        setTargetSoc(Math.floor(schedules[0].chargeModeSettings[0].targetStateOfCharge / 10) * 10);
      }
    }

    setFormValues();
  }, [schedules, overrideSchedule]);

  const chargeMode = chargeState?.mode;
  const maxOemStateOfCharge = deviceState?.maxOemStateOfCharge;

  const overrideChanges = useCallback(async () => {
    if (customerId && chargeMode) {
      const departureTimestampUTC = new Date(`${departureDay}T${departureTime}`).toISOString();
      const targetCharge = maxOemStateOfCharge && targetSoc > maxOemStateOfCharge ? maxOemStateOfCharge : targetSoc;

      await send({
        businessUnit: businessUnit,
        label: label,
        customerId,
        data: {
          deviceId: smartChargingVehicleId,
          departureTime: departureTimestampUTC, // use UTC timestamp
          chargeModeSettings: [
            {
              targetStateOfCharge: targetCharge,
              chargeMode: isStoppedOrUnknown ? 'Smart' : chargeMode,
            },
          ],
        } as Hems_ChargeSettingsOverrideRequestModel,
      });
    }
  }, [
    customerId,
    chargeMode,
    departureDay,
    departureTime,
    maxOemStateOfCharge,
    targetSoc,
    send,
    businessUnit,
    label,
    smartChargingVehicleId,
  ]);

  useEffect(() => {
    setHasPostError(isErrorChargeSettingsOverride);
  }, [isErrorChargeSettingsOverride]);

  useEffect(() => {
    setHasPostSuccess(isSuccessChargeSettingsOverride);
  }, [isSuccessChargeSettingsOverride]);

  const currentScheduleSchema = yup.object(
    isBoost
      ? {}
      : {
          formDepartureTime: yup.string().test({
            name: 'formDepartureTime',
            message: 'formDepartureTimeNotValid',
            test() {
              const minDepartureTimestampUTC = subMinutes(new Date(`${departureDay}T${departureTime}`), 30);
              const timestampNowUTC = new Date();

              return isAfter(minDepartureTimestampUTC, timestampNowUTC);
            },
          }),
        },
  );

  const resolver = yupResolver(currentScheduleSchema);

  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm<FormValues>({ resolver });

  return (
    <Stretch height>
      <Stack direction="column">
        <Stack.Item grow>
          <SmartChargingCurrentLoadingScheduleForm
            departureDay={departureDay}
            setDepartureDay={setDepartureDay}
            departureTime={departureTime}
            setDepartureTime={setDepartureTime}
            targetSoc={targetSoc}
            setTargetSoc={setTargetSoc}
            hasSuccess={hasPostSuccess}
            notification={fields.successNotification}
            register={register}
            errors={errors}
            maxOemStateOfCharge={deviceState?.maxOemStateOfCharge}
            chargeMode={chargeMode}
          />
        </Stack.Item>
        <NativeBottomNavigationWrapper>
          <SmartChargingCurrentLoadingScheduleButtons
            overrideChanges={handleSubmit(overrideChanges)}
            saveButtonText={fields.saveOverrideButtonLink.value.text}
            hasError={hasPostError}
            notification={fields.errorNotification}
          />
        </NativeBottomNavigationWrapper>
      </Stack>
    </Stretch>
  );
};

export default SmartChargingCurrentLoadingSchedulePanel;
