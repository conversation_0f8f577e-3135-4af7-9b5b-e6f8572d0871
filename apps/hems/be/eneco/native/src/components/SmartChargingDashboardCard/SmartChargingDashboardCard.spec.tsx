import { Preferences } from '@capacitor/preferences';
import { renderHook, screen, waitFor } from '@testing-library/react';

import { AppPreferences } from '@common/preferences';
import {
  useCustomerGetCustomerProfile,
  useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions,
  useHemsV2GetSessionDetailsV2,
  useHemsV2GetSessionsV2,
} from '@dc/hooks';
import { useFormatter } from '@i18n';
import renderApp from '@jest-tools/renderApp';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';

import SmartChargingDashboardCard from './SmartChargingDashboardCard';
import copy from '../../content/nl-BE.json';

const { SMARTCHARGING_COMPATIBILITY_CAR_BRAND } = AppPreferences;

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useCustomerGetCustomerProfile: jest.fn() as unknown as typeof useCustomerGetCustomerProfile,
  useHemsGetDeviceState: jest.fn() as unknown as typeof useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions: jest.fn() as unknown as typeof useSubscriptionsLookupSubscriptions,
  useHemsV2GetSessionDetailsV2: jest.fn() as unknown as typeof useHemsV2GetSessionDetailsV2,
  useHemsV2GetSessionsV2: jest.fn() as unknown as typeof useHemsV2GetSessionsV2,
}));

describe('SmartChargingDashboardCard', () => {
  beforeEach(async () => {
    jest.clearAllMocks();

    (useCustomerGetCustomerProfile as jest.Mock).mockReturnValue({
      data: {
        features: ['SmartChargingVehicle'],
      },
    });

    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {
          deviceInfo: {
            brand: 'Audi',
            model: 'A3',
            year: 0,
          },
        },
      },
      mutate: jest.fn(),
    });

    (useSubscriptionsLookupSubscriptions as jest.Mock).mockReturnValue({
      data: {
        subscriptions: [
          {
            id: '7f0784e39-7a74-4547-5ec7-08ddbf8a2f5b',
            tier: 'Free',
            startDate: '2024-10-10',
            endDate: null,
            status: 'Active',
            assets: [
              {
                externalAssetId: '87a47877-4caf-4e3f-a07b-014d497d7f25',
                vehicle: {
                  brand: 'Audi',
                  model: 'A3',
                  year: 2022,
                  alias: 'My Audi',
                },
              },
            ],
            type: 'SmartCharging',
          },
        ],
      },
    });

    (useHemsV2GetSessionDetailsV2 as jest.Mock).mockReturnValue({
      data: {
        sessionId: 'b1854a7c-fd1a-4dfd-9568-177d134950ad',
        startingStateOfCharge: 7,
        timestamps: ['2025-08-01T07:10:00Z', '2025-08-01T07:15:00Z'],
        chargeBlockPlanning: [0.5833333333333334, 0.5833333333333334],
        chargePlanning: [0.5833333333333334, 0.5833333333333334],
        chargeBlockActual: [0.22658823529411762, 0.22658823529411762],
        chargeActual: [0.29400000000000004, 0.3380000000000001],
        chargeMode: ['Smart', 'Smart'],
        customerOfftakePrice: [0.18777, 0.18777],
      },
      mutate: jest.fn(),
      isLoading: false,
    });

    (useHemsV2GetSessionsV2 as jest.Mock).mockReturnValue({
      send: jest.fn().mockResolvedValue({
        sessions: [
          {
            startTimestamp: '2025-08-01T07:10:00Z',
            endTimestamp: '2025-08-01T10:00:00Z',
            contractType: 'Variable',
            totalCost: 1.45,
            totalCharged: 7.7,
            estimatedSavings: 0.12,
            actualSavings: 0.1,
            totalCostPerUnit: {
              amount: 0.19,
              unitOfMeasurement: 'kWh',
              currency: 'EUR',
            },
            status: 'Concluded',
            unitOfMeasurement: 'kWh',
          },
        ],
      }),
      isLoading: false,
    });

    await Preferences.set({ key: SMARTCHARGING_COMPATIBILITY_CAR_BRAND, value: 'Audi' });
  });

  afterEach(async () => {
    await Preferences.clear();
  });

  it('should render when the user has the SmartChargingVehicle feature flag', () => {
    renderApp(SmartChargingDashboardCard, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    expect(screen.getByRole('img', { name: 'EV' })).toBeInTheDocument();
    expect(screen.getByText('Audi A3')).toBeInTheDocument();
  });

  it('should NOT render when the user does not have the SmartChargingVehicle feature flag', async () => {
    (useCustomerGetCustomerProfile as jest.Mock).mockReturnValue({
      data: {
        features: [],
      },
    });

    renderApp(SmartChargingDashboardCard, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    await waitFor(() => {
      expect(screen.queryByRole('img', { name: 'EV' })).not.toBeInTheDocument();
    });
  });

  // TODO add sessions planned scenario, once changes have been made to endpoint
  it.each([
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: null,
        },
        chargeState: {
          mode: 'Boost',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Charging',
        },
        chargingStateLabel: copy.chargingStateLabels.charging,
        progressBarVisible: true,
        chargingAwayVisible: false,
        chargingTargetVisible: true,
        timeTargetVisible: true,
        timeTargetLabel: copy.chargingData.timeTargetBoost,
        usageKwhVisible: false,
        loadingCostVisible: false,
        chargeModeIcon: copy.modeBadge.boost,
        chargeModeIconVisible: true,
        resumeButtonVisible: false,
      },
      description: 'car is charging in boost mode',
    },
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: null,
        },
        chargeState: {
          mode: 'Smart',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Charging',
        },
        chargingStateLabel: copy.chargingStateLabels.charging,
        timeTargetLabel: copy.chargingData.timeTargetSmart,
        progressBarVisible: true,
        chargingTargetVisible: true,
        chargingAwayVisible: false,
        timeTargetVisible: true,
        usageKwhVisible: false,
        loadingCostVisible: false,
        chargeModeIcon: copy.modeBadge.smart,
        chargeModeIconVisible: true,
        resumeButtonVisible: false,
      },
      description: 'car is charging in smart mode',
    },
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: null,
        },
        chargeState: {
          mode: 'Stopped',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Connected',
        },
        chargingStateLabel: copy.chargingStateLabels.stopped,
        timeTargetLabel: null,
        progressBarVisible: true,
        chargingAwayVisible: false,
        chargingTargetVisible: true,
        timeTargetVisible: false,
        usageKwhVisible: true,
        loadingCostVisible: true,
        chargeModeIcon: copy.modeBadge.smart,
        chargeModeIconVisible: true,
        resumeButtonVisible: true,
      },
      description: 'car is connected and in stopped mode',
    },
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: null,
        },
        chargeState: {
          mode: 'Boost',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'ChargingAway',
        },
        chargingStateLabel: copy.chargingStateLabels.charging,
        timeTargetLabel: null,
        progressBarVisible: true,
        chargingTargetVisible: false,
        chargingAwayVisible: true,
        timeTargetVisible: false,
        usageKwhVisible: false,
        loadingCostVisible: false,
        chargeModeIcon: copy.modeBadge.away,
        chargeModeIconVisible: true,
        resumeButtonVisible: false,
      },
      description: 'car is charging away',
    },
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: null,
        },
        chargeState: {
          mode: 'Unknown',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Away',
        },
        chargingStateLabel: copy.chargingStateLabels.disconnected,
        timeTargetLabel: null,
        progressBarVisible: false,
        chargingTargetVisible: false,
        chargingAwayVisible: false,
        timeTargetVisible: false,
        usageKwhVisible: false,
        loadingCostVisible: false,
        chargeModeIcon: copy.modeBadge.away,
        chargeModeIconVisible: false,
        resumeButtonVisible: false,
      },
      description: 'car is away',
    },
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: null,
        },
        chargeState: {
          mode: 'Smart',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Completed',
        },
        chargingStateLabel: copy.chargingStateLabels.completed,
        timeTargetLabel: null,
        progressBarVisible: true,
        chargingTargetVisible: false,
        chargingAwayVisible: false,
        timeTargetVisible: false,
        usageKwhVisible: true,
        loadingCostVisible: true,
        chargeModeIcon: copy.modeBadge.smart,
        chargeModeIconVisible: true,
        resumeButtonVisible: false,
      },
      description: 'car is fully charged (completed)',
    },
    {
      mockData: {
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
          maxOemStateOfCharge: 85,
          intervention: {
            id: '1234',
          },
        },
        chargeState: {
          mode: 'Smart',
          targetStateOfCharge: 10,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Completed',
        },
        chargingStateLabel: copy.chargingStateLabels.warning,
        timeTargetLabel: null,
        progressBarVisible: true,
        chargingTargetVisible: false,
        chargingAwayVisible: false,
        timeTargetVisible: false,
        usageKwhVisible: false,
        loadingCostVisible: false,
        chargeModeIcon: copy.modeBadge.smart,
        chargeModeIconVisible: false,
        resumeButtonVisible: false,
      },
      description: 'car has an intervention',
    },
  ])('should render the correct elements for each scenario: $description', async ({ mockData }) => {
    const { result } = renderHook(() => useFormatter());

    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceStateInfo: {
          deviceState: {
            stateOfCharge: 80,
            range: 400,
            lastUpdate: '2024-10-11T09:52:08.110Z',
            maxOemStateOfCharge: 85,
            intervention: mockData.deviceState.intervention,
          },
          chargeState: {
            mode: mockData.chargeState.mode,
            targetStateOfCharge: mockData.chargeState.targetStateOfCharge,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: mockData.chargeState.state,
          },
        },
      },
      mutate: jest.fn(),
    });

    renderApp(SmartChargingDashboardCard, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    expect(await screen.findByText(mockData.chargingStateLabel, { exact: true })).toBeInTheDocument();

    // Charging status label
    await waitFor(() => {
      const stateOfChargeLabel = screen.queryByText(String(mockData.deviceState.stateOfCharge), { exact: true });
      expect(Boolean(stateOfChargeLabel)).toBe(mockData.progressBarVisible);
    });

    // Charging Target label + percentage
    await waitFor(() => {
      const chargingTargetElement = screen.queryByText(String(copy.chargingData.chargingTarget), { exact: true });
      expect(Boolean(chargingTargetElement)).toBe(mockData.chargingTargetVisible);
    });
    await waitFor(() => {
      const targetPercentage = screen.queryByText(String(mockData.chargeState.targetStateOfCharge + '%'), {
        exact: true,
      });
      expect(Boolean(targetPercentage)).toBe(mockData.chargingTargetVisible);
    });

    // Time target label and date
    await waitFor(() => {
      const timeTargetLabel = screen.queryByText(String(mockData.timeTargetLabel), { exact: true });
      expect(Boolean(timeTargetLabel)).toBe(mockData.timeTargetVisible);
    });
    await waitFor(() => {
      const targetDate = screen.queryByText(
        result.current.date.dayOfWeekLong(mockData.chargeState.targetEndDate) +
          ' ' +
          result.current.date.hourLong(mockData.chargeState.targetEndDate),
        {
          exact: true,
        },
      );
      expect(Boolean(targetDate)).toBe(mockData.timeTargetVisible);
    });

    // Usage kWh label
    await waitFor(() => {
      const consumptionLabel = screen.queryByText(String(copy.chargingData.consumption), { exact: true });
      expect(Boolean(consumptionLabel)).toBe(mockData.usageKwhVisible);
    });
    await waitFor(() => {
      const totalCostElement = screen.queryByText(result.current.unit.electricity(7.7), { exact: true });
      expect(Boolean(totalCostElement)).toBe(mockData.usageKwhVisible);
    });

    // Charge cost label + savings
    await waitFor(() => {
      const chargeCostLabel = screen.queryByText(String(copy.chargingData.cost), { exact: true });
      expect(Boolean(chargeCostLabel)).toBe(mockData.loadingCostVisible);
    });
    // TODO fix below
    // await waitFor(() => {
    //   const costElement = screen.queryByText(result.current.currency.euro(1.45), { exact: true });
    //   expect(Boolean(costElement)).toBe(mockData.loadingCostVisible);
    // });
    // await waitFor(() => {
    //   const savingsElement = screen.queryByText(result.current.currency.euro(0.1), { exact: true });
    //   expect(Boolean(savingsElement)).toBe(mockData.loadingCostVisible);
    // });

    // ChargingAway label
    await waitFor(() => {
      const chargingAwayLabel = screen.queryByText(String(copy.chargingData.chargingExternal), { exact: true });
      expect(Boolean(chargingAwayLabel)).toBe(mockData.chargingAwayVisible);
    });

    // Icon
    await waitFor(() => {
      const iconLabel = screen.queryByText(String(mockData.chargeModeIcon), { exact: true });
      expect(Boolean(iconLabel)).toBe(mockData.chargeModeIconVisible);
    });

    // Resume button
    await waitFor(() => {
      const resumeButtonElement = screen.queryByText(String(copy.resumeButtonLabel), { exact: true });
      expect(Boolean(resumeButtonElement)).toBe(mockData.resumeButtonVisible);
    });
  });
});
