import { FC } from 'react';

import FeatureGuard from '@components/FeatureGuard/FeatureGuard';
import { I18nProvider, useTranslation } from '@i18n';
import { useLinkComponent } from '@link';
import { InsightsNativePaths } from '@native-components/constants/paths';
import { useContent } from '@sitecore/common';
import { SmartChargingDashboardCardRendering } from '@sitecore/types/SmartChargingDashboardCard';
import { Box, Card, Divider, LinkOverlay, Skeleton, Stack, VisuallyHidden } from '@sparky';
import { ChevronRightIcon } from '@sparky/icons';

import CarModelWithStatus from './components/CarModelWithStatus';
import ChargingData from './components/ChargingData';
import ChargingModeBadge from './components/ChargingModeBadge';
import ProgressBar from './components/ProgressBar';
import ResumeButton from './components/ResumeButton';
import { SmartChargingProvider, useSmartChargingContext } from '../../context/SmartChargingContext';
import useChargeScenarios from '../../hooks/useChargeScenarios';
import useRefreshDeviceState from '../../hooks/useRefreshDeviceState';

const SmartChargingDashboardCardComponent: FC = () => {
  const { t } = useTranslation();
  const Link = useLinkComponent();
  const { isReady, smartChargingVehicleId: userHasSmartChargingSubscription, hasError } = useSmartChargingContext();
  const { awayOrUnknown, hasIntervention } = useChargeScenarios();

  useRefreshDeviceState();

  if (!isReady) {
    return <Skeleton height={100} />;
  }
  if (!userHasSmartChargingSubscription || hasError) {
    return null;
  }

  const showBottomSection = !awayOrUnknown && !hasIntervention;
  const hasGap = !awayOrUnknown || hasIntervention;

  return (
    <Card>
      <Box padding="4">
        <LinkOverlay>
          <Link linkType="internal" href={InsightsNativePaths.EV_PATH}>
            <LinkOverlay.Link>
              <Stack gap={hasGap ? '2' : '0'}>
                <Stack direction="row" gap="1" alignY="center" alignX="justify">
                  <Stack.Item>
                    <CarModelWithStatus />
                  </Stack.Item>
                  <Stack.Item>
                    <ChevronRightIcon size="medium" />
                    <VisuallyHidden>{t('dashboardCard.linkLabel')}</VisuallyHidden>
                  </Stack.Item>
                </Stack>
                <ProgressBar />
                {showBottomSection && (
                  <>
                    <Divider />
                    <Stack direction="row" alignY="end" alignX="justify" gap="2">
                      <Stack.Item>
                        <ChargingData />
                      </Stack.Item>
                      <Stack.Item shrink={false}>
                        <ChargingModeBadge />
                      </Stack.Item>
                    </Stack>
                  </>
                )}
              </Stack>
            </LinkOverlay.Link>
          </Link>
        </LinkOverlay>
        <ResumeButton />
      </Box>
    </Card>
  );
};

const SmartChargingDashboardCard = () => {
  const {
    fields: { featureScopeList },
  } = useContent<SmartChargingDashboardCardRendering>();

  return (
    <FeatureGuard features={featureScopeList}>
      <SmartChargingProvider>
        <I18nProvider dictionary={locale => import(`../../content/${locale}.json`)}>
          <SmartChargingDashboardCardComponent />
        </I18nProvider>
      </SmartChargingProvider>
    </FeatureGuard>
  );
};

export default SmartChargingDashboardCard;
