import { FC, useEffect } from 'react';

import FeatureGuard from '@components/FeatureGuard/FeatureGuard';
import { I18nProvider, useTranslation } from '@i18n';
import { useLinkComponent } from '@link';
import { InsightsNativePaths } from '@native-components/constants/paths';
import { useContent } from '@sitecore/common';
import { SmartChargingDashboardCardRendering } from '@sitecore/types/SmartChargingDashboardCard';
import { Bleed, Box, Card, Divider, IconButton, LinkOverlay, Skeleton, Stack } from '@sparky';
import { ChevronRightIcon } from '@sparky/icons';

import CarModelWithStatus from './components/CarModelWithStatus';
import ChargingData from './components/ChargingData';
import ChargingModeBadge from './components/ChargingModeBadge';
import ProgressBar from './components/ProgressBar';
import ResumeButton from './components/ResumeButton';
import { SmartChargingProvider, useSmartChargingContext } from '../../context/SmartChargingContext';

const SmartChargingDashboardCardComponent: FC = () => {
  const { t } = useTranslation();
  const Link = useLinkComponent();
  const {
    chargeState,
    intervention,
    isReady,
    refreshDeviceState,
    refreshSessionDetails,
    smartChargingVehicleId: userHasSmartChargingSubscription,
  } = useSmartChargingContext();

  // TODO when do we refresh the deviceState/sessionsDetails?
  useEffect(() => {
    if (userHasSmartChargingSubscription) {
      const interval = setInterval(() => {
        refreshDeviceState();
        refreshSessionDetails();
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [refreshDeviceState, refreshSessionDetails, userHasSmartChargingSubscription]);

  if (!userHasSmartChargingSubscription) {
    return null;
  }

  if (!isReady) {
    return <Skeleton height={100} />;
  }

  const showBottomSection = chargeState?.state !== 'Away' && !intervention && chargeState?.state !== 'Unknown';
  const hasGap = chargeState?.state !== 'Away' && chargeState?.state !== 'Unknown';

  return (
    <Card>
      <Box padding="4">
        <LinkOverlay>
          <Stack gap={hasGap ? '2' : '0'}>
            <Stack direction="row" gap="1" alignY="center" alignX="justify">
              <Stack.Item>
                <CarModelWithStatus />
              </Stack.Item>
              <Stack.Item>
                <Bleed right="2">
                  <Link linkType="internal" href={InsightsNativePaths.EV_PATH}>
                    <LinkOverlay.Link>
                      <IconButton label={t('dashboardCard.linkLabel')} size="regular">
                        <ChevronRightIcon />
                      </IconButton>
                    </LinkOverlay.Link>
                  </Link>
                </Bleed>
              </Stack.Item>
            </Stack>
            <ProgressBar />
            {showBottomSection && (
              <>
                <Divider />
                <Stack direction="row" alignY="end" alignX="justify" gap="2">
                  <Stack.Item>
                    <ChargingData />
                  </Stack.Item>
                  <Stack.Item>
                    <ChargingModeBadge />
                  </Stack.Item>
                </Stack>
              </>
            )}
          </Stack>
        </LinkOverlay>
        <ResumeButton />
      </Box>
    </Card>
  );
};

const SmartChargingDashboardCard = () => {
  const {
    fields: { featureScopeList },
  } = useContent<SmartChargingDashboardCardRendering>();

  return (
    <FeatureGuard features={featureScopeList}>
      <SmartChargingProvider>
        <I18nProvider dictionary={locale => import(`../../content/${locale}.json`)}>
          <SmartChargingDashboardCardComponent />
        </I18nProvider>
      </SmartChargingProvider>
    </FeatureGuard>
  );
};

export default SmartChargingDashboardCard;
