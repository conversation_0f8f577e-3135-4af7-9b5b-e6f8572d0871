import { FC, useEffect, useMemo, useState } from 'react';

import { Preferences } from '@capacitor/preferences';

import { AppPreferences } from '@common/preferences';
import SmartChargingStatusBadge from '@custom-components/native/hems/SmartChargingStatusBadge';
import { useTranslation } from '@i18n';
import { Stack, Text } from '@sparky';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';

const { SMARTCHARGING_COMPATIBILITY_CAR_BRAND } = AppPreferences;

const CarModelWithStatus: FC = () => {
  const { deviceInfo, chargeState, intervention } = useSmartChargingContext();
  const { t } = useTranslation();

  const [carBrand, setCarBrand] = useState<string | null>(null);

  useEffect(() => {
    const getCarBrand = async () => {
      const { value: carBrand } = await Preferences.get({ key: SMARTCHARGING_COMPATIBILITY_CAR_BRAND });
      setCarBrand(carBrand);
    };

    getCarBrand();
  }, []);

  const carInfoTitle = useMemo(() => {
    if (deviceInfo) {
      return `${deviceInfo.brand} ${deviceInfo.model}`;
    }

    return '';
  }, [deviceInfo, carBrand]);

  const badgeStatus = useMemo(() => {
    if (intervention) {
      return 'warning';
    }

    if (chargeState?.state === 'Away' || chargeState?.state === 'Unknown') {
      return 'disconnected';
    } else if (chargeState?.state === 'Charging' || chargeState?.state === 'ChargingAway') {
      return 'charging';
    } else if (chargeState?.mode === 'Stopped' && chargeState?.state === 'Connected') {
      return 'stopped';
    } else if (chargeState?.state === 'Completed') {
      return 'completed';
    } else {
      return 'warning';
    }
  }, [chargeState?.mode, chargeState?.state, intervention]);

  const color = useMemo(() => {
    if (intervention) {
      return 'textHighlightVarTwo';
    }

    if (
      chargeState?.state === 'Away' ||
      (chargeState?.mode === 'Stopped' && chargeState?.state === 'Connected') ||
      chargeState?.state === 'Unknown'
    ) {
      return 'textPrimary';
    }

    return 'textHighlightVarFive';
  }, [chargeState?.mode, chargeState?.state, intervention]);

  return (
    <Stack direction="row" alignY="center" gap="3">
      <Stack.Item>
        <SmartChargingStatusBadge variant={badgeStatus} />
      </Stack.Item>
      <Stack.Item>
        <Text size="BodyS" color="textPrimary">
          {carInfoTitle}
        </Text>
        <Text size="BodyS" weight="bold" color={color}>
          {t(`chargingStateLabels.${badgeStatus}`)}
        </Text>
      </Stack.Item>
    </Stack>
  );
};

export default CarModelWithStatus;
