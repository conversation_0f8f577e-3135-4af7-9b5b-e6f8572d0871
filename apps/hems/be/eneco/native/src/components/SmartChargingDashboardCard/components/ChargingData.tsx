import { FC, useMemo } from 'react';

import { useFormatter, useTranslation } from '@i18n';
import { Stack, Text } from '@sparky';
import { ClockIcon, ElectricityIcon, EuroIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useGetActualSavings from '../../../hooks/useGetActualSavings';

type InfoElementProps = {
  icon: React.ReactElement;
  label: string;
  value: string | number;
  suffix?: string;
};

const InfoElement: FC<InfoElementProps> = ({ icon, label, value, suffix }) => (
  <Stack.Item>
    <Stack direction="row" gap="1" alignY="center">
      {icon}
      <Text size="BodyS">
        {label} <Text weight="bold">{value}</Text>
        {suffix && (
          <Text weight="bold" color="textHighlightVarFive">
            {suffix}
          </Text>
        )}
      </Text>
    </Stack>
  </Stack.Item>
);

const ChargingData: FC = () => {
  const { t } = useTranslation();
  const { date, currency, unit } = useFormatter();
  const { chargeState, intervention } = useSmartChargingContext();
  const sessions = useGetActualSavings();

  const targetEndDateString = useMemo(() => {
    const targetEndDate = chargeState?.targetEndDate;
    if (targetEndDate) {
      return `${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
    }

    return '';
  }, [chargeState?.targetEndDate, date]);

  if (chargeState?.state === 'Away' || intervention) {
    return;
  }

  const loadingExternally = chargeState?.state === 'ChargingAway';
  // TODO add check for session planned "ChargeState.Connected & Plugin time != StartTime (Endpoint: Charge Session Details (Insights V3)".
  //  when new endpoint is available.
  const loadingOrSessionPlanned = chargeState?.state === 'Charging';
  const carLoaded =
    chargeState?.state === 'Completed' && (chargeState?.mode === 'Smart' || chargeState?.mode === 'Boost');
  const loadingStopped = chargeState?.mode === 'Stopped' && chargeState?.state === 'Connected';

  const showChargingTargetElement = loadingOrSessionPlanned || loadingStopped;
  const showTimeTargetElement = loadingOrSessionPlanned;
  const showConsumptionElement = carLoaded || loadingStopped;
  const showCostElement = carLoaded || loadingStopped;

  // TODO test thoroughly if these values are immediately available in the useHemsV2GetSessionsV2
  //  endpoint when user stops the session
  const actualSavings = sessions?.[0]?.actualSavings;
  const charged = sessions?.[0]?.totalCharged;
  const totalCost = sessions?.[0]?.totalCost;

  return (
    <Stack gap="1">
      {!loadingExternally && (
        <>
          {showChargingTargetElement && (
            <InfoElement
              // TODO replace icon
              icon={<ClockIcon size="small" />}
              label={t('chargingData.chargingTarget')}
              value={`${chargeState?.targetStateOfCharge}%`}
            />
          )}
          {showTimeTargetElement && targetEndDateString && (
            <InfoElement
              icon={<ClockIcon size="small" />}
              label={
                chargeState?.mode === 'Smart' ? t('chargingData.timeTargetSmart') : t('chargingData.timeTargetBoost')
              }
              value={targetEndDateString}
            />
          )}
          {showConsumptionElement && charged && (
            <InfoElement
              icon={<ElectricityIcon size="small" />}
              label={t('chargingData.consumption')}
              value={unit.electricity(charged)}
            />
          )}
          {showCostElement && totalCost && (
            <InfoElement
              icon={<EuroIcon size="small" />}
              label={t('chargingData.cost')}
              value={currency.euro(totalCost)}
              suffix={actualSavings ? ` - ${currency.euro(actualSavings)}` : undefined}
            />
          )}
        </>
      )}

      {loadingExternally && (
        <Stack.Item>
          <Stack direction="row" gap="1" alignY="center">
            <Text size="BodyS">{t('chargingData.chargingExternal')}</Text>
          </Stack>
        </Stack.Item>
      )}
    </Stack>
  );
};

export default ChargingData;
