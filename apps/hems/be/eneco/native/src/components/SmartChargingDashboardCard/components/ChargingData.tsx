import { FC, useMemo } from 'react';

import { useFormatter, useTranslation } from '@i18n';
import { Stack, Text } from '@sparky';
import { ClockIcon, ElectricityIcon, EuroIcon, FlagIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useFormattedTargetTime from '../../../hooks/useFormattedTargetTime';
import useGetLatestSession from '../../../hooks/useGetLatestSession';

type InfoElementProps = {
  icon: React.ReactElement;
  label: string;
  value: string | number;
  suffix?: string;
};

const InfoElement: FC<InfoElementProps> = ({ icon, label, value, suffix }) => (
  <Stack.Item>
    <Stack direction="row" gap="1" alignY="center">
      {icon}
      <Text size="BodyS">
        {label} <Text weight="bold">{value}</Text>
        {suffix && (
          <Text weight="bold" color="textHighlightVarFive">
            {suffix}
          </Text>
        )}
      </Text>
    </Stack>
  </Stack.Item>
);

type ChargingDataProps = {
  hideChargingTarget?: boolean;
};

const ChargingData: FC<ChargingDataProps> = ({ hideChargingTarget = false }) => {
  const { t } = useTranslation();

  const { targetTime } = useFormattedTargetTime();
  const { chargeState } = useSmartChargingContext();
  const { currency, unit } = useFormatter();

  const latestSession = useGetLatestSession();
  const { isAway, isChargingAway, isCharging, chargingStopped, hasIntervention, chargingStoppedOrCompleted } =
    useChargeScenarios();

  const targetEndDateString = useMemo(() => {
    const targetEndDate = chargeState?.targetEndDate;
    if (targetEndDate) {
      return targetTime(targetEndDate);
    }

    return '';
  }, [chargeState?.targetEndDate, targetTime]);

  if (isAway || hasIntervention) {
    return;
  }

  const loadingExternally = isChargingAway;
  const loadingOrSessionPlanned = isCharging;
  const loadingStopped = chargingStopped;

  // TODO test thoroughly if these values are immediately available in the useHemsV2GetSessionsV2
  //  endpoint when user stops the session
  const actualSavings = latestSession?.[0]?.actualSavings;
  const charged = latestSession?.[0]?.totalCharged;
  const totalCost = latestSession?.[0]?.totalCost;

  const showChargingTargetElement = (loadingOrSessionPlanned || loadingStopped) && !hideChargingTarget;
  const showTimeTargetElement = loadingOrSessionPlanned && targetEndDateString;
  // using charged >= 0 to also show 0 values (virtual car sessions show 0 values); evaluate later if this is desired
  const showConsumptionElement = chargingStoppedOrCompleted && charged !== undefined && charged >= 0;
  const showCostElement = chargingStoppedOrCompleted && totalCost !== undefined && totalCost >= 0;

  return (
    <Stack gap="1">
      {!loadingExternally && (
        <>
          {showChargingTargetElement && (
            <InfoElement
              icon={<FlagIcon size="small" />}
              label={t('chargingData.chargingTarget')}
              value={`${chargeState?.targetStateOfCharge}%`}
            />
          )}
          {showTimeTargetElement && (
            <InfoElement
              icon={<ClockIcon size="small" />}
              label={
                chargeState?.mode === 'Smart' ? t('chargingData.timeTargetSmart') : t('chargingData.timeTargetBoost')
              }
              value={targetEndDateString}
            />
          )}
          {showConsumptionElement && (
            <InfoElement
              icon={<ElectricityIcon size="small" />}
              label={t('chargingData.consumption')}
              value={unit.electricity(charged)}
            />
          )}
          {showCostElement && (
            <InfoElement
              icon={<EuroIcon size="small" />}
              label={t('chargingData.cost')}
              value={currency.euro(totalCost)}
              suffix={actualSavings ? ` - ${currency.euro(actualSavings)}` : undefined}
            />
          )}
        </>
      )}

      {loadingExternally && (
        <Stack.Item>
          <Stack direction="row" gap="1" alignY="center">
            <Text size="BodyS">{t('chargingData.chargingExternal')}</Text>
          </Stack>
        </Stack.Item>
      )}
    </Stack>
  );
};

export default ChargingData;
