import { FC } from 'react';

import { useTranslation } from '@i18n';
import {
  DC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel,
  Hems_ChargeStateResponseModel,
} from '@monorepo-types/dc';
import { Bleed, Box, Stack, Stretch, Text } from '@sparky';
import { BoostChargingIcon, HomeIcon, SaveIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';

type BadgeConfig = {
  icon: JSX.Element;
  color: 'textHighlightVarFour' | 'textOnBackgroundVarOne' | 'textPrimary' | 'textOnBackgroundVarSix';
  backgroundColor: string;
  label: string;
};

type ChargeMode = 'Smart' | 'Boost';

const BADGE_CONFIGS: Record<ChargeMode | 'Away', BadgeConfig> = {
  Away: {
    icon: <HomeIcon size="small" color="iconPrimary" />,
    color: 'textHighlightVarFour',
    backgroundColor: 'backgroundSecondary',
    label: 'away',
  },
  Smart: {
    icon: <SaveIcon size="small" color="iconElectricity" />,
    color: 'textHighlightVarFour',
    backgroundColor: 'backgroundVarFive',
    label: 'smart',
  },
  Boost: {
    icon: <BoostChargingIcon size="small" color="iconOnBackgroundVarOne" />,
    color: 'textOnBackgroundVarOne',
    backgroundColor: 'backgroundVarOne',
    label: 'boost',
  },
};

const getBadgeConfig = (
  chargeState: Hems_ChargeStateResponseModel,
  hemsSessionsDetailsData: DC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel,
): BadgeConfig | null => {
  if (chargeState.state === 'ChargingAway') {
    return BADGE_CONFIGS.Away;
  }

  if (chargeState.state === 'Charging') {
    return BADGE_CONFIGS[chargeState.mode as ChargeMode];
  }

  // When the mode is Stopped or the state is Completed, we check which chargeModes where used during the whole session.
  // If multiple modes were used, e.g. smart > boost > smart, we show the CombinedBadgeIcons.
  // If only one mode was used, we show the regular icon (Smart or Boost)
  if ((chargeState.mode === 'Stopped' || chargeState.state === 'Completed') && hemsSessionsDetailsData?.chargeMode) {
    const chargeModes = hemsSessionsDetailsData.chargeMode;
    if (!chargeModes?.every(mode => mode === chargeModes[0])) {
      return null;
    }

    // The latest mode can be found in the last record
    const latestChargeMode = chargeModes[chargeModes.length - 1];
    return BADGE_CONFIGS[latestChargeMode as ChargeMode];
  }

  return null;
};

const ChargingModeBadge: FC = () => {
  const { t } = useTranslation();
  const { chargeState, intervention, hemsSessionsDetailsData } = useSmartChargingContext();

  if (!chargeState || intervention || !hemsSessionsDetailsData?.chargeMode) {
    return null;
  }

  const badgeConfig = getBadgeConfig(chargeState, hemsSessionsDetailsData);
  if (!badgeConfig) {
    const stateAwayOrUnknown = chargeState.state === 'Away' || chargeState.state === 'Unknown';
    return hemsSessionsDetailsData?.chargeMode?.length && !stateAwayOrUnknown ? <CombinedBadgeIcons /> : null;
  }

  let badgeLabel = '';
  switch (badgeConfig.label) {
    case 'away':
      badgeLabel = t('modeBadge.away');
      break;
    case 'boost':
      badgeLabel = t('modeBadge.boost');
      break;
    case 'smart':
      badgeLabel = t('modeBadge.smart');
      break;
    default:
      badgeLabel = '';
      break;
  }

  return (
    <Stretch width="false">
      <Box paddingX="2" paddingY="1" backgroundColor={badgeConfig.backgroundColor} borderRadius="m">
        <Stack direction="row" alignY="center" gap="1">
          {badgeConfig.icon}
          <Text size="BodyXS" color={badgeConfig.color} weight="bold">
            {badgeLabel}
          </Text>
        </Stack>
      </Box>
    </Stretch>
  );
};

const CombinedBadgeIcons = () => (
  <Stack direction="row">
    <Box paddingX="1" paddingY="1" backgroundColor="backgroundVarFive" borderRadius="m">
      <Stack>
        <SaveIcon size="small" color="iconElectricity" />
      </Stack>
    </Box>
    <Bleed left="1">
      <Box paddingX="1" paddingY="1" backgroundColor="backgroundVarOne" borderRadius="m">
        <Stack>
          <BoostChargingIcon size="small" color="iconOnBackgroundVarOne" />
        </Stack>
      </Box>
    </Bleed>
  </Stack>
);

export default ChargingModeBadge;
