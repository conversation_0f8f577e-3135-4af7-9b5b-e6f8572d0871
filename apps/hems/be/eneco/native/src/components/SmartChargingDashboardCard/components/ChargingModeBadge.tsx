import { FC } from 'react';

import { useTranslation } from '@i18n';
import { Hems_ChargeStateResponseModel, Hems_v2_SessionInfoResponseModel } from '@monorepo-types/dc';
import { Bleed, Box, Stack } from '@sparky';
import { BoostChargingIcon, HomeIcon, SaveIcon } from '@sparky/icons';

import ChargeModeBadge, { ChargeModeBadgeProps } from '../../../common/ChargeModeBadge';
import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useGetLatestSession from '../../../hooks/useGetLatestSession';
import { getChargeModeLatestSession } from '../../../utils/chargeMode';

type ChargeMode = 'Smart' | 'Boost';

{
  /*TODO 723744 change Home icon here*/
}
const BADGE_CONFIGS: Record<ChargeMode | 'Away', ChargeModeBadgeProps> = {
  Away: {
    icon: <HomeIcon size="small" color="iconPrimary" />,
    color: 'textHighlightVarFour',
    backgroundColor: 'backgroundSecondary',
    label: 'away',
  },
  Smart: {
    icon: <SaveIcon size="small" color="iconElectricity" />,
    color: 'textHighlightVarFour',
    backgroundColor: 'backgroundVarFive',
    label: 'smart',
  },
  Boost: {
    icon: <BoostChargingIcon size="small" color="iconOnBackgroundVarOne" />,
    color: 'textOnBackgroundVarOne',
    backgroundColor: 'backgroundVarOne',
    label: 'boost',
  },
};

const getBadgeConfig = (
  chargeState: Hems_ChargeStateResponseModel,
  chargingStoppedOrCompleted: boolean,
  isChargingAway: boolean,
  isCharging: boolean,
  lastSession: Hems_v2_SessionInfoResponseModel[] | undefined | null,
): ChargeModeBadgeProps | null => {
  if (isChargingAway) {
    return BADGE_CONFIGS.Away;
  }

  if (isCharging) {
    return BADGE_CONFIGS[chargeState.mode as ChargeMode];
  }

  const chargeMode = getChargeModeLatestSession(lastSession?.[0]);
  if (chargingStoppedOrCompleted && chargeMode) {
    if (chargeMode === 'Combined') {
      return null;
    }

    return BADGE_CONFIGS[chargeMode];
  }

  return null;
};

const ChargingModeBadge: FC = () => {
  const { t } = useTranslation();
  const { chargeState } = useSmartChargingContext();
  const lastSession = useGetLatestSession();
  const { awayOrUnknown, isChargingAway, isCharging, hasIntervention, chargingStoppedOrCompleted } =
    useChargeScenarios();

  if (!chargeState || hasIntervention) {
    return null;
  }

  const badgeConfig = getBadgeConfig(chargeState, chargingStoppedOrCompleted, isChargingAway, isCharging, lastSession);

  if (!badgeConfig) {
    const lastSessionIsValid = getChargeModeLatestSession(lastSession?.[0]);
    return lastSessionIsValid && !awayOrUnknown ? <CombinedBadgeIcons /> : null;
  }

  let badgeLabel: string;
  switch (badgeConfig.label) {
    case 'away':
      badgeLabel = t('modeBadge.away');
      break;
    case 'boost':
      badgeLabel = t('modeBadge.boost');
      break;
    case 'smart':
      badgeLabel = t('modeBadge.smart');
      break;
    default:
      badgeLabel = '';
      break;
  }

  return (
    <ChargeModeBadge
      icon={badgeConfig.icon}
      color={badgeConfig.color}
      backgroundColor={badgeConfig.backgroundColor}
      label={badgeLabel}
    />
  );
};

const CombinedBadgeIcons = () => (
  <Stack direction="row">
    <Box paddingX="1" paddingY="1" backgroundColor="backgroundVarFive" borderRadius="m">
      <Stack>
        <SaveIcon size="small" color="iconElectricity" />
      </Stack>
    </Box>
    <Bleed left="1">
      <Box paddingX="1" paddingY="1" backgroundColor="backgroundVarOne" borderRadius="m">
        <Stack>
          <BoostChargingIcon size="small" color="iconOnBackgroundVarOne" />
        </Stack>
      </Box>
    </Bleed>
  </Stack>
);

export default ChargingModeBadge;
