import { FC, useMemo } from 'react';

import SmartChargingProgressBarV2 from '@custom-components/native/hems/SmartChargingProgressBarV2';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';

const ProgressBar: FC = () => {
  const { deviceState, chargeState, intervention } = useSmartChargingContext();

  const progressStatus = useMemo(() => {
    switch (chargeState?.state) {
      case 'Charging':
      case 'ChargingAway':
        return 'charging';
      default:
        return 'active';
    }
  }, [chargeState]);

  const notChargingOrExternally = useMemo(() => {
    return (
      (chargeState?.mode === 'Stopped' && chargeState.state === 'Connected') ||
      !!intervention?.id ||
      chargeState?.state === 'ChargingAway'
    );
  }, [chargeState?.mode, chargeState?.state, intervention]);

  const isFinishFlagHidden = useMemo(() => {
    return chargeState?.state === 'ChargingAway' || !!intervention?.id;
  }, [chargeState?.state, intervention?.id]);

  // Hide progress bar when car is disconnected
  if (!deviceState || !chargeState || chargeState.state === 'Away' || chargeState?.state === 'Unknown') {
    return null;
  }

  return (
    <SmartChargingProgressBarV2
      targetCharge={chargeState?.targetStateOfCharge}
      currentCharge={deviceState.stateOfCharge}
      range={deviceState.range}
      status={progressStatus}
      showProgressBarRemaining={!notChargingOrExternally}
      showFinishFlag={!isFinishFlagHidden}
    />
  );
};

export default ProgressBar;
