import { FC, useCallback } from 'react';

import Logger from '@common/log';
import { useHemsStartSchedule } from '@dc/hooks';
import { useTranslation } from '@i18n';
import { DC_Domain_Models_HEMS_ChargeMode } from '@monorepo-types/dc';
import { Box, Button, Stretch } from '@sparky';
import { SendIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';

const ResumeButton: FC = () => {
  const { chargeState, smartChargingVehicleId, intervention, hemsSessionsDetailsData } = useSmartChargingContext();
  const { t } = useTranslation();

  const { send: sendStart, isSuccess } = useHemsStartSchedule();

  const resumeChargeRequest = useCallback(async () => {
    if (!smartChargingVehicleId) {
      return;
    }

    try {
      const lastKnownMode = hemsSessionsDetailsData?.chargeMode?.[hemsSessionsDetailsData?.chargeMode?.length - 1];

      await sendStart({
        deviceId: smartChargingVehicleId,
        requestBody: { chargeMode: (lastKnownMode as DC_Domain_Models_HEMS_ChargeMode) ?? 'Smart' },
      });
    } catch (e) {
      Logger.error('-145Rr', 'Something went wrong while trying to resume the charging session: ', e);
    }
  }, [smartChargingVehicleId, hemsSessionsDetailsData?.chargeMode, sendStart]);

  // Only render when session is stopped and mode is Smart or Boost
  if (!chargeState || chargeState.mode !== 'Stopped' || isSuccess || chargeState.state === 'Away' || intervention) {
    return;
  }

  return (
    <Box paddingTop="3">
      <Stretch>
        <Button size="compact" onClick={resumeChargeRequest}>
          {/*TODO replace with icon from figma.*/}
          <SendIcon />
          {t('resumeButtonLabel')}
        </Button>
      </Stretch>
    </Box>
  );
};

export default ResumeButton;
