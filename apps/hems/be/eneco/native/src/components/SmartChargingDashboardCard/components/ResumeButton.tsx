import React, { FC, useCallback } from 'react';

import Logger from '@common/log';
import { useHemsStartSchedule } from '@dc/hooks';
import { useTranslation } from '@i18n';
import { DC_Domain_Models_HEMS_ChargeMode } from '@monorepo-types/dc';
import { Box, Button, Stretch } from '@sparky';
import { PlayIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useGetLatestSession from '../../../hooks/useGetLatestSession';
import { getChargeModeLatestSession } from '../../../utils/chargeMode';

const ResumeButton: FC = () => {
  const { chargeState, smartChargingVehicleId, intervention } = useSmartChargingContext();
  const { t } = useTranslation();
  const { chargingStopped, isAway } = useChargeScenarios();
  const latestSession = useGetLatestSession();

  const { send: sendStart, isSuccess } = useHemsStartSchedule();

  const resumeChargeRequest = useCallback(async () => {
    if (!smartChargingVehicleId) {
      return;
    }

    try {
      const chargeModeLastSession = getChargeModeLatestSession(latestSession?.[0]);
      let chargeMode: DC_Domain_Models_HEMS_ChargeMode = 'Smart';
      if (chargeModeLastSession) {
        chargeMode = chargeModeLastSession === 'Combined' ? 'Smart' : chargeModeLastSession;
      }

      await sendStart({
        deviceId: smartChargingVehicleId,
        requestBody: { chargeMode },
      });
    } catch (e) {
      Logger.error('-145Rr', 'Something went wrong while trying to resume the charging session: ', e);
    }
  }, [smartChargingVehicleId, sendStart]);

  // Only render when session is stopped and mode is Smart or Boost
  if (!chargeState || !chargingStopped || isSuccess || isAway || intervention) {
    return;
  }

  return (
    <Box paddingTop="3">
      <Stretch>
        <Button size="compact" onClick={resumeChargeRequest}>
          <PlayIcon color="iconInverted" />
          {t('resumeButtonLabel')}
        </Button>
      </Stretch>
    </Box>
  );
};

export default ResumeButton;
