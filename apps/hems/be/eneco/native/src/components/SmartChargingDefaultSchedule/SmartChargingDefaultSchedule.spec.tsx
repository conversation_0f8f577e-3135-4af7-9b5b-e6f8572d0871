import { screen } from '@testing-library/react';

import {
  useHemsChargesettings,
  useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions,
  useHemsV2GetSessionDetailsV2,
} from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import smartChargingDefaultSchedule from '@mocks/sitecore/apps/hems/be/eneco/native/smartcharging/smartChargingDefaultSchedule';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';
import type { Hems_DeviceStateResponseModel } from '@monorepo-types/dc';
import { SmartChargingDefaultScheduleRendering } from '@sitecore/types/SmartChargingDefaultSchedule';

import SmartChargingDefaultSchedule from './SmartChargingDefaultSchedule';
const { fields } = smartChargingDefaultSchedule[1] as SmartChargingDefaultScheduleRendering;

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useHemsChargesettings: jest.fn().mockReturnValue({
    send: jest.fn(),
    data: {
      chargeModeSettings: [
        {
          targetStateOfCharge: 80,
        },
      ],
      departureTime: '07:00',
    },
    isError: false,
    isSuccess: false,
  }) as unknown as typeof useHemsChargesettings,
  useHemsGetDeviceState: jest.fn().mockReturnValue({
    send: jest.fn(),
    data: {
      deviceType: 'ElectricCar',
      deviceStateInfo: {
        deviceInfo: {
          brand: 'Audi',
          model: 'A3',
          year: 0,
        },
        deviceState: {
          stateOfCharge: 80,
          range: 400,
          lastUpdate: '2024-10-11T09:52:08.110Z',
        },
        chargeState: {
          mode: 'Boost',
          targetStateOfCharge: 0,
          targetEndDate: '2024-10-11T09:52:08.110Z',
          state: 'Charging',
        },
        schedules: [
          {
            day: 'Sunday',
            departureTime: '07:00',
            chargeModeSettings: [
              {
                targetStateOfCharge: 90,
                chargeMode: 'Smart',
              },
            ],
          },
        ],
        overrideSchedule: {
          departureTime: '2024-10-12T09:00:00Z',
          chargeModeSettings: [
            {
              targetStateOfCharge: 100,
              chargeMode: 'Smart',
            },
          ],
        },
      },
    },
    isError: false,
    isSuccess: true,
    isLoading: false,
  }) as unknown as typeof useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions: jest.fn().mockReturnValue({
    data: {
      subscriptions: [
        {
          id: '7f3699ed-5e7b-45f1-31db-08dce9263b1e',
          tier: 'FREE',
          startDate: '2024-10-10',
          endDate: null,
          status: null,
          assets: [
            {
              externalAssetId: '0d889646-937c-4d88-9fdd-1a68cc34c5c1',
              vehicle: {
                brand: 'Audi',
                model: 'A3',
                year: 2022,
                alias: 'My Audi',
              },
            },
          ],
          type: 'SmartCharging',
        },
      ],
    },
    isLoading: false,
  }) as unknown as typeof useSubscriptionsLookupSubscriptions,
  useHemsV2GetSessionDetailsV2: jest.fn().mockReturnValue({
    data: {},
    mutate: jest.fn(),
    isLoading: false,
  }) as unknown as typeof useHemsV2GetSessionDetailsV2,
}));

describe('SmartChargingDefaultSchedule', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render', () => {
    renderApp(SmartChargingDefaultSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-default-schedule',
    });

    expect(screen.getByText(fields.pageTitle.value)).toBeInTheDocument();
  });

  it('should render an error message when there is an error executing the default schedule endpoint', () => {
    (useHemsChargesettings as jest.MockedFunction<typeof useHemsChargesettings>).mockReturnValue({
      send: jest.fn(),
      data: {
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
          },
        ],
        departureTime: '07:00',
      },
      isError: true,
      isSuccess: false,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingDefaultSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-default-schedule',
    });

    expect(screen.getByText(fields.errorNotification.value.title)).toBeInTheDocument();
  });

  it('should render a confirmation error when the default schedule endpoint is successful', () => {
    (useHemsChargesettings as jest.MockedFunction<typeof useHemsChargesettings>).mockReturnValue({
      data: {
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
          },
        ],
        departureTime: '07:00',
      },
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingDefaultSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-default-schedule',
    });

    expect(screen.getByText(fields.successNotificationTitle.value)).toBeInTheDocument();
  });

  it('should render a the OEM charging limit when maxOemStateOfCharge is defined', () => {
    (useHemsGetDeviceState as jest.MockedFunction<typeof useHemsGetDeviceState>).mockReturnValue({
      data: {
        deviceStateInfo: {
          deviceState: {
            maxOemStateOfCharge: 80,
          },
        },
      } as Hems_DeviceStateResponseModel,
      isError: false,
      isSuccess: true,
    } as any); // eslint-disable-line @typescript-eslint/no-explicit-any

    renderApp(SmartChargingDefaultSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-default-schedule',
    });

    expect(screen.getByText(fields.scheduleTargetChargingPercentageHintText.value)).toBeInTheDocument();

    const maxOemPercentageElemenent = screen.queryByTestId('max-oem-percentage');
    expect(maxOemPercentageElemenent?.textContent).toEqual('80%');
  });
});
