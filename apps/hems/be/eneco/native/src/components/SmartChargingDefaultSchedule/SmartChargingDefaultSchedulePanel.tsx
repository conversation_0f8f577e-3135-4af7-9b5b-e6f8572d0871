import { FC, useEffect, useState } from 'react';

import { useForm } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useHemsChargesettings } from '@dc/hooks';
import useDC from '@dc/useDC';
import NativeInputTimePicker from '@native-components/components/NativeInputTimePicker';
import { useContent } from '@sitecore/common';
import { SmartChargingDefaultScheduleRendering } from '@sitecore/types/SmartChargingDefaultSchedule';
import { Box, Button, Heading, InputSelect, NotificationBox, Stack, Stretch, Text } from '@sparky';
import { InfoIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../context/SmartChargingContext';
import useChargeScenarios from '../../hooks/useChargeScenarios';
import { targetOfChargeLevelOptions } from '../../utils/constants';
import { getDefaultScheduleRequest } from '../../utils/defaultScheduleRequestMapper';
import SmartChargingDeviceStaterError from '../SmartChargingDeviceStateError/SmartChargingDeviceStaterError';

type FormValues = {
  departureTime: string;
  targetCharge: string;
};

const SmartChargingDefaultSchedulePanel: FC = () => {
  const { fields } = useContent<SmartChargingDefaultScheduleRendering>();
  const { businessUnit, label, customerId } = useDC();
  const {
    smartChargingVehicleId,
    schedules,
    setUserVisitedStandardSchedule,
    deviceStateError,
    deviceState,
    chargeState,
    isReady,
  } = useSmartChargingContext();
  const { isStoppedOrUnknown } = useChargeScenarios();
  const { send, isError, isLoading, isSuccess } = useHemsChargesettings();

  const [isSuccessNotificationVisible, setSuccessNotificationVisible] = useState<boolean>(false);
  const [isErrorNotificationVisible, setErrorNotificationVisible] = useState<boolean>(false);

  const { register, reset, getValues } = useForm<FormValues>({
    defaultValues: {
      targetCharge: '80',
      departureTime: '07:00',
    },
    shouldFocusError: false,
  });

  useEffect(() => {
    setUserVisitedStandardSchedule();
  }, [setUserVisitedStandardSchedule]);

  useEffect(() => {
    if (schedules?.[0].chargeModeSettings) {
      reset({
        targetCharge: String(schedules[0].chargeModeSettings[0].targetStateOfCharge),
        departureTime: schedules[0].departureTime,
      });
    }
  }, [schedules, reset]);

  useEffect(() => {
    setSuccessNotificationVisible(isSuccess);
  }, [isSuccess]);

  useEffect(() => {
    setErrorNotificationVisible(isError);
  }, [isError]);

  if (!isReady) return;

  const hideNotifications = () => {
    setErrorNotificationVisible(false);
    setSuccessNotificationVisible(false);
  };

  const maxOemStateOfCharge = deviceState?.maxOemStateOfCharge;

  const submitForm = async (departureTime: string, targetChargeInput: string) => {
    if (customerId && smartChargingVehicleId && chargeState?.mode) {
      const targetCharge =
        maxOemStateOfCharge && Number(targetChargeInput) > maxOemStateOfCharge
          ? maxOemStateOfCharge
          : Number(targetChargeInput);

      const requestBody = getDefaultScheduleRequest({
        departureTime,
        targetCharge: Number(targetCharge),
        deviceId: smartChargingVehicleId,
        chargeMode: chargeState.mode,
        isStoppedOrUnknown: isStoppedOrUnknown,
      });

      await send({
        businessUnit: businessUnit,
        label: label,
        customerId,
        requestBody,
      });
    }
  };

  return (
    <Stretch height>
      <Stack>
        <Stack.Item grow>
          <Stack gap="3">
            <Heading as="h4" size="S">
              {fields?.pageTitle.value}
            </Heading>
            {isSuccessNotificationVisible && (
              <Box paddingY="2">
                <NotificationBox
                  isAlert
                  variant="success"
                  title={fields.successNotificationTitle.value}
                  text={fields.successNotificationBodyText.value}
                />
              </Box>
            )}
            {deviceStateError ? (
              <Box paddingY="5">
                <SmartChargingDeviceStaterError />
              </Box>
            ) : (
              <>
                <Text>{fields?.defaultChargeText.value}</Text>
                <Box paddingY="5">
                  <Stack gap="6">
                    {fields?.scheduleTitle.value && (
                      <Heading as="h3" size="XS">
                        {fields?.scheduleTitle.value}
                      </Heading>
                    )}
                    <NativeInputTimePicker
                      {...register('departureTime')}
                      id="departure-time-input"
                      onFocus={() => hideNotifications()}
                      label={fields?.scheduleDailyDepartureTimeText.value}
                    />
                    <InputSelect
                      {...register('targetCharge')}
                      onFocus={() => hideNotifications()}
                      label={fields?.scheduleTargetChargingPercentageText.value}
                      options={targetOfChargeLevelOptions}
                      isOptional={false}
                      placeholder=""
                    />
                    {maxOemStateOfCharge && (
                      <Stack direction="row" gap="2" alignX="justify" alignY="start">
                        <InfoIcon color="iconSecondary" />
                        <Text>{fields?.scheduleTargetChargingPercentageHintText.value}</Text>
                        <Box backgroundColor="feedbackBackgroundInfo" borderRadius="m" padding="3">
                          <Text data-testid="max-oem-percentage">{maxOemStateOfCharge}%</Text>
                        </Box>
                      </Stack>
                    )}
                  </Stack>
                </Box>
              </>
            )}
          </Stack>
        </Stack.Item>
        {!deviceStateError && (
          <NativeBottomNavigationWrapper>
            <Stack gap="4">
              {isErrorNotificationVisible && (
                <NotificationBox
                  isAlert
                  title={fields.errorNotification.value.title}
                  text={<RichText html={fields.errorNotification.value.content} />}
                  variant={fields.errorNotification.value.variant}
                />
              )}
              <Button
                type="button"
                size="compact"
                onClick={() => submitForm(getValues('departureTime'), getValues('targetCharge'))}
                isLoading={isLoading}>
                {fields?.scheduleButtonText.value}
              </Button>
            </Stack>
          </NativeBottomNavigationWrapper>
        )}
      </Stack>
    </Stretch>
  );
};

export default SmartChargingDefaultSchedulePanel;
