import { screen } from '@testing-library/react';

import {
  useHemsV2GetSessionDetailsV2,
  useHemsV2GetSessionsAggregateV2,
  useSubscriptionsLookupSubscriptions,
} from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import { SmartChargingInsights } from '@mocks/sitecore/apps/hems/be/eneco/native/smartcharging/SmartChargingInsights';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';
import { Hems_SessionInfoAggregatedResponseModel, Hems_SessionInfoResponseModel } from '@monorepo-types/dc';

import SmartChargingInsightsComponent from './SmartChargingInsights';

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useSubscriptionsLookupSubscriptions: jest.fn() as unknown as typeof useSubscriptionsLookupSubscriptions,
  useHemsV2GetSessionsAggregateV2: jest.fn() as unknown as typeof useHemsV2GetSessionsAggregateV2,
  useHemsV2GetSessionDetailsV2: jest.fn().mockReturnValue({
    data: {},
    mutate: jest.fn(),
    isLoading: false,
  }) as unknown as typeof useHemsV2GetSessionDetailsV2,
}));

const mockUseSWRImmutable = (
  data:
    | { sessionAggregates: Hems_SessionInfoAggregatedResponseModel[] }
    | { sessions: Hems_SessionInfoResponseModel[] },
) => {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  jest.spyOn(require('swr'), 'default').mockImplementation(() => ({ data }));
};

describe('SmartChargingInsights', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useSubscriptionsLookupSubscriptions as jest.Mock).mockReturnValue({
      data: {
        subscriptions: [
          {
            id: '5ba3b360-c004-41bd-4d4a-08dd08730ead',
            tier: 'Free',
            type: 'SmartCharging',
            startDate: '2024-11-22',
            endDate: null,
            status: 'LinkCompleted',
            assets: [
              {
                externalAssetId: '351b08be-8251-4e6c-9a73-026973ed1f43',
                vehicle: {
                  brand: 'Kia',
                  model: 'EV6',
                  year: 2022,
                  alias: 'My Kia',
                },
              },
            ],
          },
        ],
      },
    });

    (useHemsV2GetSessionsAggregateV2 as jest.Mock).mockReturnValue({
      send: jest.fn().mockResolvedValue({
        sessionAggregates: [
          {
            fromDate: '2025-01-01',
            toDate: '2025-01-31',
            contractType: 'Fixed',
            totalCost: 2.24,
            totalCharged: 15.89,
            smartCharged: 0.0,
            totalEstimatedSavings: 0.0,
            totalActualSavings: 0.0,
            numberOfSessions: 8,
            totalCostPerUnit: {
              amount: 0.15043653458697112,
              unitOfMeasurement: 'kWh',
              currency: 'EUR',
            },
            unitOfMeasurement: 'kWh',
          },
        ],
      }),
    });
  });

  it('should render', async () => {
    await renderApp(SmartChargingInsightsComponent, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-insights',
    });

    expect(screen.getByText(SmartChargingInsights?.fields.yearLabel?.value, { exact: true })).toBeInTheDocument();
  });

  it('should render the SmartChargingInsightsSummary component when there is aggregate session data', async () => {
    await renderApp(SmartChargingInsightsComponent, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-insights',
    });

    expect(await screen.findByText(SmartChargingInsights.fields.totalLoadedLabel.value)).toBeVisible();
    expect(await screen.findByText(SmartChargingInsights.fields.totalCostLabel.value)).toBeVisible();
    expect(await screen.findByText(SmartChargingInsights.fields.totalSavedLabel.value)).toBeVisible();
  });

  it('should NOT render the SmartChargingInsightsSummary session items when there is NO aggregate session data', async () => {
    mockUseSWRImmutable({
      sessionAggregates: [],
    });

    await renderApp(SmartChargingInsightsComponent, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-insights',
    });

    await expect(
      screen.findByText(SmartChargingInsights.fields.totalLoadedLabel.value, {}, { timeout: 10 }),
    ).rejects.toThrow();
    await expect(
      screen.findByText(SmartChargingInsights.fields.totalCostLabel.value, {}, { timeout: 10 }),
    ).rejects.toThrow();
    await expect(
      screen.findByText(SmartChargingInsights.fields.totalSavedLabel.value, {}, { timeout: 10 }),
    ).rejects.toThrow();
  });

  it('should NOT render the SmartChargingInsightsSessionTiles session items when there is no sessions data', async () => {
    mockUseSWRImmutable({
      sessions: [],
    });

    await renderApp(SmartChargingInsightsComponent, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-insights',
    });

    await expect(screen.findByText(SmartChargingInsights.fields.endLabel.value, {}, { timeout: 10 })).rejects.toThrow();
    await expect(
      screen.findByText(SmartChargingInsights.fields.rewardsLabel.value, {}, { timeout: 10 }),
    ).rejects.toThrow();
  });

  it('should NOT render the SmartChargingAggregatesTiles session items when there is no sessions data', async () => {
    mockUseSWRImmutable({
      sessions: [],
    });

    await renderApp(SmartChargingInsightsComponent, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-insights',
    });

    await expect(screen.findByText('€', {}, { timeout: 10 })).rejects.toThrow();
    await expect(screen.findByText('kWh', {}, { timeout: 10 })).rejects.toThrow();
  });

  it('should render the SmartChargingInsightsDateNavigator', async () => {
    await renderApp(SmartChargingInsightsComponent, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-insights',
    });

    expect(
      await screen.findByRole('button', { name: SmartChargingInsights.fields.previousButtonLabel.value }),
    ).toBeVisible();
  });
});
