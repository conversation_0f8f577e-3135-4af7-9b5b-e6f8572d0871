import { I18nProvider } from '@i18n';

import SmartChargingOverrideDepartureTimeSchedulePanel from './components/SmartChargingOverrideDepartureTimeSchedulePanel';
import { SmartChargingProvider } from '../../context/SmartChargingContext';

const SmartChargingOverrideDepartureTimeSchedule = () => {
  return (
    <I18nProvider dictionary={locale => import(`../../content/${locale}.json`)}>
      <SmartChargingProvider>
        <SmartChargingOverrideDepartureTimeSchedulePanel />
      </SmartChargingProvider>
    </I18nProvider>
  );
};

export default SmartChargingOverrideDepartureTimeSchedule;
