import { FC } from 'react';

import { useTranslation } from '@i18n';
import { DC_Domain_Models_HEMS_ChargeStateNotification } from '@monorepo-types/dc';
import { Stack, Text } from '@sparky';
import { BoostChargingIcon, SaveIcon } from '@sparky/icons';

import ChargeModeBadge from '../../../common/ChargeModeBadge';
import useChargeScenarios from '../../../hooks/useChargeScenarios';

interface Props {
  sessionNotFeasibleNotification: DC_Domain_Models_HEMS_ChargeStateNotification | undefined;
}

const ChargingModeBadge: FC<Props> = ({ sessionNotFeasibleNotification }) => {
  const { t } = useTranslation();
  const { isBoost } = useChargeScenarios();

  if (sessionNotFeasibleNotification && isBoost) {
    return (
      <Stack direction="row" alignY="center" gap="1">
        <Text>{t('overrideDepartureTimeSchedule.from')}</Text>
        <ChargeModeBadge
          icon={<BoostChargingIcon color="iconOnBackgroundVarOne" size="small" />}
          color="textOnBackgroundVarOne"
          backgroundColor="backgroundVarOne"
          label="Boost"
        />
        <Text>{t('overrideDepartureTimeSchedule.to')}</Text>
        <ChargeModeBadge
          icon={<SaveIcon color="iconElectricity" size="small" />}
          color="textHighlightVarFour"
          backgroundColor="backgroundVarFive"
          label="Slim"
        />
      </Stack>
    );
  }

  if (isBoost) {
    return (
      <ChargeModeBadge
        icon={<BoostChargingIcon color="iconOnBackgroundVarOne" size="small" />}
        color="textOnBackgroundVarOne"
        backgroundColor="backgroundVarOne"
        label="Boost"
      />
    );
  }

  return (
    <ChargeModeBadge
      icon={<SaveIcon color="iconElectricity" size="small" />}
      color="textHighlightVarFour"
      backgroundColor="backgroundVarFive"
      label="Slim"
    />
  );
};

export default ChargingModeBadge;
