import { FC, PropsWithChildren } from 'react';

import RichText from '@components/RichText/RichText';
import { useTranslation } from '@i18n';
import { Box, Button, ButtonLink, NotificationBox, Stack } from '@sparky';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';

export interface SmartChargingOverrideDepartureTimeScheduleButtonsProps {
  overrideChanges: () => void;
  hasError: boolean;
}

const SmartChargingOverrideDepartureTimeScheduleButtons: FC<
  PropsWithChildren<SmartChargingOverrideDepartureTimeScheduleButtonsProps>
> = ({ overrideChanges, hasError }) => {
  const { notification: sessionNotFeasibleNotification } = useSmartChargingContext();
  const { isBoost } = useChargeScenarios();
  const { t } = useTranslation();
  return (
    <Box>
      <Stack gap="5">
        {hasError && (
          <NotificationBox
            isAlert
            title={t('overrideDepartureTimeSchedule.errorNotification.title')}
            text={<RichText html={t('overrideDepartureTimeSchedule.errorNotification.content')} />}
            variant="error"
            closeable
          />
        )}
        <Button onClick={overrideChanges} size="compact">
          {t('overrideDepartureTimeSchedule.saveButton')}
        </Button>
        {sessionNotFeasibleNotification && isBoost && (
          <ButtonLink href={t('evPageHref')} size="compact" action="secondary">
            {t('overrideDepartureTimeSchedule.cancelButton')}
          </ButtonLink>
        )}
      </Stack>
    </Box>
  );
};

export default SmartChargingOverrideDepartureTimeScheduleButtons;
