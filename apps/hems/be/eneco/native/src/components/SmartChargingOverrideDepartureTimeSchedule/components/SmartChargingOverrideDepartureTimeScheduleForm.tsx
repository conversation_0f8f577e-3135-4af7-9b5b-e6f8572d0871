import { FC, PropsWithChildren } from 'react';

import { formatISO, add } from 'date-fns';
import { FieldErrors, UseFormRegister } from 'react-hook-form';

import { capitalizeFirstLetter } from '@common/string';
import RichText from '@components/RichText/RichText';
import { useFormatter, useTranslation } from '@i18n';
import NativeInputTimePicker from '@native-components/components/NativeInputTimePicker';
import { Stack, InputSelect, Box, Heading, NotificationBox, TextLink, Text } from '@sparky';

import ChargingModeBadge from './ChargingModeBadge';
import { FormValues } from './SmartChargingOverrideDepartureTimeSchedulePanel';
import { useSmartChargingContext } from '../../../context/SmartChargingContext';

export interface SmartChargingOverrideDepartureTimeScheduleFormProps {
  departureDay: string;
  departureTime: string;
  setDepartureDay: (newState: string) => void;
  setDepartureTime: (newState: string) => void;
  hasSuccess: boolean;
  register: UseFormRegister<FormValues>;
  errors: FieldErrors<FormValues>;
}

const SmartChargingOverrideDepartureTimeScheduleForm: FC<
  PropsWithChildren<SmartChargingOverrideDepartureTimeScheduleFormProps>
> = ({ departureTime, setDepartureTime, departureDay, setDepartureDay, hasSuccess, register, errors }) => {
  const { overrideSchedule, notification: sessionNotFeasibleNotification } = useSmartChargingContext();
  const { t } = useTranslation();
  const { date } = useFormatter();

  const departureDateOptions = [
    {
      label: capitalizeFirstLetter(date.dayOfWeekLong(new Date())),
      value: formatISO(Date.now(), { representation: 'date' }),
    },
    {
      label: capitalizeFirstLetter(date.dayOfWeekLong(add(Date.now(), { days: 1 }))),
      value: formatISO(add(Date.now(), { days: 1 }), { representation: 'date' }),
    },
  ];

  return (
    <Box paddingX="2">
      <Stack gap="5">
        <Heading as="h4" size="S">
          {t('overrideDepartureTimeSchedule.targetDepartureTimeTitle')}
        </Heading>
        <ChargingModeBadge sessionNotFeasibleNotification={sessionNotFeasibleNotification} />
        {hasSuccess && (
          <NotificationBox
            isAlert
            title={t('overrideDepartureTimeSchedule.successNotification.title')}
            text={<RichText html={t('overrideDepartureTimeSchedule.successNotification.content')} />}
            variant="success"
          />
        )}
        <>
          <InputSelect
            placeholder=""
            value={departureDay}
            options={departureDateOptions}
            label={t('overrideDepartureTimeSchedule.departureDayLabel')}
            name="departure-date"
            onChange={event => setDepartureDay(event.target.value)}
          />
          <NativeInputTimePicker
            {...register('formDepartureTime')}
            id="departure-time"
            label={t('overrideDepartureTimeSchedule.departureTimeLabel')}
            name="departure-time"
            value={departureTime}
            onChange={event => {
              setDepartureTime(event.target.value);
            }}
            error={errors?.formDepartureTime?.message && t('overrideDepartureTimeSchedule.errorDepartureTimeText')}
          />
        </>
        {overrideSchedule ? (
          <Stack gap="5">
            <Text>{t('overrideDepartureTimeSchedule.restoreDefaultScheduleRemark')}</Text>
            <TextLink
              onClick={() => {
                /*TODO find way to restore departuretime override 725109*/
              }}>
              {t('overrideDepartureTimeSchedule.restoreDefaultScheduleLabel')}
            </TextLink>
          </Stack>
        ) : (
          <TextLink href={t('overrideDepartureTimeSchedule.updateDefaultScheduleHref')}>
            {t('overrideDepartureTimeSchedule.updateDefaultScheduleLabel')}
          </TextLink>
        )}
      </Stack>
    </Box>
  );
};

export default SmartChargingOverrideDepartureTimeScheduleForm;
