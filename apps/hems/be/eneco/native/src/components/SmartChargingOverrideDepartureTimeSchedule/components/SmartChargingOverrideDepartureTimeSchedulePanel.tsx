import { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { format, formatISO, isBefore, isAfter, subMinutes } from 'date-fns';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { Stack, Stretch } from '@sparky';

import SmartChargingOverrideDepartureTimeScheduleButtons from './SmartChargingOverrideDepartureTimeScheduleButtons';
import SmartChargingOverrideDepartureTimeScheduleForm from './SmartChargingOverrideDepartureTimeScheduleForm';
import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useOverrideSchedule from '../../../hooks/useOverrideSchedule';

export type FormValues = {
  formDepartureTime: string;
};

const SmartChargingOverrideDepartureTimeSchedulePanel: FC = () => {
  const { schedules, overrideSchedule } = useSmartChargingContext();
  const { isBoost } = useChargeScenarios();
  const { overrideDepartureTimeSchedule, isSuccessChargeSettingsOverride, isErrorChargeSettingsOverride } =
    useOverrideSchedule();

  const [departureDay, setDepartureDay] = useState<string>(formatISO(new Date(), { representation: 'date' }));
  const [departureTime, setDepartureTime] = useState<string>('07:30');

  const [targetSoc, setTargetSoc] = useState<number>(80);
  const [hasPostError, setHasPostError] = useState(false);
  const [hasPostSuccess, setHasPostSuccess] = useState(false);

  useEffect(() => {
    function setFormValues() {
      if (overrideSchedule?.chargeModeSettings && overrideSchedule.departureTime) {
        const overrideDepartureDay = formatISO(overrideSchedule.departureTime, { representation: 'date' });
        const now = formatISO(new Date(), { representation: 'date' });
        if (isBefore(overrideDepartureDay, now)) {
          setDepartureDay(now);
        } else {
          setDepartureDay(overrideDepartureDay);
        }
        setDepartureTime(format(overrideSchedule.departureTime, 'HH:mm'));
        setTargetSoc(overrideSchedule.chargeModeSettings[0].targetStateOfCharge);
      } else if (schedules?.[0]?.chargeModeSettings) {
        setDepartureTime(schedules[0].departureTime);
        setTargetSoc(Math.floor(schedules[0].chargeModeSettings[0].targetStateOfCharge / 10) * 10);
      }
    }

    setFormValues();
  }, [schedules, overrideSchedule]);

  useEffect(() => {
    setHasPostError(isErrorChargeSettingsOverride);
  }, [isErrorChargeSettingsOverride]);

  useEffect(() => {
    setHasPostSuccess(isSuccessChargeSettingsOverride);
  }, [isSuccessChargeSettingsOverride]);

  const currentScheduleSchema = yup.object(
    isBoost
      ? {}
      : {
          formDepartureTime: yup.string().test({
            name: 'formDepartureTime',
            message: 'formDepartureTimeNotValid',
            test() {
              const minDepartureTimestampUTC = subMinutes(new Date(`${departureDay}T${departureTime}`), 30);
              const timestampNowUTC = new Date();

              return isAfter(minDepartureTimestampUTC, timestampNowUTC);
            },
          }),
        },
  );

  const resolver = yupResolver(currentScheduleSchema);

  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm<FormValues>({ resolver });

  return (
    <Stretch height>
      <Stack direction="column">
        <Stack.Item grow>
          <SmartChargingOverrideDepartureTimeScheduleForm
            departureDay={departureDay}
            setDepartureDay={setDepartureDay}
            departureTime={departureTime}
            setDepartureTime={setDepartureTime}
            hasSuccess={hasPostSuccess}
            register={register}
            errors={errors}
          />
        </Stack.Item>
        <NativeBottomNavigationWrapper>
          <SmartChargingOverrideDepartureTimeScheduleButtons
            overrideChanges={handleSubmit(() => overrideDepartureTimeSchedule(targetSoc, departureDay, departureTime))}
            hasError={hasPostError}
          />
        </NativeBottomNavigationWrapper>
      </Stack>
    </Stretch>
  );
};

export default SmartChargingOverrideDepartureTimeSchedulePanel;
