import { screen } from '@testing-library/react';

import { useHemsGetDeviceState, useHemsV2GetSessionDetailsV2 } from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights';

import SmartChargingOverrideLoadingTargetSchedule from './SmartChargingOverrideLoadingTargetSchedule';
import copy from '../../content/nl-BE.json';

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useHemsGetDeviceState: jest.fn() as unknown as typeof useHemsGetDeviceState,
  useHemsChargesettingsOverride: jest.fn().mockReturnValue({
    send: jest.fn(),
    data: {
      departureTime: '2024-09-26T09:29:35.832Z',
      chargeModeSettings: [
        {
          targetStateOfCharge: 80,
          chargeMode: 'Smart',
        },
      ],
    },
    isError: false,
    isSuccess: false,
  }),
  useHemsV2GetSessionDetailsV2: jest.fn().mockReturnValue({
    data: {},
    mutate: jest.fn(),
    isLoading: false,
  }) as unknown as typeof useHemsV2GetSessionDetailsV2,
}));

describe('SmartChargingOverrideDepartureTimeSchedule', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {
          deviceInfo: {
            brand: 'Audi',
            model: 'A3',
            year: 0,
          },
          deviceState: {
            stateOfCharge: 80,
            range: 400,
            lastUpdate: '2024-10-11T09:52:08.110Z',
          },
          chargeState: {
            mode: 'Smart',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
          },
          schedules: [
            {
              day: 'Sunday',
              departureTime: '07:00',
              chargeModeSettings: [
                {
                  targetStateOfCharge: 90,
                  chargeMode: 'Smart',
                },
              ],
            },
          ],
          overrideSchedule: null,
        },
      },
    });
  });

  it('should render', async () => {
    renderApp(SmartChargingOverrideLoadingTargetSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-override-loading-target-schedule',
    });

    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeTitle)).toBeInTheDocument();
  });

  it('should render the default override screen when the user is charging (mode: Smart)', async () => {
    renderApp(SmartChargingOverrideLoadingTargetSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-override-loading-target-schedule',
    });

    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeTitle)).toBeInTheDocument();
    expect(await screen.findByText(copy.modeBadge.smart)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeLabel)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.saveButton)).toBeInTheDocument();
  });

  it('should show the user that they will go from boost to smart if their new schedule is feasible', async () => {
    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {
          deviceInfo: {
            brand: 'Audi',
            model: 'A3',
            year: 0,
          },
          deviceState: {
            stateOfCharge: 80,
            range: 400,
            lastUpdate: '2024-10-11T09:52:08.110Z',
          },
          chargeState: {
            mode: 'Boost',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
            notification: {
              description: '',
              notificationType: 'Error',
            },
          },
          schedules: [
            {
              day: 'Sunday',
              departureTime: '07:00',
              chargeModeSettings: [
                {
                  targetStateOfCharge: 90,
                  chargeMode: 'Smart',
                },
              ],
            },
          ],
          overrideSchedule: null,
        },
      },
    });
    renderApp(SmartChargingOverrideLoadingTargetSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-override-loading-target-schedule',
    });

    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeTitle)).toBeInTheDocument();
    expect(await screen.findByText(copy.modeBadge.smart)).toBeInTheDocument();
    expect(await screen.findByText(copy.modeBadge.boost)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeLabel)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.saveButton)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.saveButton)).toBeInTheDocument();
  });

  it('should show the override page when the user already has done a previous override', async () => {
    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {
          deviceInfo: {
            brand: 'Audi',
            model: 'A3',
            year: 0,
          },
          deviceState: {
            stateOfCharge: 80,
            range: 400,
            lastUpdate: '2024-10-11T09:52:08.110Z',
          },
          chargeState: {
            mode: 'Smart',
            targetStateOfCharge: 0,
            targetEndDate: '2024-10-11T09:52:08.110Z',
            state: 'Charging',
          },
          schedules: [
            {
              day: 'Sunday',
              departureTime: '07:00',
              chargeModeSettings: [
                {
                  targetStateOfCharge: 90,
                  chargeMode: 'Smart',
                },
              ],
            },
          ],
          overrideSchedule: {
            departureTime: '2025-02-04T17:00:00.000Z',
            chargeModeSettings: [
              {
                targetStateOfCharge: 90,
                chargeMode: 'Smart',
              },
            ],
          },
        },
      },
    });
    renderApp(SmartChargingOverrideLoadingTargetSchedule, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev/smartcharging-override-loading-target-schedule',
    });

    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeTitle)).toBeInTheDocument();
    expect(await screen.findByText(copy.modeBadge.smart)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.targetStateOfChargeLabel)).toBeInTheDocument();
    expect(
      await screen.findByText(copy.overrideLoadingTargetSchedule.restoreDefaultScheduleRemark),
    ).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.restoreDefaultScheduleLabel)).toBeInTheDocument();
    expect(await screen.findByText(copy.overrideLoadingTargetSchedule.saveButton)).toBeInTheDocument();
  });
});
