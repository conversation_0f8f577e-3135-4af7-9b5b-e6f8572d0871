import { I18nProvider } from '@i18n';

import SmartChargingOverrideLoadingTargetSchedulePanel from './components/SmartChargingOverrideLoadingTargetSchedulePanel';
import { SmartChargingProvider } from '../../context/SmartChargingContext';

const SmartChargingOverrideLoadingTargetSchedule = () => {
  return (
    <I18nProvider dictionary={locale => import(`../../content/${locale}.json`)}>
      <SmartChargingProvider>
        <SmartChargingOverrideLoadingTargetSchedulePanel />
      </SmartChargingProvider>
    </I18nProvider>
  );
};

export default SmartChargingOverrideLoadingTargetSchedule;
