import { FC } from 'react';

import { useTranslation } from '@i18n';
import { Box, Card, Heading, InputSelect, Stack, Text, TextLink } from '@sparky';
import { InfoIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useOverrideSchedule from '../../../hooks/useOverrideSchedule';
import { targetOfChargeLevelOptions } from '../../../utils/constants';
import ChargingModeBadge from '../../SmartChargingOverrideDepartureTimeSchedule/components/ChargingModeBadge';

interface Props {
  targetSoc: number;
  setTargetSoc: (newState: number) => void;
}

const SmartChargingOverrideLoadingTargetScheduleForm: FC<Props> = ({ targetSoc, setTargetSoc }) => {
  const { t } = useTranslation();
  const { overrideSchedule, chargeState, deviceState } = useSmartChargingContext();
  const { resetLoadingTargetOverride } = useOverrideSchedule();
  const maxOemStateOfCharge = deviceState?.maxOemStateOfCharge;

  return (
    <Stack gap="5">
      <Heading as="h4" size="S">
        {t('overrideLoadingTargetSchedule.targetStateOfChargeTitle')}
      </Heading>
      <ChargingModeBadge sessionNotFeasibleNotification={chargeState?.notification} />
      <InputSelect
        placeholder=""
        value={targetSoc}
        options={targetOfChargeLevelOptions}
        label={t('overrideLoadingTargetSchedule.targetStateOfChargeLabel')}
        name="target-of-charge-percentage"
        onChange={event => setTargetSoc(Number(event.target.value))}
      />
      {maxOemStateOfCharge && (
        <Card>
          <Box padding="4">
            <Stack direction="row" gap="2" alignX="justify" alignY="start">
              <InfoIcon color="iconSecondary" />
              <Text>{t('overrideLoadingTargetSchedule.maxOemChargeLimitWarningLabel')}</Text>
              <Box backgroundColor="feedbackBackgroundInfo" borderRadius="m" padding="3">
                <Text data-testid="max-oem-percentage">{maxOemStateOfCharge}%</Text>
              </Box>
            </Stack>
          </Box>
        </Card>
      )}
      {overrideSchedule &&
      overrideSchedule.chargeModeSettings?.[0]?.targetStateOfCharge !== chargeState?.targetStateOfCharge ? (
        <Stack gap="5">
          <Text>{t('overrideLoadingTargetSchedule.restoreDefaultScheduleRemark')}</Text>
          <TextLink onClick={() => resetLoadingTargetOverride()}>
            {t('overrideLoadingTargetSchedule.restoreDefaultScheduleLabel')}
          </TextLink>
        </Stack>
      ) : (
        <TextLink href={t('overrideLoadingTargetSchedule.updateTargetStateOfChargeHref')}>
          {t('overrideLoadingTargetSchedule.updateTargetStateOfChargeLabel')}
        </TextLink>
      )}
    </Stack>
  );
};

export default SmartChargingOverrideLoadingTargetScheduleForm;
