import { useState } from 'react';

import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useTranslation } from '@i18n';
import { Button, ButtonLink, Stack, Stretch } from '@sparky';

import SmartChargingOverrideLoadingTargetScheduleForm from './SmartChargingOverrideLoadingTargetScheduleForm';
import { useSmartChargingContext } from '../../../context/SmartChargingContext';
import useChargeScenarios from '../../../hooks/useChargeScenarios';
import useOverrideSchedule from '../../../hooks/useOverrideSchedule';

const SmartChargingOverrideLoadingTargetSchedulePanel = () => {
  const { t } = useTranslation();

  const { chargeState } = useSmartChargingContext();
  const { isBoost } = useChargeScenarios();
  const { overrideLoadingTargetSchedule } = useOverrideSchedule();

  const [targetSoc, setTargetSoc] = useState<number>(80);

  return (
    <Stretch height>
      <Stack>
        <Stack.Item grow>
          <SmartChargingOverrideLoadingTargetScheduleForm targetSoc={targetSoc} setTargetSoc={setTargetSoc} />
        </Stack.Item>
        <NativeBottomNavigationWrapper>
          <Stack gap="5">
            <Button onClick={() => overrideLoadingTargetSchedule(targetSoc)} size="compact">
              {t('overrideLoadingTargetSchedule.saveButton')}
            </Button>
            {chargeState?.notification && isBoost && (
              <ButtonLink href={t('evPageHref')} size="compact" action="secondary">
                {t('overrideLoadingTargetSchedule.cancelButton')}
              </ButtonLink>
            )}
          </Stack>
        </NativeBottomNavigationWrapper>
      </Stack>
    </Stretch>
  );
};

export default SmartChargingOverrideLoadingTargetSchedulePanel;
