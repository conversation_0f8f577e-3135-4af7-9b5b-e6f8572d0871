import { screen } from '@testing-library/react';

import renderApp from '@jest-tools/renderApp';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights';

import SmartChargingSchedulePreferences from './SmartChargingSchedulePreferences';
import copy from '../../content/nl-BE.json';
import { useSmartChargingContext } from '../../context/SmartChargingContext';

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('../../context/SmartChargingContext', () => ({
  ...jest.requireActual('../../context/SmartChargingContext'),
  useSmartChargingContext: jest.fn().mockReturnValue({
    schedules: [
      {
        day: 'Sunday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
      {
        day: 'Monday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
      {
        day: 'Tuesday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
      {
        day: 'Wednesday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
      {
        day: 'Thursday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
      {
        day: 'Friday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
      {
        day: 'Saturday',
        departureTime: '07:00:00',
        chargeModeSettings: [
          {
            targetStateOfCharge: 80,
            chargeMode: 'Smart',
          },
        ],
      },
    ],
  }),
}));

describe('SmartChargingSchedulePreferences', () => {
  it('should render', () => {
    renderApp(SmartChargingSchedulePreferences, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });
  });

  it('should render the default BTM smart charging target state of charge and departure time when it has not been customized', async () => {
    renderApp(SmartChargingSchedulePreferences, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(await screen.findByText(copy.preferences.title)).toBeInTheDocument();

    expect(await screen.findByText(copy.preferences.chargeTarget)).toBeInTheDocument();
    expect(await screen.findByText('80%')).toBeInTheDocument();

    expect(await screen.findByText(copy.preferences.departureTime)).toBeInTheDocument();
    expect(await screen.findByText('07:00')).toBeInTheDocument();
  });

  it('should render the smart charging target state of charge and departure time when it has been set the day', async () => {
    (useSmartChargingContext as jest.Mock).mockReturnValue({
      schedules: [
        {
          day: 'Sunday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Monday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Tuesday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Wednesday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Thursday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Friday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Saturday',
          departureTime: '06:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 95,
              chargeMode: 'Smart',
            },
          ],
        },
      ],
    });

    renderApp(SmartChargingSchedulePreferences, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(await screen.findByText(copy.preferences.title)).toBeInTheDocument();

    expect(await screen.findByText(copy.preferences.chargeTarget)).toBeInTheDocument();
    expect(await screen.findByText('95%')).toBeInTheDocument();

    expect(await screen.findByText(copy.preferences.departureTime)).toBeInTheDocument();
    expect(await screen.findByText('06:30')).toBeInTheDocument();
  });

  it('should render the target soc and multiple departure times when they have been set differently for several days', async () => {
    (useSmartChargingContext as jest.Mock).mockReturnValue({
      schedules: [
        {
          day: 'Sunday',
          departureTime: '21:45:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Monday',
          departureTime: '07:00:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Tuesday',
          departureTime: '08:00:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Wednesday',
          departureTime: '07:00:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Thursday',
          departureTime: '07:00:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Friday',
          departureTime: '13:30:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
        {
          day: 'Saturday',
          departureTime: '07:00:00',
          chargeModeSettings: [
            {
              targetStateOfCharge: 90,
              chargeMode: 'Smart',
            },
          ],
        },
      ],
    });

    renderApp(SmartChargingSchedulePreferences, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(await screen.findByText(copy.preferences.title)).toBeInTheDocument();

    expect(await screen.findByText(copy.preferences.chargeTarget)).toBeInTheDocument();
    expect(await screen.findByText('90%')).toBeInTheDocument();

    expect(await screen.findByText(copy.preferences.departureTime)).toBeInTheDocument();
    expect(await screen.findByText('07:00, 08:00, 13:30, 21:45')).toBeInTheDocument();
  });

  it('should render the OEM charge limit when user has no schedule', async () => {
    (useSmartChargingContext as jest.Mock).mockReturnValue({
      deviceState: {
        stateOfCharge: 80,
        range: 400,
        lastUpdate: '2024-10-11T09:52:08.110Z',
        maxOemStateOfCharge: 85,
      },
      schedules: [],
    });

    renderApp(SmartChargingSchedulePreferences, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(await screen.findByText(copy.preferences.chargeTarget)).toBeInTheDocument();
    expect(await screen.findByText('85%')).toBeInTheDocument();
  });

  it('should render the default BTM charge limit when user has no schedule and there is no OEM limit', async () => {
    (useSmartChargingContext as jest.Mock).mockReturnValue({
      deviceState: {
        stateOfCharge: 80,
        range: 400,
        lastUpdate: '2024-10-11T09:52:08.110Z',
        maxOemStateOfCharge: undefined,
      },
      schedules: [],
    });

    renderApp(SmartChargingSchedulePreferences, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(await screen.findByText(copy.preferences.chargeTarget)).toBeInTheDocument();
    expect(await screen.findByText('95%')).toBeInTheDocument();
  });
});
