import { I18nProvider } from '@i18n';

import SmartChargingSchedulePreferencesPanel from './SmartChargingSchedulePreferencesPanel';
import { SmartChargingProvider } from '../../context/SmartChargingContext';

const SmartChargingSchedulePreferences = () => {
  return (
    <I18nProvider dictionary={locale => import(`../../content/${locale}.json`)}>
      <SmartChargingProvider>
        <SmartChargingSchedulePreferencesPanel />
      </SmartChargingProvider>
    </I18nProvider>
  );
};

export default SmartChargingSchedulePreferences;
