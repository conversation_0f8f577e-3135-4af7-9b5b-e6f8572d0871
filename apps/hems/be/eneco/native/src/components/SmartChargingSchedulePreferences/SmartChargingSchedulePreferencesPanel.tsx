import { useMemo } from 'react';

import { useTranslation } from '@i18n';
import ListTileItem from '@native-components/components/ListTileItem';
import { Card, Heading, Stack } from '@sparky';
import { FlagIcon, SaveIcon } from '@sparky/icons';

import { useSmartChargingContext } from '../../context/SmartChargingContext';

const SmartChargingSchedulePreferencesPanel = () => {
  const { t } = useTranslation();
  const { schedules, deviceState } = useSmartChargingContext();

  const departureTimes = useMemo(() => {
    if (!schedules) {
      return undefined;
    }
    const timeStamps: string[] = [];

    schedules.forEach(schedule => {
      if (timeStamps.includes(schedule.departureTime.slice(0, 5))) {
        return;
      }
      timeStamps.push(schedule.departureTime.slice(0, 5));
    });

    return timeStamps.sort().join(', ');
  }, [schedules]);

  const targetStateOfCharge = schedules?.[0]?.chargeModeSettings?.[0].targetStateOfCharge;
  const maxOemStateOfCharge = deviceState?.maxOemStateOfCharge;
  let chargeTargetValue: number;

  if (targetStateOfCharge) {
    chargeTargetValue = targetStateOfCharge;
  } else if (maxOemStateOfCharge) {
    chargeTargetValue = maxOemStateOfCharge;
  } else {
    chargeTargetValue = 95;
  }

  return (
    <Stack gap="3">
      <Heading as="h2" size="3XS">
        {t('preferences.title')}
      </Heading>
      <Card>
        <ListTileItem
          label={t('preferences.chargeTarget')}
          href={t('preferences.chargeTargetHref')}
          lowerLabel={`${chargeTargetValue}%`}
          categoryIcon={<FlagIcon size="medium" />}
        />
        <ListTileItem
          label={t('preferences.departureTime')}
          href={t('preferences.departureTimeHref')}
          lowerLabel={departureTimes}
          categoryIcon={<SaveIcon size="medium" />}
        />
      </Card>
    </Stack>
  );
};

export default SmartChargingSchedulePreferencesPanel;
