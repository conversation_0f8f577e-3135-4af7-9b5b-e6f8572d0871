import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import {
  useCustomerGetCustomerProfile,
  useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions,
  useHemsToggleSchedule,
  useHemsV2GetSessionDetailsV2,
} from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import smartChargingSchedulingToggle from '@mocks/sitecore/apps/hems/be/eneco/native/smartcharging/smartChargingSchedulingToggle';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';

import SmartChargingSchedulingToggle from './SmartChargingSchedulingToggle';
import { useSmartChargingContext } from '../../context/SmartChargingContext';

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useCustomerGetCustomerProfile: jest.fn() as unknown as typeof useCustomerGetCustomerProfile,
  useHemsGetDeviceState: jest.fn() as unknown as typeof useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions: jest.fn() as unknown as typeof useSubscriptionsLookupSubscriptions,
  useHemsToggleSchedule: jest.fn().mockReturnValue({
    send: jest.fn(),
    isError: false,
  }),
  useHemsChargesettings: jest.fn().mockReturnValue({
    send: jest.fn(),
    isError: false,
    isSuccess: true,
  }),
  useHemsV2GetSessionDetailsV2: jest.fn().mockReturnValue({
    data: {},
    mutate: jest.fn(),
    isLoading: false,
  }) as unknown as typeof useHemsV2GetSessionDetailsV2,
}));

jest.mock('../../context/SmartChargingContext', () => ({
  ...jest.requireActual('../../context/SmartChargingContext'),
  useSmartChargingContext: jest.fn().mockReturnValue({
    smartChargingVehicleId: '0d889646-937c-4d88-9fdd-1a68cc34c5c1',
    schedulingEnabled: true,
    isReady: true,
    refreshDeviceState: jest.fn(),
  }),
}));

describe('SmartChargingSchedulingToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useCustomerGetCustomerProfile as jest.Mock).mockReturnValue({
      data: {
        features: ['SmartChargingVehicle'],
      },
    });

    (useSubscriptionsLookupSubscriptions as jest.Mock).mockReturnValue({
      data: {
        subscriptions: [
          {
            id: '7f3699ed-5e7b-45f1-31db-08dce9263b1e',
            tier: 'FREE',
            startDate: '2024-10-10',
            endDate: null,
            status: null,
            assets: [
              {
                externalAssetId: '0d889646-937c-4d88-9fdd-1a68cc34c5c1',
                vehicle: {
                  brand: 'Audi',
                  model: 'A3',
                  year: 2022,
                  alias: 'My Audi',
                },
              },
            ],
            type: 'SmartCharging',
          },
        ],
      },
    });

    (useHemsToggleSchedule as jest.Mock).mockReturnValue({
      send: jest.fn(),
    });
  });

  it('should render', () => {
    (useHemsGetDeviceState as jest.Mock).mockReturnValueOnce({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {},
      },
    });

    renderApp(SmartChargingSchedulingToggle, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(screen.getByText(smartChargingSchedulingToggle.fields.data.toggleLabel.value)).toBeInTheDocument();
  });

  it('should turn off when user clicks and scheduling toggle is enabled', async () => {
    (useHemsGetDeviceState as jest.Mock).mockReturnValueOnce({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: true,
        deviceStateInfo: {},
      },
    });

    const user = userEvent.setup();

    renderApp(SmartChargingSchedulingToggle, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    expect(useHemsGetDeviceState).toHaveBeenCalledTimes(1);

    const schedulingToggle = screen.getByRole('switch');
    expect(schedulingToggle).toBeInTheDocument();

    expect(schedulingToggle).toHaveAttribute('data-state', 'checked');

    await user.click(schedulingToggle);

    const dialogConfirmButton = screen.getByText(
      smartChargingSchedulingToggle.fields.data.toggleDialog.value.submitButtonText || 'Submit',
    );

    expect(dialogConfirmButton).toBeInTheDocument();

    await user.click(dialogConfirmButton);

    expect(dialogConfirmButton).not.toBeInTheDocument();
    expect(useHemsToggleSchedule().send).toHaveBeenCalledTimes(1);
  });

  it('should turn on when user clicks and scheduling toggle is disabled', async () => {
    (useSmartChargingContext as jest.Mock).mockReturnValue({
      smartChargingVehicleId: '0d889646-937c-4d88-9fdd-1a68cc34c5c1',
      schedulingEnabled: false,
      isReady: true,
      refreshDeviceState: jest.fn(),
    });

    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceType: 'ElectricCar',
        schedulingEnabled: false,
        deviceStateInfo: {},
      },
    });

    const user = userEvent.setup();

    renderApp(SmartChargingSchedulingToggle, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/ev',
    });

    const schedulingToggle = screen.getByRole('switch');
    expect(schedulingToggle).toBeInTheDocument();

    expect(useHemsGetDeviceState).toHaveBeenCalledTimes(1);

    expect(schedulingToggle).toHaveAttribute('data-state', 'unchecked');

    await user.click(schedulingToggle);

    expect(useHemsToggleSchedule().send).toHaveBeenCalledTimes(1);
  });
});
