/* eslint-disable jest/no-conditional-expect */
// TODO Disabling jest/no-conditional-expect because of the update to eslint 9. If you are editing this file, please fix the issue.

import React from 'react';

import { Preferences } from '@capacitor/preferences';
import { renderHook, screen, waitFor } from '@testing-library/react';

import { AppPreferences } from '@common/preferences';
import {
  useCustomerGetCustomerProfile,
  useHemsGetDeviceState,
  useHemsV2GetSessionDetailsV2,
  useSubscriptionsLookupSubscriptions,
} from '@dc/hooks';
import { useFormatter } from '@i18n';
import renderApp from '@jest-tools/renderApp';
import { deviceStateResponse as chargeStateAwayResponse } from '@mocks/dc/hems/deviceState/chargeStateAway';
import { deviceStateResponse as chargeStateChargingResponse } from '@mocks/dc/hems/deviceState/chargeStateCharging';
import { deviceStateResponse as chargeStateChargingAwayResponse } from '@mocks/dc/hems/deviceState/chargeStateChargingAway';
import { deviceStateResponse as chargeStateChargingBoostResponse } from '@mocks/dc/hems/deviceState/chargeStateChargingBoost';
import { deviceStateResponse as chargeStateChargingUnknown } from '@mocks/dc/hems/deviceState/chargeStateChargingUnknown';
import { deviceStateResponse as chargeStateCompletedResponse } from '@mocks/dc/hems/deviceState/chargeStateCompleted';
import { deviceStateResponse as chargeStateConnectedResponse } from '@mocks/dc/hems/deviceState/chargeStateConnected';
import { deviceStateResponse as chargeStateConnectedUnknownResponse } from '@mocks/dc/hems/deviceState/chargeStateConnectedUnknown';
import { deviceStateResponse as chargeStateConnectedUnknownForLongPeriodResponse } from '@mocks/dc/hems/deviceState/chargeStateConnectedUnknownForLongPeriod';
import { deviceStateResponse as chargeStateNotEnoughTimeResponse } from '@mocks/dc/hems/deviceState/chargeStateNotEnoughTime';
import { deviceStateResponse as chargeStateNotEnoughTimeBoostResponse } from '@mocks/dc/hems/deviceState/chargeStateNotEnoughTimeBoost';
import { deviceStateResponse as chargeStateStoppedResponse } from '@mocks/dc/hems/deviceState/chargeStateStopped';
import componentMock from '@mocks/sitecore/apps/hems/be/eneco/native/smartcharging/SmartChargingSessionDetails';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';
import { Hems_ChargeStateResponseModel } from '@monorepo-types/dc';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { BoostChargingIcon, CloseIcon, ElectricityIcon, PlugInIcon, PlugOffIcon } from '@sparky/icons';

import SmartChargingSessionDetails from './SmartChargingSessionDetails';
import { getStatusIcon, getStatusText } from '../../utils/chargingInfo';
import { getNotification } from '../../utils/notification';

const {
  SMARTCHARGING_COMPATIBILITY_SUCCESS,
  SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE,
  SMARTCHARGING_COMPATIBILITY_CAR_BRAND,
  SMARTCHARGING_HAS_PREVIOUS_SESSIONS,
} = AppPreferences;

const mockConnectTitle = componentMock.fields.connectCarTitle.value;

const stripTags = (string: string) => string.replace(/(<([^>]+)>)/gi, '');

function getDateFormatter() {
  const { result } = renderHook(() => useFormatter());
  const date = result.current.date;

  return date;
}

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useCustomerGetCustomerProfile: jest.fn() as unknown as typeof useCustomerGetCustomerProfile,
  useHemsGetDeviceState: jest.fn() as unknown as typeof useHemsGetDeviceState,
  useSubscriptionsLookupSubscriptions: jest.fn() as unknown as typeof useSubscriptionsLookupSubscriptions,
  useHemsV2GetSessionDetailsV2: jest.fn().mockReturnValue({
    data: {},
    mutate: jest.fn(),
    isLoading: false,
  }) as unknown as typeof useHemsV2GetSessionDetailsV2,
}));

describe('SmartChargingSessionDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useCustomerGetCustomerProfile as jest.Mock).mockReturnValue({
      data: {
        features: ['SmartChargingVehicle'],
      },
    });

    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {},
    });

    (useSubscriptionsLookupSubscriptions as jest.Mock).mockReturnValue({
      data: {
        subscriptions: [
          {
            id: '7f3699ed-5e7b-45f1-31db-08dce9263b1e',
            tier: 'FREE',
            startDate: '2024-10-10',
            endDate: null,
            status: null,
            assets: [
              {
                externalAssetId: '0d889646-937c-4d88-9fdd-1a68cc34c5c1',
                vehicle: {
                  brand: 'Audi',
                  model: 'A3',
                  year: 2022,
                  alias: 'My Audi',
                },
              },
            ],
            type: 'SmartCharging',
          },
        ],
      },
    });
  });

  afterEach(() => {
    Preferences.clear();
  });

  it('should NOT render the component when the user does not have the SmartCharging feature', async () => {
    Preferences.set({ key: SMARTCHARGING_COMPATIBILITY_CAR_BRAND, value: 'BMW' });

    (useCustomerGetCustomerProfile as jest.Mock).mockReturnValue({
      data: {
        features: [],
      },
    });

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    const { value: carBrand } = await Preferences.get({ key: SMARTCHARGING_COMPATIBILITY_CAR_BRAND });

    await expect(screen.findByText(`${mockConnectTitle} ${carBrand}`, {}, { timeout: 10 })).rejects.toThrow();
  });

  it('should render connect car info when the compatibility flow is successful and user has no smart charging asset', async () => {
    Preferences.set({ key: SMARTCHARGING_COMPATIBILITY_SUCCESS, value: 'true' });
    Preferences.set({ key: SMARTCHARGING_COMPATIBILITY_CAR_BRAND, value: 'BMW' });

    (useSubscriptionsLookupSubscriptions as jest.Mock).mockReturnValue({
      data: {
        subscriptions: [
          {
            id: '7f3699ed-5e7b-45f1-31db-08dce9263b1e',
            tier: 'FREE',
            startDate: '2024-10-10',
            endDate: null,
            status: null,
            assets: [],
            type: 'SMART_CHARGING',
          },
        ],
      },
    });

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    const mockConnectContent = stripTags(componentMock.fields.connectCarContent.value);
    const mockConnectCarButtonLinkText = componentMock.fields.connectCarButtonLink.value.text;
    const { value: carBrand } = await Preferences.get({ key: SMARTCHARGING_COMPATIBILITY_CAR_BRAND });

    expect(
      await screen.findByRole('heading', {
        level: 2,
        name: `${mockConnectTitle} ${carBrand}`,
      }),
    ).toBeInTheDocument();

    expect(await screen.findByText(mockConnectContent)).toBeInTheDocument();
    expect(await screen.findByText(mockConnectCarButtonLinkText)).toBeInTheDocument();
  });

  it('should show car brand if device state is has valid data', async () => {
    (useHemsGetDeviceState as jest.Mock).mockReturnValue({
      data: {
        deviceStateInfo: {
          vehicleId: '1234',
          deviceInfo: {
            brand: 'Audi',
            model: 'A3',
          },
        },
      },
    });

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    const carInfoTitle = 'Audi A3';

    expect(
      await screen.findByRole('heading', {
        level: 2,
        name: carInfoTitle,
      }),
    ).toBeInTheDocument();
  });

  it('should not render when device state is not available and when compatibility flow is not successful', async () => {
    Preferences.set({ key: SMARTCHARGING_COMPATIBILITY_SUCCESS, value: 'false' });
    (useSubscriptionsLookupSubscriptions as jest.Mock).mockReturnValue({
      data: null,
    });

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    await waitFor(() => {
      expect(screen.queryByRole('heading')).not.toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user changes charge mode and it returns an error', async () => {
    const notificationTitle = componentMock.fields.notificationChangingChargeModeIssueTitle.value;
    const notificationDescription = componentMock.fields.notificationChangingChargeModeIssueContent.value;
    const deviceStateInfo = chargeStateChargingResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        true,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: false,
      variant: 'error',
    });

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state Away and mode Unknown', async () => {
    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContentWithLink.value;
    const deviceStateInfo = chargeStateAwayResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateAwayResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugOffIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusAwayText.value,
    );

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).not.toBeInTheDocument();

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).not.toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state ChargingAway and mode Unknown', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContentWithLink.value;
    const deviceStateInfo = chargeStateChargingAwayResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingAwayResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <ElectricityIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusExternText.value,
    );

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).not.toBeInTheDocument();

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).not.toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user has issues with first charging session where state is charging with mode unknown', async () => {
    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;

    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateChargingUnknown?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingUnknown);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        true,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // await expect(screen.findByText(notificationTitle, {}, { timeout: 100 })).resolves.toBeVisible();
    // await expect(screen.findByText(notificationDescription, {}, { timeout: 100 })).resolves.toBeVisible();
  });

  it('should render the correct state for scenario: user has issues with third charging session where state is charging with mode unknown', async () => {
    Preferences.set({ key: SMARTCHARGING_HAS_PREVIOUS_SESSIONS, value: 'true' });
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.firstSessionIssuesNotification.value.title;
    const notificationDescription = componentMock.fields.firstSessionIssuesNotification.value.content;
    const deviceStateInfo = chargeStateChargingUnknown?.data?.deviceStateInfo;

    const currentTime = new Date();
    if (deviceStateInfo?.deviceState?.lastUpdate) {
      deviceStateInfo.deviceState.lastUpdate = String(currentTime);
    }

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        true,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: false,
      variant: 'info',
    });
  });

  it('should render the correct state for scenario: Charging is unknown  If this goes on for more than 30 minutes, request the user to replug the car', async () => {
    Preferences.set({ key: SMARTCHARGING_HAS_PREVIOUS_SESSIONS, value: 'true' });
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.firstSessionIssuesNotification.value.title;
    const notificationDescription = componentMock.fields.firstSessionIssuesNotification.value.content;
    const deviceStateInfo = chargeStateChargingUnknown?.data?.deviceStateInfo;

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        true,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: false,
      variant: 'info',
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state Connected', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateConnectedResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateConnectedResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');

    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).toEqual(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement?.textContent).toEqual(componentMock.fields.changeSessionLink.value.text);

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state Charging', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateChargingResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).toEqual(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement?.textContent).toEqual(componentMock.fields.changeSessionLink.value.text);

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state Charging and mode Boost', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateChargingBoostResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingBoostResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <BoostChargingIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusDirectText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).not.toEqual(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state Charging and notification NotEnoughTime', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateNotEnoughTimeResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateNotEnoughTimeResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;
    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + targetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    expect(readyOnElement).not.toBeInTheDocument();

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement?.textContent).toEqual(componentMock.fields.changeSessionLink.value.text);

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state Charging, mode Boost with notification NotEnoughTime', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateNotEnoughTimeBoostResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateNotEnoughTimeBoostResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <BoostChargingIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusDirectText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    expect(readyOnElement).not.toBeInTheDocument();

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule no, charge state mode Stopped', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'false' });

    const notificationTitle = componentMock.fields.notificationDefaultLoadingScheduleTitle.value;
    const notificationDescription = componentMock.fields.notificationDefaultLoadingScheduleContent.value;
    const deviceStateInfo = chargeStateStoppedResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateStoppedResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <CloseIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusStoppedText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    expect(loadUntilElement).not.toBeInTheDocument();

    const readyOnElement = screen.queryByTestId('ready-on-label');
    expect(readyOnElement).not.toBeInTheDocument();

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        false,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: true,
      variant: 'info',
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state ChargingAway and mode Unknown', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.notificationNotHomeTitle.value;
    const notificationDescription = componentMock.fields.notificationNotHomeContent.value;
    const deviceStateInfo = chargeStateChargingAwayResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingAwayResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <ElectricityIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusExternText.value,
    );

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: false,
      variant: 'info',
    });

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).not.toBeInTheDocument();

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).not.toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Away and mode Unknown', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.notificationSmartChargingNotPossibleTitle.value;
    const notificationDescription = componentMock.fields.notificationSmartChargingNotPossibleContent.value;
    const deviceStateInfo = chargeStateAwayResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateAwayResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugOffIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusAwayText.value,
    );

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      userCanClose: false,
      variant: 'info',
    });

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).not.toBeInTheDocument();

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).not.toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Connected', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const deviceStateInfo = chargeStateConnectedResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateConnectedResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).toEqual(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual(null);

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement?.textContent).toEqual(componentMock.fields.changeSessionLink.value.text);

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Charging', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const deviceStateInfo = chargeStateChargingResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).toEqual(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual(null);

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement?.textContent).toEqual(componentMock.fields.changeSessionLink.value.text);

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Charging and mode Boost', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const deviceStateInfo = chargeStateChargingBoostResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateChargingBoostResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <BoostChargingIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusDirectText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).not.toBe(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual(null);

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Completed', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const deviceStateInfo = chargeStateCompletedResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateCompletedResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    expect(readyOnElement).not.toBeInTheDocument();

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel?.textContent).toEqual(componentMock.fields.carIsChargedText.value);

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual(null);

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state mode Stopped', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.notificationUserStoppedChargingTitle.value;
    const notificationDescription = componentMock.fields.notificationUserStoppedChargingContent.value;
    const deviceStateInfo = chargeStateStoppedResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateStoppedResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <CloseIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusStoppedText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    expect(loadUntilElement).not.toBeInTheDocument();

    const readyOnElement = screen.queryByTestId('ready-on-label');
    expect(readyOnElement).not.toBeInTheDocument();

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      variant: 'info',
      userCanClose: false,
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).not.toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Charging with notification NotEnoughTime', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.notificationNotEnoughTimeTitle.value;
    const notificationDescription = componentMock.fields.notificationNotEnoughTimeContent.value;
    const deviceStateInfo = chargeStateNotEnoughTimeResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateNotEnoughTimeResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <PlugInIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusSmartText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;
    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + targetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    expect(readyOnElement).not.toBeInTheDocument();

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      variant: 'warning',
      userCanClose: false,
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement?.textContent).toEqual(componentMock.fields.changeSessionLink.value.text);

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct state for scenario: user checked load schedule yes, charge state Charging and mode Boost with notification NotEnoughTime', async () => {
    Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });

    const notificationTitle = componentMock.fields.notificationNotEnoughTimeTitle.value;
    const notificationDescription = componentMock.fields.notificationNotEnoughTimeContent.value;
    const deviceStateInfo = chargeStateNotEnoughTimeBoostResponse?.data?.deviceStateInfo;

    (useHemsGetDeviceState as jest.Mock).mockReturnValue(chargeStateNotEnoughTimeBoostResponse);

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    // Status icon & text
    expect(getStatusIcon(deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      <BoostChargingIcon color="iconElectricity" />,
    );
    expect(getStatusText(componentMock.fields, deviceStateInfo?.chargeState as Hems_ChargeStateResponseModel)).toEqual(
      componentMock.fields.statusDirectText.value,
    );

    // Charging progress fields
    const chargingProgressElement = screen.queryByTestId('charging-progress');
    expect(chargingProgressElement).toBeInTheDocument();

    const stateOfChargeElement = screen.queryByTestId('state-of-charge');
    const stateOfChargeValue = deviceStateInfo?.deviceState.stateOfCharge + '%';
    expect(stateOfChargeElement?.textContent).toEqual(stateOfChargeValue);

    const stateRangeElement = screen.queryByTestId('state-range');
    const deviceStateRange = deviceStateInfo?.deviceState.range + 'km';
    expect(stateRangeElement?.textContent).toEqual(deviceStateRange);

    const loadUntilElement = screen.queryByTestId('load-until-label');
    const label = componentMock.fields.loadsUntilText.value;

    const targetStateOfCharge = deviceStateInfo?.chargeState?.targetStateOfCharge;
    const maxOemStateOfCharge = deviceStateInfo?.deviceState?.maxOemStateOfCharge;
    const cappedTargetStateOfCharge =
      targetStateOfCharge && maxOemStateOfCharge && targetStateOfCharge > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : targetStateOfCharge;
    expect(loadUntilElement?.textContent).toEqual(label + ' ' + cappedTargetStateOfCharge + '%');

    const readyOnElement = screen.queryByTestId('ready-on-label');
    const targetEndDate = deviceStateInfo?.chargeState?.targetEndDate;

    if (targetEndDate) {
      const date = getDateFormatter();
      // \u00A0 = non-breaking space which is the same as &nbsp; in html
      const validationString = `${componentMock.fields.readyOnText.value}\u00A0${date.dayOfWeekLong(targetEndDate)} ${date.hourLong(targetEndDate)}`;
      expect(readyOnElement?.textContent).not.toBe(validationString);
    }

    const carIsChargedLabel = screen.queryByTestId('car-is-charged-label');
    expect(carIsChargedLabel).not.toBeInTheDocument();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      variant: 'warning',
      userCanClose: false,
    });

    // Change session link
    const changeSessionElement = screen.queryByTestId('change-session-page');
    expect(changeSessionElement).toBeInTheDocument();

    // Toggle group
    const toggles = screen.queryAllByTestId('smart-charging-toggle');
    toggles?.forEach(toggle => {
      expect(toggle).toBeInTheDocument();
    });
  });

  it('should render the correct icon and status text for each charge state', async () => {
    const mockFields = componentMock.fields;
    const chargeStates: Hems_ChargeStateResponseModel[] = [
      {
        mode: 'Smart',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Connected',
      },
      {
        mode: 'Boost',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Charging',
      },
      {
        mode: 'Unknown',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Away',
      },
      {
        mode: 'Unknown',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'ChargingAway',
      },
      {
        mode: 'Smart',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Completed',
      },
      {
        mode: 'Boost',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Completed',
      },
      {
        mode: 'Stopped',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Connected',
      },
      {
        mode: 'Unknown',
        targetStateOfCharge: 30,
        targetEndDate: '',
        state: 'Charging',
      },
    ];

    await renderApp(SmartChargingSessionDetails, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/dashboard',
    });

    expect(getStatusIcon(chargeStates[0])).toEqual(<PlugInIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[1])).toEqual(<BoostChargingIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[2])).toEqual(<PlugOffIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[3])).toEqual(<ElectricityIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[4])).toEqual(<PlugInIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[5])).toEqual(<BoostChargingIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[6])).toEqual(<CloseIcon color="iconElectricity" />);
    expect(getStatusIcon(chargeStates[7])).toEqual(<ElectricityIcon color="iconElectricity" />);

    expect(getStatusText(mockFields, chargeStates[0])).toEqual(mockFields.statusSmartText.value);
    expect(getStatusText(mockFields, chargeStates[1])).toEqual(mockFields.statusDirectText.value);
    expect(getStatusText(mockFields, chargeStates[2])).toEqual(mockFields.statusAwayText.value);
    expect(getStatusText(mockFields, chargeStates[3])).toEqual(mockFields.statusExternText.value);
    expect(getStatusText(mockFields, chargeStates[4])).toEqual(mockFields.statusSmartText.value);
    expect(getStatusText(mockFields, chargeStates[5])).toEqual(mockFields.statusDirectText.value);
    expect(getStatusText(mockFields, chargeStates[6])).toEqual(mockFields.statusStoppedText.value);
    expect(getStatusText(mockFields, chargeStates[7])).toEqual(mockFields.statusExternText.value);
  });

  it('should render the correct notification when chargemode is unknown for more than 30 minutes', () => {
    const notificationTitle = 'Connectie problemen';
    const notificationDescription =
      'De verbinding met je wagen is niet optimaal. Probeer de auto opnieuw in te steken.';
    const deviceStateInfo = chargeStateConnectedUnknownForLongPeriodResponse?.data?.deviceStateInfo;

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      variant: 'warning',
      userCanClose: false,
    });
  });

  it('should render the correct notification when chargemode is unknown', () => {
    const notificationTitle = 'Gegevens ophalen';
    const notificationDescription =
      'Wanneer we de status van je auto kennen, kan je weer beginnen met SlimLaden. Dit kan tot enkele minuten duren.';
    const deviceStateInfo = chargeStateConnectedUnknownResponse?.data?.deviceStateInfo;

    const date = new Date();
    if (deviceStateInfo) deviceStateInfo.deviceState.lastUpdate = date.toISOString();

    // Notification
    expect(
      getNotification(
        componentMock.fields,
        deviceStateInfo?.deviceState,
        deviceStateInfo?.chargeState,
        true,
        deviceStateInfo?.deviceState.intervention,
        false,
        false,
      ),
    ).toEqual({
      title: notificationTitle,
      text: notificationDescription,
      variant: 'warning',
      userCanClose: false,
    });
  });
});
