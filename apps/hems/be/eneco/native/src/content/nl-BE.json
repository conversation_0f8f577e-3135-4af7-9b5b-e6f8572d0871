{"currentScheduleOverride": {"today": "Vandaag", "tomorrow": "<PERSON><PERSON>"}, "lookupSubscriptionError": {"title": "<PERSON>r liep iets mis", "message": "<PERSON><PERSON><PERSON> later opnieuw. <a href=\"https://eneco.be/nl/contact/\">Contacteer ons</a> indien het zich blijft voordoen."}, "deviceStateError": {"title": "<PERSON>r liep iets mis", "message": "<PERSON><PERSON><PERSON> later opnieuw. <a href=\"https://eneco.be/nl/contact/\">Contacteer ons</a> indien het zich blijft voordoen."}, "hemsLinkDeviceError": {"dynamicContract": {"title": "", "message": "De Eneco Smart Meter app ondersteunt op dit moment nog geen dynamische tarieven. Je kan dus helaas geen account aanmaken."}}, "smartCharge": "<PERSON> laden", "boostCharge": "Boost", "active": "actief", "dashboardCard": {"linkLabel": "Ga naar EV pagina"}, "chargingData": {"chargingTarget": "<PERSON><PERSON><PERSON><PERSON>", "timeTargetSmart": "Klaar op", "timeTargetBoost": "Geschat k<PERSON>ar tegen", "chargingExternal": "<PERSON><PERSON><PERSON><PERSON> of vertrekmoment instellen niet mogelijk", "consumption": "Verbruik", "cost": "Laadkost"}, "modeBadge": {"smart": "<PERSON>", "boost": "Boost", "away": "Op locatie"}, "chargingStateLabels": {"charging": "<PERSON>an het laden", "paused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completed": "Opgeladen", "stopped": "<PERSON><PERSON> gestopt", "disconnected": "<PERSON><PERSON> ing<PERSON>d", "warning": "Verbindingsprobleem"}, "resumeButtonLabel": "<PERSON><PERSON>", "preferences": {"title": "Laadvoorkeuren", "chargeTarget": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "chargeTargetHref": "/?item=%2Fev%2Fsmartcharging-default-schedule", "departureTime": "Slim vertrekmoment", "departureTimeHref": "/?item=%2Fev%2Fsmartcharging-default-schedule"}}