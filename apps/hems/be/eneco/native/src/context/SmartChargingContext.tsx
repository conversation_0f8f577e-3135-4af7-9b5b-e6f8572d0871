import React, { PropsWith<PERSON>hildren, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { Preferences } from '@capacitor/preferences';

import { AppPreferences } from '@common/preferences';
import { useHemsGetDeviceState, useHemsV2GetSessionDetailsV2, useSubscriptionsLookupSubscriptions } from '@dc/hooks';
import {
  type Hems_DailyChargeSettingsResponseModel,
  type Hems_ChargeSettingsOverrideResponseModel,
  type DC_Domain_Models_HEMS_Intervention,
  type Hems_ChargeStateResponseModel,
  type Hems_EvDeviceInfoResponseModel,
  Hems_EvDeviceStateResponseModel,
  DC_Domain_Models_HEMS_ChargeStateNotification,
  DC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel,
} from '@monorepo-types/dc';

const { SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, SMARTCHARGING_COMPATIBILITY_SUCCESS } = AppPreferences;

interface ContextProps {
  deviceInfo: Hems_EvDeviceInfoResponseModel | undefined;
  chargeState: Hems_ChargeStateResponseModel | undefined;
  deviceState: Hems_EvDeviceStateResponseModel | undefined;
  setUserVisitedStandardSchedule: () => void;
  userHasStandardSchedule: boolean;
  isPreEnodeFlowCard: boolean;
  schedules: Hems_DailyChargeSettingsResponseModel[] | undefined;
  overrideSchedule: Hems_ChargeSettingsOverrideResponseModel | undefined;
  smartChargingVehicleId: string | null | undefined;
  intervention: DC_Domain_Models_HEMS_Intervention | undefined;
  notification?: DC_Domain_Models_HEMS_ChargeStateNotification;
  refreshDeviceState: () => Promise<void>;
  refreshSessionDetails: () => Promise<void>;
  isErrorChangingChargeMode: boolean;
  setIsErrorChangingChargeMode: (newState: boolean) => void;
  lookUpSubscriptionError: boolean;
  deviceStateError: boolean;
  isReady: boolean;
  schedulingEnabled: boolean | null;
  hemsSessionsDetailsData: DC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel | undefined;
}

const SmartChargingContext = React.createContext<ContextProps | null>(null);

const SmartChargingProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [userHasStandardSchedule, setUserHasStandardSchedule] = useState<boolean>(false);
  const [isPreEnodeFlowCard, setIsPreEnodeFlowCard] = useState(false);
  const [isErrorChangingChargeMode, setIsErrorChangingChargeMode] = useState<boolean>(false);
  const { data: lookUpSubscriptionData, isLoading: isLoadingSubscription } = useSubscriptionsLookupSubscriptions({
    type: 'SmartCharging',
    tier: 'Free',
  });

  // TODO error handling when call fails.
  const {
    data: hemsSessionsDetailsData,
    isLoading: isLoadingSessionDetails,
    mutate: mutateSessionDetails,
    // TODO replace with correct values
  } = useHemsV2GetSessionDetailsV2({
    sessionId: 'b1854a7c-fd1a-4dfd-9568-177d134950ad',
    deviceId: 'a9a22d0b-0921-4ef9-8fe1-db17f46c67ec',
    granularity: 'Cu',
  });

  const lookUpSubscriptionError = useMemo(() => {
    return !lookUpSubscriptionData;
  }, [lookUpSubscriptionData]);

  const smartChargingVehicleId = useMemo(() => {
    return lookUpSubscriptionData?.subscriptions?.[0]?.assets?.[0]?.externalAssetId;
  }, [lookUpSubscriptionData?.subscriptions]);

  const {
    data,
    mutate,
    isLoading: isLoadingDeviceSate,
  } = useHemsGetDeviceState(smartChargingVehicleId ? { deviceId: smartChargingVehicleId } : null);

  const deviceStateError = useMemo(() => {
    return !data;
  }, [data]);

  const refreshDeviceState = useCallback(async () => {
    // This will trigger a re-fetch and update the cache
    await mutate();
  }, [mutate]);

  const refreshSessionDetails = useCallback(async () => {
    await mutateSessionDetails();
  }, [mutateSessionDetails]);

  const deviceStateInfo = data?.deviceStateInfo;
  const schedulingEnabled = data?.schedulingEnabled || null;

  useEffect(() => {
    Preferences.get({ key: SMARTCHARGING_COMPATIBILITY_SUCCESS }).then(({ value }) => {
      setIsPreEnodeFlowCard(value === 'true' && !smartChargingVehicleId);
    });
  }, [smartChargingVehicleId]);

  useEffect(() => {
    const getStandardScheduleKey = async () => {
      const { value } = await Preferences.get({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE });

      if (value === 'true') {
        setUserHasStandardSchedule(true);
      }
    };

    getStandardScheduleKey();
  }, []);

  const setUserVisitedStandardSchedule = useCallback(() => {
    const setLocalStorageKey = async () => {
      const { value } = await Preferences.get({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE });

      if (value !== 'true') {
        Preferences.set({ key: SMARTCHARGING_USER_HAS_STANDARD_SCHEDULE, value: 'true' });
        setUserHasStandardSchedule(true);
      }
    };

    setLocalStorageKey();
  }, []);

  const { deviceInfo, chargeState, deviceState, schedules, overrideSchedule, intervention, notification } =
    useMemo(() => {
      const deviceInfo = deviceStateInfo?.deviceInfo;
      const chargeState = deviceStateInfo?.chargeState;
      const deviceState = deviceStateInfo?.deviceState;
      const schedules = deviceStateInfo?.schedules;
      const overrideSchedule = deviceStateInfo?.overrideSchedule;
      const intervention = deviceState?.intervention;
      const notification = chargeState?.notification;

      return { deviceInfo, chargeState, deviceState, schedules, overrideSchedule, intervention, notification };
    }, [deviceStateInfo]);

  const isReady = useMemo(() => {
    return !isLoadingDeviceSate && !isLoadingSubscription && !isLoadingSessionDetails;
  }, [isLoadingDeviceSate, isLoadingSessionDetails, isLoadingSubscription]);

  return (
    <SmartChargingContext.Provider
      value={{
        intervention,
        notification,
        deviceInfo,
        chargeState,
        deviceState,
        userHasStandardSchedule,
        setUserVisitedStandardSchedule,
        isPreEnodeFlowCard,
        schedules,
        overrideSchedule,
        smartChargingVehicleId,
        refreshDeviceState,
        refreshSessionDetails,
        isErrorChangingChargeMode,
        setIsErrorChangingChargeMode,
        lookUpSubscriptionError,
        deviceStateError,
        isReady,
        schedulingEnabled,
        hemsSessionsDetailsData,
      }}>
      {children}
    </SmartChargingContext.Provider>
  );
};

const useSmartChargingContext = () => {
  const context = useContext(SmartChargingContext);

  if (context === null) {
    throw new Error('useSmartChargingContext must be within the SmartChargingProvider');
  }

  return context;
};

export { useSmartChargingContext, SmartChargingProvider, SmartChargingContext };
