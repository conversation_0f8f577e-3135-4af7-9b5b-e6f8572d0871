import { useMemo } from 'react';

import { useSmartChargingContext } from '../context/SmartChargingContext';

const useChargeScenarios = () => {
  const { deviceState, chargeState, intervention } = useSmartChargingContext();

  return useMemo(() => {
    const mode = chargeState?.mode;
    const state = chargeState?.state;

    const isAway = state === 'Away';
    const isUnknown = state === 'Unknown';
    const isConnected = state === 'Connected';
    const isStopped = mode === 'Stopped';
    const isCompleted = state === 'Completed';
    const isStoppedOrUnknown = mode === 'Unknown' || mode === 'Stopped';
    const isCharging = state === 'Charging';
    const isChargingAway = state === 'ChargingAway';
    const isSmart = mode === 'Smart';
    const isBoost = mode === 'Boost';

    const chargingStopped = isStopped && isConnected;
    const awayOrUnknown = isAway || isUnknown;
    const charging = isCharging || isChargingAway;
    const notCharging = awayOrUnknown || chargingStopped;
    const carDisconnected = !deviceState || !chargeState || awayOrUnknown;
    const chargingStoppedOrCompleted = isStopped || isCompleted;
    const hasIntervention = Boolean(intervention?.id);
    const connectedOrCharging = isConnected || isCharging;

    return {
      chargingStopped,
      awayOrUnknown,
      isCompleted,
      charging,
      notCharging,
      carDisconnected,
      chargingStoppedOrCompleted,
      hasIntervention,
      isAway,
      isUnknown,
      isChargingAway,
      isCharging,
      isConnected,
      isSmart,
      isBoost,
      isStopped,
      isStoppedOrUnknown,
      connectedOrCharging
    };
  }, [deviceState, chargeState, intervention?.id]);
};

export default useChargeScenarios;
