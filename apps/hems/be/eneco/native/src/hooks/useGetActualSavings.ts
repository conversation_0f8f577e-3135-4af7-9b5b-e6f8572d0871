import { useEffect, useState } from 'react';

import Logger from '@common/log';
import { useHemsV2GetSessionsV2 } from '@dc/hooks';
import useDC from '@dc/useDC';
import { Hems_v2_SessionInfoResponseModel } from '@monorepo-types/dc';

import { useSmartChargingContext } from '../context/SmartChargingContext';

const useGetActualSavings = () => {
  const { businessUnit, label, customerId } = useDC();
  const { smartChargingVehicleId, hemsSessionsDetailsData, chargeState } = useSmartChargingContext();
  const { send, isLoading } = useHemsV2GetSessionsV2();
  const [sessions, setSessions] = useState<Hems_v2_SessionInfoResponseModel[] | null | undefined>(null);

  useEffect(() => {
    async function getSessions() {
      // TODO we should test this thoroughly, because it's unsure when the charge state is 'Completed' (opgeladen)
      //  that this session will be visible in the endpoint used below. If this is not the case we should do some magic
      //  with the useHemsV2GetSessionDetailsV2 endpoint data
      try {
        const { sessions } =
          (await send({
            businessUnit,
            label,
            customerId,
            deviceId: smartChargingVehicleId,
            data: {
              limit: 1,
            },
          })) ?? {};

        setSessions(sessions);
      } catch (e) {
        Logger.error('syw-UO', 'Something went wrong fetching sessions: ' + e);
      }
    }

    if (
      !isLoading &&
      (chargeState?.state === 'Completed' || chargeState?.mode === 'Stopped') &&
      hemsSessionsDetailsData?.chargeMode?.includes('Smart')
    ) {
      getSessions();
    } else {
      setSessions(null);
    }
  }, [chargeState]);

  return sessions;
};

export default useGetActualSavings;
