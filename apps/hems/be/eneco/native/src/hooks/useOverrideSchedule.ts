import Logger from '@common/log';
import { useHemsChargesettingsOverride } from '@dc/hooks';
import useDC from '@dc/useDC';
import { Hems_ChargeSettingsOverrideRequestModel } from '@monorepo-types/dc';

import useChargeScenarios from './useChargeScenarios';
import { getFirstInLineDefaultSchedule } from '../common/helpers';
import { useSmartChargingContext } from '../context/SmartChargingContext';

const useOverrideSchedule = () => {
  const { businessUnit, label, customerId } = useDC();
  const { smartChargingVehicleId, chargeState, schedules, overrideSchedule, deviceState } = useSmartChargingContext();
  const maxOemStateOfCharge = deviceState?.maxOemStateOfCharge;

  const {
    send,
    isError: isErrorChargeSettingsOverride,
    isSuccess: isSuccessChargeSettingsOverride,
  } = useHemsChargesettingsOverride();
  const { isStoppedOrUnknown } = useChargeScenarios();

  const sendOverrideSchedule = async (departureTimestampUTC: string, targetCharge: number) => {
    if (!customerId || !chargeState?.mode) {
      return;
    }
    try {
      await send({
        businessUnit: businessUnit,
        label: label,
        customerId,
        data: {
          deviceId: smartChargingVehicleId,
          departureTime: departureTimestampUTC, // use UTC timestamp
          chargeModeSettings: [
            {
              targetStateOfCharge: targetCharge,
              chargeMode: isStoppedOrUnknown ? 'Smart' : chargeState.mode,
            },
          ],
        } as Hems_ChargeSettingsOverrideRequestModel,
      });
    } catch (error) {
      Logger.error('syw1UO', 'Something went wrong sending override schedule ' + error);
    }
  };

  const resetLoadingTargetOverride = async () => {
    if (!overrideSchedule || !schedules) {
      return;
    }

    const defaultSchedulesTargetSoc =
      schedules
        .find(schedule => schedule.chargeModeSettings.some(setting => setting.targetStateOfCharge !== undefined))
        ?.chargeModeSettings.find(setting => setting.targetStateOfCharge !== undefined)?.targetStateOfCharge ?? 0;

    const departureTimestampUTC = overrideSchedule.departureTime;
    const targetCharge =
      maxOemStateOfCharge && defaultSchedulesTargetSoc > maxOemStateOfCharge
        ? maxOemStateOfCharge
        : defaultSchedulesTargetSoc;

    await sendOverrideSchedule(departureTimestampUTC, targetCharge);
  };

  const overrideLoadingTargetSchedule = async (targetSoc: number) => {
    let departureTimestampUTC = '';
    if (overrideSchedule) {
      departureTimestampUTC = overrideSchedule.departureTime;
    } else {
      if (!schedules) {
        return;
      }
      departureTimestampUTC = getFirstInLineDefaultSchedule(schedules, 1);
    }
    const targetCharge = maxOemStateOfCharge && targetSoc > maxOemStateOfCharge ? maxOemStateOfCharge : targetSoc;

    await sendOverrideSchedule(departureTimestampUTC, targetCharge);
  };

  const overrideDepartureTimeSchedule = async (targetSoc: number, departureDay: string, departureTime: string) => {
    const departureTimestampUTC = new Date(`${departureDay}T${departureTime}`).toISOString();
    const targetCharge = maxOemStateOfCharge && targetSoc > maxOemStateOfCharge ? maxOemStateOfCharge : targetSoc;

    await sendOverrideSchedule(departureTimestampUTC, targetCharge);
  };

  return {
    sendOverrideSchedule,
    resetLoadingTargetOverride,
    overrideLoadingTargetSchedule,
    overrideDepartureTimeSchedule,
    send,
    isErrorChargeSettingsOverride,
    isSuccessChargeSettingsOverride,
  };
};

export default useOverrideSchedule;
