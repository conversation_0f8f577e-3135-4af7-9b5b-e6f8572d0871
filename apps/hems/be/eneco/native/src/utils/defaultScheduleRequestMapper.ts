import type {
  DC_Domain_Models_HEMS_ChargeMode,
  Hems_DailyChargeSettingRequestModel,
  System_DayOfWeek,
} from '@monorepo-types/dc';

export interface scheduleProps {
  departureTime: string;
  targetCharge: number;
  deviceId: string;
  chargeMode: DC_Domain_Models_HEMS_ChargeMode;
  isStoppedOrUnknown: boolean;
}

const daysOfWeek: System_DayOfWeek[] = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export const getDefaultScheduleRequest = ({
  departureTime,
  targetCharge,
  deviceId,
  chargeMode,
  isStoppedOrUnknown,
}: scheduleProps) => {
  const dailyChargeSettings: Hems_DailyChargeSettingRequestModel[] = [];
  for (const weekday of daysOfWeek) {
    const formattedDepartureTime: string =
      departureTime.includes(':') && departureTime.split(':').length === 2 ? `${departureTime}:00` : departureTime;

    dailyChargeSettings.push({
      day: weekday,
      departureTime: formattedDepartureTime,
      chargeModeSettings: [
        {
          targetStateOfCharge: targetCharge,
          chargeMode: isStoppedOrUnknown ? 'Smart' : chargeMode,
        },
      ],
    });
  }

  const data = {
    deviceId: deviceId,
    dailyChargeSettings: dailyChargeSettings,
  };

  return {
    data: data,
  };
};
