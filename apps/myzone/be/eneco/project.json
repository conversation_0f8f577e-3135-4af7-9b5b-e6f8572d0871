{"name": "myzone-be-eneco", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/myzone/be/eneco/src", "projectType": "library", "tags": ["app:next"], "implicitDependencies": ["mocks"], "targets": {"lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/myzone/be/eneco/src/**/*.{ts,tsx}"]}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "apps/myzone/be/eneco/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "runInBand": true}}}, "tsc": {"executor": "./tools/executors/tsc:tsc", "options": {"tsConfig": ["tsconfig.json"]}}}}