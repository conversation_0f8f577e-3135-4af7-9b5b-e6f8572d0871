/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable dxp-rules/no-custom-styling */
import { FC, PropsWithChildren, useContext, useState } from 'react';

import RichText from '@components/RichText/RichText';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { FlowStepIndicatorHeaderWithItems } from '@sitecore/types/manual/be/FlowStepIndicatorHeaderWithItems';
import { AlertDialog, Box, Divider, IconButton, NavLink, ProgressIndicator, Stack } from '@sparky';
import { ChevronLeftIcon, CloseIcon } from '@sparky/icons';
import { styled } from '@sparky/stitches';
import { Logo } from 'libs/sparky/src/components/Logo/Eneco/Logo';

import { LogoWrapper } from './utils/LogoWrapper';
import { FlowNavigationContext } from '../../../providers/FlowNavigationProvider';

const NavigationBarWithFlow: FC<PropsWithChildren> = ({ children }) => {
  const { fields } = useContent<FlowStepIndicatorHeaderWithItems>();
  const { push } = useRouter();
  const { triggerOnPreviousClick, activeStepIndicator, showPreviousBtn } = useContext(FlowNavigationContext);

  const [showCloseDialog, setShowCloseDialog] = useState(false);

  return (
    <Box backgroundColor="backgroundPrimary">
      <Stack direction="column">
        <BoxMaxWidth paddingX={{ initial: '4', md: '8' }}>
          <Stack as="nav" direction="row" alignY="center">
            <Stack.Item>
              <Stack alignY="center">
                {showPreviousBtn && (
                  <NavLink onClick={triggerOnPreviousClick} variant="secondary" leftIcon={<ChevronLeftIcon />}>
                    {fields.data?.previousLabel?.value}
                  </NavLink>
                )}
              </Stack>
            </Stack.Item>

            <Stack.Item grow={true}>
              <Stack direction="row" alignX="center">
                <LogoWrapper>
                  <Logo />
                </LogoWrapper>
              </Stack>
            </Stack.Item>

            <Stack.Item>
              <Stack alignY="center">
                <IconButton label={'close'} onClick={() => setShowCloseDialog(true)}>
                  <CloseIcon color="iconSecondary" />
                </IconButton>
                {children}
              </Stack>
            </Stack.Item>
          </Stack>
        </BoxMaxWidth>

        {fields.items && (
          <>
            <Box>
              <Divider />
            </Box>

            <ProgressIndicator>
              {fields.items.map((item, idx) => {
                const label = item?.fields?.content?.label?.value ?? '';
                return (
                  <ProgressIndicator.Item key={`${idx}_${label}`} isActive={idx === activeStepIndicator}>
                    {label ?? ['[not in sitecore]']}
                  </ProgressIndicator.Item>
                );
              })}
            </ProgressIndicator>
          </>
        )}
      </Stack>
      <AlertDialog
        title={fields.data?.stopDialog?.value?.title}
        isOpen={showCloseDialog}
        setOpen={setShowCloseDialog}
        onConfirm={() => {
          push(fields.data?.closeLink?.value?.href);
        }}
        confirmText={fields.data?.stopDialog?.value?.submitButtonText || ''}
        denyText={fields.data?.stopDialog?.value?.cancelButtonText || ''}
        onDeny={() => {}}>
        <RichText html={fields.data?.stopDialog?.value?.content} />
      </AlertDialog>
    </Box>
  );
};

export default NavigationBarWithFlow;

const BoxMaxWidth = styled(Box, {
  // libs/custom-components/src/flows/Layout.ts same width as NL
  maxWidth: '1304px',
  margin: 'auto',
  width: '100%',
});
