import { FC, useCallback, useEffect, useState } from 'react';

import { signIn } from 'next-auth/react';
import { useLocalStorage } from 'react-use';

import { useApplication } from '@common/application';
import { NO_TOKEN, TOKEN_EXPIRED, TOKEN_REDEEMED, TOKEN_REVOKED } from '@dc-be/client/constants';
import { getEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidity } from '@dc-be/client/services.gen';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useFormatter } from '@i18n';
import { mapImage } from '@sitecore/common';
import { AccountInviteFormRendering } from '@sitecore/types/AccountInviteForm';
import { Box, Card, Grid, Image, PageGrid, Skeleton, Stack, Stretch } from '@sparky';

import AlertStep from './steps/AlertStep';
import AliasStep from './steps/AliasStep';
import LinkTokenExpiredStep from './steps/LinkTokenExpiredStep';
import WelcomeStep from './steps/WelcomeStep';

export type formStates =
  | 'LOADING'
  | 'WELCOME_SCREEN'
  | 'ALIAS_FORM'
  | 'SUCCESS'
  | 'LINKTOKEN_EXPIRED'
  | 'LINKTOKEN_ERROR';
const AccountInviteForm: FC<AccountInviteFormRendering> = ({ fields }) => {
  const { searchParams } = useApplication();
  const statusParam = searchParams.get('status');
  const aliasOwnerParam = searchParams.get('aliasOwner');
  const crmAccountNumberParam = searchParams.get('accountNumber');
  const crmAccountAliasCurrentUserParam = searchParams.get('aliasCurrentUser');
  const linkToken = searchParams.get('sessionToken');
  const [formState, setFormState] = useState<formStates>(statusParam === 'SUCCESS' ? 'SUCCESS' : 'WELCOME_SCREEN');
  const [localToken, setLocalToken] = useLocalStorage<string | null>('sessionToken', null);
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const { format } = useFormatter();

  useEffect(() => {
    if (!!linkToken && (!localToken || localToken !== linkToken)) {
      setLocalToken(linkToken);
    }
    if (localToken && status === 'unauthenticated') {
      signIn('okta');
    }
    if (!linkToken && !localToken) {
      setFormState('LINKTOKEN_ERROR');
    }
  }, [localToken, linkToken, status, setLocalToken]);

  const { switchAccount } = useSelfServiceAccount();

  const { data } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidity,
    {
      path: {
        sessionToken: linkToken || localToken || NO_TOKEN,
      },
    },
    [linkToken || NO_TOKEN],
    session,
  );

  const validityData = unwrapData(data);
  const welcomeImage = mapImage(fields.welcomeScreenHeaderImage);

  useEffect(() => {
    if (formState === 'SUCCESS') {
      switchAccount({ crmAccountNumber: validityData?.crmAccountNumber ?? '', reloadPage: false });
      return;
    }
    if (!data && isLoading) {
      setFormState('LOADING');
      return;
    }
    if (!data) {
      setIsLoading(false);
      setFormState('LINKTOKEN_ERROR');
      return;
    }
    if (
      validityData?.status === TOKEN_EXPIRED ||
      validityData?.status === TOKEN_REDEEMED ||
      validityData?.status === TOKEN_REVOKED
    ) {
      setIsLoading(false);
      setFormState('LINKTOKEN_EXPIRED');
      return;
    }
    if (data.response.status === 200) {
      setIsLoading(false);
      setFormState('WELCOME_SCREEN');
      return;
    }
    setIsLoading(false);
    setFormState('LINKTOKEN_ERROR');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, isLoading]);

  const renderFormStep = useCallback(() => {
    switch (formState) {
      case 'LOADING':
        return <Skeleton width={'stretch'} height={400} />;
      case 'WELCOME_SCREEN':
        return (
          <WelcomeStep
            fields={{
              title: fields?.welcomeScreenTitle.value,
              subTitle: fields?.welcomeScreenContent.value,
              buttonText: fields?.welcomeScreenButtonText.value,
            }}
            nextStep={() => {
              setFormState('ALIAS_FORM');
            }}
          />
        );
      case 'ALIAS_FORM':
        return (
          <AliasStep
            linkToken={linkToken ?? ''}
            displayNameOwner={validityData?.displayNameOwner ?? ''}
            crmAccountNumber={validityData?.crmAccountNumber ?? ''}
            fields={{
              title: fields?.aliasScreenTitle?.value,
              subTitle: fields?.aliasScreenContent?.value,
              buttonText: fields?.aliasScreenButtonText?.value,
              errorText: fields?.aliasScreenValidationNotification?.value.title,
              formLabel: fields?.aliasScreenFormLabel?.value,
              placeholderText: fields?.aliasScreenFormPlaceholderText?.value,
            }}
            setFormState={setFormState}
            nextStep={() => {
              setFormState('SUCCESS');
            }}
          />
        );
      case 'SUCCESS':
        return (
          <AlertStep
            fields={{
              title: fields?.successScreenTitle?.value,
              alertTitle: format(fields?.successScreenNotification?.value.title, {
                accountNumber: crmAccountNumberParam ?? '',
                aliasOwner: aliasOwnerParam ?? '',
                aliasCurrentUser: crmAccountAliasCurrentUserParam ?? '',
              }),
              alertSubtitle: fields?.successScreenNotification?.value.content,
              variant: 'success',
              buttonText: fields?.successScreenButtonLink?.value.text,
              link: fields?.successScreenButtonLink?.value.href,
            }}
          />
        );
      case 'LINKTOKEN_ERROR':
        return (
          <AlertStep
            fields={{
              title: fields?.linkTokenFailureTitle?.value,
              alertTitle: fields?.linkTokenFailureNotification?.value.title,
              alertSubtitle: fields?.linkTokenFailureNotification?.value.content,
              variant: 'error',
              buttonText: fields?.linkTokenFailureButtonText?.value,
              link: fields?.successScreenButtonLink?.value.href,
            }}
          />
        );
      case 'LINKTOKEN_EXPIRED':
        return (
          <LinkTokenExpiredStep
            fields={{
              title: fields?.linkTokenExpiredNotification?.value.title,
              subTitle: fields?.linkTokenExpiredNotification?.value.content,
              buttonText: fields?.linkTokenExpiredButtonLink?.value.text,
              secondaryButtonText: fields?.linkTokenExpiredSecondaryButtonLink?.value.text,
              link: fields?.linkTokenExpiredButtonLink?.value.href,
              secondaryLink: fields?.linkTokenExpiredSecondaryButtonLink?.value.href,
              email: validityData?.emailOwner || '',
            }}
          />
        );
    }
  }, [formState]);

  return (
    <Stretch>
      <Stack alignY={'center'}>
        <Box padding="8">
          <PageGrid>
            <Grid.Item gridColumn={{ initial: '1/-1', md: '3 / span 8', lg: '5 / span 4' }}>
              <Card as="div" corners="rounded" elevation="S" overflow="hidden">
                <Box padding="10">
                  <Stack gap="6">
                    {isLoading ? (
                      <Skeleton height={'200px'} />
                    ) : (
                      welcomeImage?.src && (
                        <Image src={welcomeImage.src} alt={welcomeImage.alt} width="100%" objectFit="contain" />
                      )
                    )}
                    {renderFormStep()}
                  </Stack>
                </Box>
              </Card>
            </Grid.Item>
          </PageGrid>
        </Box>
      </Stack>
    </Stretch>
  );
};

export default AccountInviteForm;
