import { Dispatch, FC, SetStateAction, useState } from 'react';

import { useRouter } from 'next/router';
import { signIn } from 'next-auth/react';

import { putEnecoBeXapiSiteApiV1AccountsLinkChild } from '@dc-be/client';
import { Box, Button, Heading, InputText, Stack, Text } from '@sparky';

import { formStates } from '../AccountInviteForm';

interface AliasStepProps {
  fields: {
    title: string;
    subTitle: string;
    errorText: string;
    formLabel: string;
    placeholderText: string;
    buttonText: string;
  };
  linkToken: string;
  displayNameOwner: string;
  crmAccountNumber: string;

  nextStep(): void;

  setFormState: Dispatch<SetStateAction<formStates>>;
}

const AliasStep: FC<AliasStepProps> = ({ fields, linkToken, displayNameOwner, crmAccountNumber, setFormState }) => {
  const [alias, setAlias] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [aliasError, setAliasError] = useState<string | undefined>(undefined);
  const { asPath } = useRouter();

  async function onSubmit() {
    if (alias === '' || alias === undefined) {
      setAliasError(fields.errorText);
    } else {
      setIsLoading(true);
      const { response } = await putEnecoBeXapiSiteApiV1AccountsLinkChild({
        body: {
          sessionToken: linkToken,
          alias,
        },
      });
      if (response.ok) {
        const successCallbackUrl = `${asPath}&status=SUCCESS&aliasOwner=${displayNameOwner}&accountNumber=${crmAccountNumber}&aliasCurrentUser=${alias}`;
        signIn('okta', { callbackUrl: successCallbackUrl });
      } else {
        setFormState('LINKTOKEN_ERROR');
        setIsLoading(false);
      }
    }
  }

  return (
    <>
      <Stack gap="4">
        <Heading as="h3" size="S">
          {fields?.title}
        </Heading>
        <Text>{fields?.subTitle}</Text>
        <Box paddingTop="2">
          <InputText
            error={aliasError}
            label={fields?.formLabel}
            placeholder={fields?.placeholderText}
            name={'alias'}
            value={alias}
            onChange={e => setAlias(e.target.value)}
          />
        </Box>
      </Stack>
      <Button onClick={onSubmit} isLoading={isLoading}>
        {fields?.buttonText}
      </Button>
    </>
  );
};

export default AliasStep;
