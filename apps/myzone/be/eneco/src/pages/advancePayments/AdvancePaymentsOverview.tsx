import { isFuture, isPast, isToday } from 'date-fns';

import { AdvanceAmountCollectionResponse } from '@dc-be/client';
import { useContent } from '@sitecore/common';
import { ManageAdvanceAmountRendering } from '@sitecore/types/ManageAdvanceAmount';
import { Stack } from '@sparky';

import AdvanceNotifications from './AdvanceNotifications';
import AdvancePaymentByEnergyTypes from './components/AdvancePaymentByEnergyTypes';
import { useAdvances } from './hooks/useAdvances';
import { AdvanceOverviewSkeletons } from './skeletons/AdvanceOverviewSkeletons';
import TotalAdvancesCard from './TotalAdvancesCard';
import { hasAdvanceAmountForContracts } from './utils/filterActiveAdvances';
import { VacancyCard } from './VacancyCard';
import { AddressFilter } from '../../components/AddressFilter';
import DataErrorNotification from '../../components/DataErrorNotification';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const getRelevantContractStartDate = (advanceData: AdvanceAmountCollectionResponse | null) => {
  const paymentsWithDates =
    advanceData?.advancePayments?.map(payment => ({
      ...payment,
      parsedDate: new Date(payment.contractDetails!.contractStartDate!),
    })) ?? [];

  const pastPayments = paymentsWithDates.filter(p => isPast(p.parsedDate) || isToday(p.parsedDate));

  if (pastPayments.length > 0) {
    pastPayments.sort((a, b) => b.parsedDate.getTime() - a.parsedDate.getTime());
    return pastPayments[0].contractDetails?.contractStartDate;
  }

  const futurePayments = paymentsWithDates.filter(p => isFuture(p.parsedDate));

  if (futurePayments.length > 0) {
    futurePayments.sort((a, b) => a.parsedDate.getTime() - b.parsedDate.getTime());
    return futurePayments[0].contractDetails?.contractStartDate;
  }

  return null;
};

const AdvanceOverview = () => {
  const { fields } = useContent<ManageAdvanceAmountRendering>();
  const { addressIdentifier, isLoading: isLoadingAddress, error: errorAddress } = useAddressOptions();

  const {
    advances: allAdvancesIncludingInactives,
    isLoading,
    error,
    activeAdvances,
    currentAmount,
    isInMeterReadingMonth,
    isDefaulter,
    reconcileAdvanceAmount,
    hideEdit,
    submitGlobalRecommendedAdvance,
    isCurrentAccountReader,
  } = useAdvances(addressIdentifier, {
    title: fields.commodityCard.recommendedAdvanceAmountNotification.value.title,
    text: fields.commodityCard.recommendedAdvanceAmountNotification.value.content,
    variant: fields.commodityCard.recommendedAdvanceAmountNotification.value.variant,
    isAlert: true,
  });

  const hasAdvanceAmounts = hasAdvanceAmountForContracts({
    advances: allAdvancesIncludingInactives,
  });
  const hasYearlyInvoicedMeter = activeAdvances?.advancePayments?.some(
    x => x.meterDetails?.invoiceFrequency === 'Yearly',
  );

  const relevantContractStartDate = getRelevantContractStartDate(activeAdvances);

  if (isLoading || isLoadingAddress) return <AdvanceOverviewSkeletons />;

  const fetchingAdvancesError = error !== undefined && allAdvancesIncludingInactives === undefined;
  const fetchingAddressError = errorAddress !== undefined && addressIdentifier === null;

  if (!isLoading && (fetchingAdvancesError || fetchingAddressError)) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Stack gap="6">
      <AddressFilter label={fields.data.addressSelectLabel?.value || 'Verbruiksadres [not in sitecore]'} />

      <AdvanceNotifications
        advanceData={activeAdvances}
        hasAdvanceData={hasAdvanceAmounts ?? false}
        fields={fields}
        hasAnActiveContract={!!hasAdvanceAmounts}
        isInMeterReadingMonth={!!isInMeterReadingMonth}
        relevantContractStartDate={relevantContractStartDate}
        hasYearlyInvoicedMeter={hasYearlyInvoicedMeter ?? false}
        isDefaulter={isDefaulter ?? false}
        isLoading={isLoading}
      />

      {hasAdvanceAmounts &&
        hasAdvanceAmounts &&
        hasYearlyInvoicedMeter &&
        relevantContractStartDate &&
        !isFuture(relevantContractStartDate) && (
          <TotalAdvancesCard
            fields={fields}
            recommendedAmount={activeAdvances?.totals?.recommendedAmount}
            currentAmount={currentAmount}
            hideRecommendedAmount={(isInMeterReadingMonth || isDefaulter) ?? true}
            reconcileAdvanceAmount={reconcileAdvanceAmount}
            hideEdit={hideEdit}
            addressIdentifier={addressIdentifier}
            globalRecommendedAdvancesOnChange={amount => submitGlobalRecommendedAdvance({ amount })}
          />
        )}

      {activeAdvances && hasYearlyInvoicedMeter && (
        <AdvancePaymentByEnergyTypes
          fields={fields}
          activeAdvancesData={activeAdvances}
          addressIdentifier={addressIdentifier}
        />
      )}

      <VacancyCard hideRequestLink={isDefaulter || isCurrentAccountReader} />
    </Stack>
  );
};

export default AdvanceOverview;
