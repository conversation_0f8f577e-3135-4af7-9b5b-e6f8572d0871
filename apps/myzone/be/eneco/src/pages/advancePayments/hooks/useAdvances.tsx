import { useMemo } from 'react';

import { useSWRConfig } from 'swr';

import RichText from '@components/RichText/RichText';
import { useSetNotification } from '@components-next/Notification/Notification';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts,
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { NotificationBoxProps } from '@sparky/types';
import { useTracking } from '@tracking';

import { ADDRESS_PARAM } from '../../../utils/addSearchParams';
import { calculateReconcileGlobalWithExistingCurrentAmounts } from '../utils/calculateReconcileAdvanceAmount';
import { canEditAdvanceAmount } from '../utils/canEditAdvances';
import { filteredAdvancesWithActiveContractAndCurrentAmount } from '../utils/filterActiveAdvances';

/**
 * A hook to fetch the advances for a specific address and provide shared logic
 * @param addressIdentifier
 * @param notificationFields - Fields object for notification content (different structure for different components)
 */
export const useAdvances = (addressIdentifierParam?: string, notificationFields?: NotificationBoxProps) => {
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
    isCurrentAccountReader,
  } = useSelfServiceAccount();
  const { query } = useRouter();
  const { data: session } = useSession();
  const { mutate } = useSWRConfig();
  const setNotification = useSetNotification();
  const { trackEventBE } = useTracking();

  const addressIdentifier = addressIdentifierParam ? addressIdentifierParam : (query[ADDRESS_PARAM] as string);
  const advanceCacheKey = `/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/advance-amounts`;

  const { data, isLoading, error } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
    },
    [advanceCacheKey],
    session,
  );

  const allAdvancesIncludingInactives = unwrapData(data);
  const path = { accountNumber, addressIdentifier };

  const activeAdvances = useMemo(
    () =>
      filteredAdvancesWithActiveContractAndCurrentAmount({
        advances: allAdvancesIncludingInactives,
      }),
    [allAdvancesIncludingInactives],
  );

  const currentAmount = activeAdvances?.totals?.currentAmount;

  const currentMonth = new Date().getMonth() + 1;
  const isInMeterReadingMonth = activeAdvances?.advancePayments?.some(payment => {
    const meterReadingMonth = payment.meterDetails?.meterReadingMonth;
    const meterType = payment.meterDetails?.meterType;

    if (meterReadingMonth == null) return true;
    return (
      (meterType === 'Digital' && meterReadingMonth + 1 === currentMonth) ||
      (meterType === 'Analogue' && meterReadingMonth === currentMonth)
    );
  });

  const isDefaulter = activeAdvances?.advancePayments?.some(x => x.details?.isDefaulter);

  const reconcileAdvanceAmount = calculateReconcileGlobalWithExistingCurrentAmounts({
    advances: activeAdvances,
  });

  const hideEdit = !canEditAdvanceAmount({
    isCurrentAccountReader,
    advancePayments: activeAdvances?.advancePayments ?? [],
  });

  const submitGlobalRecommendedAdvance = async ({ amount }: { amount: number | null | undefined }) => {
    if (amount == null) return;

    try {
      const { response } =
        await postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts({
          path: { ...path },
          body: { amount, type: 'Global' },
        });
      if (!response.ok) {
        throw new Error('request failed');
      }
      trackEventBE('be_bbp_advice', { status: 'success' });
    } catch (e) {
      void e;
      trackEventBE('be_bbp_advice', { status: 'failed' });
      throw new Error('request failed');
    }

    await mutate(advanceCacheKey);

    if (notificationFields) {
      setNotification({
        title: notificationFields.title,
        text: <RichText html={notificationFields.text?.toString()} />,
        variant: notificationFields.variant,
      });
    }
  };

  return {
    advances: allAdvancesIncludingInactives,
    addressIdentifier,
    path,
    session,
    advanceCacheKey,
    isLoading,
    error,
    activeAdvances,
    currentAmount,
    isInMeterReadingMonth,
    isDefaulter,
    reconcileAdvanceAmount,
    hideEdit,
    submitGlobalRecommendedAdvance,
    isCurrentAccountReader,
  };
};
