import { FC, useEffect } from 'react';

import { ParentSize } from '@visx/responsive';
import { StringParam, useQueryParam } from 'use-query-params';

import RichText from '@components/RichText/RichText';
import {
  ConsumptionTypeDto,
  ConsumptionResponse,
  PeakResponse,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
} from '@dc-be/client';
import { PeakAverageResponse, TimeFrameDto } from '@dc-be/client/types.gen';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be/useSelfServiceAccount';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { Bleed, Box, Card, Divider, Grid, Heading, IconButton, InputSelect, Stack, Stretch, Text } from '@sparky';
import { ChevronLeftIcon, ChevronRightIcon } from '@sparky/icons';

import { ConsumptionGraphicalOverviewGraph } from './components/ConsumptionGraphicalOverviewGraph';
import MeterReadingsTable from './components/MeterReadingTable';
import PeakValuesConsumptionData from './components/PeakValuesConsumptionData';
import PeakValuesConsumptionDataLegend from './components/PeakValuesConsumptionDataLegend';
import StandardConsumptionData from './components/StandardConsumptionData';
import StandardConsumptionDataLegend from './components/StandardConsumptionDataLegend';
import TotalAdvancesCardWrapper from './components/TotalAdvancesCardWrapper';
import { useConsumptionGraphTimeFrame } from './hooks/useConsumptionGraphTimeFrame';
import { useSelectedTimeframe } from './hooks/useSelectedTimeframe';
import { IsAddressInWalloniaOrBrussels } from './utils/addressUtils';
import { getFirstAndLastDayOfLastSixMonths, getFirstAndLastDayOfLastSixYears } from './utils/dateUtils';
import { AddressFilter } from '../../components/AddressFilter';
import InfoIconButton from '../../components/InfoIconButton';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const ConsumptionGraphicalOverview: FC = () => {
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();
  const { addressIdentifier, currentAddress } = useAddressOptions();
  const { data: session } = useSession();

  const periodOptions = fields.data.periodOptionsList.value.enum;
  const consumptionTypeOptions = fields.data.consumptionTypeOptionsList.value.enum;

  const [consumptionType, setConsumptionType] = useQueryParam('consumptionType', StringParam);
  const { timeFrame, setTimeFrame, startDate, endDate, getPeriodTitle, changePeriod } = useConsumptionGraphTimeFrame();
  const { setSelected } = useSelectedTimeframe((timeFrame ?? 'Monthly') as TimeFrameDto);

  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();

  const { data: serviceDeliveryPoints, isLoading: isLoadingServiceDeliveryPoints } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
    },
    [`/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/service-delivery-points`],
    session,
  );

  const serviceDeliveryPointsData = unwrapData(serviceDeliveryPoints);
  const hasDigitalElecMeter = serviceDeliveryPointsData?.serviceDeliveryPoints?.some(
    serviceDeliveryPoint => serviceDeliveryPoint.meterType === 'Digital' && serviceDeliveryPoint.type === 'Electricity',
  );

  useEffect(() => {
    if (!consumptionType) {
      setConsumptionType('Total');
    }
  }, [consumptionType, setConsumptionType]);

  useEffect(() => {
    if (!timeFrame) {
      setTimeFrame('Monthly');
    }
    // Reset the selected timeframe back to the latest when the time frame changes
    setSelected(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timeFrame]);

  const periodTitle = getPeriodTitle(timeFrame ?? 'Monthly', startDate, endDate);

  const isAddressInWalloniaOrBrussels = IsAddressInWalloniaOrBrussels(currentAddress?.details);
  const showAnalogueMeterMessage = !hasDigitalElecMeter && consumptionType === 'PeakValues';

  const renderConsumptionContent = (
    consumptionData: ConsumptionResponse | PeakResponse | null,
    peakValueAverage: PeakAverageResponse | null,
    isLoading: boolean,
  ) => (
    <Bleed top="4">
      <Box paddingRight={{ initial: '1', md: '6' }} paddingBottom="8">
        <ParentSize>
          {({ width }) => (
            <ConsumptionGraphicalOverviewGraph
              isLoading={isLoading || isLoadingServiceDeliveryPoints}
              timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
              consumptionType={(consumptionType as ConsumptionTypeDto) || 'Total'}
              consumptionData={consumptionData}
              width={width}
              height={500}
              showAnalogueMeterMessage={showAnalogueMeterMessage}
              peakValueAverage={peakValueAverage}
            />
          )}
        </ParentSize>
      </Box>
    </Bleed>
  );

  return (
    <Stretch>
      <Grid
        gridTemplateColumns="repeat(19, 1fr)"
        gridTemplateRows={{ initial: 'auto 1fr', lg: '1fr' }}
        rowGap={6}
        columnGap={{ md: 6 }}>
        <Grid.Item gridColumn={'-1/1'}>
          <Box paddingTop="10">
            <Heading as="h1">{fields.data.title.value}</Heading>
          </Box>
        </Grid.Item>
        <Grid.Item gridColumn={{ initial: '-1/1', lg: '1/14' }}>
          <Stack gap="6">
            <Stack direction="row" alignX="justify" wrap gap={4}>
              <Stack.Item>
                <AddressFilter label={fields.data.addressFilterLabel.value} />
              </Stack.Item>
              <Stack gap="2" direction="row" wrap>
                <InputSelect
                  label={fields.data.consumptionTypeLabel.value}
                  name="consumptionType"
                  options={
                    isAddressInWalloniaOrBrussels
                      ? consumptionTypeOptions.filter(option => option.value !== 'PeakValues')
                      : consumptionTypeOptions
                  }
                  placeholder=""
                  value={(consumptionType as ConsumptionTypeDto) || 'Total'}
                  onChange={e => setConsumptionType(e.currentTarget.value as ConsumptionTypeDto)}
                />
                <InputSelect
                  label={fields.data.periodLabel.value}
                  name="period"
                  options={periodOptions}
                  placeholder=""
                  value={timeFrame ?? 'Monthly'}
                  onChange={e => setTimeFrame(e.currentTarget.value as TimeFrameDto)}
                />
              </Stack>
            </Stack>
            <Card>
              <Stack>
                <Box padding="4">
                  <Stack alignX="justify" alignY="center" direction="row" gap="2">
                    <IconButton label="Previous period" onClick={() => changePeriod('previous')}>
                      <ChevronLeftIcon />
                    </IconButton>
                    <Heading as="h2" size="2XS">
                      {/* eslint-disable-next-line dxp-rules/no-inline-css */}
                      <span style={{ textTransform: 'capitalize' }}>{periodTitle}</span>
                    </Heading>
                    <IconButton
                      isDisabled={
                        endDate ===
                        (timeFrame === 'Monthly'
                          ? getFirstAndLastDayOfLastSixMonths().endDate
                          : getFirstAndLastDayOfLastSixYears().endDate)
                      }
                      label="Next period"
                      onClick={() => changePeriod('next')}>
                      <ChevronRightIcon />
                    </IconButton>
                  </Stack>
                </Box>
                <Divider />
                <Box paddingX="6" paddingTop="5">
                  <Stack direction="row" alignX="justify">
                    <Heading as="h3" size="3XS">
                      {fields.data.graphTitle.value}
                    </Heading>
                  </Stack>
                </Box>
                {consumptionType === 'PeakValues' ? (
                  <PeakValuesConsumptionData
                    addressIdentifier={addressIdentifier}
                    timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                    startDate={startDate}
                    endDate={endDate}
                    session={session}>
                    {({ consumptionData, peakValueAverage, isLoading }) =>
                      renderConsumptionContent(consumptionData, peakValueAverage, isLoading)
                    }
                  </PeakValuesConsumptionData>
                ) : (
                  <StandardConsumptionData
                    addressIdentifier={addressIdentifier}
                    consumptionType={consumptionType as Exclude<ConsumptionTypeDto, 'PeakValues'>}
                    timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                    startDate={startDate}
                    endDate={endDate}
                    session={session}>
                    {({ consumptionData, isLoading }) => renderConsumptionContent(consumptionData, null, isLoading)}
                  </StandardConsumptionData>
                )}
              </Stack>
            </Card>
            {consumptionType === 'PeakValues' && !showAnalogueMeterMessage && (
              <Stack direction="row" alignY="center" gap="1">
                <Text>{fields.data.disclaimerText.value}</Text>
                <InfoIconButton triggerText={fields.data.disclaimerText.value}>
                  <RichText html={fields.data.disclaimerInfoDescription.value} />
                </InfoIconButton>
              </Stack>
            )}
            {consumptionType !== 'Total' && consumptionType !== 'PeakValues' && (
              <StandardConsumptionData
                addressIdentifier={addressIdentifier}
                consumptionType={consumptionType as Exclude<ConsumptionTypeDto, 'PeakValues'>}
                timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                startDate={startDate}
                endDate={endDate}
                session={session}>
                {({ consumptionData }) => (
                  <MeterReadingsTable
                    consumptionData={consumptionData}
                    consumptionType={(consumptionType as ConsumptionTypeDto) || 'Total'}
                    addressIdentifier={addressIdentifier}
                    fields={fields.meterReadingsTable}
                  />
                )}
              </StandardConsumptionData>
            )}
          </Stack>
        </Grid.Item>
        <Grid.Item gridColumn={{ initial: '-1/1', lg: '14/19' }}>
          <Stack gap="4">
            {consumptionType === 'PeakValues' ? (
              <PeakValuesConsumptionData
                addressIdentifier={addressIdentifier}
                timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                startDate={startDate}
                endDate={endDate}
                session={session}>
                {({ consumptionData, peakValueAverage }) => (
                  <PeakValuesConsumptionDataLegend
                    consumptionData={consumptionData}
                    timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                    showAnalogueMeterMessage={showAnalogueMeterMessage}
                    consumptionType={consumptionType as ConsumptionTypeDto}
                    peakValueAverage={peakValueAverage}
                  />
                )}
              </PeakValuesConsumptionData>
            ) : (
              <StandardConsumptionData
                addressIdentifier={addressIdentifier}
                consumptionType={consumptionType as Exclude<ConsumptionTypeDto, 'PeakValues'>}
                timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                startDate={startDate}
                endDate={endDate}
                session={session}>
                {({ consumptionData }) => (
                  <StandardConsumptionDataLegend
                    consumptionData={consumptionData}
                    timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                  />
                )}
              </StandardConsumptionData>
            )}
            <TotalAdvancesCardWrapper />
          </Stack>
        </Grid.Item>
      </Grid>
    </Stretch>
  );
};

export default ConsumptionGraphicalOverview;
