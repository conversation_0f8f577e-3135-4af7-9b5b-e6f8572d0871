import { <PERSON>Bottom, AxisRight } from '@visx/axis';
import { localPoint } from '@visx/event';
import { GridRows } from '@visx/grid';
import { Group } from '@visx/group';
import { scaleBand, scaleLinear, scaleOrdinal } from '@visx/scale';
import { BarRounded, BarStack } from '@visx/shape';
import { BarGroupBar, BarStack as BarStackType, SeriesPoint } from '@visx/shape/lib/types';
import Text from '@visx/text/lib/Text';

import {
  ConsumptionResponse,
  ConsumptionTypeDto,
  PeakAverageResponse,
  PeakResponse,
  TimeFrameDto,
} from '@dc-be/client';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { useMediaQuery } from '@sparky/hooks';
import { SpinnerIcon } from '@sparky/icons';
// eslint-disable-next-line dxp-rules/no-custom-styling
import { getTokens, styled } from '@sparky/stitches';

import { TooltipContainer, TooltipData } from './TooltipContainer';
import { useSelectedTimeframe } from '../hooks/useSelectedTimeframe';
import { getAdjustedHeight, getAdjustedY } from '../utils/chartUtils';
import { getColorRange } from '../utils/colorMappings';
import { getConsumptionTotals, getInjectionTotals } from '../utils/dataCalculations';
import { filterConsumptionData } from '../utils/dataTransformers';

const { colors } = getTokens();

const GraphContainer = styled('div', {
  position: 'relative',
});

const SpinnerOverlay = styled('div', {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  pointerEvents: 'none',
});

export type BarStackProps = {
  width: number;
  height: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  events?: boolean;
  consumptionData: ConsumptionResponse | PeakResponse | null;
  timeFrame: TimeFrameDto;
  consumptionType: ConsumptionTypeDto;
  isLoading: boolean;
  showAnalogueMeterMessage?: boolean;
  peakValueAverage: PeakAverageResponse | null;
};

const defaultMargin = { top: 40, right: 60, bottom: 40, left: 0 };

export function ConsumptionGraphicalOverviewGraph({
  consumptionData,
  width,
  height,
  margin = defaultMargin,
  timeFrame,
  consumptionType,
  isLoading,
  showAnalogueMeterMessage,
  peakValueAverage = null,
}: BarStackProps) {
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();
  const { date: dateFormatter } = useFormatter();
  const isDesktop = useMediaQuery('lg');
  const showEmptyState = isLoading || showAnalogueMeterMessage;

  const data = filterConsumptionData(consumptionData, consumptionType);

  type Data = (typeof data)[number];
  type StackBarGroupBar = Omit<BarGroupBar<string>, 'value' | 'key'> & {
    bar: SeriesPoint<Data>;
    key: string;
  };

  const { selected, setSelected } = useSelectedTimeframe(timeFrame);

  const colorScaleDomain = Array.from(
    new Set(data.flatMap(d => Object.keys(d).filter(key => key !== 'date' && key !== 'total'))),
  );
  const hasSecondaryTariff = data.some(d => 'electricitySecondary' in d);

  const consumptionTotals = getConsumptionTotals(consumptionData);
  const injectionTotals = getInjectionTotals(consumptionData);
  const consumptionDomain =
    consumptionType === 'Injection'
      ? [0, Math.max(...injectionTotals)]
      : [consumptionType === 'Gas' ? 0 : -Math.max(...injectionTotals), Math.max(...consumptionTotals)];
  const colorRange = getColorRange(consumptionType ?? 'Total', hasSecondaryTariff);

  if (width < 10) return null;

  const xMax = width - margin.right;
  const yMax = height - margin.top - margin.bottom;

  const formatDate = (date: string) => {
    try {
      if (timeFrame === 'Yearly') {
        return dateFormatter.year(date);
      }
      return dateFormatter.monthShort(date);
    } catch {
      return '';
    }
  };

  const getDate = (d: Data) => d.date;

  const dateScaleDomain = showEmptyState ? Array.from({ length: 6 }, () => '') : (data.map(getDate) as string[]);

  const dateScale = scaleBand<string>({
    domain: dateScaleDomain,
    padding: isDesktop ? 0.6 : 0.2,
  });
  const consumptionScale = scaleLinear<number>({
    domain: showEmptyState ? [0, 100] : consumptionDomain,
    nice: true,
  });
  const colorScale = scaleOrdinal<string, string>({
    domain: colorScaleDomain,
    range: colorRange,
  });

  dateScale.rangeRound([0, xMax]);
  consumptionScale.range([yMax, 0]);

  const zeroLine = consumptionScale(0);

  const getDefaultBarProps = (
    groupBar: StackBarGroupBar,
    barStacks: BarStackType<Data, string>[],
    showTooltip: (args: { tooltipData: TooltipData; tooltipTop?: number; tooltipLeft?: number }) => void,
    hideTooltip: () => void,
  ) => {
    const stackOrder = barStacks
      .flatMap(({ bars }) => bars)
      .filter(({ index, height }) => index === groupBar.index && height)
      .sort((a, b) => {
        const aY = a.key === 'injection' ? zeroLine : a.y;
        const bY = b.key === 'injection' ? zeroLine : b.y;

        return aY > bY ? -1 : 1;
      });

    const hasRoundedBottomCorners = stackOrder[0].key === groupBar.key && groupBar.height < 0;
    const hasRoundedTopCorners = stackOrder.slice(-1)?.[0].key === groupBar.key && groupBar.bar[0] >= 0;

    const barDate = groupBar.bar.data.date?.split('T')[0];
    const isSelected = selected === barDate;

    return {
      ...groupBar,
      y: getAdjustedY({ consumptionType, y: groupBar.y, key: groupBar.key, zeroLine }),
      height: getAdjustedHeight({ consumptionType, height: groupBar.height, key: groupBar.key }),
      fill: groupBar.color,
      stroke: isSelected ? colors.backgroundDark.toString() : 'none',
      strokeWidth: isSelected ? 2 : 0,
      style: {
        cursor: 'pointer',
      },
      radius: isDesktop ? 16 : 8,
      topLeft: hasRoundedTopCorners,
      topRight: hasRoundedTopCorners,
      bottomLeft: hasRoundedBottomCorners,
      bottomRight: hasRoundedBottomCorners,
      onMouseMove: (event: React.MouseEvent<SVGPathElement>) => {
        const eventSvgCoords = localPoint(event);
        const left = groupBar.x + groupBar.width / 2;
        showTooltip({
          tooltipData: groupBar,
          tooltipTop: eventSvgCoords?.y,
          tooltipLeft: left,
        });
      },
      onMouseLeave: () => {
        hideTooltip();
      },
    };
  };

  const peakValuesAverageLine = consumptionScale(peakValueAverage?.average ?? 0);

  return (
    <TooltipContainer consumptionType={consumptionType}>
      {({ showTooltip, hideTooltip, containerRef }) => (
        <GraphContainer>
          <svg width={width} height={height} ref={containerRef}>
            <GridRows
              top={margin.top}
              left={margin.left}
              scale={consumptionScale}
              width={xMax}
              height={yMax}
              stroke={colors.borderDividerLowEmphasis.toString()}
            />
            <Group top={margin.top}>
              <BarStack<Data, string>
                data={showAnalogueMeterMessage ? [] : data}
                keys={colorScaleDomain}
                x={getDate}
                xScale={dateScale}
                yScale={consumptionScale}
                color={colorScale}>
                {barStacks =>
                  barStacks.map(barStack => {
                    return barStack.bars.map(bar => {
                      const height = getAdjustedHeight({ consumptionType, height: bar.height, key: bar.key });
                      if (height === 0 && bar.y === zeroLine) {
                        return (
                          <g key={`bar-stack-${barStack.index}-${bar.index}`}>
                            <BarRounded
                              height={16}
                              width={bar.width}
                              x={bar.x}
                              y={bar.y - 16}
                              radius={16}
                              top
                              fill={colors.graphsEstimatedPrimary.toString()}></BarRounded>
                          </g>
                        );
                      }
                      return (
                        <g key={`bar-stack-${barStack.index}-${bar.index}`}>
                          {height > 0 && (
                            <BarRounded
                              {...getDefaultBarProps(bar, barStacks, showTooltip, hideTooltip)}
                              key={null}
                              onClick={() => {
                                const date = bar.bar.data.date?.split('T')[0];
                                if (date) {
                                  setSelected(date);
                                }
                              }}></BarRounded>
                          )}
                        </g>
                      );
                    });
                  })
                }
              </BarStack>
            </Group>
            {showAnalogueMeterMessage && (
              <Text
                x={xMax / 2}
                y={yMax / 2}
                textAnchor="middle"
                fill={colors.textPrimary.toString()}
                fontSize={24}
                fontWeight={'bold'}>
                {fields.data.noDigitalMeterText?.value ?? 'Geen digitale meter gevonden'}
              </Text>
            )}
            <line
              x1={margin.left}
              x2={xMax}
              y1={isNaN(zeroLine) ? 0 : zeroLine + margin.top}
              y2={isNaN(zeroLine) ? 0 : zeroLine + margin.top}
              stroke={colors.borderDividerLowEmphasis.toString()}
              strokeWidth={2}
              strokeOpacity={0.5}
            />
            {consumptionType === 'PeakValues' &&
              peakValuesAverageLine > 0 &&
              timeFrame !== 'Yearly' &&
              !showAnalogueMeterMessage && (
                <line
                  x1={margin.left}
                  x2={xMax}
                  y1={peakValuesAverageLine + margin.top}
                  y2={peakValuesAverageLine + margin.top}
                  stroke={colors.feedbackInfo.toString()}
                  strokeWidth={1}
                  strokeDasharray="4 4"
                />
              )}
            <AxisBottom
              top={yMax + margin.top}
              scale={dateScale}
              tickFormat={formatDate}
              stroke={colors.borderDividerLowEmphasis.toString()}
              hideTicks
              tickLabelProps={{
                fill: colors.textPrimary.toString(),
                fontSize: 14,
                textAnchor: 'middle',
                fontFamily: 'Etelka',
              }}
            />
            <AxisRight
              top={margin.top}
              left={xMax}
              scale={consumptionScale}
              tickFormat={consumptionType === 'PeakValues' ? value => `${value} kW` : value => `${value} kWh`}
              hideAxisLine
              hideTicks
              tickLabelProps={{
                fontSize: 12,
                textAnchor: 'end',
                fill: showEmptyState ? colors.backgroundPrimary.toString() : colors.textPrimary.toString(),
                fontFamily: 'Etelka',
                verticalAnchor: 'middle',
                transform: 'translate(45, 0)',
              }}
            />
          </svg>
          {isLoading && (
            <SpinnerOverlay>
              <SpinnerIcon size="medium" />
            </SpinnerOverlay>
          )}
        </GraphContainer>
      )}
    </TooltipContainer>
  );
}
