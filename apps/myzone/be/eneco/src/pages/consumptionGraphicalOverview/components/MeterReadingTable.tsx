import RichText from '@components/RichText/RichText';
import {
  ConsumptionResponse,
  ConsumptionTypeDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadings,
  MeterReadingType,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useFormatter } from '@i18n';
import { MeterReadingsTable } from '@sitecore/types/ConsumptionGraphicalOverview';
import { Box, ButtonLink, Heading, Popover, Stack, Table, Text, TextLink } from '@sparky';
import { InfoIcon } from '@sparky/icons';

export type MeterReadingsTableProps = {
  consumptionData: ConsumptionResponse | null;
  consumptionType: ConsumptionTypeDto;
  addressIdentifier: string;
  fields: MeterReadingsTable;
};

const MeterReadingsTableComponent: React.FC<MeterReadingsTableProps> = ({
  consumptionType,
  addressIdentifier,
  fields,
}) => {
  const { data: session } = useSession();
  const { date, format } = useFormatter();
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();

  const { data: serviceDeliveryData } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
    {
      path: {
        accountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
      query: {
        includeMetersFromTerminatedAccounts: true,
      },
    },
    [`/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/service-delivery-points`],
    session,
  );

  const unwrappedSdps = unwrapData(serviceDeliveryData);
  const selectedEnergyType = consumptionType === 'Injection' ? 'Electricity' : consumptionType;
  const selectedEan = unwrappedSdps?.serviceDeliveryPoints?.find(sdp => sdp.type === selectedEnergyType)?.ean || '';

  const query = {
    eans: [selectedEan],
  };

  const { data } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadings,
    {
      path: {
        accountNumber,
      },
      query,
    },
    [`/eneco-be/xapi/site/api/v1/accounts/${accountNumber}/meter-readings`, selectedEan, selectedEnergyType],
    session,
  );

  const unwrappedData = unwrapData(data);

  if (unwrappedData === null) {
    return;
  }

  const meterReadingOriginMap: Record<MeterReadingType, string> = {
    Official: fields.originOfficialText?.value ?? '',
    Informative: fields.originInformativeText?.value ?? '',
    CustomerAdded: fields.originUserText?.value ?? '',
    RLP: fields.originRlpText?.value ?? '',
  };

  const indexesSortOrder = [
    'consumption-th',
    'offtake-th',
    'consumption-hi',
    'offtake-hi',
    'consumption-lo',
    'offtake-lo',
    'consumption-ex',
    'offtake-ex',
    'production-th',
    'injection-th',
    'production-hi',
    'injection-hi',
    'production-lo',
    'injection-lo',
    'production-ex',
    'injection-ex',
  ];

  const unSortedIndexes: string[] = Array.from(
    new Set(
      unwrappedData.flatMap(
        item =>
          item.indexValues
            ?.filter(iv => typeof iv.direction === 'string' && typeof iv.meterTimeFrameType === 'string')
            .map(iv => `${iv.direction}-${iv.meterTimeFrameType}`) ?? [],
      ),
    ),
  );

  const sortedIndexes = unSortedIndexes.sort(
    (a, b) => indexesSortOrder.indexOf(a.toLowerCase()) - indexesSortOrder.indexOf(b.toLowerCase()),
  );

  const dataByYear: Record<string, typeof unwrappedData> = {};

  unwrappedData.forEach(item => {
    const year = new Date(item.date!).getFullYear().toString();
    if (!dataByYear[year]) {
      dataByYear[year] = [];
    }
    dataByYear[year].push(item);
  });

  // Sort the entries within each year by descending date
  Object.keys(dataByYear).forEach(year => {
    dataByYear[year].sort((a, b) => new Date(b.date!).getTime() - new Date(a.date!).getTime());
  });

  const sortedYears = Object.keys(dataByYear).sort((a, b) => Number(b) - Number(a));

  if (consumptionType === 'Total') {
    return;
  }

  const meterReadingTableText =
    consumptionType === 'Gas' ? fields.meterReadingGasTableText?.value : fields.meterReadingElectricityTableText?.value;

  return (
    <Box paddingTop={4}>
      <Stack gap="8">
        <Heading as="h3" size="S">
          {fields.meterReadingTableTitle.value}
        </Heading>
        <Stack direction="row" gap="8" alignY="start" alignX={'justify'} wrap>
          <Stack.Item grow={{ initial: false, md: true }}>
            <Text size="BodyM">{format(meterReadingTableText, { eanCode: selectedEan })}</Text>
          </Stack.Item>
          <Stack.Item>
            <ButtonLink action="primary" tone="onLight">
              <Text>{fields.addMeterReadingButtonText.value}</Text>
            </ButtonLink>
          </Stack.Item>
        </Stack>

        {unwrappedData.length && (
          <>
            <Table>
              <Table.Header>
                {[
                  <Text key="datum">{fields.dateTableHeaderText.value}</Text>,
                  ...sortedIndexes.map(timeFrame => (
                    <Text align={'center'} key={timeFrame}>
                      {
                        fields.energyOptionsList.value.enum.find(
                          item => item.name.toLowerCase() === timeFrame?.toLowerCase(),
                        )?.label
                      }
                    </Text>
                  )),
                  <Text align={'center'} key="meterReadingType">
                    {fields.originText.value}
                  </Text>,
                  <Text align={'center'} key="remove">
                    {fields.deleteValueText.value}
                  </Text>,
                ]}
              </Table.Header>

              {sortedYears.map(year => (
                <Table.Section key={year} title={year} isCollapsible>
                  {dataByYear[year].map((item, idx) => {
                    const indexValueMap = item.indexValues?.reduce<Record<string, string>>((acc, iv) => {
                      if (iv) {
                        acc[iv.direction + '-' + iv.meterTimeFrameType] = iv.value ? iv.value + '' : '-';
                      }
                      return acc;
                    }, {});

                    return (
                      <Table.Row key={idx}>
                        {[
                          <Text key="date">{date.medium(item.date!)}</Text>,
                          ...sortedIndexes.map(timeFrame => (
                            <Text align={'center'} key={timeFrame}>
                              {indexValueMap?.[timeFrame ?? '']}
                            </Text>
                          )),
                          <Text align={'center'} key="type">
                            {(item.meterReadingType && meterReadingOriginMap[item.meterReadingType]) || ''}
                          </Text>,
                          <Text align={'center'}>
                            {item.meterReadingType !== 'CustomerAdded' ? '/' : fields.deleteText.value}
                          </Text>,
                        ]}
                      </Table.Row>
                    );
                  })}
                </Table.Section>
              ))}
            </Table>

            <Text>
              {fields.originMoreInfoText.value}
              <Popover
                side="top"
                size="large"
                title=""
                trigger={<TextLink rightIcon={<InfoIcon size="small" verticalAlign="center" />}>{''}</TextLink>}>
                <RichText html={fields.originMoreInfoPopoverContent.value} />
              </Popover>
            </Text>
          </>
        )}
      </Stack>
    </Box>
  );
};

export default MeterReadingsTableComponent;
