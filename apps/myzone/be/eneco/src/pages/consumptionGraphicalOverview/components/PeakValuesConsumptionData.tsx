import { FC, ReactNode } from 'react';

import { Session } from 'next-auth';

import { addMonths } from '@common/date';
import {
  DisplayModeDto,
  PeakAverageResponse,
  PeakResponse,
  TimeFrameDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptar,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarAverage,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSelfServiceAccount } from '@dxp-auth-be';

interface PeakValuesConsumptionDataProps {
  addressIdentifier: string;
  timeFrame: TimeFrameDto;
  startDate: string;
  endDate: string;
  session: Session | null;
  children: (data: {
    consumptionData: PeakResponse | null;
    peakValueAverage: PeakAverageResponse | null;
    isLoading: boolean;
  }) => ReactNode;
}

const PeakValuesConsumptionData: FC<PeakValuesConsumptionDataProps> = ({
  addressIdentifier,
  timeFrame,
  startDate,
  endDate,
  session,
  children,
}) => {
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();

  const query = {
    consumptionType: 'PeakValues' as const,
    displayMode: 'Consumption' as DisplayModeDto,
    timeFrame: timeFrame,
    startDate,
    endDate,
  };

  const { data, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptar,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
      query,
    },
    [
      `/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/consumption`,
      timeFrame,
      startDate,
      endDate,
      'PeakValues',
    ],
    session,
  );

  const today = new Date();
  const oneYearAgo = addMonths(today, -11);

  const queryPeakValueAverage = {
    consumptionType: 'PeakValues' as const,
    displayMode: 'Consumption' as DisplayModeDto,
    startDate: oneYearAgo.toISOString().split('T')[0],
    endDate: today.toISOString().split('T')[0],
  };

  const { data: peakValueAvererageData } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarAverage,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
      query: queryPeakValueAverage,
    },
    [
      `/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/captar/average`,
      oneYearAgo.toISOString().split('T')[0],
      today.toISOString().split('T')[0],
      'PeakValues',
    ],
    session,
  );

  const consumptionData = unwrapData(data);
  const peakValueAverage = unwrapData(peakValueAvererageData);

  return <>{children({ consumptionData, peakValueAverage, isLoading })}</>;
};

export default PeakValuesConsumptionData;
