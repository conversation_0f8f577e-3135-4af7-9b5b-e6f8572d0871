import { FC } from 'react';

import { addHours } from 'date-fns';

import { ConsumptionTypeDto, PeakAverageResponse, PeakResponse, TimeFrameDto } from '@dc-be/client';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { Card, Box, Heading, Divider, Text, Stack } from '@sparky';

import { useSelectedTimeframe } from '../hooks/useSelectedTimeframe';
import { getFirstDayOfMonth } from '../utils/dateUtils';

const PeakValuesConsumptionDataLegend: FC<{
  consumptionData: PeakResponse | null;
  timeFrame: TimeFrameDto;
  showAnalogueMeterMessage: boolean;
  consumptionType: ConsumptionTypeDto;
  peakValueAverage: PeakAverageResponse | null;
}> = ({ consumptionData, timeFrame, showAnalogueMeterMessage, consumptionType, peakValueAverage }) => {
  const { selected } = useSelectedTimeframe(timeFrame);
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();
  const { date: dateFormatter } = useFormatter();

  if (!selected) {
    return null;
  }

  if (showAnalogueMeterMessage) {
    return null;
  }

  const selectedPeriod = consumptionData?.periods?.find(period => getFirstDayOfMonth(period.date) === selected);

  return (
    <Card>
      {selectedPeriod?.date && selectedPeriod.value && (
        <>
          <Box paddingY="4" paddingX="6">
            <Heading as="h3" size="3XS">
              {fields.legend?.peakValuesTitle?.value ?? 'Piekwaarden'} {dateFormatter.monthLong(selectedPeriod.date)}{' '}
              {dateFormatter.year(selectedPeriod.date)}
            </Heading>
          </Box>
          <Divider />
          <Box paddingY="4" paddingX="6">
            <Stack gap="6">
              <Stack direction="row" gap="2" alignX="justify">
                <Text>
                  {dateFormatter.dayOfMonthNumeric(selectedPeriod.date)} {dateFormatter.monthLong(selectedPeriod.date)}{' '}
                  {dateFormatter.hourLong(selectedPeriod.date)} -{' '}
                  {dateFormatter.hourLong(addHours(selectedPeriod.date, 0.25))}
                </Text>
                <Text weight="bold" align="right">
                  {`${Number(Number(selectedPeriod.value).toFixed(2))} ${consumptionType === 'PeakValues' ? 'kW' : 'kWh'}`}
                </Text>
              </Stack>
              <Divider />
              <Stack direction="row" gap="2" alignX="justify">
                <Text>{fields.legend?.averagePeakValueLabel?.value ?? 'Gemiddeld over 12 maanden'}</Text>
                <Text weight="bold" align="right">
                  {`${Number(Number(peakValueAverage?.average).toFixed(2))} ${consumptionType === 'PeakValues' ? 'kW' : 'kWh'}`}
                </Text>
              </Stack>
            </Stack>
          </Box>
        </>
      )}
    </Card>
  );
};

export default PeakValuesConsumptionDataLegend;
