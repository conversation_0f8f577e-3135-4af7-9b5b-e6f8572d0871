import React, { memo } from 'react';

import { SeriesPoint } from '@visx/shape/lib/types';
import { useTooltip, useTooltipInPortal, defaultStyles } from '@visx/tooltip';

import { ConsumptionTypeDto } from '@dc-be/client';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { getTokens } from '@sparky/stitches';

const { colors } = getTokens();

const tooltipStyles = {
  ...defaultStyles,
  minWidth: 60,
  backgroundColor: colors.backgroundDark.toString(),
  color: colors.textInverted.toString(),
  borderRadius: 4,
  padding: 8,
};

type Data = Record<string, string | number | undefined>;

export type TooltipData = {
  bar: SeriesPoint<Data>;
  key: string;
  index: number;
  height: number;
  width: number;
  x: number;
  y: number;
  color: string;
};

interface TooltipContainerProps {
  consumptionType: ConsumptionTypeDto;
  children: (tooltipHandlers: {
    showTooltip: (args: { tooltipData: TooltipData; tooltipTop?: number; tooltipLeft?: number }) => void;
    hideTooltip: () => void;
    containerRef: (element: HTMLElement | SVGElement | null) => void;
  }) => React.ReactNode;
}

export const TooltipContainer = memo<TooltipContainerProps>(({ consumptionType, children }) => {
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();

  const { tooltipOpen, tooltipLeft, tooltipTop, tooltipData, hideTooltip, showTooltip } = useTooltip<TooltipData>();
  const { containerRef, TooltipInPortal } = useTooltipInPortal({
    scroll: true,
  });
  return (
    <>
      {children({ showTooltip, hideTooltip, containerRef })}
      {tooltipOpen && tooltipData && (
        // eslint-disable-next-line dxp-rules/no-inline-css
        <TooltipInPortal
          style={tooltipStyles}
          top={tooltipTop}
          left={tooltipLeft}
          key={`tooltip-${tooltipData.key}-${tooltipData.index}`}>
          <div>
            <p>
              {
                fields.data.consumptionTypeOptionsList.value.enum.find(c => c.value.toLowerCase() === tooltipData.key)
                  ?.label
              }
            </p>
            <p>
              {/* Number twice so decimals are only shown when needed */}
              {`${Number(Number(tooltipData.bar.data[tooltipData.key as keyof Data]).toFixed(2))} ${consumptionType === 'PeakValues' ? 'kW' : 'kWh'}`}
            </p>
          </div>
        </TooltipInPortal>
      )}
    </>
  );
});
