import { StringParam, useQueryParam } from 'use-query-params';

import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';

import { useAddressOptions } from '../../../hooks/useAddressOptions';
import { useAdvances } from '../../advancePayments/hooks/useAdvances';
import TotalAdvancesCard from '../../advancePayments/TotalAdvancesCard';
import {
  getAdvancesByConsumptionType,
  getFilteredCurrentAmount,
  getFilteredRecommendedAmount,
} from '../utils/filterAdvancesByConsumptionType';

const TotalAdvancesCardWrapper = () => {
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();
  const { addressIdentifier } = useAddressOptions();
  const [consumptionType] = useQueryParam('consumptionType', StringParam);

  const {
    activeAdvances,
    isInMeterReading<PERSON>onth,
    isDefaulter,
    reconcileAdvanceAmount,
    hideEdit,
    submitGlobalRecommendedAdvance,
  } = useAdvances(addressIdentifier, {
    title: fields.advanceCard?.recommendedAmountNotification?.value.title,
    text: fields.advanceCard?.recommendedAmountNotification?.value.content,
    variant: fields.advanceCard?.recommendedAmountNotification?.value.variant,
    isAlert: true,
  });

  const advancesByConsumptionType = getAdvancesByConsumptionType(activeAdvances, consumptionType);
  const filteredCurrentAmount = getFilteredCurrentAmount(advancesByConsumptionType);
  const filteredRecommendedAmount = getFilteredRecommendedAmount(advancesByConsumptionType);

  return (
    <TotalAdvancesCard
      fields={fields}
      recommendedAmount={filteredRecommendedAmount}
      currentAmount={filteredCurrentAmount}
      reconcileAdvanceAmount={reconcileAdvanceAmount}
      hideRecommendedAmount={(isInMeterReadingMonth || isDefaulter) ?? true}
      hideEdit={hideEdit}
      addressIdentifier={addressIdentifier}
      globalRecommendedAdvancesOnChange={amount => submitGlobalRecommendedAdvance({ amount })}
    />
  );
};

export default TotalAdvancesCardWrapper;
