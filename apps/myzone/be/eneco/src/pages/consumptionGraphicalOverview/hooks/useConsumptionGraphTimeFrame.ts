import { useMemo, useState } from 'react';

import { addMonths, addYears, subMonths, subYears } from 'date-fns';

import { TimeFrameDto } from '@dc-be/client';
import { useFormatter } from '@i18n';

import { getFirstAndLastDayOfLastSixYears, getFirstAndLastDayOfLastSixMonths } from '../utils/dateUtils';

export interface TimeFrame {
  timeFrame: TimeFrameDto;
  startDate: string;
  endDate: string;
}

export const useConsumptionGraphTimeFrame = () => {
  const [timeFrame, setTimeFrame] = useState<TimeFrameDto>('Monthly');
  const defaultStartDate = useMemo(() => {
    return timeFrame === 'Monthly'
      ? getFirstAndLastDayOfLastSixMonths().startDate
      : getFirstAndLastDayOfLastSixYears().startDate;
  }, [timeFrame]);
  const defaultEndDate = useMemo(() => {
    return timeFrame === 'Monthly'
      ? getFirstAndLastDayOfLastSixMonths().endDate
      : getFirstAndLastDayOfLastSixYears().endDate;
  }, [timeFrame]);
  const [startDate, setStartDate] = useState<string>(defaultStartDate);
  const [endDate, setEndDate] = useState<string>(defaultEndDate);

  const { date } = useFormatter();

  const handleTimeFrameChange = (timeFrame: TimeFrameDto) => {
    switch (timeFrame) {
      case 'Monthly':
        setTimeFrame(timeFrame);
        setStartDate(getFirstAndLastDayOfLastSixMonths().startDate);
        setEndDate(getFirstAndLastDayOfLastSixMonths().endDate);
        break;
      case 'Yearly':
        setTimeFrame(timeFrame);
        setStartDate(getFirstAndLastDayOfLastSixYears().startDate);
        setEndDate(getFirstAndLastDayOfLastSixYears().endDate);
        break;
    }
  };

  const changePeriod = (direction: 'previous' | 'next') => {
    const getBaseDate = (): Date => {
      if (direction === 'previous') {
        return timeFrame === 'Monthly' ? subMonths(new Date(startDate), 1) : subYears(new Date(startDate), 1);
      }

      // For 'next' direction
      return timeFrame === 'Monthly' ? addMonths(new Date(endDate), 6) : addYears(new Date(endDate), 6);
    };

    const baseDate = getBaseDate();
    const newTimeFrameData =
      timeFrame === 'Monthly'
        ? getFirstAndLastDayOfLastSixMonths(baseDate)
        : getFirstAndLastDayOfLastSixYears(baseDate);

    setTimeFrame(timeFrame);
    setStartDate(newTimeFrameData.startDate);
    setEndDate(newTimeFrameData.endDate);
  };

  const getPeriodTitle = (timeFrame: string, startDate: string, endDate: string) => {
    switch (timeFrame) {
      case 'Monthly': {
        const yearFirstMonth = date.year(startDate) !== date.year(endDate) ? ` ${date.year(startDate)}` : '';
        return `${date.monthLong(startDate)}${yearFirstMonth} - ${date.monthLong(endDate)} ${date.year(endDate)}`;
      }
      case 'Yearly':
        return `${date.year(startDate)} - ${date.year(endDate)}`;
    }
  };

  return { timeFrame, setTimeFrame: handleTimeFrameChange, getPeriodTitle, changePeriod, startDate, endDate };
};
