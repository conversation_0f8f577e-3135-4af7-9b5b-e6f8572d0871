import { ConsumptionResponse, PeakResponse } from '@dc-be/client';

import { isConsumptionResponse, isPeakResponse } from './typeGuards';

export const getInjectionTotals = (consumptionData: ConsumptionResponse | PeakResponse | null): number[] => {
  if (!isConsumptionResponse(consumptionData)) {
    return [0];
  }

  if (!consumptionData.periods) return [0];

  return consumptionData.periods.reduce(
    (allTotals, currentConsumptionPeriod) => {
      const total =
        currentConsumptionPeriod?.data?.reduce(
          (acc, d) => {
            if (d.energyType === 'Electricity' && d.direction === 'Injection') {
              acc.electricity += d.value ?? 0;
            }
            return acc;
          },
          { electricity: 0, gas: 0 },
        )?.electricity ?? 0;
      return [...allTotals, total];
    },
    [0],
  );
};

export const getConsumptionTotals = (consumptionData: ConsumptionResponse | PeakResponse | null): number[] => {
  if (!isConsumptionResponse(consumptionData)) {
    if (isPeakResponse(consumptionData)) {
      return consumptionData.periods?.map(p => p.value ?? 0) ?? [];
    }
    return [];
  }

  if (!consumptionData.periods) return [];

  return consumptionData.periods.reduce((allTotals, currentConsumptionPeriod) => {
    // total is the sum of all the values of the consumption type present minus the injection
    const total =
      (currentConsumptionPeriod.data?.reduce(
        (acc, d) => {
          if (d.energyType === 'Electricity' && d.direction === 'Consumption') {
            acc.electricity += d.value ?? 0;
          }
          return acc;
        },
        { electricity: 0, gas: 0 },
      )?.electricity ?? 0) + (currentConsumptionPeriod.data?.find(d => d.energyType === 'Gas')?.value ?? 0);
    return [...allTotals, total];
  }, [] as number[]);
};
