import { ConsumptionResponse, ConsumptionTypeDto, PeakResponse } from '@dc-be/client';

import { isConsumptionResponse, isPeakResponse } from './typeGuards';

/**
 * Filters and transforms consumption data based on the consumption type
 */
export const filterConsumptionData = (
  consumptionData: ConsumptionResponse | PeakResponse | null,
  consumptionType: ConsumptionTypeDto,
) => {
  if (!consumptionData) return [];

  if (consumptionType === 'PeakValues') {
    if (!isPeakResponse(consumptionData)) return [];
    return (
      consumptionData.periods?.map(p => {
        return {
          date: p.periodStart,
          value: p.value,
        };
      }) || []
    );
  }

  if (!isConsumptionResponse(consumptionData)) return [];

  if (consumptionType === 'Total') {
    return (
      consumptionData.periods?.map(p => {
        // The order of the keys is important for the color scale
        return {
          date: p.startDate,
          gas: p.data?.find(d => d.energyType === 'Gas')?.value ?? 0,
          electricity:
            p.data?.reduce(
              (acc, d) => {
                if (d.energyType === 'Electricity' && d.direction === 'Consumption') {
                  acc.electricity += d.value ?? 0;
                }
                return acc;
              },
              { electricity: 0 },
            )?.electricity ?? 0,
          injection:
            -1 *
            (p.data?.reduce(
              (acc, d) => {
                if (d.energyType === 'Electricity' && d.direction === 'Injection') {
                  acc.injection += d.value ?? 0;
                }
                return acc;
              },
              { injection: 0 },
            )?.injection ?? 0),
        };
      }) || []
    );
  }

  if (consumptionType === 'Injection') {
    return (
      consumptionData.periods?.map(p => {
        return {
          date: p.startDate,
          // we show the injection as a positive value here, so no need to invert it
          injection:
            p.data?.reduce(
              (acc, d) => {
                if (d.energyType === 'Electricity' && d.direction === 'Injection') {
                  acc.injection += d.value ?? 0;
                }
                return acc;
              },
              { injection: 0 },
            )?.injection ?? 0,
        };
      }) || []
    );
  }

  if (consumptionType === 'Electricity') {
    const hasBothTariffs = consumptionData.periods?.some(p =>
      p.data?.some(d => d.meterTimeFrameType === 'HI' || d.meterTimeFrameType === 'LO'),
    );
    return (
      consumptionData.periods?.map(p => {
        const electricityData = p.data?.reduce(
          (acc, d) => {
            if (d.energyType === 'Electricity' && d.direction === 'Consumption') {
              if (d.meterTimeFrameType === 'HI') acc.hi = d.value ?? 0;
              else if (d.meterTimeFrameType === 'LO') acc.lo = d.value ?? 0;
              else acc.total = d.value ?? 0;
            } else if (d.energyType === 'Electricity' && d.direction === 'Injection') {
              acc.injection += d.value ?? 0;
            }
            return acc;
          },
          { hi: 0, lo: 0, total: 0, injection: 0 },
        ) ?? { hi: 0, lo: 0, total: 0, injection: 0 };

        return {
          date: p.startDate,
          electricity: hasBothTariffs ? electricityData.hi : electricityData.total,
          ...(hasBothTariffs && { electricitySecondary: electricityData.lo }),
          injection: -electricityData.injection,
        };
      }) || []
    );
  }

  return (
    consumptionData.periods?.map(p => {
      return {
        date: p.startDate,
        [consumptionType?.toLowerCase()]: p.data?.find(d => d.energyType === consumptionType)?.value ?? 0,
      };
    }) || []
  );
};
