import { AdvanceAmountDto, AdvanceAmountCollectionResponse } from '@dc-be/client';

const getAdvancesByConsumptionType = (
  advances: AdvanceAmountCollectionResponse | null,
  consumptionType?: string | null,
) => {
  if (consumptionType?.toLowerCase() === 'total') {
    return advances?.advancePayments;
  }
  // if the consumption type is injection or peakvalues, we need to filter the advances by electricity
  if (consumptionType?.toLowerCase() === 'injection' || consumptionType?.toLowerCase() === 'peakvalues') {
    return advances?.advancePayments?.filter(advance => advance.meterDetails?.energyType === 'Electricity');
  }
  // 'electricity' or 'gas'
  return advances?.advancePayments?.filter(advance => advance.meterDetails?.energyType === consumptionType);
};

const getFilteredCurrentAmount = (advancePayments: AdvanceAmountDto[] | null | undefined) => {
  return advancePayments?.reduce((acc, advance) => acc + (advance.details?.currentAmount ?? 0), 0);
};

const getFilteredRecommendedAmount = (advancePayments: AdvanceAmountDto[] | null | undefined) => {
  return advancePayments?.reduce((acc, advance) => acc + (advance.details?.recommendedAmount ?? 0), 0);
};

export { getAdvancesByConsumptionType, getFilteredCurrentAmount, getFilteredRecommendedAmount };
