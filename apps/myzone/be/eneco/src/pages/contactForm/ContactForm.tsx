/* eslint-disable dxp-rules/no-custom-styling */

import React, { FocusEvent, useRef, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import { useChat } from '@components/chat/useChat';
import { Recaptcha, RecaptchaRef } from '@components/Recaptcha/Recaptcha';
import RichText from '@components/RichText/RichText';
import {
  postEnecoBeXapiSiteApiV1ContactContactForm,
  postEnecoBeXapiSiteApiV1ContactContactFormAttachment,
} from '@dc-be/client';
import { useSession } from '@dxp-auth';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import {
  ContactUsWithSubjectsExtended,
  GeneralLinkField,
  NotificationField,
  TextField,
} from '@sitecore/types/manual/be/ContactUsWithSubjectsExtended';
import {
  Box,
  Bucket,
  Button,
  Card,
  FilePicker,
  Grid,
  Heading,
  InputSelect,
  InputText,
  NotificationBox,
  PageGrid,
  RadioButton,
  RadioGroup,
  Stack,
  TextArea,
} from '@sparky';
import { styled } from '@sparky/stitches';
import { NotificationBoxProps } from '@sparky/types';
import { useTracking } from '@tracking';

import EmailOurSupportBanner from './components/EmailOurSupportBanner';
import { usePersistedForm } from './hooks/usePersistedForm';
import { CompanyNumberInput, isValidCompanyNumber } from '../../components/CompanyNumberInput';
import PhoneNumberInputField, { phoneValidationScheme } from '../../components/PhoneNumberInputField';
import { RecaptchaFormBe } from '../../utils/reCaptcha/ReCaptchaFormBe';
import { link } from '../../utils/richTextReplacements';

type UploadedFileType = { id: string; file: File; fileExtension?: string; fileName?: string };
export const CONTACT_FORM_SCHEMA_FIELDS = {
  SUBJECT: 'subject',
  QUESTION: 'question',
  FIRST_NAME: 'firstName',
  LAST_NAME: 'lastName',
  PHONE_NUMBER: 'phoneNumber',
  EMAIL: 'email',
  CUSTOMER_NUMBER: 'customerNumber',
  IS_COMPANY: 'isCompany',
  COMPANY_NUMBER: 'companyNumber',
  COMPANY_NAME: 'companyName',
  RECAPTCHA_TOKEN: 'recaptchaToken',
} as const;

const ContactForm: React.FC = () => {
  const { fields } = useContent<ContactUsWithSubjectsExtended>();
  const subjectItems = fields.items.map(item => item.fields.data);

  const { language } = useApplication();
  const { format } = useFormatter();
  const { mutate } = useSWRConfig();
  const { trackEventBE } = useTracking();

  const recaptchaRef = useRef<RecaptchaRef>(null);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);
  const [isLoadingSubmitLoginForm, setIsLoadingSubmitLoginForm] = useState<boolean>(false);
  const [files, setFiles] = useState<File[]>([]);

  const [hasFileError, setHasFileError] = useState(false);
  const [shouldOpenChatWindow, setShouldOpenChatWindow] = useState<boolean>(true);
  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [mount] = useChat();
  const { data: session } = useSession();

  const isLoggedIn = !!session;

  const contactDataSchema = yup.object({
    [CONTACT_FORM_SCHEMA_FIELDS.SUBJECT]: yup
      .string()
      .required(fields.contactForm.subjectFormField.value.requiredMessage),
    [CONTACT_FORM_SCHEMA_FIELDS.QUESTION]: yup
      .string()
      .required(fields.contactForm.questionFormField.value.requiredMessage),
    [CONTACT_FORM_SCHEMA_FIELDS.FIRST_NAME]: yup
      .string()
      .required(fields.contactForm.firstNameFormField.value.requiredMessage),
    [CONTACT_FORM_SCHEMA_FIELDS.LAST_NAME]: yup
      .string()
      .required(fields.contactForm.firstNameFormField.value.requiredMessage),
    [CONTACT_FORM_SCHEMA_FIELDS.PHONE_NUMBER]: phoneValidationScheme({
      validationMessage: fields.contactForm.phoneNumberFormField.value.validationMessage,
      isRequired: true,
    }),
    [CONTACT_FORM_SCHEMA_FIELDS.EMAIL]: yup
      .string()
      .email(fields.contactForm.emailFormField.value.validationMessage)
      .required(fields.contactForm.emailFormField.value.requiredMessage),
    [CONTACT_FORM_SCHEMA_FIELDS.CUSTOMER_NUMBER]: yup
      .string()
      .test('required', fields.contactForm.customerNumberFormField.value.requiredMessage, function (value) {
        if (isLoggedIn) return !!value;
        return true;
      }),
    [CONTACT_FORM_SCHEMA_FIELDS.IS_COMPANY]: yup
      .string()
      .oneOf(['yes', 'no'], fields.contactForm.professionalUserFormField.value.requiredMessage)
      .required(fields.contactForm.professionalUserFormField.value.requiredMessage),
    [CONTACT_FORM_SCHEMA_FIELDS.COMPANY_NUMBER]: yup
      .string()
      .test('pattern', fields.contactForm.companyNumberFormField?.value.validationMessage, function (value) {
        const isCompany = this.parent.isCompany === 'yes';
        if (!isCompany) return true;
        return isValidCompanyNumber(value);
      }),
    [CONTACT_FORM_SCHEMA_FIELDS.COMPANY_NAME]: yup
      .string()
      .test('required', fields.contactForm.companyNameFormField.value.requiredMessage, function (value) {
        const isCompany = this.parent.isCompany === 'yes';
        if (isCompany) return !!value;
        return true;
      }),
    [CONTACT_FORM_SCHEMA_FIELDS.RECAPTCHA_TOKEN]: yup.string().nullable(),
  });

  type FormValues = yup.InferType<typeof contactDataSchema>;

  const resolver = yupResolver(contactDataSchema);

  const form = useForm<FormValues>({
    resolver,
    defaultValues: { isCompany: 'no' },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { clearPersistentData } = usePersistedForm(form as any);

  const {
    register,
    watch,
    control,
    formState: { errors },
  } = form;

  const question = watch(CONTACT_FORM_SCHEMA_FIELDS.QUESTION);
  const questionRef = useRef(question);
  const subject = watch(CONTACT_FORM_SCHEMA_FIELDS.SUBJECT);
  const isCompany = watch(CONTACT_FORM_SCHEMA_FIELDS.IS_COMPANY);
  const customerNumber = watch(CONTACT_FORM_SCHEMA_FIELDS.CUSTOMER_NUMBER);

  const { onChange: onChangeSubject, ...registeredSubject } = register(CONTACT_FORM_SCHEMA_FIELDS.SUBJECT);
  const { onBlur: onBlurCustomerNumber, ...registeredCustomerNumber } = register(
    CONTACT_FORM_SCHEMA_FIELDS.CUSTOMER_NUMBER,
  );
  const { onBlur: onBlurRegisteredQuestion, ...registeredQuestion } = register(CONTACT_FORM_SCHEMA_FIELDS.QUESTION);

  const submitForm: SubmitHandler<FormValues> = async ({
    email,
    firstName,
    isCompany,
    lastName,
    question,
    subject,
    companyName,
    customerNumber,
    phoneNumber,
    recaptchaToken,
    companyNumber,
  }) => {
    try {
      setSuccess(false);
      setIsLoadingSubmit(true);
      const filesArray = await uploadFiles(files);
      const { response } = await postEnecoBeXapiSiteApiV1ContactContactForm({
        body: {
          firstName,
          lastName,
          question,
          company: isCompany === 'yes',
          companyName,
          companyNumber: companyNumber?.replaceAll('.', '').trim(),
          email,
          subject,
          telephone: phoneNumber,
          customerNumber,
          language: language === 'nl' ? 'DUTCH' : 'FRENCH',
          recaptchaToken,
          attachments: filesArray.map(x => ({
            ixosArchiveId: x?.id ?? '',
            fileExtension: x?.fileExtension ?? '',
            fileName: x?.fileName ?? '',
          })),
        },
      });
      if (!response.ok) {
        throw new Error('request failed');
      }
      trackEventBE('be_cf_submit', {
        status: 'success',
        data: { hasCustomerNumber: !!customerNumber?.trim(), question, hasAttachments: !!filesArray.length },
      });
      setIsLoadingSubmit(false);
      clearPersistentData();
      setFiles([]);
      setSuccess(true);
      await mutate('/contact/contact/form');
    } catch (e) {
      void e;
      setIsLoadingSubmit(false);
      setSuccess(false);
      trackEventBE('be_cf_submit', { status: 'failed' });
      setError({
        isAlert: false,
        title: fields.contactForm.errorNotification.value.title,
        text: fields.contactForm.errorNotification.value.content,
        variant: fields.contactForm.errorNotification.value.variant,
      });
    }
  };

  const uploadFiles = async (files: File[]): Promise<UploadedFileType[]> => {
    const promises = files.map(async (file): Promise<UploadedFileType | undefined> => {
      try {
        const { response, data } = await postEnecoBeXapiSiteApiV1ContactContactFormAttachment({
          body: {
            form: file,
          },
        });
        if (response.ok) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const dataResponse = (data as any)?.data;
          return {
            file: file,
            id: dataResponse?.ixosArchiveId,
            fileName: dataResponse?.fileName,
            fileExtension: dataResponse?.fileExtension,
          };
        }
      } catch (e) {
        void e;
        setError({
          isAlert: false,
          title: fields.contactForm.errorNotification.value.title,
          text: fields.contactForm.errorNotification.value.content,
          variant: fields.contactForm.errorNotification.value.variant,
        });
      }
    });

    const result = await Promise.all(promises);
    const resultFiltered: UploadedFileType[] = result.filter((x): x is UploadedFileType => x !== undefined);
    return resultFiltered;
  };

  const handleQuestionOnBlur = (event: FocusEvent<HTMLTextAreaElement>) => {
    if (questionRef.current == null) trackEventBE('cf_question', { data: { question } });
    else trackEventBE('cf_question_revised', { data: { question } });

    questionRef.current = question;

    if (shouldOpenChatWindow) {
      setShouldOpenChatWindow(false);
      trackEventBE('be_chatbot_opened_chat');
      mount({
        visibility: 'open',
        askText: `${question}`,
        topic: fields.contactForm?.chatTopicText?.value ?? 'herosearch1',
        sendTopicAndAskText: true,
      });
    }
    onBlurRegisteredQuestion(event);
  };

  return (
    <>
      <EmailOurSupportBanner />
      <Box backgroundColor="backgroundPrimary" paddingY={16}>
        <PageGrid>
          <Grid.Item gridColumn={{ initial: '1/-1', md: '3/-3' }}>
            <Stack gap="10">
              <LoginForCustomers
                fields={fields}
                isLoadingSubmitLoginForm={isLoadingSubmitLoginForm}
                isLoggedIn={isLoggedIn}
                language={language}
                setIsLoadingSubmitLoginForm={setIsLoadingSubmitLoginForm}
              />
              <FormProvider {...form}>
                <RecaptchaFormBe recaptchaRef={recaptchaRef} submitForm={submitForm}>
                  <Stack gap="10">
                    <Stack.Item>
                      <Stack gap={4}>
                        <Heading size="S" as="h2">
                          {fields.contactForm.yourQuestionTitle.value}
                        </Heading>
                        <InputSelect
                          options={subjectItems.map(subject => ({
                            label: subject.label.value,
                            value: subject.valueText.value,
                          }))}
                          label={fields.contactForm.subjectFormField.value.label}
                          hint={fields.contactForm.subjectFormField.value.hint}
                          placeholder={fields.contactForm.subjectFormField.value.placeholder}
                          error={errors.subject?.message as string}
                          onChange={e => {
                            trackEventBE('cf_topic_select', { status: 'click', data: { subject } });
                            onChangeSubject(e);
                          }}
                          {...registeredSubject}
                        />
                        <SubjectNotification selectedSubjectValue={subject} subjectItems={subjectItems} />
                        <TextArea
                          rows={6}
                          label={fields.contactForm.questionFormField.value.label}
                          placeholder={fields.contactForm.questionFormField.value.placeholder}
                          error={errors.question?.message as string}
                          onBlur={handleQuestionOnBlur}
                          {...registeredQuestion}
                        />
                        <FilePicker
                          files={files}
                          hasError={hasFileError}
                          name="files"
                          setFiles={files => {
                            trackEventBE('be_cf_file_upload');
                            setFiles(files);
                          }}
                          setHasError={setHasFileError}
                          maxAmount={5}
                          maxTotalSize={25}
                          accept={['PDF', 'PNG', 'JPG', 'JPEG', 'DOC', 'DOCX']}
                        />
                      </Stack>
                    </Stack.Item>
                    <Stack.Item>
                      <Stack gap={4}>
                        <Heading size="S" as="h2">
                          {fields.contactForm.yourDataTitle.value}
                        </Heading>
                        <Heading size="3XS" as="h3">
                          {fields.contactForm.yourDataSubTitle.value}
                        </Heading>
                        <InputText
                          error={errors.firstName?.message as string}
                          label={fields?.contactForm.firstNameFormField.value.label}
                          placeholder={fields?.contactForm.firstNameFormField.value.placeholder}
                          {...register('firstName')}
                        />
                        <InputText
                          error={errors.lastName?.message as string}
                          label={fields?.contactForm.lastNameFormField.value.label}
                          placeholder={fields?.contactForm.lastNameFormField.value.placeholder}
                          {...register('lastName')}
                        />
                        <PhoneNumberInputField
                          label={fields.contactForm.phoneNumberFormField.value.label}
                          hint={<RichText html={fields.contactForm.phoneNumberFormField.value.hint} />}
                          placeholder={fields.contactForm.phoneNumberFormField.value.placeholder}
                          {...register('phoneNumber')}
                        />
                        <InputText
                          error={errors.email?.message as string}
                          label={fields?.contactForm.emailFormField.value.label}
                          placeholder={fields?.contactForm.emailFormField.value.placeholder}
                          {...register('email')}
                        />
                        <InputText
                          error={errors.customerNumber?.message as string}
                          label={fields?.contactForm.customerNumberFormField.value.label}
                          placeholder={fields?.contactForm.customerNumberFormField.value.placeholder}
                          onBlur={() => {
                            trackEventBE('cf_klantnummer_filled', { data: { customerNumber } });
                          }}
                          {...registeredCustomerNumber}
                        />
                      </Stack>
                    </Stack.Item>

                    <Stack.Item>
                      <Stack gap={4}>
                        <label htmlFor="isCompany">
                          <Heading size="S" as="h2">
                            {fields.contactForm.professionalUserFormField.value.label}
                          </Heading>
                        </label>
                        <Controller
                          control={control}
                          name="isCompany"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <RadioGroup
                                name="isCompany"
                                aria-labelledby="isCompany"
                                direction="row"
                                value={value as string}
                                onValueChange={onChange}>
                                {fields.contactForm.professionalUserFormField.value.options
                                  .sort((a, b) => b.value.localeCompare(a.value))
                                  .map(option => {
                                    const optionValue = option.name?.toLocaleLowerCase();
                                    const isSelected = value === optionValue;
                                    return (
                                      <div key={optionValue} onClick={() => onChange(optionValue)}>
                                        {isSelected ? (
                                          <SelectedRadioButtonCard corners="rounded" elevation="S">
                                            <RadioButton value={optionValue}>{option.label}</RadioButton>
                                          </SelectedRadioButtonCard>
                                        ) : (
                                          <UnSelectedRadioButtonCard corners="rounded" elevation="S">
                                            <RadioButton value={optionValue}>{option.label}</RadioButton>
                                          </UnSelectedRadioButtonCard>
                                        )}
                                      </div>
                                    );
                                  })}
                              </RadioGroup>
                            );
                          }}
                        />
                        {isCompany === 'yes' && (
                          <>
                            <Controller
                              control={control}
                              name="companyNumber"
                              render={({ field: { onChange, name } }) => (
                                <CompanyNumberInput
                                  label={fields.contactForm?.companyNumberFormField?.value?.label}
                                  name={name}
                                  placeholder={fields?.contactForm?.companyNumberFormField?.value?.placeholder}
                                  error={errors.companyNumber?.message}
                                  onChange={onChange}
                                  hint={<RichText html={fields.contactForm?.companyNumberFormField?.value?.hint} />}
                                />
                              )}
                            />
                            <InputText
                              error={errors.companyName?.message as string}
                              label={fields?.contactForm.companyNameFormField.value.label}
                              placeholder={fields?.contactForm.companyNameFormField.value.placeholder}
                              {...register('companyName')}
                            />
                          </>
                        )}
                      </Stack>
                    </Stack.Item>

                    <Stack.Item>
                      <Recaptcha hasError={!!errors.recaptchaToken} ref={recaptchaRef} />
                    </Stack.Item>
                    <Stack.Item>
                      <Stack gap={6} direction="row" alignY="center">
                        <SubmitButton type="submit" isLoading={isLoadingSubmit}>
                          <span>{fields.contactForm.sendButtonLink.value.text}</span>
                        </SubmitButton>
                        <Stack.Item>
                          <RichText
                            html={format(fields.contactForm.privacyStatementContent.value, {
                              url: `<a href="${fields.contactForm.privacyStatementLink.value.href}" color="textBrand">${fields.contactForm.privacyStatementLink.value.text}</a>`,
                            })}
                            replacements={link}
                          />
                        </Stack.Item>
                      </Stack>
                    </Stack.Item>
                    {error && <NotificationBox {...error} />}
                    {success && (
                      <NotificationBox
                        text={fields.contactForm.successNotification.value.content}
                        title={fields.contactForm.successNotification.value.title}
                        variant={fields.contactForm.successNotification.value.variant}
                        isAlert={false}
                      />
                    )}
                  </Stack>
                </RecaptchaFormBe>
              </FormProvider>
            </Stack>
          </Grid.Item>
        </PageGrid>
      </Box>
    </>
  );
};

export default ContactForm;

const SubjectNotification = ({
  selectedSubjectValue,
  subjectItems,
}: {
  selectedSubjectValue: string;
  subjectItems: { notification: NotificationField; valueText: TextField; label: TextField; link: GeneralLinkField }[];
}) => {
  const { format } = useFormatter();
  if (!selectedSubjectValue) return null;

  const selectedSubjectItem = subjectItems.find(x => x.valueText.value === selectedSubjectValue);

  if (!selectedSubjectItem?.notification?.value?.content) return null;

  return (
    <NotificationBox
      title={selectedSubjectItem.notification.value.title ?? '[not in sitecore]: title'}
      text={
        <RichText
          html={format(selectedSubjectItem.notification?.value?.content ?? '[not in sitecore]: content', {
            url: `<a href="${selectedSubjectItem?.link?.value?.href ?? ''}">${selectedSubjectItem?.link?.value?.text ?? '[not in sitecore]: url link missing'}</a>`,
          })}
          replacements={link}
        />
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      variant={(selectedSubjectItem.notification?.value?.variant as any) ?? '[not in sitecore]: variant'}
      isAlert={false}
    />
  );
};

const LoginForCustomers = ({
  fields,
  isLoadingSubmitLoginForm,
  isLoggedIn,
  setIsLoadingSubmitLoginForm,
  language,
}: {
  isLoggedIn: boolean;
  fields: ContactUsWithSubjectsExtended['fields'];
  setIsLoadingSubmitLoginForm: (bool: boolean) => void;
  isLoadingSubmitLoginForm: boolean;
  language: string;
}) => {
  const { push, asPath } = useRouter();
  const { trackEventBE } = useTracking();

  if (isLoggedIn) return null;

  const encodedAsPath = encodeURIComponent(asPath);
  const isProd = process.env.NODE_ENV === 'production';
  const returnUrl = isProd ? `/login?returnUrl=${encodedAsPath}` : `/login?returnUrl=/${language}${encodedAsPath}`;

  return (
    <Bucket title={fields.loginProposal.title.value}>
      <Bucket.Content>
        <span>{fields.loginProposal.text.value}</span>
      </Bucket.Content>
      <Bucket.Footer>
        <Bucket.Actions>
          <Button
            type="button"
            onClick={() => {
              trackEventBE('be_cf_click_login_cta', { status: 'click' });
              setIsLoadingSubmitLoginForm(true);
              push(returnUrl);
            }}
            isLoading={isLoadingSubmitLoginForm}>
            {fields.loginProposal.link.value.text}
          </Button>
        </Bucket.Actions>
      </Bucket.Footer>
    </Bucket>
  );
};

const UnSelectedRadioButtonCard = styled(Card, {
  border: '0',
  width: 'unset',
  padding: '$6 $4',
  cursor: 'pointer',
});

const SelectedRadioButtonCard = styled(UnSelectedRadioButtonCard, {
  border: '2px solid $borderSelected',
});

const SubmitButton = styled(Button, { minWidth: 'unset !important' });
