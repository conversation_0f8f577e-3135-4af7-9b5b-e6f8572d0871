/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect } from 'react';

import { FieldValues, UseFormReturn } from 'react-hook-form';

import { useApplication } from '@common/application';
import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { ContactUsWithSubjectsExtended } from '@sitecore/types/manual/be/ContactUsWithSubjectsExtended';

import { CONTACT_FORM_SCHEMA_FIELDS } from '../ContactForm';

export const usePersistedForm = (form: UseFormReturn<FieldValues>) => {
  const { selectedAccount } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { fields } = useContent<ContactUsWithSubjectsExtended>();

  const { watch, setValue, reset } = form;
  const { searchParams } = useApplication();
  const subjectSearchParam = searchParams.get('subject');

  const subject = watch(CONTACT_FORM_SCHEMA_FIELDS.SUBJECT);
  const question = watch(CONTACT_FORM_SCHEMA_FIELDS.QUESTION);

  const { data } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
      },
    },
    [`/accounts/${selectedAccount.crmAccountNumber}/contact/details`],
    session,
  );

  const contactData = unwrapData(data);

  useEffect(() => {
    const stored = localStorage.getItem('contactForm');
    if (!stored) return;
    try {
      const { subject: s = '', question: q = '' } = JSON.parse(stored);
      setValue(CONTACT_FORM_SCHEMA_FIELDS.SUBJECT, s);
      setValue(CONTACT_FORM_SCHEMA_FIELDS.QUESTION, q);
    } catch (e) {
      void e;
      localStorage.removeItem('contactForm');
      throw new Error('Error parsing persistent state in contact form');
    }
  }, []);

  useEffect(() => {
    if (subjectSearchParam && fields.items) {
      const subjectItems = fields.items.map(item => item.fields.data);
      if (subjectItems.some(subject => subject.valueText.value === subjectSearchParam))
        setValue(CONTACT_FORM_SCHEMA_FIELDS.SUBJECT, subjectSearchParam);
    }
  }, [subjectSearchParam]);

  useEffect(() => {
    const data = { subject, question };
    if (subject != null && question != null) {
      localStorage.setItem('contactForm', JSON.stringify(data));
    }
  }, [subject, question]);

  useEffect(() => {
    contactData?.firstName && setValue(CONTACT_FORM_SCHEMA_FIELDS.FIRST_NAME, contactData.firstName);
    contactData?.lastName && setValue(CONTACT_FORM_SCHEMA_FIELDS.LAST_NAME, contactData.lastName);
    contactData?.email && setValue(CONTACT_FORM_SCHEMA_FIELDS.EMAIL, contactData.email);
    contactData?.accountNumber && setValue(CONTACT_FORM_SCHEMA_FIELDS.CUSTOMER_NUMBER, contactData.accountNumber);
    if (contactData?.customerType === 'Residential') {
      setValue(CONTACT_FORM_SCHEMA_FIELDS.IS_COMPANY, 'no');
      (contactData?.mobileNumber || contactData?.telephoneNumber) &&
        setValue(CONTACT_FORM_SCHEMA_FIELDS.PHONE_NUMBER, contactData.mobileNumber || contactData.telephoneNumber);
    } else {
      setValue(CONTACT_FORM_SCHEMA_FIELDS.IS_COMPANY, 'yes');
      contactData?.companyData?.name && setValue(CONTACT_FORM_SCHEMA_FIELDS.COMPANY_NAME, contactData.companyData.name);
      contactData?.companyData?.companyNumber &&
        setValue(CONTACT_FORM_SCHEMA_FIELDS.COMPANY_NUMBER, contactData.companyData.companyNumber);
      (contactData?.telephoneNumber || contactData?.mobileNumber) &&
        setValue(CONTACT_FORM_SCHEMA_FIELDS.PHONE_NUMBER, contactData.telephoneNumber || contactData.mobileNumber);
    }
  }, [contactData]);

  const clearPersistentData = () => {
    try {
      localStorage.removeItem('contactForm');
      reset();
    } catch (e) {
      void e;
      throw new Error('Something went wrong clearing form state');
    }
  };

  return { clearPersistentData };
};
