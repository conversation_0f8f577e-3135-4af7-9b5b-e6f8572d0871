import { FC } from 'react';

import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdNextBestActions,
  NextBestActionBlockDto,
  NextBestActionCardResponse,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth/useSession';
import { useSelfServiceAccount } from '@dxp-auth-be/useSelfServiceAccount';
import { useContent, useLayoutData } from '@sitecore/common';
import { DashboardRendering } from '@sitecore/types/manual/Dashboard';
import { Box, Grid, Heading, PageGrid } from '@sparky';

import { DashboardBlock } from './components/DashboardBlock';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const createBlockOrderMapping = (fields: DashboardRendering['fields']) => {
  const blockToOrder: Record<NextBestActionBlockDto, number> = {
    Pay: parseInt(fields.payBlockOrderNumber?.value || '0', 10),
    Adv: parseInt(fields.advBlockOrderNumber?.value || '0', 10),
    Usage: parseInt(fields.usageBlockOrderNumber?.value || '0', 10),
    Sit: parseInt(fields.sitBlockOrderNumber?.value || '0', 10),
    Eng: parseInt(fields.engBlockOrderNumber?.value || '0', 10),
    Pers: parseInt(fields.persBlockOrderNumber?.value || '0', 10),
    Any: 999,
  };

  return blockToOrder;
};

const GRID_AREAS = ['one', 'two', 'three', 'four', 'five', 'six'];

const sortCardsByOrder = (
  cards: NextBestActionCardResponse[] | null | undefined,
  blockOrderMapping: Record<NextBestActionBlockDto, number>,
): NextBestActionCardResponse[] => {
  if (!cards) return [];

  return [...cards].sort((a, b) => {
    const orderA = blockOrderMapping[a.block || 'Any'];
    const orderB = blockOrderMapping[b.block || 'Any'];
    return orderA - orderB;
  });
};

const Dashboard: FC = () => {
  const { fields } = useContent<DashboardRendering>();
  const { route } = useLayoutData();
  const title = typeof route.fields?.pageTitle?.value === 'string' ? route.fields.pageTitle.value : '';

  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { addressIdentifier } = useAddressOptions();

  const { data: unwrappedData } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdNextBestActions,
    {
      path: {
        accountNumber,
        deliveryAddressId: addressIdentifier,
      },
    },
    [`/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/next-best-actions`],
    session,
  );

  const data = unwrapData(unwrappedData);

  const blockOrderMapping = createBlockOrderMapping(fields);
  const sortedCards = sortCardsByOrder(data?.cards, blockOrderMapping);

  return (
    <PageGrid>
      <PageGrid.Item gridColumn="1/-1">
        <Box
          paddingLeft={{ initial: '2', sm: '0' }}
          paddingTop={{ initial: '10', md: '16' }}
          paddingBottom={{ initial: '8', md: '12' }}>
          <Heading as="h1" size={{ initial: 'M', lg: 'L' }}>
            {title}
          </Heading>
        </Box>

        <Box paddingBottom={6}>
          <Grid
            gridTemplateAreas={{
              initial: 'none',
              lg: `"one two three"
                   "four five six"`,
            }}
            gridTemplateRows={{ initial: 'none', lg: '1fr 1fr' }}
            gap="6">
            {GRID_AREAS.map((gridArea, index) => {
              const card = sortedCards[index];

              if (!card) {
                return null;
              }

              return (
                <Grid.Item key={gridArea} gridArea={{ initial: 'unset', lg: gridArea }}>
                  <DashboardBlock card={card} />
                </Grid.Item>
              );
            })}
          </Grid>
        </Box>
      </PageGrid.Item>
    </PageGrid>
  );
};

export default Dashboard;
