import { useCallback } from 'react';

import { NextBestActionCardResponse, PayInvoiceCardResponse } from '@dc-be/client';
import { Bucket, Stretch } from '@sparky';

import { PayInvoiceCard } from './PayInvoiceCard';

export const DashboardBlock = ({ card }: { card: NextBestActionCardResponse }) => {
  const getCardContent = useCallback(() => {
    switch (card.key) {
      case 'PayInvoice':
        return {
          title: 'Betalen [not in Sitecore]',
          content: <PayInvoiceCard details={(card as PayInvoiceCardResponse).details} />,
        };
      // TODO: add other variants
      default:
        return {
          title: card.key || '',
          content: (
            <Bucket.Content>
              <div>Content coming soon [not in Sitecore]</div>
            </Bucket.Content>
          ),
        };
    }
  }, [card]);

  const { title, content } = getCardContent();
  return (
    <Stretch>
      <Bucket title={title}>{content}</Bucket>
    </Stretch>
  );
};
