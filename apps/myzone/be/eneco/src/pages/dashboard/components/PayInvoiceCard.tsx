import { PayInvoiceCardResponse } from '@dc-be/client';
import { Bucket } from '@sparky';

export const PayInvoiceCard = ({ details }: { details: PayInvoiceCardResponse['details'] }) => {
  return (
    <Bucket.Content>
      <div>{details?.amountOfOpenInvoices} open facturen [not in Sitecore]</div>
      <div>{details?.totalOpenAmount} open bedrag [not in Sitecore]</div>
      <div>{details?.amountOfBillingAccounts} factuuraccounts [not in Sitecore]</div>
    </Bucket.Content>
  );
};
