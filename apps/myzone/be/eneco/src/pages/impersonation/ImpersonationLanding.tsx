import { useState } from 'react';

import { useApplication } from '@common/application';
import {
  AccessTypeDto,
  CrmAccountClaimDto,
  ImpersonationRequest,
  ImpersonationTypeDto,
  postEnecoBeXapiSiteApiV1ImpersonationInitiate,
  PostEnecoBeXapiSiteApiV1ImpersonationInitiateResponse,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ImpersonationLandingRendering } from '@sitecore/types/ImpersonationLanding';
import { Box, Button, Card, Grid, Heading, NotificationBox, PageGrid, Stack, Text } from '@sparky';
import { CrmAccountWithRole, Impersonation } from 'types-be/next-auth';

function mapImpersonationType(data: PostEnecoBeXapiSiteApiV1ImpersonationInitiateResponse | undefined) {
  if (data?.data?.impersonationType === 'Email') {
    return 'Email';
  } else {
    if (data?.data?.impersonationType === 'Account') {
      return 'CrmAccountNumber';
    }
    return 'None';
  }
}

function mapImpersonationRole(data: PostEnecoBeXapiSiteApiV1ImpersonationInitiateResponse | undefined) {
  if (data?.data?.impersonationRole === 'Read') {
    return 'reader';
  } else {
    if (data?.data?.impersonationRole === 'Write') {
      return 'editor';
    }
  }
  return 'reader';
}

const mapToCrmAccountWithRole: (value: CrmAccountClaimDto) => CrmAccountWithRole = value => ({
  crmAccountNumber: value.crmAccountNumber!,
  alias: value.alias!,
  role: mapRole(value.accessType)!,
});

const mapRole = (accessType: AccessTypeDto | undefined) => {
  if (accessType)
    switch (accessType) {
      case 'Read': {
        return 'reader';
      }
      case 'Write': {
        return 'editor';
      }
      case 'Owner': {
        return 'owner';
      }
    }
  return undefined;
};

const parseImpersonationType = (impersonationFor: string): ImpersonationTypeDto => {
  if (impersonationFor.includes('@')) {
    return 'Email';
  } else {
    return 'Account';
  }
};

const ImpersonationLanding = () => {
  const { fields } = useContent<ImpersonationLandingRendering>();
  const [isLoading, setLoading] = useState(false);
  const [hasError, setError] = useState(false);
  const { searchParams } = useApplication();
  const { format } = useFormatter();
  const router = useRouter();

  const impersonationFor = searchParams.get('for');
  const { impersonate, setAccounts, switchAccount } = useSelfServiceAccount();

  async function executeApiCall() {
    try {
      setLoading(true);
      if (!impersonationFor) {
        setError(true);
        return;
      }
      const impersonationType = parseImpersonationType(impersonationFor);
      const body: ImpersonationRequest = { type: impersonationType, value: impersonationFor };
      const requestOption = { body: body };

      const { response, data } = await postEnecoBeXapiSiteApiV1ImpersonationInitiate(requestOption);

      if (!response.ok) {
        setError(true);
      } else {
        const impersonateData: Impersonation = {
          impersonatorSubject: data!.data!.impersonatorSubject!,
          actsAsIdentityProviderDisplayName: data!.data!.actsAsIdentityProviderDisplayName!,
          actsAsIdentityProviderId: data!.data!.actsAsIdentityProviderId!,
          type: mapImpersonationType(data),
          role: mapImpersonationRole(data),
          accounts: (data!.data!.crmAccountClaims ?? []).map(claim => mapToCrmAccountWithRole(claim))!,
        };
        impersonate(impersonateData);

        if (impersonateData.accounts.length > 0) {
          setAccounts(impersonateData.accounts);
          switchAccount({
            crmAccountNumber: impersonateData.accounts[0].crmAccountNumber,
            reloadPage: true,
            newAccounts: impersonateData.accounts,
          });
        }
        await router.push(fields.dashboardLink.value.href);
      }
    } catch {
      setError(true);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  }

  return (
    <PageGrid>
      <Grid.Item gridColumn={{ initial: '1/-1', md: '4/-2', lg: '5/9' }} gridRowStart="1">
        <Box>
          {!impersonationFor || hasError ? (
            <NotificationBox
              isAlert={true}
              variant={'error'}
              title={fields.cannotImpersonateErrorNotification?.value.title}
              text={fields.cannotImpersonateErrorNotification?.value.content}
            />
          ) : (
            <Card as="div" corners="rounded" elevation="S">
              <Card.Content>
                <Stack direction="column" gap="6">
                  <Heading size="S" as="h2">
                    {fields.title.value}
                  </Heading>
                  <Text>{format((fields.description.value as string) ?? '', { user: impersonationFor })}</Text>
                  <Button
                    action="primary"
                    size="regular"
                    tone="onLight"
                    type="submit"
                    onClick={executeApiCall}
                    isLoading={isLoading}>
                    {fields.impersonateButtonText.value}
                  </Button>
                </Stack>
              </Card.Content>
            </Card>
          )}
        </Box>
      </Grid.Item>
    </PageGrid>
  );
};

export default ImpersonationLanding;
