import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import {
  MoveFileDetailResponse,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddress,
} from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { MoveNewAddressStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Button, Form, InputText, NotificationBox, Stack } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';
import { useTracking } from '@tracking';

import AddressAutocomplete from '../../../../utils/addresses/AddressAutocomplete';
import { addressSearchParam } from '../../../../utils/addSearchParams';
import { moveStepNameToLink } from '../../../../utils/flow/flowStepNameToLink';
import { useMove } from '../../hooks/useMove';

const MoveNewAddressForm = (props: MoveFileDetailResponse) => {
  const { fields } = useContent<MoveNewAddressStepRenderingExtended>();
  const { push } = useRouter();
  const { trackEventBE } = useTracking();
  const { moveCacheKey, path, move } = useMove();
  const { addressIdentifier } = path;
  const { mutate } = useSWRConfig();
  const { isCurrentAccountReader } = useSelfServiceAccount();

  const [error, setError] = useState<NotificationBoxProps>();
  const [errorDuplicateAddress, setErrorDuplicateAddress] = useState<NotificationBoxProps | undefined>();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const FormSchema = yup.object({
    streetName: yup.string().required(fields.addressForm.streetFormField.value.requiredMessage),
    houseNumber: yup.string().required(fields.addressForm.houseNumberFormField.value.requiredMessage),
    busNumber: yup.string().optional(),
    zipCode: yup.string().required(fields.addressForm.zipCodeFormField.value.requiredMessage),
    city: yup.string().required(fields.addressForm.cityFormField.value.requiredMessage),
  });
  type FormValues = yup.InferType<typeof FormSchema>;

  const form = useForm<FormValues>({
    mode: 'onBlur',
    resolver: yupResolver(FormSchema),
    defaultValues: { busNumber: '', city: '', houseNumber: '', streetName: '', zipCode: '' },
  });
  const {
    handleSubmit,
    register,
    setValue,
    watch,
    formState: { errors },
  } = form;

  useEffect(() => {
    const newAddress = props?.newAddress?.newAddress;
    if (newAddress) {
      if (newAddress.bus) {
        setValue('busNumber', newAddress.bus);
      }
      if (newAddress.municipality) {
        setValue('city', newAddress.municipality);
      }
      if (newAddress.houseNumber) {
        setValue('houseNumber', newAddress.houseNumber);
      }
      if (newAddress.street) {
        setValue('streetName', newAddress.street);
      }
      if (newAddress.postalCode) {
        setValue('zipCode', newAddress.postalCode);
      }
    }
  }, [props?.newAddress, setValue]);

  const streetName = watch('streetName');
  const houseNumber = watch('houseNumber');
  const busNumber = watch('busNumber');
  const zipCode = watch('zipCode');
  const city = watch('city');

  useEffect(() => {
    const oldAddress = props?.oldAddress?.oldAddress;
    if (!oldAddress) return;

    const isDuplicateAddress =
      oldAddress.bus?.toLocaleLowerCase() === busNumber?.toLocaleLowerCase() &&
      oldAddress.houseNumber?.toLocaleLowerCase() === houseNumber?.toLocaleLowerCase() &&
      oldAddress.municipality?.toLocaleLowerCase() === city?.toLocaleLowerCase() &&
      oldAddress.postalCode?.toLocaleLowerCase() === zipCode?.toLocaleLowerCase() &&
      oldAddress.street?.toLocaleLowerCase() === streetName?.toLocaleLowerCase();

    if (isDuplicateAddress)
      setErrorDuplicateAddress({
        isAlert: false,
        text:
          fields.addressForm.sameAddressNotification?.value?.content ?? 'duplicate address content [not in sitecore]',
        variant: fields.addressForm.sameAddressNotification?.value?.variant ?? 'warning',
        title: fields.addressForm.sameAddressNotification?.value?.title ?? '[not in sitecore]',
      });
    else setErrorDuplicateAddress(undefined);
  }, [
    busNumber,
    city,
    fields.addressForm.sameAddressNotification?.value,
    houseNumber,
    props?.oldAddress?.oldAddress,
    streetName,
    zipCode,
  ]);

  const onSubmit: SubmitHandler<FormValues> = async ({ city, houseNumber, streetName, zipCode, busNumber }) => {
    if (errorDuplicateAddress || error) return;

    setIsLoadingSubmit(true);

    const { response } =
      await putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddress({
        path,
        body: {
          address: { bus: busNumber, houseNumber, street: streetName, municipality: city, postalCode: zipCode },
        },
      });

    if (response.ok) {
      trackEventBE('be_myeneco_move_new_adress', {
        status: 'success',
        data: {
          moveReason: move?.reason,
          moveFlow: move?.flow,
          eodOld: !!move?.oldAddress?.eod,
          eodNew: !!move?.newAddress?.eod,
        },
      });
      await mutate(moveCacheKey);
      push(
        moveStepNameToLink('NewAddressKeyTransfer', fields.navigation.continueButtonLinkList, 'Continue') +
          addressSearchParam(addressIdentifier),
      );
    } else {
      trackEventBE('be_myeneco_move_new_adress', {
        status: 'failed',
        data: {
          moveReason: move?.reason,
          moveFlow: move?.flow,
          eodOld: !!move?.oldAddress?.eod,
          eodNew: !!move?.newAddress?.eod,
        },
      });
      setIsLoadingSubmit(false);
      setError({
        isAlert: true,
        title: fields.addressForm.addNewAddressErrorNotification.value.title,
        text: <RichText html={fields.addressForm.addNewAddressErrorNotification.value.content} />,
        variant: 'error',
      });
    }
  };

  return (
    <FormProvider {...form}>
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Stack gap="6">
          <AddressAutocomplete
            cityFormField={{
              ...fields.addressForm.cityFormField.value,
              defaultValue: props.newAddress?.newAddress?.municipality ?? '',
              yupProps: register('city'),
            }}
            onZipCodeCityChange={value => {
              setValue('zipCode', value.zipCode as string);
              setValue('city', value.city as string);
            }}
            zipCodeFormField={{
              ...fields.addressForm.zipCodeFormField.value,
              defaultValue: props.newAddress?.newAddress?.postalCode ?? '',
              yupProps: register('zipCode'),
            }}
            zipCodeCityError={fields.addressForm.zipCodeFormField.value.validationMessage}
            streetNameFormField={{
              ...fields.addressForm.streetFormField.value,
              defaultValue: props.newAddress?.newAddress?.street ?? '',
              yupProps: register('streetName'),
            }}
            onStreetNameChange={streetName => {
              setValue('streetName', streetName);
            }}
            streetNameNotFoundWarningTitle={
              fields.addressForm.streetNameNotFoundNotification?.value?.title ?? 'Waarschuwing [not in sitecore]'
            }
            streetNameNotFoundWarningContent={
              fields.addressForm.streetNameNotFoundNotification?.value?.content ??
              'Let op: je straatnaam is niet gevonden. Indien je straatnaam toch correct is kan je verder gaan met het formulier. [not in sitecore]'
            }
            shouldValidateAddressInApi={true}
            isDisabled={isCurrentAccountReader}
          />

          <Stack gap={4} direction="row">
            <InputText
              label={fields.addressForm.houseNumberFormField.value.label}
              hint={<RichText html={fields.addressForm.houseNumberFormField.value.hint} />}
              error={errors.houseNumber?.message}
              isDisabled={isCurrentAccountReader}
              {...register('houseNumber')}
              value={houseNumber}
            />

            <InputText
              label={fields.addressForm.busNumberFormField.value.label}
              hint={<RichText html={fields.addressForm.busNumberFormField.value.hint} />}
              error={errors.busNumber?.message}
              isDisabled={isCurrentAccountReader}
              {...register('busNumber')}
              value={busNumber ?? ''}
            />
          </Stack>
          {error && <NotificationBox {...error} />}
          {errorDuplicateAddress && <NotificationBox {...errorDuplicateAddress} />}
          <Stack gap={4} direction="row">
            <Button type="submit" isLoading={isLoadingSubmit}>
              {fields.navigation.nextButtonText.value}
            </Button>
            <Button
              action="secondary"
              onClick={() => {
                const isContinueFlow = move?.flow === 'ContinueFlow';
                const links = isContinueFlow
                  ? fields.navigation.continueButtonLinkList
                  : fields.navigation.cancelButtonLinkList;

                const isPreviousStepMoveReasons = move?.lastSubmittedStep === 'MoveReasons';
                const url = isPreviousStepMoveReasons
                  ? moveStepNameToLink('MoveReasons', links, 'Continue')
                  : moveStepNameToLink('Eod', links, 'Continue');
                push(url + addressSearchParam(addressIdentifier));
              }}>
              {fields.navigation.previousButtonText.value}
            </Button>
          </Stack>
        </Stack>
      </Form>
    </FormProvider>
  );
};

export default MoveNewAddressForm;
