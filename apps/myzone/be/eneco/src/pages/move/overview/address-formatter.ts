import { AddressDetailsDto } from '@dc-be/client';

export const addressFormatter = (address: AddressDetailsDto | undefined, busLabel: string): string => {
  if (!address) return '';

  const { street, houseNumber, bus, postalCode, municipality } = address;

  const streetParts = [street, houseNumber, bus ? `${busLabel} ${bus}` : ''].filter(Boolean).join(' ');

  const cityParts = [postalCode, municipality].filter(Boolean).join(' ');

  return `${streetParts}, ${cityParts}`;
};
