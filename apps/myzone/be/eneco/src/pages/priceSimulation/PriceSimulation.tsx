import { FC, useEffect } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, useForm } from 'react-hook-form';
import * as yup from 'yup';

import { useContent } from '@sitecore/common';
import { PriceSimulationRendering } from '@sitecore/types/PriceSimulation';
import { Box, Form, PageGrid, Stack } from '@sparky';

import { PriceSimulationProvider } from './context/PriceSimulationContext';
import PriceSimulationStepperFlow from './flows/PriceSimulationStepperFlow';
import {
  clearPriceSimulationState,
  loadPriceSimulationState,
  savePriceSimulationState,
} from './utils/priceSimulationCookie';

export const PRICE_SIMULATION_FORM_SCHEMA_FIELDS = {
  IS_PROSPECT: 'isProspect',
  CUSTOMER_TYPE: 'customerType',
  POSTAL_CODE: 'postalCode',
} as const;

const priceSimulationFormSchema = yup.object({
  [PRICE_SIMULATION_FORM_SCHEMA_FIELDS.IS_PROSPECT]: yup.boolean().required(),
  [PRICE_SIMULATION_FORM_SCHEMA_FIELDS.CUSTOMER_TYPE]: yup.string().required(),
  [PRICE_SIMULATION_FORM_SCHEMA_FIELDS.POSTAL_CODE]: yup.string().required(),
});

export type PriceSimulationFormSchema = yup.InferType<typeof priceSimulationFormSchema>;

const PriceSimulation: FC = () => {
  const { fields } = useContent<PriceSimulationRendering>();

  // Load saved state from cookie on mount
  const savedState = loadPriceSimulationState();

  const form = useForm<PriceSimulationFormSchema>({
    resolver: yupResolver(priceSimulationFormSchema),
    defaultValues: savedState?.formValues || { isProspect: false },
  });

  // Save state to cookie whenever step or form values change
  useEffect(() => {
    const subscription = form.watch(formValues => {
      savePriceSimulationState({
        formValues,
      });
    });

    return () => subscription.unsubscribe();
  }, [form]);

  const onSubmit = (data: PriceSimulationFormSchema) => {
    // eslint-disable-next-line no-console
    console.log(data);

    // Clear the saved state once form is successfully submitted
    clearPriceSimulationState();
  };

  return (
    <FormProvider {...form}>
      <PriceSimulationProvider>
        <Form onSubmit={form.handleSubmit(onSubmit)}>
          <PageGrid>
            <PageGrid.Item gridColumn={{ initial: '-1/1', md: '2/12', lg: '4/10' }}>
              <Box paddingY="10">
                <Stack gap="10" alignX="start">
                  <PriceSimulationStepperFlow fields={fields} />
                </Stack>
              </Box>
            </PageGrid.Item>
          </PageGrid>
        </Form>
      </PriceSimulationProvider>
    </FormProvider>
  );
};

export default PriceSimulation;
