import { createContext, PropsWithChildren, useEffect, useMemo, useState } from 'react';

import { PriceSimulationStep } from '../types/PriceSimulationSteps';
import { loadPriceSimulationState, savePriceSimulationState } from '../utils/priceSimulationCookie';

interface PriceSimulationContextType {
  priceSimulationStep: PriceSimulationStep;
  setPriceSimulationStep: (priceSimulationStep: PriceSimulationStep) => void;
}

export const PriceSimulationContext = createContext<PriceSimulationContextType>({
  priceSimulationStep: 'prospectCheck',
  setPriceSimulationStep: () => {},
});

export const PriceSimulationProvider = ({ children }: PropsWithChildren) => {
  // Load saved state from cookie on mount
  const savedState = loadPriceSimulationState();

  const [priceSimulationStep, setPriceSimulationStep] = useState<PriceSimulationStep>(
    savedState?.step || 'prospectCheck',
  );

  useEffect(() => {
    savePriceSimulationState({
      step: priceSimulationStep,
    });
  }, [priceSimulationStep]);

  const value = useMemo<PriceSimulationContextType>(
    () => ({
      priceSimulationStep,
      setPriceSimulationStep,
    }),
    [priceSimulationStep],
  );

  return <PriceSimulationContext.Provider value={value}>{children}</PriceSimulationContext.Provider>;
};
