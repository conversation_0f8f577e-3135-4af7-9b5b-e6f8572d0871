import { useCallback, useContext, useEffect } from 'react';

import { useFormContext } from 'react-hook-form';

import { useSession } from '@dxp-auth';
import { Fields } from '@sitecore/types/PriceSimulation';

import { FlowNavigationContext } from '../../../providers/FlowNavigationProvider';
import { PriceSimulationContext } from '../context/PriceSimulationContext';
import { PRICE_SIMULATION_FORM_SCHEMA_FIELDS } from '../PriceSimulation';
import { CustomerTypeCheck } from '../steps/CustomerTypeCheck';
import { EnergyTypeCheck } from '../steps/EnergyTypeCheck';
import { FlowSelector } from '../steps/FlowSelector';
import { PostalCodeCheck } from '../steps/PostalCodeCheck';
import { ProspectCheck } from '../steps/ProspectCheck';
import {
  activeHeaderIndexForStep,
  orderedPriceSimulationSteps,
  PriceSimulationStep,
} from '../types/PriceSimulationSteps';
type PriceSimulationStepperFlowProps = {
  fields: Fields;
};

const PriceSimulationStepperFlow: React.FC<PriceSimulationStepperFlowProps> = () => {
  const { setPriceSimulationStep, priceSimulationStep } = useContext(PriceSimulationContext);
  const { subscribeTriggerOnPreviousClick, setActiveStepIndicator, setShowPreviousBtn } =
    useContext(FlowNavigationContext);
  const { setValue, getValues } = useFormContext();
  const { data: session } = useSession();

  const isLoggedIn = !!session;
  const currentStepIndex = orderedPriceSimulationSteps.indexOf(priceSimulationStep);

  const nextMainStepLogicManager = () => {
    const isProspect = getValues(PRICE_SIMULATION_FORM_SCHEMA_FIELDS.IS_PROSPECT);

    switch (priceSimulationStep) {
      case 'prospectCheck':
        if (isProspect) setPriceSimulationStep('customerTypeCheck');
        else {
          setPriceSimulationStep('flowSelector');
        }
        break;
      default:
        setPriceSimulationStep(orderedPriceSimulationSteps[currentStepIndex + 1]);
    }
  };

  const previousMainStepLogicManager = useCallback(() => {
    const isProspect = getValues(PRICE_SIMULATION_FORM_SCHEMA_FIELDS.IS_PROSPECT);

    switch (priceSimulationStep) {
      case 'customerTypeCheck':
        if (isProspect) setPriceSimulationStep('prospectCheck');
        else {
          setPriceSimulationStep('flowSelector');
        }
        break;
      default:
        setPriceSimulationStep(orderedPriceSimulationSteps[currentStepIndex - 1]);
    }
  }, [currentStepIndex, priceSimulationStep, getValues, setPriceSimulationStep]);

  const nextMainStep = (transition?: PriceSimulationStep) => {
    if (transition) setPriceSimulationStep(transition);
    else nextMainStepLogicManager();
  };

  const previousMainStep = useCallback(
    (transition?: PriceSimulationStep) => {
      if (transition) setPriceSimulationStep(transition);
      else previousMainStepLogicManager();
    },
    [previousMainStepLogicManager, setPriceSimulationStep],
  );

  useEffect(() => {
    // update the header step indicator
    setActiveStepIndicator(activeHeaderIndexForStep.get(priceSimulationStep) ?? 0);

    // update whether or not to show the previous button in the header
    if (currentStepIndex === 0) setShowPreviousBtn(false);
    else setShowPreviousBtn(true);
  }, [currentStepIndex, getValues, priceSimulationStep, setActiveStepIndicator, setShowPreviousBtn]);

  useEffect(() => {
    const unsubscribe = subscribeTriggerOnPreviousClick(() => {
      previousMainStep();
    });
    return unsubscribe;
  }, [subscribeTriggerOnPreviousClick, previousMainStep]);

  if (priceSimulationStep === 'prospectCheck' && isLoggedIn) {
    setValue(PRICE_SIMULATION_FORM_SCHEMA_FIELDS.IS_PROSPECT, false);
    nextMainStep();
  }

  switch (priceSimulationStep) {
    case 'prospectCheck':
      return <ProspectCheck nextMainStep={nextMainStep} />;
    case 'customerTypeCheck':
      return <CustomerTypeCheck nextMainStep={nextMainStep} />;
    case 'flowSelector':
      return <FlowSelector nextMainStep={nextMainStep} />;
    case 'postalCodeCheck':
      return <PostalCodeCheck nextMainStep={nextMainStep} />;
    case 'energyTypeCheck':
      return <EnergyTypeCheck nextMainStep={nextMainStep} />;
    default:
      return null;
  }
};

export default PriceSimulationStepperFlow;
