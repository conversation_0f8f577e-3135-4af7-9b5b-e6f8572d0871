import { FC, useState } from 'react';

import { Controller, useFormContext } from 'react-hook-form';

import { useContent } from '@sitecore/common';
import { PriceSimulationRendering } from '@sitecore/types/manual/PriceSimulation';
import { Button, Heading, NotificationBox, RadioGroup, RadioTile, Stack, Stretch } from '@sparky';
import { House, SmallBusiness } from '@sparky/illustrations';
import { NotificationBarProps } from '@sparky/types';

import { PriceSimulationFormSchema } from '../PriceSimulation';
import { FlowChildProps } from '../types/PriceSimulationSteps';

export const CustomerTypeCheck: FC<FlowChildProps> = ({ nextMainStep }) => {
  const { fields } = useContent<PriceSimulationRendering>();
  const { control, getValues } = useFormContext<PriceSimulationFormSchema>();
  const [error, setError] = useState<NotificationBarProps | null>(null);

  const onNextClick = () => {
    if (getValues('customerType') === undefined) {
      setError({
        variant: 'error',
        title: fields.customerTypeCheck?.error?.value?.title ?? 'Let op! [Not in Sitecore]',
        text: fields.customerTypeCheck?.error?.value?.content ?? 'Je moet een antwoord geven [Not in Sitecore]',
      });
      return;
    }
    nextMainStep();
  };

  return (
    <>
      <Heading as="h1" size="M">
        {fields.customerTypeCheck?.title?.value ??
          'Wil je energie voor je privé of zakelijk gebruik? [Not in Sitecore]'}
      </Heading>
      <Stretch>
        <div>
          <Controller
            control={control}
            name="customerType"
            render={({ field: { onChange, value } }) => (
              <RadioGroup
                name="customerType"
                aria-labelledby="customerType"
                direction="column"
                value={value}
                onValueChange={onChange}>
                <RadioTile icon={<House size={'small'} />} value={'Residential'}>
                  {fields.customerTypeCheck?.residentialLabel?.value ?? 'Particulier [Not in Sitecore]'}
                </RadioTile>
                <RadioTile icon={<SmallBusiness size={'small'} />} value={'SOHO'}>
                  {fields.customerTypeCheck?.sohoLabel?.value ?? 'SOHO [Not in Sitecore]'}
                </RadioTile>
              </RadioGroup>
            )}
          />
        </div>
      </Stretch>
      {error && <NotificationBox variant="error" isAlert {...error} />}
      <Stack gap="2" direction="row">
        <Button onClick={onNextClick}>{fields.prospectCheck?.nextButtonText?.value ?? 'Volgende'}</Button>
      </Stack>
    </>
  );
};
