import { FC, useState } from 'react';

import { useContent } from '@sitecore/common';
import { PriceSimulationRendering } from '@sitecore/types/manual/PriceSimulation';
import { Button, Heading, NotificationBox, RadioGroup, RadioTile, Stretch, Stack, Text } from '@sparky';
import { NotificationBarProps } from '@sparky/types';

import { FlowChildProps } from '../types/PriceSimulationSteps';

export const FlowSelector: FC<FlowChildProps> = ({ nextMainStep }) => {
  const { fields } = useContent<PriceSimulationRendering>();

  const [flow, setFlow] = useState<string | undefined>();
  const [error, setError] = useState<NotificationBarProps | null>(null);
  const flowLinkMapping = new Map<string | undefined, string>([
    ['move', fields.flowSelector?.moveLink?.value.href ?? '/my-eneco/move'],
    ['productSwitch', fields.flowSelector?.productSwitchLink?.value.href ?? '/my-eneco/product-switch'],
  ]);

  const onNextClick = () => {
    if (flow === undefined) {
      setError({
        variant: 'error',
        title: fields.flowSelector?.error?.value?.title ?? 'Let op! [Not in Sitecore]',
        text: fields.flowSelector?.error?.value?.content ?? 'Je moet een antwoord geven [Not in Sitecore]',
      });
      return;
    }
    if (flow === 'priceSimulation') {
      nextMainStep();
    } else {
      const href = flowLinkMapping.get(flow);
      if (href) {
        window.location.href = href;
      }
    }
  };

  return (
    <>
      <Stack gap="2">
        <Heading as="h1" size="M">
          {fields.flowSelector?.title?.value ?? 'Ben je op zoek naar?'}
        </Heading>
        <Text>{fields.flowSelector?.description?.value ?? 'Selecteer wat voor jou van toepassing is'}</Text>
      </Stack>
      <Stretch>
        <div>
          <RadioGroup
            aria-labelledby="existingCustomerCheck"
            direction="column"
            name="existingCustomerCheck"
            value={flow}
            onValueChange={val => setFlow(val)}>
            <RadioTile value={'priceSimulation'}>
              {fields.flowSelector?.priceSimulationLabel?.value ?? 'Ik wil een bijkomend contract afsluiten'}
            </RadioTile>
            <RadioTile value={'move'}>
              {fields.flowSelector?.moveLabel?.value ?? 'Ik ga verhuizen en wens een contract op een nieuw adres'}
            </RadioTile>
            <RadioTile value={'productSwitch'}>
              {fields.flowSelector?.productSwitchLabel?.value ?? 'Ik wil mijn product wijzigen op mijn huidig adres'}
            </RadioTile>
          </RadioGroup>
        </div>
      </Stretch>
      {error && <NotificationBox variant="error" isAlert {...error} />}
      <Stack gap="2" direction="row">
        <Button onClick={onNextClick}>{fields.flowSelector?.nextButtonText?.value ?? 'Volgende'}</Button>
      </Stack>
    </>
  );
};
