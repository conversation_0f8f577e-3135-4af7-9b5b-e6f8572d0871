import { FC } from 'react';

import { useFormContext } from 'react-hook-form';

import { useContent } from '@sitecore/common';
import { PriceSimulationRendering } from '@sitecore/types/manual/PriceSimulation';
import { Button, Checkbox, Heading, InputText, Stack, Stretch, Text } from '@sparky';

import { PriceSimulationFormSchema } from '../PriceSimulation';
import { FlowChildProps } from '../types/PriceSimulationSteps';

export const PostalCodeCheck: FC<FlowChildProps> = ({ nextMainStep }) => {
  const { fields } = useContent<PriceSimulationRendering>();
  const { register } = useFormContext<PriceSimulationFormSchema>();
  return (
    <>
      <Stack gap="2">
        <Heading as="h1" size="M">
          {fields.postalCodeCheck?.title?.value ?? 'Wat is jouw postcode? [Not in Sitecore]'}
        </Heading>
        <Text>
          {fields.postalCodeCheck?.description?.value ??
            'Een juiste postcode is belangrijke om de simulatie zo correct mogelijk te doen. [Not in Sitecore]'}
        </Text>
      </Stack>

      <Stretch>
        <Stack>
          <InputText
            label={fields.postalCodeCheck?.postalCodeLabel?.value ?? 'Postcode [Not in Sitecore]'}
            {...register('postalCode')}
          />
        </Stack>
      </Stretch>

      <Checkbox
        label={fields.postalCodeCheck?.postalCodeCheckLabel?.value ?? 'Postcode check [Not in Sitecore]'}
        name="postalCodeCheck"
      />

      <Stack gap="2" direction="row">
        <Button onClick={() => nextMainStep()}>
          {fields.postalCodeCheck?.nextButtonText?.value ?? 'Volgende [Not in Sitecore]'}
        </Button>
      </Stack>
    </>
  );
};
