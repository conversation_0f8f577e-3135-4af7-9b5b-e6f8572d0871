import { FC } from 'react';

import { Controller, useFormContext } from 'react-hook-form';

import { useContent } from '@sitecore/common';
import { PriceSimulationRendering } from '@sitecore/types/manual/PriceSimulation';
import { Button, Heading, RadioGroup, RadioTile, Stack, Stretch } from '@sparky';

import { PRICE_SIMULATION_FORM_SCHEMA_FIELDS, PriceSimulationFormSchema } from '../PriceSimulation';
import { FlowChildProps } from '../types/PriceSimulationSteps';

export const ProspectCheck: FC<FlowChildProps> = ({ nextMainStep }) => {
  const { fields } = useContent<PriceSimulationRendering>();
  const { control } = useFormContext<PriceSimulationFormSchema>();

  const onNextClick = () => {
    nextMainStep();
  };

  return (
    <>
      <Heading as="h1" size="M">
        {fields.prospectCheck?.title?.value ?? 'Ben je al klant bij Eneco? [Not in Sitecore]'}
      </Heading>
      <Stretch>
        <div>
          <Controller
            control={control}
            name={PRICE_SIMULATION_FORM_SCHEMA_FIELDS.IS_PROSPECT}
            render={({ field: { onChange, value } }) => (
              <RadioGroup
                aria-labelledby="isProspect"
                direction="column"
                value={value?.toString()}
                onValueChange={val => onChange(val === 'true')}
                name="isProspect">
                <RadioTile value={'false'}>
                  {fields?.prospectCheck?.yesLabel?.value ?? 'Ja [Not in Sitecore]'}
                </RadioTile>
                <RadioTile value={'true'}>{fields?.prospectCheck?.noLabel?.value ?? 'Nee [Not in Sitecore]'}</RadioTile>
              </RadioGroup>
            )}
          />
        </div>
      </Stretch>
      <Stack gap="2" direction="row">
        <Button onClick={onNextClick}>{fields.prospectCheck?.nextButtonText?.value ?? 'Volgende'}</Button>
      </Stack>
    </>
  );
};
