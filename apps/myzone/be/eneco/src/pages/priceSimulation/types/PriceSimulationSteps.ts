export type PriceSimulationStep =
  | 'prospectCheck'
  | 'flowSelector'
  | 'customerTypeCheck'
  | 'postalCodeCheck'
  | 'energyTypeCheck';

export const orderedPriceSimulationSteps: PriceSimulationStep[] = [
  'prospectCheck',
  'flowSelector',
  'customerTypeCheck',
  'postalCodeCheck',
  'energyTypeCheck',
];

export const activeHeaderIndexForStep: Map<PriceSimulationStep, number> = new Map<PriceSimulationStep, number>([
  [orderedPriceSimulationSteps[0], 0],
  [orderedPriceSimulationSteps[1], 0],
  [orderedPriceSimulationSteps[2], 0],
  [orderedPriceSimulationSteps[3], 0],
  [orderedPriceSimulationSteps[4], 0],
]);

export type FlowChildProps = {
  nextMainStep: (step?: PriceSimulationStep) => void;
};
