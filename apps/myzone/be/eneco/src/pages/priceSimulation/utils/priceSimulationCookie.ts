import Cookies from 'js-cookie';

import logger from '@common/log';

import { PriceSimulationFormSchema } from '../PriceSimulation';
import { PriceSimulationStep } from '../types/PriceSimulationSteps';

const COOKIE_NAME = 'priceSimulationState';
const COOKIE_EXPIRY_DAYS = 1;

export interface PriceSimulationCookieData {
  step: PriceSimulationStep;
  formValues: Partial<PriceSimulationFormSchema>;
  timestamp: number;
}

/**
 * Save the current step and form values to a cookie
 */
export const savePriceSimulationState = ({
  step,
  formValues,
}: {
  step?: PriceSimulationStep;
  formValues?: Partial<PriceSimulationFormSchema>;
}): void => {
  try {
    const cookieValue = Cookies.get(COOKIE_NAME);
    const data: PriceSimulationCookieData = cookieValue
      ? JSON.parse(cookieValue)
      : {
          step: 'prospectCheck',
          formValues: {},
          timestamp: Date.now(),
        };

    if (step) {
      data.step = step;
    }
    if (formValues) {
      data.formValues = formValues;
    }

    Cookies.set(COOKIE_NAME, JSON.stringify(data), {
      expires: COOKIE_EXPIRY_DAYS,
      sameSite: 'lax',
    });
  } catch (error) {
    logger.error(
      'Failed to save price simulation state to cookie:',
      error instanceof Error ? error.message : String(error),
    );
  }
};

/**
 * Load the step and form values from the cookie
 */
export const loadPriceSimulationState = (): PriceSimulationCookieData | null => {
  try {
    const cookieValue = Cookies.get(COOKIE_NAME);
    if (!cookieValue) {
      return null;
    }

    const data: PriceSimulationCookieData = JSON.parse(cookieValue);

    const expiryInMs = COOKIE_EXPIRY_DAYS * 24 * 60 * 60 * 1000;
    if (Date.now() - data.timestamp > expiryInMs) {
      clearPriceSimulationState();
      return null;
    }

    return data;
  } catch (error) {
    logger.error(
      'Failed to load price simulation state from cookie:',
      error instanceof Error ? error.message : String(error),
    );
    return null;
  }
};

/**
 * Clear the price simulation state cookie
 */
export const clearPriceSimulationState = (): void => {
  try {
    Cookies.remove(COOKIE_NAME);
  } catch (error) {
    logger.error(
      'Failed to clear price simulation state cookie:',
      error instanceof Error ? error.message : String(error),
    );
  }
};
