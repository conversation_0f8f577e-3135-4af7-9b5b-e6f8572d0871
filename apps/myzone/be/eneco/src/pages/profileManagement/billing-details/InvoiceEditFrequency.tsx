import { FC, Fragment, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';
import * as yup from 'yup';

import { useApplication } from '@common/application';
import RichText from '@components/RichText/RichText';
import {
  $InvoiceFrequency,
  EnergyTypeDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
  InvoiceFrequency,
  putEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequency,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import mapScEnumToMap from '@dc-be/utils/mapScEnumToMap';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MyEnecoInvoiceEditInvoiceFrequencyRendering } from '@sitecore/types/MyEnecoInvoiceEditInvoiceFrequency';
import { Bucket, Button, ButtonLink, Form, RadioButton, RadioGroup, Text } from '@sparky';
import { useTracking } from '@tracking';

import { canUpdateMeterFrequency } from './utils/canUpdateMeterFrequency';
import DataErrorNotification from '../../../components/DataErrorNotification';
import { RoundedSkeleton } from '../../../components/Loading/RoundedSkeleton';
import ReadFormField from '../../../components/ReadFormField';
import { useRedirectAndNotifyBE } from '../../../hooks/useRedirectAndNotifyBE';
import { ADDRESS_PARAM } from '../../../utils/addSearchParams';

const InvoiceEditFrequency: FC<MyEnecoInvoiceEditInvoiceFrequencyRendering> = () => {
  const { fields } = useContent<MyEnecoInvoiceEditInvoiceFrequencyRendering>();
  const [hasDefaultValue, setHasDefaultValue] = useState(false);
  const { data: session } = useSession();
  const { trackEventBE } = useTracking();
  const { selectedAccount } = useSelfServiceAccount();
  const { searchParams } = useApplication();
  const addressIdentifier = searchParams.get('address') || '';
  const redirectAndNotify = useRedirectAndNotifyBE();
  const { mutate } = useSWRConfig();
  const [isLoadingSubmit, setIsLoadingSubmit] = useState<boolean>(false);

  const InvoiceFrequencySchema = yup.object({
    serviceDeliveryPoints: yup.array().of(
      yup.object({
        contractNumber: yup.string().required(),
        frequency: yup.string().oneOf($InvoiceFrequency.enum),
      }),
    ),
  });

  type FormValues = yup.InferType<typeof InvoiceFrequencySchema>;
  const resolver = yupResolver(InvoiceFrequencySchema);

  const form = useForm<FormValues>({
    resolver,
  });

  const { control, setValue, setError } = form;
  const cacheKey = `/accounts/${selectedAccount.crmAccountNumber}/delivery-addresses/${addressIdentifier}/service-delivery-points`;

  const {
    data: serviceDeliveryData,
    isLoading,
    error,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        addressIdentifier: addressIdentifier ?? '',
      },
    },
    [cacheKey],
    session,
  );

  const serviceDeliveryPoints = unwrapData(serviceDeliveryData)?.serviceDeliveryPoints;
  const consumptionTypesList = fields.content.consumptionTypesList;
  const frequencyPeriodList = fields.content.frequencyPeriodsList;

  const submitForm: SubmitHandler<FormValues> = async ({ serviceDeliveryPoints }) => {
    setIsLoadingSubmit(true);
    const { response } = await putEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequency({
      path: { accountNumber: selectedAccount.crmAccountNumber, addressIdentifier },
      body: {
        serviceDeliveryPoints: serviceDeliveryPoints?.map(serviceDeliveryPoint => ({
          contractNumber: serviceDeliveryPoint.contractNumber,
          invoiceFrequency: serviceDeliveryPoint.frequency,
        })),
      },
    });
    if (response.ok) {
      await mutate(cacheKey);
      trackEventBE('be_billing_frequency_submit', {
        status: 'success',
        data: { invoiceFrequencies: serviceDeliveryPoints?.map(sp => sp.frequency) },
      });
      redirectAndNotify({
        route: fields.content.submitButtonLink.value.href + `?${ADDRESS_PARAM}=${addressIdentifier}`,
        text: <RichText html={fields.content.successNotification.value.content} />,
        title: fields.content.successNotification.value.title,
        variant: 'success',
      });
    } else {
      setIsLoadingSubmit(false);
      trackEventBE('be_billing_frequency_submit', {
        status: 'failed',
        data: { invoiceFrequencies: serviceDeliveryPoints?.map(sp => sp.frequency) },
      });
      setError('root', {
        message: fields.content.errorNotification.value.content,
      });
    }
  };

  useEffect(() => {
    if (serviceDeliveryPoints && !hasDefaultValue) {
      setValue(
        'serviceDeliveryPoints',
        serviceDeliveryPoints.map(serviceDeliveryPoint => ({
          contractNumber: serviceDeliveryPoint.contractId ?? '',
          frequency: serviceDeliveryPoint.invoiceFrequency?.current,
        })),
      );
      setHasDefaultValue(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serviceDeliveryPoints]);

  if (!isLoading && error !== undefined && serviceDeliveryData === undefined) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <Bucket title={fields.content.title.value}>
      <FormProvider {...form}>
        <Form onSubmit={form.handleSubmit(submitForm)}>
          <Bucket.Content>
            {isLoading ? (
              <ContentSkeletons />
            ) : (
              serviceDeliveryPoints?.map((serviceDeliveryPoint, index) => {
                const invoiceFrequencyMap = mapScEnumToMap<InvoiceFrequency>(frequencyPeriodList.value);
                const energyTypeMap = mapScEnumToMap<EnergyTypeDto>(consumptionTypesList.value);

                const ean = serviceDeliveryPoint.ean;
                const energyTypeFromMap = energyTypeMap.get(serviceDeliveryPoint?.type);
                const serviceDeliveryPointLabel = `${energyTypeFromMap} - ${ean}`;
                const frequency = invoiceFrequencyMap.get(serviceDeliveryPoint?.invoiceFrequency?.current) || '';
                const canUpdate = canUpdateMeterFrequency(
                  serviceDeliveryPoint.meterType,
                  serviceDeliveryPoint.invoiceFrequency?.changeRequestPending,
                );

                return (
                  <Fragment key={ean}>
                    {canUpdate ? (
                      <>
                        <Text weight={'bold'} size={'BodyM'}>
                          <label id={`serviceDeliveryPoints.${index}.frequency`}>{serviceDeliveryPointLabel}</label>
                        </Text>
                        <Controller
                          control={control}
                          name={`serviceDeliveryPoints.${index}.frequency`}
                          render={({ field: { onChange, value } }) => (
                            <RadioGroup
                              name={frequency}
                              value={value}
                              onValueChange={onChange}
                              direction={'column'}
                              aria-labelledby={`serviceDeliveryPoints.${index}.frequency`}>
                              {invoiceFrequencyMap
                                .entries()
                                //@ts-ignore - TS doesn't know that entries() returns an iterable
                                .flatMap(([value, label]) => (value ? [{ value, label, name: value }] : []))
                                .toArray()
                                //@ts-ignore - TS doesn't know that entries() returns an iterable
                                .map(({ value, label }, index) => {
                                  if (!value) return null;
                                  return (
                                    <RadioButton key={value + index} value={value}>
                                      {label}
                                    </RadioButton>
                                  );
                                })}
                            </RadioGroup>
                          )}
                        />
                      </>
                    ) : (
                      <ReadFormField
                        label={serviceDeliveryPointLabel}
                        value={invoiceFrequencyMap.get(serviceDeliveryPoint.invoiceFrequency?.current)}
                      />
                    )}
                  </Fragment>
                );
              })
            )}

            <RichText html={fields.content.description.value} />
          </Bucket.Content>
          <Bucket.Footer>
            <Bucket.Actions>
              <Button type="submit" isLoading={isLoadingSubmit}>
                {fields.content.submitButtonLink.value.text}
              </Button>
              <ButtonLink
                action="secondary"
                href={fields.content.cancelButtonLink.value.href + `?${ADDRESS_PARAM}=${addressIdentifier}`}>
                {fields.content.cancelButtonLink.value.text}
              </ButtonLink>
            </Bucket.Actions>
          </Bucket.Footer>
        </Form>
      </FormProvider>
    </Bucket>
  );
};

export default InvoiceEditFrequency;

const ContentSkeletons: FC = () => {
  return (
    <>
      <RoundedSkeleton width="100" height={50} />
      <RoundedSkeleton width="100" height={50} />
      <RoundedSkeleton width="100" height={50} />
    </>
  );
};
