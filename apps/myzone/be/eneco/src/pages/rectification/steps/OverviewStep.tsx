import { useState } from 'react';

import { useFormContext } from 'react-hook-form';

import {
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectification,
  RectificationContestedMeter,
  SubmitRectificationRequest,
} from '@dc-be/client';
import { useFormatter } from '@i18n';
import { Bucket, Button, NotificationBox, Stack, Text, TextLink } from '@sparky';
import { ElectricityIcon, GasIcon } from '@sparky/icons';

import ReadFormField from '../../../components/ReadFormField';
import { formattedAddress } from '../../../utils/addresses/addressFormatter';
import { useRectification } from '../hooks/useRectification';
import { useRectificationAddressOptions } from '../hooks/useRectificationAdressOptions';
import { CorrectedMeterReadingType } from '../types/correctedMeterReading.type';
import { FormValuesRectification, RECTIFICATION_SCHEMA_FIELDS } from '../types/schema.types';
import { EanStep, MainStepProps } from '../types/steps.types';

const OverviewStep: React.FC<MainStepProps> = ({ fields, onNextStep, onPreviousStep }) => {
  const { watch } = useFormContext<FormValuesRectification>();
  const {
    path: { accountNumber, invoiceNumber },
  } = useRectification();
  const { date: dateFormatter } = useFormatter();
  const { rectificationAddressOptions } = useRectificationAddressOptions();
  const [error, setError] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const form: CorrectedMeterReadingType[] = watch(`${RECTIFICATION_SCHEMA_FIELDS.CORRECTED_METER_READINGS}`) ?? [];
  const selectedAddress = watch(RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS);

  const onSubmit = async () => {
    try {
      const request: SubmitRectificationRequest = {
        contestedMeterReadings: [],
        rectificationDocuments: [],
        comment: '',
      };
      setIsLoading(true);

      if (!request.contestedMeterReadings) request.contestedMeterReadings = [];
      if (!request.rectificationDocuments) request.rectificationDocuments = [];

      form.map(({ ean, utilityType, meterReadings }) => {
        const checkedMeterReading = meterReadings.find(
          mr => mr.isChecked && mr?.deviation === 'HasDeviation' && mr?.eligibility === 'Eligible',
        );
        if (!checkedMeterReading) return null;

        const { raw, corrected, files, extraComment } = checkedMeterReading;

        const contestedMeter: RectificationContestedMeter = {
          ean,
          utilityType,
          originalDate: raw?.date,
          correctedDate: corrected?.date,
          rectificationContestedMeterReadingRecords: [],
        };

        const rawValues = raw?.indexValues;
        const correctedValues = corrected?.indexValues;

        if (Array.isArray(rawValues) && Array.isArray(correctedValues)) {
          const mappedRecords = rawValues.map((raw, i) => ({
            originalValue: raw?.value,
            correctedValue: correctedValues[i]?.value,
            timeFrameType: raw?.meterTimeFrameType,
          }));

          contestedMeter?.rectificationContestedMeterReadingRecords?.push(...mappedRecords);
        }

        request?.contestedMeterReadings?.push(contestedMeter);

        const mappedFileRecords = files?.map(file => {
          return {
            fileName: file.fileName ?? '',
            fileExtension: file.fileExtension ?? '',
            ixosArchiveId: file.ixosArchiveId ?? '',
          };
        });

        if (mappedFileRecords !== undefined) {
          request.rectificationDocuments?.push(...mappedFileRecords);
        }

        if (extraComment !== undefined) {
          request.comment += extraComment;
        }

        return null;
      });

      const { response } = await postEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectification({
        body: request,
        path: { accountNumber, invoiceNumber },
      });
      if (!response.ok) throw new Error('request failed');
      if (response.ok) {
        onNextStep();
      }
    } catch (e) {
      void e;
      setIsLoading(false);
      setError(true);
    }
  };

  const getMeterTimeFrameLabel = (
    enumList: { name: string; label: string }[],
    meterTimeFrameType?: string,
  ): string | undefined => {
    return enumList.find(val => val.name === meterTimeFrameType)?.label;
  };

  return (
    <Stack gap={'4'}>
      {error && (
        <NotificationBox
          title={fields.summaryStep.errorNotification.value.title ?? 'Er ging iets mis [not in sitecore]'}
          text={
            fields.summaryStep.errorNotification.value.content ??
            'Er ging iets mis bij het indienen van dit formulier [not in sitecore]'
          }
          variant={fields.summaryStep.errorNotification.value.variant}
          isAlert={false}
        />
      )}
      <Bucket title={fields.summaryStep.connectionAddressTitle.value ?? 'Aansluitadres [not in sitecore]'}>
        <Bucket.Content>
          <Text>
            {formattedAddress({
              ...rectificationAddressOptions?.addresses?.find(x => x.id === selectedAddress)?.details,
              busLabel: fields.chooseConnectionAddressStep?.busLabel?.value ?? '[not in sitecore]: bus',
            })}
          </Text>
        </Bucket.Content>
      </Bucket>
      {form?.map((val, index) => {
        const checkedMeterReading = val?.meterReadings.find(
          mr => mr?.isChecked && mr?.deviation === 'HasDeviation' && mr?.eligibility === 'Eligible',
        );

        if (!checkedMeterReading) {
          return null;
        }

        const rawMeterReadings = checkedMeterReading?.raw;
        const correctedMeterReadings = checkedMeterReading?.corrected;

        // If you put the icon and label directly in the title the text won't be bold
        const bucketTitle =
          val.utilityType === 'Electricity' ? (
            <Stack direction="row" gap="3">
              <ElectricityIcon color="iconElectricity" />
              <Text weight={'bold'} size={'BodyM'}>
                {fields.summaryProductCard.electricityLabel?.value ?? 'Elektriciteit[not in sitecore]'}
              </Text>
            </Stack>
          ) : (
            <Stack direction="row" gap="3">
              <GasIcon color="iconGas" />
              <Text weight={'bold'} size={'BodyM'}>
                {fields.summaryProductCard.gasLabel?.value ?? 'Gas [not in sitecore]'}
              </Text>
            </Stack>
          );

        return (
          <Bucket title={bucketTitle} key={`${val.ean}_${val.utilityType}`}>
            <Bucket.Content>
              <ReadFormField
                label={fields.summaryProductCard.eanCodeLabel.value ?? 'EAN code [not in sitecore]'}
                value={val.ean}
                size="BodyM"
              />
              <ReadFormField
                label={
                  fields.summaryProductCard.contestedMeterReadingLabel.value ?? 'Betwiste meterstand [not in sitecore]'
                }
                value={
                  rawMeterReadings ? (
                    <Stack direction="column" gap={'2'}>
                      <Stack.Item>{dateFormatter.medium(rawMeterReadings.date!)}</Stack.Item>
                      {rawMeterReadings.indexValues?.map((indexValue, idx) => (
                        <Stack.Item key={idx}>
                          {getMeterTimeFrameLabel(
                            fields.meterReadingDateStep.registerValuesList.value.enum,
                            indexValue.meterTimeFrameType,
                          ) ?? indexValue?.meterTimeFrameType}
                          {`: ${indexValue?.value} `}
                          {val.utilityType === 'Electricity'
                            ? fields.summaryProductCard.electricityUnitLabel.value
                            : fields.summaryProductCard.gasUnitLabel.value}
                        </Stack.Item>
                      ))}
                    </Stack>
                  ) : null
                }
                size="BodyM"
              />
              <ReadFormField
                label={
                  fields.summaryProductCard.correctMeterReadingsLabel.value ?? 'Correcte meterstand [not in sitecore]'
                }
                value={
                  correctedMeterReadings ? (
                    <Stack direction="column" gap={'2'}>
                      <Stack.Item>{correctedMeterReadings.date}</Stack.Item>
                      {correctedMeterReadings.indexValues?.map((indexValue, idx) => (
                        <Stack.Item key={idx}>
                          {getMeterTimeFrameLabel(
                            fields.meterReadingDateStep.registerValuesList.value.enum,
                            indexValue.meterTimeFrameType,
                          ) ?? indexValue?.meterTimeFrameType}
                          {`: ${indexValue?.value} `}
                          {val.utilityType === 'Electricity'
                            ? fields.summaryProductCard.electricityUnitLabel.value
                            : fields.summaryProductCard.gasUnitLabel.value}
                        </Stack.Item>
                      ))}
                      <Stack.Item>
                        {fields.summaryProductCard.addedPhotoLabel.value ?? 'Foto toegevoegd: [not in sitecore]'}{' '}
                        {(checkedMeterReading?.files?.length ?? 0) > 0
                          ? fields.summaryProductCard.yesLabel.value
                          : fields.summaryProductCard.noLabel.value}
                      </Stack.Item>
                      {checkedMeterReading.extraComment !== undefined && (
                        <Stack.Item>
                          {fields.summaryProductCard.extraInformationLabel.value ?? 'Extra info: [not in sitecore]'}{' '}
                          {checkedMeterReading.extraComment}
                        </Stack.Item>
                      )}
                    </Stack>
                  ) : null
                }
                size="BodyM"
              />
              <TextLink
                emphasis={'high'}
                onClick={() =>
                  onPreviousStep({ targetEanStep: EanStep.CorrectValues, targetActiveDisplayedEanIndex: index })
                }>
                {fields.summaryProductCard.editButtonLabel.value ?? 'Wijzigen [not in sitecore]'}
              </TextLink>
            </Bucket.Content>
          </Bucket>
        );
      })}

      <Stack direction={'row'} gap={'3'}>
        <Button
          action="primary"
          size="regular"
          tone="onLight"
          type="button"
          onClick={() => onSubmit()}
          isLoading={isLoading}>
          {fields.navigation.nextButtonLabel.value ?? 'Volgende [not in sitecore]'}
        </Button>
        <Button
          action="secondary"
          size="regular"
          tone="onLight"
          type="button"
          onClick={() =>
            onPreviousStep({ targetEanStep: EanStep.UploadFiles, targetActiveDisplayedEanIndex: form.length - 1 })
          }>
          {fields.navigation.previousButtonLabel.value ?? 'Vorige [not in sitecore]'}
        </Button>
      </Stack>
    </Stack>
  );
};

export default OverviewStep;
