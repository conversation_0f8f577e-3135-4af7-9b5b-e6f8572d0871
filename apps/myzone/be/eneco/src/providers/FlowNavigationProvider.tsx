import { createContext, Dispatch, FC, PropsWithChildren, SetStateAction, useCallback, useRef, useState } from 'react';

type Unsubscribe = () => void;
type Listener = () => void;
type Subscribe = (listener: Listener) => Unsubscribe;

export type FlowNavigationContextProps = {
  activeStepIndicator: number;
  showPreviousBtn: boolean;
  setShowPreviousBtn: Dispatch<SetStateAction<boolean>>;
  setActiveStepIndicator: Dispatch<SetStateAction<number>>;
  triggerOnPreviousClick: () => void;
  subscribeTriggerOnPreviousClick: Subscribe;
};

export const FlowNavigationContext = createContext<FlowNavigationContextProps>({
  activeStepIndicator: 0,
  setShowPreviousBtn: () => {},
  showPreviousBtn: false,
  setActiveStepIndicator: () => {},
  triggerOnPreviousClick: () => {},
  subscribeTriggerOnPreviousClick: () => () => {},
});

export const FlowNavigationProvider: FC<PropsWithChildren> = ({ children }) => {
  const [activeStep, setActiveStep] = useState<number>(0);
  const [showPreviousBtn, setShowPreviousBtn] = useState<boolean>(false);
  const previousClickListenersRef = useRef<Set<() => void>>(new Set());

  const subscribeTriggerOnPreviousClick = useCallback((listener: () => void) => {
    previousClickListenersRef.current.add(listener);
    return () => {
      previousClickListenersRef.current.delete(listener);
    };
  }, []);

  const triggerOnPreviousClick = useCallback(() => {
    const listeners = Array.from(previousClickListenersRef.current);
    for (const listener of listeners) {
      try {
        listener();
      } catch (err) {
        void err;
        throw new Error('Listener error in triggerOnPreviousClick');
      }
    }
  }, []);

  return (
    <FlowNavigationContext.Provider
      value={{
        activeStepIndicator: activeStep,
        showPreviousBtn,
        setShowPreviousBtn,
        setActiveStepIndicator: setActiveStep,
        triggerOnPreviousClick,
        subscribeTriggerOnPreviousClick,
      }}>
      {children}
    </FlowNavigationContext.Provider>
  );
};
