import { FC, useEffect } from 'react';

import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import { useProductsGetProductsForAccountV2 } from '@dc/hooks';
import { usePageTitle } from '@hooks/pageTitle';
import { useCustomerProfileAccount } from '@hooks/profile';
import { useLinkComponent } from '@link';
import { useContent, usePlaceholder } from '@sitecore/common';
import { ProductsV2Rendering } from '@sitecore/types/ProductsV2';
import { Skeleton, Stack, TextLink } from '@sparky';

import { AllocatedHoursCard } from './AllocatedHoursCard';
import { GridOperatorCard } from './GridOperatorCard';
import { HappyPowerLink } from './HappyPowerLink';
import { MeterCard } from './MeterCard';
import { TariffsCard } from './TariffsCard';
import { ProductHeaderCard } from '../Shared/ProductHeaderCard/ProductHeaderCard';

interface RouteProps {
  type?: string;
  agreementId?: string;
}

export const ProductDetail: FC<RouteProps> = ({ agreementId }) => {
  const { data, isValidating, error } = useProductsGetProductsForAccountV2({ includeProductRates: true });
  const { currentAccount, error: customerError } = useCustomerProfileAccount();
  const { updateTitle, reset } = usePageTitle();
  const { f, fields } = useContent<ProductsV2Rendering>();
  const Link = useLinkComponent();
  const { setScope } = usePlaceholder();
  const product = data?.products?.find(product => product.agreementId === Number(agreementId));

  useEffect(() => {
    updateTitle(product?.type?.description || '');

    return () => {
      reset();
    };
  }, [product, updateTitle, reset]);

  if (isValidating) return <Skeleton height={200} />;

  const FailedProductRetrievalNotification = (
    <TrackedNotificationBox
      title={f('failedProductRetrievalErrorNotification.value.title')}
      text={f('failedProductRetrievalErrorNotification.value.content')}
      variant={f('failedProductRetrievalErrorNotification.value.variant')}
      isAlert={false}
    />
  );

  if (!agreementId || error || !data || customerError) return FailedProductRetrievalNotification;

  if (!product) return FailedProductRetrievalNotification;

  const optionalSlash = fields.productMaintenanceLink.value.href.endsWith('/') ? '' : '/';
  const maintenanceHref = `${fields.productMaintenanceLink.value.href}${optionalSlash}${product.agreementId || ''}`;

  const maintenanceLink = {
    ...fields.productMaintenanceLink,
    value: {
      ...fields.productMaintenanceLink,
      href: maintenanceHref,
    },
  };

  const showMaintenanceLink =
    product.hasMaintenance && fields.productMaintenanceLink.value.href && fields.productMaintenanceLink.value.text;

  const agreementPeriod = product.indefinite ? 'indefinite' : 'definite';
  const hasRedelivery = currentAccount?.hasRedelivery?.toString() || null;
  const agreementType = product.isAccountHybrid ? 'hybrid' : currentAccount?.hasDynamicPricing ? 'dynamic' : 'fixed';
  const productType = product.type?.name || null;

  setScope('jss-main-bottom', {
    'agreement-period': agreementPeriod,
    ...(hasRedelivery && { 'has-redelivery': hasRedelivery }),
    ...(agreementType && { 'agreement-type': agreementType }),
    ...(productType && { 'product-type': productType }),
  });

  return (
    <Stack gap="6">
      <ProductHeaderCard product={product}>
        {showMaintenanceLink && (
          <Link linkType={fields.productMaintenanceLink.value.linktype} linkValue={maintenanceLink.value}>
            <TextLink emphasis="high">{f('productMaintenanceLink.value.text')}</TextLink>
          </Link>
        )}
        <HappyPowerLink product={product} />
      </ProductHeaderCard>
      <TariffsCard product={product} />
      {product.meters?.map(meter => (
        <MeterCard key={meter.ean} meter={meter} />
      ))}
      <AllocatedHoursCard product={product} />
      <GridOperatorCard
        gridOperatorName={product.gridOperatorName}
        gridOperatorLabel={product.gridOperatorLabel}
        gridOperatorDescription={product.gridOperatorDescription}
      />
    </Stack>
  );
};
