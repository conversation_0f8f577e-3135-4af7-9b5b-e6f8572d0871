import { screen } from '@testing-library/react';

import { useCustomerGetCustomerProfileBe } from '@dc/hooks';
import renderApp from '@jest-tools/renderApp';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/index';

import DongleInfo from './DongleInfo';

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useCustomerGetCustomerProfileBe: jest.fn() as unknown as typeof useCustomerGetCustomerProfileBe,
}));

describe('DongleInfo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly when the dongle is active', () => {
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: {
        hasDongle: true,
        dongleInfoResponse: {
          macAddress: '00-B0-D0-63-C2-26',
          status: 'InSupply',
          wifiName: 'EnecoWifi',
          firmWare: 'Firmware 1.0.0',
        },
      },
      mutate: jest.fn(),
    });

    renderApp(DongleInfo, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/connections/dongle',
    });

    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Actief')).toBeInTheDocument();
    expect(screen.getByText('Wifi-netwerk')).toBeInTheDocument();
    expect(screen.getByText('EnecoWifi')).toBeInTheDocument();
    expect(screen.getByText('Serienummer')).toBeInTheDocument();
    expect(screen.getByText('00-B0-D0-63-C2-26')).toBeInTheDocument();
  });

  it('should render correctly when the dongle is not connected', () => {
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: {
        hasDongle: false,
        dongleInfoResponse: {
          macAddress: '00-B0-D0-63-C2-26',
          status: 'Unknown',
          wifiName: 'EnecoWifi',
          firmWare: 'Firmware 1.0.0',
        },
      },
      mutate: jest.fn(),
    });

    renderApp(DongleInfo, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/connections/dongle',
    });

    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Nog niet verbonden')).toBeInTheDocument();
    expect(screen.getByText('Verbind nu je dongle')).toBeInTheDocument();
    expect(screen.queryByText('Wifi-netwerk')).not.toBeInTheDocument();
    expect(screen.queryByText('EnecoWifi')).not.toBeInTheDocument();
    expect(screen.queryByText('Serienummer')).not.toBeInTheDocument();
    expect(screen.queryByText('00-B0-D0-63-C2-26')).not.toBeInTheDocument();
  });

  it('should render correctly when there is a problem with the dongle', () => {
    (useCustomerGetCustomerProfileBe as jest.Mock).mockReturnValue({
      data: {
        hasDongle: true,
        dongleInfoResponse: {
          macAddress: '00-B0-D0-63-C2-26',
          status: 'Error',
          wifiName: 'EnecoWifi',
          firmWare: 'Firmware 1.0.0',
        },
      },
      mutate: jest.fn(),
    });

    renderApp(DongleInfo, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/profile/connections/dongle',
    });

    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Connectieprobleem')).toBeInTheDocument();
    expect(screen.getByText('Wifi-netwerk')).toBeInTheDocument();
    expect(screen.getByText('EnecoWifi')).toBeInTheDocument();
    expect(screen.getByText('Serienummer')).toBeInTheDocument();
    expect(screen.getByText('00-B0-D0-63-C2-26')).toBeInTheDocument();
  });
});
