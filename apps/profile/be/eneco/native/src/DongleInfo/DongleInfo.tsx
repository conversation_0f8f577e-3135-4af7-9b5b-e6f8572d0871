import React, { FC, useEffect } from 'react';

import logger from '@common/log';
import { useLinkComponent } from '@link';
import ListTileItem from '@native-components/components/ListTileItem';
import { useCustomerProfileContext } from '@native-components/components/wrappers/CustomerProfileProvider/CustomerProfileProvider';
import NativeErrorProvider from '@native-components/components/wrappers/NativeErrorProvider/NativeErrorProvider';
import { useContent } from '@sitecore/common';
import { DongleInfoRendering } from '@sitecore/types/DongleInfo';
import { Box, ButtonLink, Card, NotificationBox, Skeleton, Stack, Stretch } from '@sparky';

import { createDongleBadgeMap } from './helpers/mappers';

const DongleInfo: FC = () => {
  const { data, isLoading, error, mutate } = useCustomerProfileContext();
  const { fields } = useContent<DongleInfoRendering>();
  const Link = useLinkComponent();

  useEffect(() => {
    try {
      mutate();
    } catch (e) {
      logger.error('HaDloc', 'Error while mutating customer profile data', e);
    }
  }, [mutate]);

  const dongleStatus = data?.dongleInfoResponse?.status;

  if (isLoading) {
    return <Skeleton height="300px" />;
  }

  if (error) {
    return <NativeErrorProvider error={error} />;
  }

  if (!dongleStatus) {
    return null;
  }

  const { enum: statusEnums } = fields.connectionStatusList.value;
  const dongleBadgeMap = createDongleBadgeMap(statusEnums);
  const badgeInfo = dongleBadgeMap[dongleStatus];
  const isNotConnected = dongleStatus !== 'InSupply' && dongleStatus !== 'Error';

  return (
    <Card>
      <ListTileItem
        label={fields.connectionStatusLabel.value}
        badge={{ label: badgeInfo.label, color: badgeInfo.color }}
        hasDivider={!!data.dongleInfoResponse?.wifiName || !!data.dongleInfoResponse?.macAddress}
      />
      {!isNotConnected && data.dongleInfoResponse && (
        <>
          {data.dongleInfoResponse.wifiName && (
            <ListTileItem
              label={fields.networkLabel.value}
              lowerLabel={data.dongleInfoResponse.wifiName}
              hasDivider={!!data.dongleInfoResponse.macAddress}
            />
          )}
          {data.dongleInfoResponse.macAddress && (
            <ListTileItem
              label={fields.macAddressLabel.value}
              lowerLabel={data.dongleInfoResponse.macAddress}
              hasDivider={false}
            />
          )}
        </>
      )}
      {isNotConnected && (
        <Box paddingBottom="4" paddingX="4">
          <Stack gap="3">
            <NotificationBox
              isAlert={false}
              title={fields.connectionNotification.value.title}
              variant={fields.connectionNotification.value.variant}
              text={fields.connectionNotification.value.content}
            />
            <Stretch>
              <Link href={fields.connectDongleButtonLink.value.href} linkType="internal">
                <ButtonLink action="secondary" size="compact">
                  {fields.connectDongleButtonLink.value.text}
                </ButtonLink>
              </Link>
            </Stretch>
          </Stack>
        </Box>
      )}
    </Card>
  );
};

export default DongleInfo;
