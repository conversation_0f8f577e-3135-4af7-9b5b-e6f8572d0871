parameters:
  - name: nodeVersion
    displayName: Node version (default 22.6.0)
    type: string
    default: 22.6.0

steps:
  - task: UseNode@1
    displayName: Use Node ${{ parameters.nodeVersion }}
    inputs:
      version: ${{ parameters.nodeVersion }}

  - task: CmdLine@2
    displayName: Show Node and NPM version
    inputs:
      script: |
        echo "Node version: $(node --version)"
        echo "npm version: $(npm --version)"

  - task: CmdLine@2
    displayName: Install Global packages
    inputs:
      script: |
        npm install --global nx@21 typescript@5
