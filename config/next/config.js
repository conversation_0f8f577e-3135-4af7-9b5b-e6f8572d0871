const sparkyChunkConfig = require('../../libs/sparky/chunk.config');
const { createVanillaExtractPlugin } = require('@vanilla-extract/next-plugin');
const { join } = require('path');
const { existsSync } = require('node:fs');

/*
 * The Next.js containers strictly follow the rule of "build once, run
 * everywhere". This means that the Next.js configuration is written during
 * build time, and read when starting the Next.js server in production (any of
 * DTAP). This also means that on Azure, during build time, basically no
 * environment variables are available. They're only available during runtime.
 */

const { getCSPHeader } = require('./security-headers');
const { withNx } = require('@nx/next');
const path = require('path');

const buildCSPConfig = (environment, sourceRoot, host) => {
  const cspPath = join(process.env.NX_WORKSPACE_ROOT, sourceRoot, 'csp.json');
  if (!existsSync(cspPath)) {
    throw new Error(`CSP file not found at ${sourceRoot}/csp.json`);
  }
  const cspJson = require(cspPath);

  return {
    source: '/(.*)',
    has: [
      {
        type: 'host',
        value: host,
      },
    ],
    headers: [
      {
        key: 'Content-Security-Policy',
        value: getCSPHeader(cspJson, environment),
      },
    ],
  };
};

module.exports.baseConfig = {
  experimental: {
    // Next.js prevents code being loaded from other directories, Nx appends
    // only the /libs folder to the loader. As we are including code from /apps
    // this flag is necessary:
    externalDir: true,
    scrollRestoration: true,
  },
  outputFileTracingRoot: path.join(__dirname, '../../'),
  output: 'standalone',
  basePath: '',
  poweredByHeader: false,
  reactStrictMode: true,
  // Source maps are useful in the browser console. And so is the `uuid` that we
  // send with the `logger.*` messages. Both help to find related source code.
  // To enable source maps in Application Insights these artifacts should be
  // uploaded to an Azure blob storage container. This is absent, since both
  // this configuration/process and the UX in AI do not spark joy.
  productionBrowserSourceMaps: true,
  trailingSlash: true,
  transpilePackages: ['@eneco-online/sparky'],
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.devtool = 'source-map';
    }

    config.optimization.splitChunks = {
      ...config.optimization.splitChunks,
      cacheGroups: {
        ...config.optimization.splitChunks.cacheGroups,
        sparky: sparkyChunkConfig,
      },
    };

    return config;
  },
};

module.exports.basePlugins = [createVanillaExtractPlugin(), withNx];

module.exports.withCSPHeaders =
  (sourceRoot, containerName) =>
  (nextConfig = {}) => {
    return async () => {
      if (process.env.NODE_ENV !== 'production') return nextConfig;
      return {
        ...nextConfig,
        headers: () => {
          return [
            buildCSPConfig('test', sourceRoot, `${containerName}-web-test.dxp.enecogroup.com`),
            buildCSPConfig('acceptance', sourceRoot, `${containerName}-web-acc.dxp.enecogroup.com`),
            buildCSPConfig('production', sourceRoot, `${containerName}-web.dxp.enecogroup.com`),
          ];
        },
      };
    };
  };

const hostedBasePath = process.env.HOSTED_PR ? String(process.env.HOSTED_PR).replace(/^\/?/, '/') : '';

const getBasePath = basePath => {
  return hostedBasePath + (basePath ?? '');
};

const withAssetPrefix =
  assetPrefix =>
  (nextConfig = {}) => {
    return async phase => {
      const basePath = getBasePath();
      const prefix = basePath + (process.env.NODE_ENV !== 'production' ? '' : assetPrefix);
      return { ...nextConfig, assetPrefix: prefix };
    };
  };

module.exports.getBasePath = getBasePath;
module.exports.withAssetPrefix = withAssetPrefix;
