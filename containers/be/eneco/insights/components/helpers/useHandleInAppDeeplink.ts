import { useEffect } from 'react';

import { App, URLOpenListenerEvent } from '@capacitor/app';
import { useRouter } from '@dxp-next';

const useHandleInAppDeeplink = () => {
  const router = useRouter();

  useEffect(() => {
    App.addListener('appUrlOpen', (event: URLOpenListenerEvent) => {
      const url = new URL(event.url);

      if (url.pathname.search('/deeplink') !== -1) {
        const path = url.pathname.replace('/deeplink', '');
        url.searchParams.set('item', encodeURIComponent(path));
      }

      window.location.assign(`/?${url.searchParams}`);
    });
  }, [router]);

  useEffect(() => {
    const deepLinkListener = (event: CustomEvent) => {
      router.push(event.detail);
    };

    document.addEventListener('deepLink', deepLinkListener as EventListenerOrEventListenerObject, false);

    return () => {
      document.removeEventListener('deepLink', deepLinkListener as EventListenerOrEventListenerObject, false);
    };
  }, [router]);
};

export default useHandleInAppDeeplink;
