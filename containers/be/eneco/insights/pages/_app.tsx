import '@common/application-insights';

import React, { ReactElement, ReactNode } from 'react';

import GTM from '@apps/gtm/shared/multilabel/src/native';
import { ApplicationProvider } from '@common/application';
import { SWRCacheProvider } from '@common/swr';
import { ErrorBoundary } from '@custom-components/native/ErrorBoundary';
import { AppProps, NextPage } from '@dxp-next';
import ReturnPathProvider from '@native-auth/providers/ReturnPathProvider';
import NativeApp from '@native-components/components/NativeApp';
import NetworkStatus from '@native-components/components/NetworkStatus';
import { SmaNotificationContextProvider } from '@native-components/components/SmaNotification';
import { CustomerProfileProvider } from '@native-components/components/wrappers/CustomerProfileProvider/CustomerProfileProvider';
import NativeAppProvider from '@native-components/components/wrappers/NativeAppProvider/NativeAppProvider';
import { PushNotificationsProvider } from '@native-components/providers/PushNotificationsProvider';
import { SparkyProvider } from '@sparky/providers';
import { TrackingProvider } from '@tracking';

import useAppInit from '../components/helpers/useAppInit';
import { NativeDigitalCoreProvider } from '../components/providers/NativeDigitalCoreProvider';
import SWRConfigClient from '../components/SWRConfigClient';
import { getPath } from '../components/utils/common';
import { name, i18n } from '../config';
import ErrorBoundaryPage from '../src/pages/ErrorBoundaryPage';

type NextPageWithLayout = NextPage & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

const BeInsightsApp = ({ Component, pageProps }: AppPropsWithLayout) => {
  const getLayout = Component.getLayout || (page => page);

  useAppInit();

  return (
    <ApplicationProvider i18n={i18n} path={getPath()}>
      <TrackingProvider scope={name}>
        <SparkyProvider locale={i18n.locale}>
          <SWRCacheProvider>
            <SWRConfigClient>
              <NativeDigitalCoreProvider>
                <ReturnPathProvider ignorePaths={['/login/callback/', '/login/failed/']}>
                  <NativeAppProvider>
                    <CustomerProfileProvider>
                      <SmaNotificationContextProvider>
                        <PushNotificationsProvider>
                          <>
                            <GTM />
                            <NativeApp>
                              <NetworkStatus />
                              <ErrorBoundary fallback={<ErrorBoundaryPage />}>
                                {getLayout(<Component {...pageProps} />)}
                              </ErrorBoundary>
                            </NativeApp>
                          </>
                        </PushNotificationsProvider>
                      </SmaNotificationContextProvider>
                    </CustomerProfileProvider>
                  </NativeAppProvider>
                </ReturnPathProvider>
              </NativeDigitalCoreProvider>
            </SWRConfigClient>
          </SWRCacheProvider>
        </SparkyProvider>
      </TrackingProvider>
    </ApplicationProvider>
  );
};

export default BeInsightsApp;
