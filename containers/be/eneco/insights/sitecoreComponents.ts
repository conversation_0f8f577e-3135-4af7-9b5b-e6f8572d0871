import dynamic from 'next/dynamic';

export default {
  /* APPS */
  Registration: dynamic(
    () => import('@apps/accessmanagement/be/eneco/native/src/components/Registration/Registration'),
  ),
  PrivacyConsent: dynamic(
    () => import('@apps/accessmanagement/be/eneco/native/src/components/PrivacyConsent/PrivacyConsent'),
  ),
  AppLandingPage: dynamic(
    () => import('@apps/accessmanagement/be/eneco/native/src/components/AppLandingPage/AppLandingPage'),
  ),
  FluviusFlow: dynamic(() => import('@apps/accessmanagement/be/eneco/native/src/components/FluviusFlow/FluviusFlow')),
  DongleOnboardingFlow: dynamic(
    () => import('@apps/dongle/be/eneco/native/src/components/Onboarding/DongleOnboardingFlow'),
  ),
  WelcomePage: dynamic(() => import('@apps/accessmanagement/be/eneco/native/src/components/WelcomePage/WelcomePage')),
  AppVersion: dynamic(() => import('@apps/profile/be/eneco/native/src/AppInfo/AppInfo')),
  CustomerDetailsHeader: dynamic(
    () => import('@apps/profile/be/eneco/native/src/CustomerDetailsHeader/CustomerDetailsHeader'),
  ),
  SignOutButton: dynamic(() => import('@apps/profile/be/eneco/native/src/SignOutButton/SignOutButton')),
  PushNotificationToggles: dynamic(
    () => import('@apps/profile/be/eneco/native/src/PushNotificationsToggles/PushNotificationToggles'),
  ),
  DashboardServiceError: dynamic(
    () => import('@apps/dashboard/be/eneco/native/src/components/DashboardServiceError/DashboardServiceError'),
  ),
  WoonprofielSuccessCard: dynamic(
    () => import('@apps/dashboard/be/eneco/native/src/components/WoonprofielSuccessCard/WoonprofielSuccessCard'),
  ),
  MonthSummaryCard: dynamic(
    () => import('@apps/dashboard/be/eneco/native/src/components/MonthSummaryCard/MonthSummaryCard'),
  ),
  MotivationCard: dynamic(() => import('@apps/dashboard/be/eneco/native/src/components/MotivationCard/MotivationCard')),
  DashboardHeader: dynamic(
    () => import('@apps/dashboard/be/eneco/native/src/components/DashboardHeader/DashboardHeader'),
  ),
  GeneralInformationalBanner: dynamic(
    () =>
      import('@apps/dashboard/be/eneco/native/src/components/GeneralInformationalBanner/GeneralInformationalBanner'),
  ),
  UsageCards: dynamic(() => import('@apps/dashboard/be/eneco/native/src/components/UsageCards/UsageCards')),
  FluviusOnboardingButton: dynamic(
    () => import('@apps/mandate/be/eneco/native/src/components/FluviusOnboardingButton/FluviusOnboardingButton'),
  ),
  MandateStatus: dynamic(() => import('@apps/mandate/be/eneco/native/src/components/MandateStatus/MandateStatus')),
  MandateInfo: dynamic(() => import('@apps/profile/be/eneco/native/src/MandateInfo/MandateInfo')),
  SmartChargingInfo: dynamic(() => import('@apps/profile/be/eneco/native/src/SmartChargingInfo/SmartChargingInfo')),
  PremiumServices: dynamic(() => import('@apps/profile/be/eneco/native/src/PremiumServices/PremiumServices')),
  PremiumServiceBanner: dynamic(
    () => import('@apps/profile/be/eneco/native/src/PremiumServiceBanner/PremiumServiceBanner'),
  ),
  PremiumServiceDetails: dynamic(
    () => import('@apps/profile/be/eneco/native/src/PremiumServiceDetails/PremiumServiceDetails'),
  ),
  DongleInfo: dynamic(() => import('@apps/profile/be/eneco/native/src/DongleInfo/DongleInfo')),
  SmartChargingConnectionPanel: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingConnection/SmartChargingConnectionPanel'),
  ),
  SmartChargingConnectionIssue: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingConnectionIssue/SmartChargingConnectionIssue'),
  ),
  MandateWarning: dynamic(() => import('@apps/mandate/be/eneco/native/src/components/MandateWarning/MandateWarning')),
  NativeUsage: dynamic(() => import('@apps/usage/be/eneco/native/src/components/NativeUsage/NativeUsage')),
  CustomerConnections: dynamic(
    () => import('@apps/profile/be/eneco/native/src/CustomerConnections/CustomerConnections'),
  ),
  EnergyProfile: dynamic(() => import('@apps/energyprofile/be/eneco/native/src/EnergyProfileFlow')),
  HomeProfileInsightsApp: dynamic(() => import('@apps/energyprofile/be/eneco/native/src/EnergyProfileOverview')),
  RevokePrivacy: dynamic(() => import('@apps/profile/be/eneco/native/src/RevokePrivacy/RevokePrivacy')),
  SmaPasswordDetailsForm: dynamic(
    () => import('@apps/profile/be/eneco/native/src/SmaPasswordDetailsForm/SmaPasswordDetailsForm'),
  ),
  SmaUsernameDetailsForm: dynamic(
    () => import('@apps/profile/be/eneco/native/src/SmaUsernameDetailsForm/SmaUsernameDetailsForm'),
  ),
  RemoveAccount: dynamic(() => import('@apps/profile/be/eneco/native/src/RemoveAccount/RemoveAccount')),
  SmartChargingCompatibilityCheck: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingCompatibilityCheck/SmartChargingCompatibilityCheck'
      ),
  ),
  SmartChargingCompatibilityCheckResult: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingCompatibilityCheck/SmartChargingCompatibilityCheckResult'
      ),
  ),
  SmartChargingConsentButton: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingConsentButton/SmartChargingConsentButton'),
  ),
  SmartChargingSessionDetails: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingSessionDetails/SmartChargingSessionDetails'),
  ),
  SmartChargingQuickLinksAndReward: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingQuickLinksAndReward/SmartChargingQuickLinksAndReward'
      ),
  ),
  SmartChargingDefaultSchedule: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingDefaultSchedule/SmartChargingDefaultSchedule'),
  ),
  SmartChargingSchedulePreferences: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingSchedulePreferences/SmartChargingSchedulePreferences'
      ),
  ),
  StopSmartChargingButton: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/StopSmartChargingButton/StopSmartChargingButton'),
  ),
  StopSmartChargingNotification: dynamic(
    () =>
      import('@apps/hems/be/eneco/native/src/components/StopSmartChargingNotification/StopSmartChargingNotificiation'),
  ),
  SmartChargingCurrentLoadingSchedule: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingCurrentLoadingSchedule/SmartChargingCurrentLoadingSchedule'
      ),
  ),
  SmartChargingOverrideDepartureTimeSchedule: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingOverrideDepartureTimeSchedule/SmartChargingOverrideDepartureTimeSchedule'
      ),
  ),
  SmartChargingOverrideLoadingTargetSchedule: dynamic(
    () =>
      import(
        '@apps/hems/be/eneco/native/src/components/SmartChargingOverrideLoadingTargetSchedule/SmartChargingOverrideLoadingTargetSchedule'
      ),
  ),
  SmartChargingInsights: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingInsights/SmartChargingInsights'),
  ),

  ProductPurchaseFlow: dynamic(() => import('@apps/launchpad/be/eneco/native/src/ProductPurchase/ProductPurchaseFlow')),
  ProductPurchaseResult: dynamic(
    () => import('@apps/launchpad/be/eneco/native/src/ProductPuchaseResult/ProductPurchaseResult'),
  ),
  SmartChargingSchedulingToggle: dynamic(
    () =>
      import('@apps/hems/be/eneco/native/src/components/SmartChargingSchedulingToggle/SmartChargingSchedulingToggle'),
  ),
  AccountAndSettings: dynamic(() => import('@apps/profile/be/eneco/native/src/AccountAndSettings/AccountAndSettings')),
  SmaPaymentDetails: dynamic(() => import('@apps/profile/be/eneco/native/src/SmaPaymentDetails/SmaPaymentDetails')),
  InvoicesPremiumServices: dynamic(
    () => import('@apps/profile/be/eneco/native/src/InvoicesPremiumServices/InvoicesPremiumServices'),
  ),
  ReportProblem: dynamic(() => import('@apps/profile/be/eneco/native/src/ReportProblem/ReportProblem')),
  FeaturedBannerContainer: dynamic(
    () => import('@apps/dashboard/be/eneco/native/src/components/FeaturedBannerContainer/FeaturedBannerContainer'),
  ),
  SmartChargingDashboardCard: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingDashboardCard/SmartChargingDashboardCard'),
  ),
  SmartChargingCard: dynamic(
    () => import('@apps/hems/be/eneco/native/src/components/SmartChargingCard/SmartChargingCard'),
  ),

  /* SITECORE COMPONENTS */
  ButtonLink: dynamic(() => import('@sitecore/components/content/ButtonLink/ButtonLink')),
  Image: dynamic(() => import('@sitecore/components/content/Image/Image')),
  RichText: dynamic(() => import('@sitecore/components/content/RichText/RichText')),
  NotificationBox: dynamic(() => import('@sitecore/components/content/NotificationBox/NotificationBox')),
  Accordion: dynamic(() => import('@sitecore/components/content/Accordion/Accordion')),
  TitleTextCTA: dynamic(() => import('@sitecore/components/content/TitleTextCTA/TitleTextCTA')),
  Checklist: dynamic(() => import('@sitecore/components/content/Checklist/Checklist')),
  StepExplainer: dynamic(() => import('@sitecore/components/content/StepExplainerVertical/StepExplainerVertical')),

  /* NATIVE SITECORE COMPONENTS */
  BottomNavigationsButtons: dynamic(
    () => import('@sitecore/native-components/content/HemsBottomNavButtons/HemsBottomNavButtons'),
  ),
  BottomMenu: dynamic(() => import('@sitecore/native-components/content/BottomMenu/BottomMenu')),
  HEMSMinimalNavigationBar: dynamic(
    () => import('@sitecore/native-components/content/HemsMinimalNavigationBar/HemsMinimalNavigationBar'),
  ),
  TopBar: dynamic(() => import('@sitecore/native-components/content/TopBar/TopBar')),
  IconLink: dynamic(() => import('@sitecore/native-components/content/IconLink/IconLink')),
  PageTitle: dynamic(() => import('@sitecore/native-components/content/PageTitle/PageTitle')),
  TopNavigation: dynamic(() => import('@sitecore/native-components/content/TopNavigation/TopNavigation')),
  RealtimeContent: dynamic(() => import('@sitecore/native-components/content/RealtimeContent/RealtimeContent')),
  Bleed: dynamic(() => import('@sitecore/native-components/content/Bleed/Bleed')),
  ErrorMessageContainer: dynamic(
    () => import('@sitecore/native-components/content/ErrorMessageContainer/ErrorMessageContainer'),
  ),
  InsightsTutorial: dynamic(() => import('@sitecore/native-components/content/InsightsTutorial/InsightsTutorial')),
  SmaButtonLink: dynamic(() => import('@sitecore/native-components/content/SmaButtonLink/SmaButtonLink')),
};
