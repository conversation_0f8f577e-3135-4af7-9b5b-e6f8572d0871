import dynamic from 'next/dynamic';

export default {
  // Navigation
  BreadCrumbs: dynamic(() => import('@sitecore/components/content/Breadcrumbs/Breadcrumbs')),
  FooterMenu: dynamic(() => import('@sitecore/components/content/Footer/FooterMenu')),
  Footer: dynamic(() => import('@sitecore/components/content/Footer/Footer')),
  LanguageSelector: dynamic(() => import('@sitecore/components/content/LanguageSelector/LanguageSelector')),
  Logo: dynamic(() => import('@sitecore/components/eneco/ImageLink/ImageLink')),
  MainNavigation: dynamic(() => import('@apps/header/be/eneco/src/BeHeader')),
  HeaderLink: dynamic(() => import('@apps/header/be/eneco/src/HeaderLink')),
  MegaMenu: dynamic(() => import('@sitecore/components/content/MegaMenu/MegaMenu')),
  MegaMenuV2: dynamic(() => import('@sitecore/components/content/MegaMenuV2/MegaMenuV2')),
  MegaMenuDivision: dynamic(() => import('@sitecore/components/content/MegaMenuDivision/MegaMenuDivision')),
  MessageBar: dynamic(() => import('@sitecore/components/content/MessageBar/MessageBar')),
  NotificationsBar: dynamic(() => import('@sitecore/components/content/NotificationsBar/NotificationsBar')),
  SubFooterMenu: dynamic(() => import('@sitecore/components/content/Footer/SubFooterMenu')),
  SocialMediaMenu: dynamic(() => import('@sitecore/components/content/Footer/SocialMediaMenu')),

  // Content components
  ContentCategories: dynamic(() => import('@sitecore/components/content/ContentCategories/ContentCategories')),
  ChatLinks: dynamic(() => import('@sitecore/components/content/ChatLinks/ChatLinks')),
  Accordion: dynamic(() => import('@sitecore/components/content/Accordion/Accordion')),
  Hero: dynamic(() => import('@sitecore/components/content/Hero/Hero')),
  HeroRebranded: dynamic(() => import('@sitecore/components/content/HeroRebranded/HeroRebranded')),
  NotificationBox: dynamic(() => import('@sitecore/components/content/NotificationBox/NotificationBox')),
  RichText: dynamic(() => import('@sitecore/components/content/RichText/RichText')),
  TopTasks: dynamic(() => import('@sitecore/components/content/TopTasks/TopTasks')),
  ContactCustomerService: dynamic(
    () => import('@sitecore/components/content/ContactCustomerService/ContactCustomerService'),
  ),
  Image: dynamic(() => import('@sitecore/components/content/Image/Image')),
  List: dynamic(() => import('@sitecore/components/content/List/List')),
  HeroCard: dynamic(() => import('@sitecore/components/content/HeroCard/HeroCard')),
  HeroCardRebranded: dynamic(() => import('@sitecore/components/content/HeroCardRebranded/HeroCardRebranded')),
  LinkCard: dynamic(() => import('@sitecore/components/content/LinkCard/LinkCard')),
  StoreButtons: dynamic(() => import('@sitecore/components/content/StoreButtons/StoreButtons')),
  SimpleTable: dynamic(() => import('@sitecore/components/content/SimpleTable/SimpleTable')),
  TextStreamer: dynamic(() => import('@sitecore/components/content/TextStreamer/TextStreamer')),
  UsabillaFeedback: dynamic(() => import('@sitecore/components/system/UsabillaFeedback/UsabillaFeedback')),
  WeDoenHetNuTestimonialsCard: dynamic(
    () => import('@sitecore/components/content/WeDoenHetNuTestimonialsCard/WeDoenHetNuTestimonialsCard'),
  ),
  AddressFinder: dynamic(() => import('@sitecore/components/content/AddressFinder/AddressFinder')),
  StepExplainer: dynamic(() => import('@sitecore/components/content/StepExplainerVertical/StepExplainerVertical')),
  CreditWarningBar: dynamic(() => import('@sitecore/components/content/CreditWarningBar/CreditWarningBar')),
  ShareOnSocials: dynamic(() => import('@sitecore/components/content/ShareOnSocials/ShareOnSocials')),
  ButtonLink: dynamic(() => import('@sitecore/components/content/ButtonLink/ButtonLink')),
  ButtonLineUp: dynamic(() => import('@sitecore/components/content/ButtonLineUp/ButtonLineUp')),
  Price: dynamic(() => import('@sitecore/components/content/Price/Price')),
  IconList: dynamic(() => import('@sitecore/components/content/IconList/IconList')),
  ContentHubFilter: dynamic(() => import('@sitecore/components/content/ContentHubFilter/ContentHubFilter')),
  FlourishChart: dynamic(() => import('@sitecore/components/content/FlourishChart/FlourishChart')),
  ProjectOverview: dynamic(() => import('@sitecore/components/content/ProjectOverview/ProjectOverview')),
  SmartHomeBattery: dynamic(() => import('@sitecore/components/content/SmartHomeBattery/SmartHomeBattery')),

  // Apps
  CustomerReview: dynamic(() => import('@apps/customerreview/shared/multilabel/src/CustomerReview')),
  DynamicPricingTariffs: dynamic(
    () => import('@apps/dynamicpricingtariffs/shared/multilabel/src/DynamicPricingTariffs/DynamicPricingTariffs'),
  ),

  // Other
  CookieWall: dynamic(() => import('@apps/cookiewall/nl/multilabel/src/CookieWall')),
  ErrorPage: dynamic(() => import('@sitecore/components/content/ErrorPage/ErrorPage')),
  GTM: dynamic(() => import('@apps/gtm/be/eneco/src/GTMTag')),
  Vwo: dynamic(() => import('@sitecore/components/system/Vwo/Vwo')),
  Hotjar: dynamic(() => import('@sitecore/components/system/Hotjar/Hotjar')),
  MetaData: dynamic(() => import('@sitecore/components/system/MetaData/MetaData')),
  MetaTags: dynamic(() => import('@sitecore/components/system/MetaTags/MetaTags')),
  StructuredDataSchema: dynamic(() => import('@sitecore/components/system/StructuredDataSchema/StructuredDataSchema')),
  Chat: dynamic(() => import('@apps/chat/shared/multilabel/src/Chat')),
  ContractConfirmation: dynamic(() => import('@sitecore/components/content/ContractConfirmation/ContractConfirmation')),

  // Wrapper components
  Article: dynamic(() => import('@sitecore/components/content/Article/Article')),
  Centered: dynamic(() => import('@sitecore/components/content/Centered/Centered')),
  Section: dynamic(() => import('@sitecore/components/content/Section/Section')),
  LineUp: dynamic(() => import('@sitecore/components/content/LineUp/LineUp')),
  NavigationView: dynamic(() => import('@sitecore/components/content/NavigationView/NavigationView')),
  SplitView: dynamic(() => import('@sitecore/components/content/SplitView/SplitView')),
  Carousel: dynamic(() => import('@sitecore/components/content/Carousel/Carousel')),

  // Wrappable components
  ArticleCard: dynamic(() => import('@sitecore/components/content/ArticleCard/ArticleCard')),
  ArticleContentCard: dynamic(() => import('@sitecore/components/content/ArticleContentCard/ArticleContentCard')),
  ContentCard: dynamic(() => import('@sitecore/components/content/ContentCard/ContentCard')),
  ContentCardRebranded: dynamic(() => import('@sitecore/components/content/ContentCardRebranded/ContentCardRebranded')),
  ProductCard: dynamic(() => import('@sitecore/components/content/ProductCard/ProductCard')),
  StickyNav: dynamic(() => import('@sitecore/components/content/StickyNav/StickyNav')),
  Table: dynamic(() => import('@sitecore/components/content/Table/Table')),
  TitleTextCTA: dynamic(() => import('@sitecore/components/content/TitleTextCTA/TitleTextCTA')),
  PersonalizedTitleTextCTA: dynamic(
    () => import('@sitecore/components/content/PersonalizedTitleTextCTA/PersonalizedTitleTextCTA'),
  ),
  Checklist: dynamic(() => import('@sitecore/components/content/Checklist/Checklist')),
  USPItem: dynamic(() => import('@sitecore/components/content/USPItem/USPItem')),
  Video: dynamic(() => import('@sitecore/components/content/Video/Video')),

  // EnergyRating
  RegistrationAccountCreate: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationFlow/RegistrationFlow'),
  ),
  RegistrationAccountReset: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationFlow/RegistrationFlow'),
  ),
  RegistrationFooter: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationFooter/RegistrationFooter'),
  ),
  RegistrationHeader: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationHeader/RegistrationHeader'),
  ),

  // Campaign components
  WeDoenHetNuCard: dynamic(() => import('@sitecore/components/content/Road2Zero/WeDoenHetNuCard/WeDoenHetNuCard')),
  WeDoenHetNuTextImage: dynamic(
    () => import('@sitecore/components/content/Road2Zero/WeDoenHetNuTextImage/WeDoenHetNuTextImage'),
  ),
  TextImageRebranded: dynamic(() => import('@sitecore/components/content/TextImageRebranded/TextImageRebranded')),

  // Forms
  ContactForm: dynamic(() => import('@sitecore/components/forms/ContactForm/ContactForm')),
  GenericForm: dynamic(() => import('@sitecore/components/forms/GenericForm/GenericForm')),
  EloquaForm: dynamic(() => import('@sitecore/components/forms/GenericForm/EloquaForm')),
  ServiceSignupForm: dynamic(() => import('@sitecore/components/forms/ServiceSignupForm/ServiceSignupForm')),
  HeatDamageForm: dynamic(() => import('@sitecore/components/forms/HeatDamageForm/HeatDamageForm')),
  RegisterToNewsletterForm: dynamic(
    () => import('@sitecore/components/forms/RegisterToNewsletterForm/RegisterToNewsletterForm'),
  ),
  EmailRequestWrapper: dynamic(() => import('@sitecore/components/forms/EmailRequestWrapper/EmailRequestWrapper')),

  GenericFormAddressField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormAddress/GenericFormAddressField'),
  ),
  GenericFormBeAddressField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormAddress/GenericFormBeAddressField'),
  ),
  GenericFormNameField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormNameField/GenericFormNameField'),
  ),
  GenericFormParagraph: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormParagraph/GenericFormParagraph'),
  ),
  GenericFormSalutationField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormSalutation/GenericFormSalutation'),
  ),
  GenericFormRadioGroup: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormRadioGroup/GenericFormRadioGroup'),
  ),
  GenericFormCheckboxField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormCheckboxField/GenericFormCheckboxField'),
  ),
  GenericFormCheckboxGroup: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormCheckboxGroup/GenericFormCheckboxGroup'),
  ),
  GenericFormDateField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormDateField/GenericFormDateField'),
  ),
  GenericFormEmailInputField: dynamic(
    () =>
      import('@sitecore/components/forms/GenericForm/Components/GenericFormEmailInputField/GenericFormEmailInputField'),
  ),
  GenericFormNumberInputField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormNumberInputField/GenericFormNumberInputField'
      ),
  ),
  GenericFormPhoneNumberInputField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormPhoneNumberInputField/GenericFormPhoneNumberInputField'
      ),
  ),
  GenericFormInputField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormInputField/GenericFormInputField'),
  ),
  GenericFormTextAreaInputField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormTextAreaInputField/GenericFormTextAreaInputField'
      ),
  ),
  GenericFormInputSelect: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormInputSelect/GenericFormInputSelect'),
  ),
  GenericFormDivider: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormDivider/GenericFormDivider'),
  ),
  GenericFormInputHiddenField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormInputHiddenField/GenericFormInputHiddenField'
      ),
  ),
  DynamicPricingGraph: dynamic(() => import('@sitecore/components/content/DynamicPricingGraph/DynamicPricingGraph')),
  SearchChatBot: dynamic(() => import('@apps/header/be/eneco/src/components/SearchChatBot')),

  // MyZone

  ProfileManagementLoginDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/loginDetails/ProfileManagementLoginDetails'),
  ),
  ProfileManagementLoginDetailsUpdate: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/loginDetails/ProfileManagementLoginDetailsUpdate'),
  ),
  ProfileManagementLoginDetailsConfirmEmail: dynamic(
    () =>
      import(
        '@apps/myzone/be/eneco/src/pages/profileManagement/loginDetails/ProfileManagementLoginDetailsConfirmEmail'
      ),
  ),
  ProfileManagementLoginDetailsLinkItsme: dynamic(
    () =>
      import('@apps/myzone/be/eneco/src/pages/profileManagement/loginDetails/ProfileManagementLoginDetailsLinkItsme'),
  ),
  ForgotEmail: dynamic(() => import('@apps/myzone/be/eneco/src/pages/login/ForgotEmail')),
  ForgotEmailConfirmed: dynamic(() => import('@apps/myzone/be/eneco/src/pages/login/ForgotEmailConfirmed')),
  ProfileMenuBe: dynamic(() => import('@apps/header/be/eneco/src/components/ProfileMenuBE')),
  Agreement: dynamic(() => import('@apps/myzone/be/eneco/src/pages/agreement/Agreement')),
  AccountInviteForm: dynamic(() => import('@apps/myzone/be/eneco/src/pages/accountinvite/AccountInviteForm')),
  EditChildAccountForm: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/accountOverview/EditChildAccountForm'),
  ),
  AccountLinkDialog: dynamic(() => import('@apps/myzone/be/eneco/src/components/AccountLinkDialog/AccountLinkDialog')),
  AccountLinkDialogNavLink: dynamic(
    () => import('@apps/myzone/be/eneco/src/components/AccountLinkDialog/AccountLinkDialogNavLink'),
  ),
  DashboardOverview: dynamic(() => import('@apps/myzone/be/eneco/src/pages/dashboard/Dashboard')),
  AccountOverview: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/accountOverview/AccountOverview'),
  ),
  EditCrmAccountDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/edit-account/EditCrmAccountDetails'),
  ),
  SideNav: dynamic(() => import('@sitecore/components/content/SideNav/SideNav')),
  LogoutLink: dynamic(() => import('@apps/header/be/eneco/src/components/LogoutNavLink')),
  ImpersonationLanding: dynamic(() => import('@apps/myzone/be/eneco/src/pages/impersonation/ImpersonationLanding')),
  ContactData: dynamic(() => import('@apps/myzone/be/eneco/src/pages/profileManagement/contactData/ContactData')),
  ContactPreferences: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/contact-preferences/ContactPreferences'),
  ),
  EditContactData: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/editContactData/EditContactData'),
  ),
  EditGezinsbond: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/contactData/editGezinsbond/EditGezinsbond'),
  ),
  CompanyData: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/contactData/editCompanyData/EditCompanyData'),
  ),
  ProspectCta: dynamic(() => import('@apps/myzone/be/eneco/src/components/ProspectCta/ProspectCta')),
  MyEnecoProduct: dynamic(() => import('@apps/myzone/be/eneco/src/pages/products/ProductOverview')),
  MyEnecoProductDetails: dynamic(() => import('@apps/myzone/be/eneco/src/pages/products/ProductDetail')),
  MyEnecoComfortBonus: dynamic(() => import('@apps/myzone/be/eneco/src/pages/products/ComfortBonus')),
  MyEnecoProductUpdateMeterRegime: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/products/meter-regime/EditMeterRegime'),
  ),
  MyEnecoInvoice: dynamic(() => import('@apps/myzone/be/eneco/src/pages/invoices/InvoiceOverview')),
  MyEnecoExportPaymentsAndInvoices: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/invoices/ExportPaymentsOverview'),
  ),
  MyEnecoInvoiceBillingDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/billing-details/BillingDetails'),
  ),
  MyEnecoUpdateInvoicePaymentMethod: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/billing-details/UpdateInvoicePaymentMethod'),
  ),
  MyEnecoInvoiceEditDueDate: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/billing-details/InvoiceEditDueDate'),
  ),
  MyEnecoEditInvoiceFrequency: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/billing-details/InvoiceEditFrequency'),
  ),
  QuickLinks: dynamic(() => import('@sitecore/components/eneco/QuickLinks/MyEnecoQuickLinksWrapper')),
  MyEnecoUpcomingYearlyInvoice: dynamic(
    () =>
      import(
        '@apps/myzone/be/eneco/src/pages/invoices/components/sidebar/UpcomingYearlyInvoice/UpcomingYearlyInvoiceWrapper'
      ),
  ),
  MoveOnboarding: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/onboarding/MoveOnboarding')),
  SelectMoveReason: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/selectReason/MoveSelectReason')),

  MoveEODUpload: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/eodUpload/MoveEodUploadStep')),
  MoveNewAddressStep: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/new-address/MoveNewAddress')),
  MoveOverviewStep: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/overview/MoveOverviewStep')),
  MoveInDateStep: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/moveInDateStep/MoveInDateStep')),
  MovingOutDateStep: dynamic(() => import('@apps/myzone/be/eneco/src/pages/move/moveOutDateStep/MoveOutDateStep')),
  MoveFileAddressDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/move/moveFileAddressDetails/MoveFileAddressDetails'),
  ),
  MoveBillingAddressStep: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/move/billingAddress/MoveBillingAddressStep'),
  ),
  MoveResidentChangeContactDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/move/residentChangeContactDetails/MoveResidentChangeContactDetails'),
  ),
  MoveFileEditAddress: dynamic(() => import('@apps/myzone/be/eneco/src/pages/moveFileEditAddress/MoveFileEditAddress')),
  MoveFileContactDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/moveFileContactDetails/MoveFileContactDetails'),
  ),
  MoveEditMeterDetails: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/move/editMeterDetails/MoveEditMeterDetailsNavigationWrapper'),
  ),
  MoveOutStatusIndicator: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/move/components/MoveOutStatusIndicator'),
  ),
  FlowStepIndicator: dynamic(() => import('@apps/myzone/be/eneco/src/components/FlowStepIndicator')),
  ManageAdvanceAmount: dynamic(() => import('@apps/myzone/be/eneco/src/pages/advancePayments/AdvancePaymentsOverview')),
  EditAdvanceAmount: dynamic(() => import('@apps/myzone/be/eneco/src/pages/advancePayments/edit/EditAdvancePayments')),
  AnalogueMeterCustomersCTA: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/advancePayments/components/EnergyMonitorCta'),
  ),
  RequestVacancy: dynamic(() => import('@apps/myzone/be/eneco/src/pages/advancePayments/RequestVacancy')),
  PaymentPlanOnboarding: dynamic(() => import('@apps/myzone/be/eneco/src/pages/paymentPlan/PaymentPlanOnboarding')),
  PaymentPlanAmountStep: dynamic(() => import('@apps/myzone/be/eneco/src/pages/paymentPlan/PaymentPlanAmountStep')),
  PaymentPlanTypeStep: dynamic(() => import('@apps/myzone/be/eneco/src/pages/paymentPlan/PaymentPlanTypeStep')),
  PaymentArrangementProposalPlanStep: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/paymentPlan/PaymentPlanProposalStep'),
  ),
  MandateInformation: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/profileManagement/billing-details/components/MandateInfoBucket'),
  ),
  ProductSwitch: dynamic(() => import('@apps/myzone/be/eneco/src/pages/products/productSwitch/ProductSwitch')),
  ConsumptionGraphicalOverview: dynamic(
    () => import('@apps/myzone/be/eneco/src/pages/consumptionGraphicalOverview/ConsumptionGraphicalOverview'),
  ),
  ContactUs: dynamic(() => import('@apps/myzone/be/eneco/src/pages/contactForm/ContactForm')),
  Rectification: dynamic(() => import('@apps/myzone/be/eneco/src/pages/rectification/Rectification')),
  PriceSimulation: dynamic(
    () =>
      import(
        '@apps/myzone/be/eneco/src/components/navigation/flow/pages/priceSimulation/PriceSimulationFlowNavigation'
      ),
  ),
  FlowStepIndicatorHeader: dynamic(
    () => import('@apps/myzone/be/eneco/src/components/navigation/flow/NavigationBarWithFlow'),
  ),
  Dashboard: dynamic(() => import('@apps/myzone/be/eneco/src/pages/dashboard/Dashboard')),
};
