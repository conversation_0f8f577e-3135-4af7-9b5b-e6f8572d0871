import dynamic from 'next/dynamic';

export default {
  // Navigation
  BreadCrumbs: dynamic(() => import('@sitecore/components/content/Breadcrumbs/Breadcrumbs')),
  FooterMenu: dynamic(() => import('@sitecore/components/content/Footer/FooterMenu')),
  Footer: dynamic(() => import('@sitecore/components/content/Footer/Footer')),
  LanguageSelector: dynamic(() => import('@sitecore/components/content/LanguageSelector/LanguageSelector')),
  Logo: dynamic(() => import('@sitecore/components/eneco/ImageLink/ImageLink')),
  MainNavigation: dynamic(() => import('@apps/header/shared/multilabel/src/Header')),
  MegaMenu: dynamic(() => import('@sitecore/components/content/MegaMenu/MegaMenu')),
  MegaMenuV2: dynamic(() => import('@sitecore/components/content/MegaMenuV2/MegaMenuV2')),
  MegaMenuDivision: dynamic(() => import('@sitecore/components/content/MegaMenuDivision/MegaMenuDivision')),
  MessageBar: dynamic(() => import('@sitecore/components/content/MessageBar/MessageBar')),
  NotificationsBar: dynamic(() => import('@sitecore/components/content/NotificationsBar/NotificationsBar')),
  SubFooterMenu: dynamic(() => import('@sitecore/components/content/Footer/SubFooterMenu')),
  SocialMediaMenu: dynamic(() => import('@sitecore/components/content/Footer/SocialMediaMenu')),
  TabNavigation: dynamic(() => import('@sitecore/components/content/TabNavigation/TabNavigation')),

  // Content components
  ContentCategories: dynamic(() => import('@sitecore/components/content/ContentCategories/ContentCategories')),
  ChatLinks: dynamic(() => import('@sitecore/components/content/ChatLinks/ChatLinks')),
  Accordion: dynamic(() => import('@sitecore/components/content/Accordion/Accordion')),
  Hero: dynamic(() => import('@sitecore/components/content/Hero/Hero')),
  NotificationBox: dynamic(() => import('@sitecore/components/content/NotificationBox/NotificationBox')),
  RichText: dynamic(() => import('@sitecore/components/content/RichText/RichText')),
  TopTasks: dynamic(() => import('@sitecore/components/content/TopTasks/TopTasks')),
  ContactCustomerService: dynamic(
    () => import('@sitecore/components/content/ContactCustomerService/ContactCustomerService'),
  ),
  IconList: dynamic(() => import('@sitecore/components/content/IconList/IconList')),
  Image: dynamic(() => import('@sitecore/components/content/Image/Image')),
  List: dynamic(() => import('@sitecore/components/content/List/List')),
  HeroCard: dynamic(() => import('@sitecore/components/content/HeroCard/HeroCard')),
  LinkCard: dynamic(() => import('@sitecore/components/content/LinkCard/LinkCard')),
  Price: dynamic(() => import('@sitecore/components/content/Price/Price')),
  StoreButtons: dynamic(() => import('@sitecore/components/content/StoreButtons/StoreButtons')),
  SimpleTable: dynamic(() => import('@sitecore/components/content/SimpleTable/SimpleTable')),
  TextStreamer: dynamic(() => import('@sitecore/components/content/TextStreamer/TextStreamer')),
  UsabillaFeedback: dynamic(() => import('@sitecore/components/system/UsabillaFeedback/UsabillaFeedback')),
  WeDoenHetNuTestimonialsCard: dynamic(
    () => import('@sitecore/components/content/WeDoenHetNuTestimonialsCard/WeDoenHetNuTestimonialsCard'),
  ),
  AddressFinder: dynamic(() => import('@sitecore/components/content/AddressFinder/AddressFinder')),
  SmeAddressFinder: dynamic(() => import('@sitecore/components/content/AddressFinder/SmeAddressFinder')),
  StepExplainer: dynamic(() => import('@sitecore/components/content/StepExplainerVertical/StepExplainerVertical')),
  CreditWarningBar: dynamic(() => import('@sitecore/components/content/CreditWarningBar/CreditWarningBar')),
  ShareOnSocials: dynamic(() => import('@sitecore/components/content/ShareOnSocials/ShareOnSocials')),
  ButtonLink: dynamic(() => import('@sitecore/components/content/ButtonLink/ButtonLink')),
  ButtonLineUp: dynamic(() => import('@sitecore/components/content/ButtonLineUp/ButtonLineUp')),

  // Apps
  DecarbonizationPotentialBreakdown: dynamic(
    () => import('@apps/decarbonizationPotentialBreakdown/nl/eneco/src/DecarbonizationPotentialBreakdown'),
  ),
  CustomerReview: dynamic(() => import('@apps/customerreview/shared/multilabel/src/CustomerReview')),
  HeatComparisonTool: dynamic(() => import('@apps/calculator/nl/eneco/src/HeatComparisonCalculator')),
  InspirationAdviceFilter: dynamic(() => import('@apps/inspirationadvicefilter/nl/eneco/src/InspirationAdviceFilter')),
  ShopWindow: dynamic(() => import('@apps/shopwindow/nl/eneco/src/ShopWindow')),

  // Other
  CookieWall: dynamic(() => import('@apps/cookiewall/nl/multilabel/src/CookieWall')),
  ErrorPage: dynamic(() => import('@sitecore/components/content/ErrorPage/ErrorPage')),
  GTM: dynamic(() => import('@apps/gtm/shared/multilabel/src')),
  MetaData: dynamic(() => import('@sitecore/components/system/MetaData/MetaData')),
  MetaTags: dynamic(() => import('@sitecore/components/system/MetaTags/MetaTags')),
  StructuredDataSchema: dynamic(() => import('@sitecore/components/system/StructuredDataSchema/StructuredDataSchema')),
  Chat: dynamic(() => import('@apps/chat/shared/multilabel/src/Chat')),

  // Wrapper components
  Article: dynamic(() => import('@sitecore/components/content/Article/Article')),
  Centered: dynamic(() => import('@sitecore/components/content/Centered/Centered')),
  Section: dynamic(() => import('@sitecore/components/content/Section/Section')),
  LineUp: dynamic(() => import('@sitecore/components/content/LineUp/LineUp')),
  NavigationView: dynamic(() => import('@sitecore/components/content/NavigationView/NavigationView')),
  SplitView: dynamic(() => import('@sitecore/components/content/SplitView/SplitView')),
  Carousel: dynamic(() => import('@sitecore/components/content/Carousel/Carousel')),
  ContentHubFilter: dynamic(() => import('@sitecore/components/content/ContentHubFilter/ContentHubFilter')),

  // Wrappable components
  ArticleCard: dynamic(() => import('@sitecore/components/content/ArticleCard/ArticleCard')),
  ArticleContentCard: dynamic(() => import('@sitecore/components/content/ArticleContentCard/ArticleContentCard')),
  ContentCard: dynamic(() => import('@sitecore/components/content/ContentCard/ContentCard')),
  ProductCard: dynamic(() => import('@sitecore/components/content/ProductCard/ProductCard')),
  StickyNav: dynamic(() => import('@sitecore/components/content/StickyNav/StickyNav')),
  Table: dynamic(() => import('@sitecore/components/content/Table/Table')),
  TitleTextCTA: dynamic(() => import('@sitecore/components/content/TitleTextCTA/TitleTextCTA')),
  PersonalizedTitleTextCTA: dynamic(
    () => import('@sitecore/components/content/PersonalizedTitleTextCTA/PersonalizedTitleTextCTA'),
  ),
  Checklist: dynamic(() => import('@sitecore/components/content/Checklist/Checklist')),
  USPItem: dynamic(() => import('@sitecore/components/content/USPItem/USPItem')),
  Video: dynamic(() => import('@sitecore/components/content/Video/Video')),

  // Appointments
  'appointments-flow-v2': dynamic(() => import('@apps/appointmentsV2/shared/multilabel/src/AppointmentsFlow')),

  // MeterReadings
  'meterreadings-flow': dynamic(() => import('@apps/meterreadings/nl/multilabel/src/MeterReadingsFlow')),
  // Decarbonization lead flow
  'decarbonization-lead-flow': dynamic(
    () => import('@apps/flows/nl/eneco/decarbonisationLeadFlow/src/DecarbonisationLeadFlow'),
  ),
  // AddressFinder Example
  ExampleAddressFinderForm: dynamic(
    () => import('@sitecore/components/content/ExampleAddressFinderForm/ExampleAddressFinderForm'),
  ),

  // EnergyRating
  RegistrationAccountCreate: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationFlow/RegistrationFlow'),
  ),
  RegistrationAccountReset: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationFlow/RegistrationFlow'),
  ),
  RegistrationFooter: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationFooter/RegistrationFooter'),
  ),
  RegistrationHeader: dynamic(
    () => import('@apps/accessmanagement/nl/multilabel/src/components/RegistrationHeader/RegistrationHeader'),
  ),

  // Heatpump shop window
  HeatPumpSpecTable: dynamic(() => import('@apps/shopwindow/nl/eneco/src/components/HeatPumpSpecTable')),
  PriceDetailsTable: dynamic(() => import('@apps/shopwindow/nl/eneco/src/components/PriceDetailsTable')),

  // Campaign components
  WeDoenHetNuCard: dynamic(() => import('@sitecore/components/content/Road2Zero/WeDoenHetNuCard/WeDoenHetNuCard')),
  WeDoenHetNuTextImage: dynamic(
    () => import('@sitecore/components/content/Road2Zero/WeDoenHetNuTextImage/WeDoenHetNuTextImage'),
  ),

  // Forms
  GenericForm: dynamic(() => import('@sitecore/components/forms/GenericForm/GenericForm')),
  RevolutionForm: dynamic(() => import('@sitecore/components/forms/GenericForm/RevolutionForm')),
  GenericFormAddressField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormAddress/GenericFormAddressField'),
  ),
  GenericFormNameField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormNameField/GenericFormNameField'),
  ),
  GenericFormParagraph: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormParagraph/GenericFormParagraph'),
  ),
  GenericFormSalutationField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormSalutation/GenericFormSalutation'),
  ),
  GenericFormRadioGroup: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormRadioGroup/GenericFormRadioGroup'),
  ),
  GenericFormCheckboxField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormCheckboxField/GenericFormCheckboxField'),
  ),
  GenericFormCheckboxWithValueField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormCheckboxWithValueField/GenericFormCheckboxWithValueField'
      ),
  ),
  GenericFormCheckboxGroup: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormCheckboxGroup/GenericFormCheckboxGroup'),
  ),
  GenericFormDateField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormDateField/GenericFormDateField'),
  ),
  GenericFormEmailInputField: dynamic(
    () =>
      import('@sitecore/components/forms/GenericForm/Components/GenericFormEmailInputField/GenericFormEmailInputField'),
  ),
  GenericFormNumberInputField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormNumberInputField/GenericFormNumberInputField'
      ),
  ),
  GenericFormPhoneNumberInputField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormPhoneNumberInputField/GenericFormPhoneNumberInputField'
      ),
  ),
  GenericFormTextAreaInputField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormTextAreaInputField/GenericFormTextAreaInputField'
      ),
  ),
  GenericFormInputSelect: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormInputSelect/GenericFormInputSelect'),
  ),
  GenericFormInputField: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormInputField/GenericFormInputField'),
  ),
  GenericFormInputHiddenField: dynamic(
    () =>
      import(
        '@sitecore/components/forms/GenericForm/Components/GenericFormInputHiddenField/GenericFormInputHiddenField'
      ),
  ),
  GenericFormDivider: dynamic(
    () => import('@sitecore/components/forms/GenericForm/Components/GenericFormDivider/GenericFormDivider'),
  ),
  ContactForm: dynamic(() => import('@sitecore/components/forms/ContactForm/ContactForm')),
  RegisterToNewsletterForm: dynamic(
    () => import('@sitecore/components/forms/RegisterToNewsletterForm/RegisterToNewsletterForm'),
  ),
  RevolutionRegisterToNewsletterForm: dynamic(
    () => import('@sitecore/components/forms/RegisterToNewsletterForm/RevolutionRegisterToNewsletterForm'),
  ),
  EmailRequestWrapper: dynamic(() => import('@sitecore/components/forms/EmailRequestWrapper/EmailRequestWrapper')),
  ContentCardRebranded: dynamic(() => import('@sitecore/components/content/ContentCardRebranded/ContentCardRebranded')),
  HeroRebranded: dynamic(() => import('@sitecore/components/content/HeroRebranded/HeroRebranded')),
  HeroCardRebranded: dynamic(() => import('@sitecore/components/content/HeroCardRebranded/HeroCardRebranded')),
  TextImageRebranded: dynamic(() => import('@sitecore/components/content/TextImageRebranded/TextImageRebranded')),
};
