# Agentportal frontend

The Agent Portal is a [NextJS](../../../../docs/nextjs.md) application that is
designed to provide insights to customer service agents. It contains different
features, some of which are shared with the selfservice environment. It is
designed to replace the previous Agent Portal built in Blazor by the Digital
Core team.

| Environment | URL                                          |
| ----------- | -------------------------------------------- |
| Dev         | https://agentportal-dev.dxp.enecogroup.com/  |
| Test        | https://agentportal-test.dxp.enecogroup.com/ |
| Acceptance  | https://agentportal-acc.dxp.enecogroup.com/  |
| Production  | https://agentportal.dxp.enecogroup.com/      |

## Running the project

`npx nx serve nl-internal-agentportal`

To run this project, make sure the environmental variables for the agentportal
are available. Use `npm run switch-env` to set the correct variables or use the listed variables in [`.env.example`](../../../../.env.example).
The project should run on port 3000.

### With mocks

The application works with DC mocks built for dxpweb endpoints. Make sure to
have `FE_DC_MOCKS=1` inside of your `.env` file. Also, `FE_DC_PREPENDREPLACE`
should be omitted. 

There are no Sitecore mocks available. When you used:
- `npm run switch-env` to set the mocked development environment, `Sitecore development` is used as the source for the simulated My Environment.
- [`.env.example`](../../../../.env.example) as the source for the mocked development environment, `Sitecore acceptance` is used as the source for the simulated My Environment.

If you want to test without Azure Active Directory
integration, temporarily hard-code `authenticationRequired={false}` inside the
[`_app` file](./pages/_app.tsx).

### Hosted PR's

Because of the way this project is set up,
[hosted PR's](../../../../docs/hosted-pr-containers.md) currently do not work
for the Agent Portal. If you want to test certain functionalities, consider
running a [local Docker build](../../../../docs/docker.md). Alternatively, you
can temporarily deploy your branch to the development environment by running the
[DXP_Web_CI-Release pipeline](https://dev.azure.com/enecomanagedcloud/BTO%20Digital%20Solutions%20%E2%80%93%20DXP/_build?definitionId=932).
To make sure the deployment is not overwritten, you can turn on approval for
`dxp-web-nl-internal-agentportal-development`.

## Shadowing dxpweb features

The Agent Portal is built to simulate pages from dxpweb, giving customer service
agent access to customer features. The Agent Portal has access to the same
DC endpoints as dxpweb, but with a different prefix (`/v1/agentportal`). The
environment variable `FE_DC_PREPENDREPLACE` is used to replace the `dxpweb`
prefix with the `agentportal` prefix. The result is that, when using features
built for　containers such as `nl-eneco-main` or `nl-eneco-selfservice`, the
Agent Portal will be able to render the same components, just with a different
data source.

Note that endpoints are implemented separately, so it could happen that
functionality is not available or there is a mismatch. If this is the case,
please report it to the Digital Core team. You can also inspect the
[Swagger or generated Hooks specific to the agentportal](#Endpoints) to debug
any issues.

### Customer context

Features inside the Agent Portal are available on
[dynamic routes](https://nextjs.org/docs/pages/building-your-application/routing/dynamic-routes),
which contain the label, customerId and accountId. A page is set up like this:

```tsx
// eslint-disable-next-line @nx/enforce-module-boundaries
import { NextPage } from 'next';

import { getConfig, queryParams } from '../../../../../util/customerContext';

const MyPage: NextPage<queryParams> = (queryParams: queryParams) => (
  <MyComponent />
);

MyPage.getInitialProps = ({ query }) => {
  return getConfig(query);
};

export default MyPage;
```

Make sure `getInitialProps` is implemented like in the example. This is required
to be able to derive customer information from the URL and pass it on to the
`CustomerWrapper`, which is done in the [\_app.tsx](./pages/_app.tsx) file. The
`CustomerWrapper` is used to pass the customer information to the `DCProvider`
and `SessionProvider`.

### Simulated my environment

It is possible to visit the my environment of a specific customer. The way this
works, is that we call a sitecore client similar to the one used in the
selfservice containers. Because we have near parity of the `dxpweb` endpoint
used in the selfservice environment and the `v1/agentportal` endpoint, most
components should work the same. As these are still separate implementations,
however, it can happen that there are some differences. Furthermore, we do not
have access to as much Sitecore customization features. This is a discrepancy
that is planned to be fixed in the migration trajectory to the Sitecore cloud.

## Endpoints

In addition to parity with the dxpweb endpoints, the Agent Portal has its own
endpoints related to agent-specific features. As there is no logical separation
between the two types of endpoints, all can be found in the dc directory inside
the agentportal project.

To update the available endpoints, download the
[OpenApi specification](https://portal-a.dxp.enecogroup.com/swaggers) and run
the following command:
`npm run generate:types:dc:agentportal -- [swagger-file].json`.

## Azure Active Directory.

Most pages are locked behind an Authentication barrier. This is implemented with
[MSAL Authentication](../../../../libs/internal/README.md#msal-authentication).
Visitors will be prompted to sign in with their Microsoft Single Sign-On. To
view it, you need to have access to the Azure Active Directory application.

The [MSALGuard](../../../../libs/internal/src/Authentication/MSALGuard.tsx)
component is used to restrict access to certain functionalities based on user
roles. Additionally, DC verifies user roles to ensure that unauthorized data
retrieval or action triggering is prevented.

| Environment | Name                                             | ClientID                             |
| ----------- | ------------------------------------------------ | ------------------------------------ |
| Acceptance  | appreg-digitalsolutions-dxp-agentportal-webapp-a | 42dd0128-c17a-4344-93f9-5fda5c84f210 |
| Production  | appreg-digitalsolutions-dxp-agentportal-webapp-p | cd6e8def-e0a3-4d82-baf2-da5920b62002 |

All developers should have access to acceptance as members of the
[sg-eneco-frontend-dxp group](https://portal.azure.com/#view/Microsoft_AAD_IAM/GroupDetailsMenuBlade/~/Overview/groupId/c9a39be9-2c1f-49e6-a808-a66181892a36).
If you are not, ask to be added. Developers do not have access to production,
contact someone from customer care to test production.

# TODO

- [ ] When the API is available, add a customer lookup functionality to the
      landing page.
- [ ] Move existing features from the old portal (Blazor app). Give priority to
      the happypower & boete terug features.
