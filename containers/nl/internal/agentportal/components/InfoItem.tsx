import { FC } from 'react';

import RichText from '@components/RichText/RichText';
import { Stack, Text, Popover, TextLink, Skeleton } from '@sparky';
import { InfoIcon } from '@sparky/icons';

type InfoItemProps = {
  description: string;
  value?: any;
  explanationTitle?: string;
  explanationText?: string;
};
export const InfoItem: FC<InfoItemProps> = ({ description, value, explanationTitle, explanationText }) => (
  <Stack direction="row" alignY="end" gap="1">
    <Text as="dt">
      {explanationTitle && explanationText ? (
        <>
          <Popover
            side="bottom"
            trigger={<TextLink rightIcon={<InfoIcon />}>{`${description} `}</TextLink>}
            title={explanationTitle}>
            <RichText html={explanationText} />
          </Popover>
          {':'}
        </>
      ) : (
        `${description}:`
      )}
    </Text>
    {value === undefined ? (
      <Skeleton width="50px" />
    ) : (
      <Text as="dd" weight="bold">
        {value === null ? 'Onbekend' : value}
      </Text>
    )}
  </Stack>
);
