import React from 'react';

import { render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';

import { getByTextOverMultipleElements } from '@jest-tools/helpers/dom';

import { getStatusTranslation, LoginAccountsCard } from './LoginAccountsCard';
import * as hooks from '../dc/hooks';
import MultipleAccountsResponse from '../dc/mocks/GetUserAccountsForCustomer/14021/14021';
import NoAccountResponse from '../dc/mocks/GetUserAccountsForCustomer/15848/15848';
import OneAccountResponse from '../dc/mocks/GetUserAccountsForCustomer/********/********';

jest.mock('../dc/hooks', () => ({
  useUserAccountsGetUserAccountsForCustomer: jest.fn(),
}));

jest.mock('@i18n', () => ({
  useFormatter: () => ({
    date: {
      long: (date: string) => `${date}`,
    },
  }),
}));

describe('LoginAccountsCard', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('shows a title when loading', () => {
    (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({ data: undefined });

    render(<LoginAccountsCard />);
    expect(screen.getByText('Login account (web/app)')).toBeVisible();
  });

  describe('when loaded with no accounts', () => {
    it('shows a title', () => {
      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({ data: NoAccountResponse.data });

      render(<LoginAccountsCard />);
      expect(screen.getByText('Login account (web/app)')).toBeVisible();
    });

    it('shows an info notification', async () => {
      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({ data: NoAccountResponse.data });

      render(<LoginAccountsCard />);
      expect(await screen.findByText('Geen login account gevonden.')).toBeVisible();
    });
  });

  describe('when loaded with one account', () => {
    it('shows a title', () => {
      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({ data: OneAccountResponse.data });

      render(<LoginAccountsCard />);
      expect(screen.getByText('Login account (web/app)')).toBeVisible();
    });

    it('renders login account details', () => {
      const endpointData = OneAccountResponse.data;
      const userAccount = endpointData!.users![0];

      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({
        data: endpointData,
      });

      render(<LoginAccountsCard />);

      expect(getByTextOverMultipleElements(`Login e-mailadres:${userAccount.username}`)).toBeVisible();
      expect(
        getByTextOverMultipleElements(`Login-gerelateerde communicatie e-mailadres :${userAccount.emailAddress}`),
      ).toBeVisible();
      expect(getByTextOverMultipleElements(`Status :${getStatusTranslation(userAccount.status!)}`)).toBeVisible();
      expect(getByTextOverMultipleElements(`Laatst status verandert op:${userAccount.statusChangeDate}`)).toBeVisible();
      expect(getByTextOverMultipleElements(`Aangemaakt op:${userAccount.createdOn}`)).toBeVisible();
      expect(getByTextOverMultipleElements(`Geactiveerd op:${userAccount.activationDate}`)).toBeVisible();
      expect(getByTextOverMultipleElements(`Laatst ingelogd op:${userAccount.lastLogin}`)).toBeVisible();
      expect(
        getByTextOverMultipleElements(`Laatst wachtwoord gewijzigd op:${userAccount.passwordChangeDate}`),
      ).toBeVisible();
    });
  });

  describe('when loaded with multiple accounts', () => {
    it('shows a title', () => {
      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({
        data: MultipleAccountsResponse.data,
      });

      render(<LoginAccountsCard />);
      expect(screen.getByText('Login accounts (web/app)')).toBeVisible();
    });

    it('shows a warning notification', () => {
      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({
        data: MultipleAccountsResponse.data,
      });

      render(<LoginAccountsCard />);
      expect(
        screen.getByText(
          'Meerdere login accounts gevonden. Iedere klant dient slechts één geregistreerd login account te hebben. Neem contact op met een Okta systeembeheerder om eventuele loginproblemen te verhelpen.',
        ),
      ).toBeVisible();
    });

    it('renders login account details for multiple accounts', () => {
      const endpointData = MultipleAccountsResponse.data;

      (hooks.useUserAccountsGetUserAccountsForCustomer as jest.Mock).mockReturnValue({
        data: endpointData,
      });

      render(<LoginAccountsCard />);

      endpointData!.users!.forEach(userAccount => {
        expect(getByTextOverMultipleElements(`Login e-mailadres:${userAccount.username}`)).toBeVisible();
        expect(
          getByTextOverMultipleElements(`Login-gerelateerde communicatie e-mailadres :${userAccount.emailAddress}`),
        ).toBeVisible();
        expect(getByTextOverMultipleElements(`Status :${getStatusTranslation(userAccount.status!)}`)).toBeVisible();
        expect(
          getByTextOverMultipleElements(`Laatst status verandert op:${userAccount.statusChangeDate}`),
        ).toBeVisible();
        expect(getByTextOverMultipleElements(`Aangemaakt op:${userAccount.createdOn}`)).toBeVisible();
        expect(getByTextOverMultipleElements(`Geactiveerd op:${userAccount.activationDate}`)).toBeVisible();
        expect(getByTextOverMultipleElements(`Laatst ingelogd op:${userAccount.lastLogin}`)).toBeVisible();
        expect(
          getByTextOverMultipleElements(`Laatst wachtwoord gewijzigd op:${userAccount.passwordChangeDate}`),
        ).toBeVisible();
      });
    });
  });
});
