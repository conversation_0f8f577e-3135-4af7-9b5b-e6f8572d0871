import { FC, Fragment } from 'react';

import { useFormatter } from '@i18n';
import { Card, Divider, Heading, NotificationBox, Skeleton, Stack } from '@sparky';

import { InfoItem } from './InfoItem';
import { useUserAccountsGetUserAccountsForCustomer } from '../dc/hooks';

const statusMap: Record<string, string> = {
  STAGED: 'In aanmaak',
  PROVISIONED: 'Aangemaakt',
  ACTIVE: 'Actief',
  LOCKED_OUT: 'Vergrendeld',
  PASSWORD_EXPIRED: 'Wachtwoord verlopen',
  RECOVERY: 'In wachtwoordherstel',
  SUSPENDED: 'Inactief',
  DEPROVISIONED: 'Gedeactiveerd',
};

export const getStatusTranslation = (status: string) => statusMap[status] || 'Onbekend';

export const LoginAccountsCard: FC = () => {
  const { date } = useFormatter();
  const { data } = useUserAccountsGetUserAccountsForCustomer();
  const userAccounts = data?.users;

  const formatDate = (unformattedDate?: string | null) =>
    unformattedDate ? date.long(unformattedDate) : unformattedDate;

  return (
    <Card>
      <Card.Content>
        <Stack gap="6">
          <Heading as="h2" size="M">
            Login account{userAccounts && userAccounts.length > 1 && 's'} (web/app)
          </Heading>
          {!userAccounts && <Skeleton height="58px" />}
          {userAccounts && userAccounts.length < 1 && (
            <NotificationBox text="Geen login account gevonden." variant="info" isAlert />
          )}
          {userAccounts && userAccounts.length > 1 && (
            <NotificationBox
              text="Meerdere login accounts gevonden. Iedere klant dient slechts één geregistreerd login account te hebben. Neem contact op met een Okta systeembeheerder om eventuele loginproblemen te verhelpen."
              variant="warning"
              isAlert
            />
          )}
          {userAccounts?.map((userAccount, i, arr) => (
            <Fragment key={userAccount.oktaId}>
              <Stack as="dl" gap="2">
                <InfoItem description="Login e-mailadres" value={userAccount.username} />
                <InfoItem
                  description="Login-gerelateerde communicatie e-mailadres"
                  value={userAccount.emailAddress}
                  explanationTitle="Uitleg over login-gerelateerde communicatie e-mailadres"
                  explanationText="Dit e-mailadres wordt gebruikt voor het versturen van service berichten tijdens het inloggen en resetten van je wachtwoord. Dit zijn e-mails met een inlogcode, reset link/code of account geblokkeerd melding."
                />
                <InfoItem
                  description="Status"
                  value={userAccount.status ? getStatusTranslation(userAccount.status) : userAccount.status}
                  explanationTitle="Uitleg over status"
                  explanationText="<ul><li>In aanmaak: account moet nog aangemaakt worden.</li><li>Aangemaakt: account is aangemaakt, maar nog niet geactiveerd. Activatie is mogelijk door een wachtwoord in te stellen.</li><li>Actief: account is actief en kan worden gebruikt om in te loggen.</li><li>Vergrendeld: account is vergrendeld na meerdere mislukte inlogpogingen.</li><li>Wachtwoord verlopen: wachtwoord is verlopen en moet worden gewijzigd.</li><li>In wachtwoordherstel: account is in het proces om het wachtwoord opnieuw in te stellen.</li><li>Inactief: account is gepauzeerd en kan niet worden gebruikt om in te loggen.</li><li>Gedeactiveerd: account is gedeactiveerd en kan niet meer worden gebruikt.</li></ul>"
                />
                <InfoItem description="Laatst status verandert op" value={formatDate(userAccount.statusChangeDate)} />
                <InfoItem description="Aangemaakt op" value={formatDate(userAccount.createdOn)} />
                <InfoItem description="Geactiveerd op" value={formatDate(userAccount.activationDate)} />
                <InfoItem description="Laatst ingelogd op" value={formatDate(userAccount.lastLogin)} />
                <InfoItem
                  description="Laatst wachtwoord gewijzigd op"
                  value={formatDate(userAccount.passwordChangeDate)}
                />
              </Stack>
              {i < arr.length - 1 && <Divider />}
            </Fragment>
          ))}
        </Stack>
      </Card.Content>
    </Card>
  );
};
