import { capitalizeFirstLetter } from '@common/string';
import useDC from '@dc/useDC';
import { useRouter } from '@dxp-next';
import { useCustomerProfileAccount } from '@hooks/profile';
import { useFormatter } from '@i18n';
import { useLinkComponent } from '@link';
import { Card, Heading, TextLink, Stack, InputSelect, NotificationBox, Box, PageGrid } from '@sparky';
import { ChevronRightIcon } from '@sparky/icons';

import { InfoItem } from './InfoItem';
import { LoginAccountsCard } from './LoginAccountsCard';
import { createCustomerPath } from '../util/createBasepath';

const convertBooleanToString = (bool?: boolean | null) => (bool === true ? 'ja' : bool === false ? 'nee' : null);

export const CustomerOverview = () => {
  const { date, address: addressFormatter } = useFormatter();

  const router = useRouter();
  const { accountId, customerId, label } = useDC();
  const { currentAccount, data, error } = useCustomerProfileAccount();

  const Link = useLinkComponent();

  const accounts = data?.accounts?.map(account => {
    const label =
      account.address && account.address.street && account.address.houseNumber
        ? addressFormatter.streetAddress(account.address)
        : `Account ${accountId}`;
    return { value: account.accountId.toString(), label };
  });

  const address = addressFormatter.streetAddress(currentAccount?.address);

  return (
    <PageGrid>
      <PageGrid.Item gridColumn="1/-1">
        <Stack gap="6">
          <Card>
            <Card.Content>
              <Stack gap="6">
                <Heading as="h2" size="M">
                  Klantinformatie
                </Heading>

                {error ? (
                  <NotificationBox
                    variant="error"
                    isAlert={true}
                    text="Er is een technische fout opgetreden bij het ophalen van de klantinformatie. Zie de console voor meer informatie."
                  />
                ) : null}
                <Stack gap="2">
                  <InfoItem description="Klantnummer" value={customerId} />
                  <InfoItem
                    description="Merk"
                    value={typeof label === 'string' ? capitalizeFirstLetter(label) : undefined}
                  />
                </Stack>
                <Stack gap="2">
                  <Heading as="h3" size="XS">
                    Algemene informatie
                  </Heading>
                  <InfoItem description="Naam" value={data?.contact?.name} />
                  <InfoItem
                    description="Communicatie e-mailadres"
                    value={data?.contact?.emailAddress}
                    explanationTitle="Uitleg over communicatie e-mailadres"
                    explanationText="Dit e-mailadres wordt gebruikt voor het versturen van product- en dienst-gerelateerde berichten, zoals nieuwsbrieven en jaarnota's. Login-gerelateerde berichten die verstuurd worden tijdens het inloggen en resetten van wachtwoorden, vallen hier niet onder."
                  />
                  <InfoItem description="Vast telefoonnummer" value={data?.contact?.phoneNumber} />
                  <InfoItem description="Mobiel telefoonnummer" value={data?.contact?.mobilePhoneNumber} />
                  <InfoItem
                    description="Type"
                    value={
                      data?.customerType ? (data.customerType === 'person' ? 'persoon' : 'organisatie') : undefined
                    }
                  />
                </Stack>
                <Stack gap="2">
                  <Heading as="h3" size="XS">
                    Accountinfo
                  </Heading>
                  <InfoItem description="Geselecteerd account" value={accountId} />
                  {currentAccount ? (
                    <>
                      <InfoItem description="Adres" value={address} />
                      <InfoItem description="Postcode" value={currentAccount.address?.postalCode} />
                      <InfoItem description="Plaats" value={currentAccount.address?.city} />
                      <br />
                      <InfoItem
                        description="Is account actief?"
                        value={convertBooleanToString(currentAccount.active)}
                      />
                      <InfoItem
                        description="Heeft dynamische tarieven"
                        value={convertBooleanToString(currentAccount.hasDynamicPricing)}
                      />
                      <InfoItem
                        description="Teruglevering"
                        value={convertBooleanToString(currentAccount.hasRedelivery)}
                      />
                      <InfoItem
                        description="Heeft onderhoudscontract"
                        value={convertBooleanToString(currentAccount.hasServiceContract)}
                      />
                      <br />
                      <InfoItem
                        description="Begin contract"
                        value={currentAccount.startDate ? date.long(currentAccount.startDate) : null}
                      />
                      <InfoItem
                        description="Eerstvolgende afschrijving"
                        value={currentAccount.nextChargeDate ? date.long(currentAccount.nextChargeDate) : null}
                      />
                    </>
                  ) : (
                    <NotificationBox
                      text="Geselecteerd account voor dit klantnummer niet gevonden"
                      variant="error"
                      isAlert={true}
                    />
                  )}
                </Stack>
                {accounts && accounts.length > 1 ? (
                  <Box backgroundColor="backgroundVarThree" padding="2">
                    <Box backgroundColor="backgroundPrimary" padding="4">
                      <InputSelect
                        label="Selecteer account"
                        name="accounts"
                        placeholder="Accounts"
                        value={currentAccount?.accountId}
                        onChange={e =>
                          router.push(
                            createCustomerPath({
                              label,
                              customerId: customerId?.toString() as string,
                              accountId: +e.target.value,
                            }),
                          )
                        }
                        options={accounts}
                      />
                    </Box>
                  </Box>
                ) : null}

                <Link href="/klant-opzoeken" linkType="internal">
                  <TextLink leftIcon={<ChevronRightIcon />} emphasis="high">
                    Andere klant opzoeken
                  </TextLink>
                </Link>
              </Stack>
            </Card.Content>
          </Card>
          <LoginAccountsCard />
        </Stack>
      </PageGrid.Item>
    </PageGrid>
  );
};
