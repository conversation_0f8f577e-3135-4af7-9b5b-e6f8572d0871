import { ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel } from '../../../types';

const response: ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel = {
  data: {
    users: [
      {
        oktaId: '1236',
        username: '<EMAIL>',
        emailAddress: '<EMAIL>',
        customerId: 14021,
        createdOn: '2022-01-01T00:00:00Z',
        lastLogin: '2025-05-06T10:30:00Z',
        activationDate: '2022-02-01T00:00:00Z',
        status: 'ACTIVE',
        statusChangeDate: '2025-05-06T00:00:00Z',
        lastUpdateDate: '2025-05-06T00:00:00Z',
        passwordChangeDate: '2025-05-06T00:00:00Z',
      },
      {
        oktaId: '1237',
        username: '<EMAIL>',
        emailAddress: '<EMAIL>',
        customerId: 14021,
        createdOn: '2021-01-01T00:00:00Z',
        lastLogin: '2024-05-06T10:30:00Z',
        activationDate: '2021-02-01T00:00:00Z',
        status: 'SUSPENDED',
        statusChangeDate: '2024-05-06T00:00:00Z',
        lastUpdateDate: '2024-05-06T00:00:00Z',
        passwordChangeDate: '2024-05-06T00:00:00Z',
      },
    ],
  },
};

export default response;
