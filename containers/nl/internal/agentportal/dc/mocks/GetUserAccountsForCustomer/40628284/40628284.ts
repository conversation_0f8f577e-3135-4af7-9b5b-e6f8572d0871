import { ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel } from '../../../types';

const response: ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel = {
  data: {
    users: [
      {
        oktaId: '1235',
        username: '<EMAIL>',
        emailAddress: '<EMAIL>',
        customerId: ********,
        createdOn: '2022-01-01T00:00:00Z',
        lastLogin: '2025-05-06T10:30:00Z',
        activationDate: '2022-02-01T00:00:00Z',
        status: 'ACTIVE',
        statusChangeDate: '2025-05-06T00:00:00Z',
        lastUpdateDate: '2025-05-06T00:00:00Z',
        passwordChangeDate: '2025-05-06T00:00:00Z',
      },
    ],
  },
};

export default response;
