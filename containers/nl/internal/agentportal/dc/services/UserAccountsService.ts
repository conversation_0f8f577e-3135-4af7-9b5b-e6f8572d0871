import { request } from '@dc/client';
import type { ApiRequestConfig } from '@dc/client/types';

import { DC_Repositories_Base_Enumerations_BusinessUnit } from '../types';
import { DC_Repositories_Base_Enumerations_Label } from '../types';
import { ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel } from '../types';

type GetUserAccountsForCustomer = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  customerId: number;
};
/**
 * GetUserAccountsForCustomer
 * Returns all user accounts for the given customer. Most likely this will return only one user account
 * @returns ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel Success
 */
export function getUserAccountsForCustomer(
  { businessUnit, label, customerId }: GetUserAccountsForCustomer,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataDC_AgentPortal_BusinessLogic_ResponseModels_UserAccounts_UserAccountsResponseModel> {
  return request(
    {
      method: 'GET',
      path: `/v1/agentportal/${businessUnit}/${label}/customers/${customerId}/useraccounts`,
      errors: { 400: 'Bad Request', 404: 'Not Found' },
    },
    requestConfig,
  );
}
