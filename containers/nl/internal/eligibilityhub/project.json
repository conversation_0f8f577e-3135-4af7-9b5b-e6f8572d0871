{"name": "nl-internal-eligibilityhub", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "containers/nl/internal/eligibilityhub/src", "projectType": "application", "tags": ["container:internal"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "swc", "outputPath": "dist/containers/nl/internal/eligibilityhub", "index": "containers/nl/internal/eligibilityhub/src/index.html", "baseHref": "/", "main": "containers/nl/internal/eligibilityhub/src/main.tsx", "polyfills": "", "tsConfig": "containers/nl/internal/eligibilityhub/tsconfig.json", "assets": [{"input": "libs/sparky/static", "glob": "(favicon.ico|mockServiceWorker.js)", "output": "."}, {"input": "libs/sparky/static/font", "glob": "etelka*.*", "output": "./font"}], "styles": [], "scripts": [], "webpackConfig": "containers/nl/internal/eligibilityhub/webpack.config.js", "babelUpwardRootMode": true}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": true, "namedChunks": true, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "options": {"buildTarget": "nl-internal-eligibilityhub:build", "port": 3000, "hmr": true}, "configurations": {"production": {"buildTarget": "nl-internal-eligibilityhub:build:production", "host": "0.0.0.0", "hmr": false, "watch": false, "liveReload": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["containers/nl/internal/eligibilityhub/**/*.{ts,tsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/containers/nl/internal/eligibilityhub"], "options": {"jestConfig": "containers/nl/internal/eligibilityhub/jest.config.ts"}, "configurations": {"ci": {"runInBand": true}}}, "tsc": {"executor": "./tools/executors/tsc:tsc", "options": {"tsConfig": ["tsconfig.json"]}}}}