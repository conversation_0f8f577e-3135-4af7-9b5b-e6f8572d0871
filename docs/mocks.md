# Mocks

API mocks are simulated endpoints that mimic the behavior of actual endpoints.
Sitecore and Digital Core are mocked by [MSW][1]. The Sitecore client is used
exclusively server-side, while the DC client is used in the browser only.

One exception to this are the Jest tests, where the Sitecore client isn't used
at all, and the DC client in Node.js only.

The mocks are all located in `/mocks`.

## Sitecore

For [Sitecore][2], we have a single handler (`/sitecore/api/layout/render/jss`)
which is currently the only Sitecore endpoint we use. This endpoint uses the
`item` query parameter to fetch content for specific pages.

The Sitecore mocks are at [/mocks/sitecore][3] and cover a wide range of
containers, labels, business units and components.

TODO: add more details and example(s)

## Digital Core

[Digital Core][4] has many endpoints and most are covered in our mocks by
handlers in MSW. See the current endpoints in `/mocks/dc` for examples and
`/mocks/dc/docs/README.md` for an overview of the many scenarios currently
covered by the dynamic mocks.

All available DC endpoints are available through generated [services][5],
[hooks][6] and [models][7] based on their [OpenAPI specification][8]. These can
be used as a basis to add and maintain the mocks, although it's recommended to
prevent surprises and [integrate with actual endpoints][9] and/or consult a
member of the DC team to receive actual real-world JSON responses.

Here's an example of a mock handler for a `GET` request:

```ts
import { http, HttpResponse } from 'msw';
import env from '@common/env';

const host = env('DC_HOST');

export default [
  rest.get(
    // See the service functions in `/libs/dc/services` to find the endpoint
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/info`,
    ({ params }) => {
      const { customerId } = params;
      if (customerId) {
        // Note that DC usually responds with the `data` property:
        return HttpResponse.json({ data: { id: 1, name: 'E. Neco' } });
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    },
  ),
];
```

Make sure to add this exported array to the [existing MSW handlers][10].

Consult the [MSW docs about request handlers][11] for more details.

Like in the example often the `customerId` is sent with the request and used to
return various scenarios. E.g. different customers with no or many outstanding
invoices in order to develop and cover the divergent UI variances and scenarios.
Find `/mocks/dc/docs/README.md` for an overview of the many scenarios currently
covered by the dynamic mocks.

### Troubleshooting
If you run into issues with the mocks, check the following in the DevTools of your browser:
- Ensure that the MSW service worker is registered and active in the 'Application' tab.
- Ensure that the option 'Bypass for network' is not enabled in the 'Application' tab.
- Check the console of the browser for warnings mentioning there is a missing handler for an endpoint.

[1]: https://mswjs.io
[2]: ./sitecore.md
[3]: ../mocks/sitecore
[4]: ./digital-core.md
[5]: ../libs/dc/src/services
[6]: ../libs/dc/src/hooks
[7]: ../types/dc
[8]: https://portal-a.dxp.enecogroup.com/swaggers
[9]: ./integrated-development.md
[10]: ../mocks/dc/handlers.ts
[11]: https://mswjs.io/docs/basics/request-handler
