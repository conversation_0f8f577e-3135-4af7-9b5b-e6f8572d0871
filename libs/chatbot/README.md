# 📚 Review Pulse Chatbot Widget Documentation

Complete documentation for the React-based embeddable chatbot widget with Bot Framework integration.

## 🎯 Overview

A fully encapsulated chatbot widget that can be embedded in any website with:

- **Shadow DOM isolation** - Complete style encapsulation
- **Build-time Brand Themes** - Eneco (red) and Oxxio (blue) with light/dark variants
- **DirectLine WebSocket Streaming** - Real-time Bot Framework communication
- **Suggested Actions** - Interactive buttons from Bot Framework activities
- **Lazy Connection** - DirectLine only connects when user opens chat
- **Session persistence** - 24h localStorage with WebSocket state preservation
- **Environment Configuration** - Build-time brand and proxy URL configuration

## 📖 Documentation Structure

### 🛠️ Development

- **[Development Guide](development/development-guide.md)** - Complete technical development guide
- **[Architecture](development/architecture.md)** - System architecture and design patterns
- **[API Reference](development/api-reference.md)** - Complete API documentation and usage examples

### 🤖 DirectLine Proxy Integration

- **[Proxy Setup Guide](../PROXY-SETUP.md)** - DirectLine proxy server configuration
- **[Bot Framework Server](../directline-proxy-server/)** - Complete DirectLine proxy server implementation
- **[Test Pages](../test.html)** - Bot Framework and Copilot Studio integration testing

## 🚀 Quick Start

### Basic Widget

```bash
npm install
npm run build:eneco  # or npm run build:oxxio
```

```html
<!-- Just include the script - no HTML div needed! -->
<script src="dist/chatbot-widget.iife.js"></script>
<script>
  ChatbotWidget.push({
    action: 'init',
    args: {
      theme: 'light', // light or dark variant
      context: { variables: { name: 'User' } },
      visibility: 'minimized',
    },
  });
</script>
```

### Bot Framework Testing

```bash
# Terminal 1: Start proxy server
cd directline-proxy-server && npm install && npm start

# Terminal 2: Build widget
npm run build

# Browser: Open test.html with Bot Framework configuration
```

## 📊 Current Features

- ✅ **Shadow DOM isolation** - Complete style encapsulation
- ✅ **Build-time Brand Themes** - Eneco (red) and Oxxio (blue) with light/dark variants
- ✅ **DirectLine WebSocket Streaming** - Real-time Bot Framework communication via proxy
- ✅ **Suggested Actions** - Interactive buttons from Bot Framework activities with click handlers
- ✅ **Welcome Messages** - Smart Bot Framework activity handling for new vs existing sessions
- ✅ **Lazy Connection** - DirectLine only connects when user opens chat or manually triggered
- ✅ **Widget Persistence** - Hide/show without unmounting preserves WebSocket
- ✅ **Session persistence** - 24h localStorage with conversation state
- ✅ **Layout flexibility** - Window, inline, visibility controls
- ✅ **Modern UI** - Brand-specific Avatar system with Eva bot avatar and Etelka fonts
- ✅ **Message Timestamps** - HH:MM format above each message
- ✅ **Component Architecture** - Individual component folders with CSS files
- ✅ **Bot Framework Integration** - Microsoft DirectLine API support via proxy server
- ✅ **Environment Configuration** - Build-time proxy URLs and debug mode
- ✅ **Type Safety** - Centralized theme types in src/types/theme.ts
- ✅ **Clean API** - Single ChatArgs type for initialization
- ✅ **Auto Container** - Creates its own div element automatically

## 🎨 Theme System

**Build-time Brand Selection:**

- **Eneco Brand**: Red theme (`npm run build:eneco`)
  - `light` - Eneco red with light background
  - `dark` - Eneco red with dark background
- **Oxxio Brand**: Blue theme (`npm run build:oxxio`)
  - `light` - Oxxio blue with light background
  - `dark` - Oxxio blue with dark background

**Usage:**

```javascript
ChatbotWidget.push({
  action: 'init',
  args: {
    theme: 'light', // light or dark variant
    // proxyConfig auto-configured from VITE_PROXY_URL
  },
});
```

## 📁 Project Structure

```
src/
├── components/          # React UI components (individual folders with CSS)
├── contexts/           # React contexts (ChatBotProvider for Bot Framework)
├── hooks/              # Custom hooks (useMessageHandler for DirectLine streaming)
├── services/           # Business logic (sessionManager with Bot Framework persistence)
├── theme/              # Design system (config.ts, shadowDomStyles.ts, components.css)
├── types/              # TypeScript type definitions (including theme.ts)
└── main.tsx            # Entry point and Shadow DOM setup

public/
├── eneco/              # Eneco brand assets
│   ├── fonts/          # Etelka font family
│   └── eva.png         # Brand-specific bot avatar
└── oxxio/              # Oxxio brand assets
    ├── fonts/          # Etelka font family
    └── eva.png         # Brand-specific bot avatar

docs/
├── development/        # Technical documentation
└── README.md           # Main documentation hub

directline-proxy-server/ # Complete DirectLine proxy server with auth
test.html              # Comprehensive testing page with Bot Framework examples
```

## 🔧 Development Commands

```bash
npm run dev           # Development server (http://localhost:5173)
npm run build         # Build Eneco version (default)
npm run build:eneco   # Build Eneco brand (red theme)
npm run build:oxxio   # Build Oxxio brand (blue theme)
```

## 📝 Integration Examples

See the documentation files for detailed examples of:

- Basic widget integration
- Bot Framework DirectLine integration
- Theme customization
- Session management
- Error handling

---

**For detailed technical information, see the individual documentation files in the development/ and bot-framework/ folders.**
