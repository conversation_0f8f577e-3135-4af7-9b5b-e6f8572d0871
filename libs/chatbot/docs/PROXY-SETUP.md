# 🔒 Secure DirectLine Proxy Setup Guide

This guide walks you through setting up the secure proxy server for Bot Framework DirectLine integration.

## 📋 Overview

The proxy server architecture provides:
- **Security**: DirectLine secrets never exposed to client-side code
- **Token Management**: Automatic token generation, caching, and refresh
- **Rate Limiting**: Built-in protection against abuse
- **Monitoring**: Comprehensive logging and statistics
- **Error Handling**: Graceful error handling and recovery

## 🏗️ Architecture

```
Client Widget → Proxy Server → Bot Framework DirectLine API
           ↑                 ↑
    Token-based         Secret-based
    Authentication      Authentication
```

## 🚀 Quick Start

### 1. Start the Proxy Server

```bash
# Navigate to proxy server directory
cd directline-proxy-server

# Install dependencies
npm install

# Configure environment variables
cp .env.example .env
# Edit .env with your DirectLine secret

# Start development server
npm run dev
```

The proxy server will be available at `http://localhost:3001`

### 2. Configure DirectLine Secret

Edit `directline-proxy-server/.env`:

```bash
# Replace with your actual Bot Framework DirectLine secret
DIRECTLINE_SECRET=your_actual_directline_secret_here

# Other configuration
PORT=3001
NODE_ENV=development
JWT_SECRET=your_jwt_secret_for_production
```

### 3. Test the Proxy Server

```bash
# Test health endpoint
curl http://localhost:3001/health

# Test token generation (requires valid DirectLine secret)
curl -X POST http://localhost:3001/api/auth/token \
  -H "Content-Type: application/json" \
  -d '{"userId": "test-user"}'
```

### 4. Use Proxy Integration Test Page

Open `test-proxy-integration.html` in your browser to test the complete integration.

## 🔧 Integration with Bot Framework

### Option 1: Use with Real Bot Framework Bot

1. **Set up Bot Framework bot** in Azure
2. **Configure DirectLine channel** and get the secret
3. **Update proxy server `.env`** with the real secret
4. **Test integration** using the test pages

### Testing the Integration

```bash
# Terminal 1: Start proxy server
cd directline-proxy-server && npm start

# Terminal 2: Build widget
npm run build
```

Then configure the proxy server to point to the mock server (modify DirectLine base URL in the service).

## 📁 Project Structure

```
directline-proxy-server/
├── src/
│   ├── controllers/
│   │   ├── authController.js      # Token management
│   │   └── conversationController.js  # Message handling
│   ├── middleware/
│   │   ├── auth.js               # Authentication
│   │   └── security.js           # Security & rate limiting
│   ├── services/
│   │   ├── directLineService.js  # DirectLine API client
│   │   └── tokenService.js       # Token caching
│   ├── utils/
│   │   └── logger.js             # Logging configuration
│   └── app.js                    # Express app setup
├── .env                          # Environment variables
├── package.json                  # Dependencies
└── README.md                     # Detailed documentation
```

## 🔒 Security Features

### Token-Based Authentication
- DirectLine secrets stay on server
- Time-limited tokens issued to clients
- Automatic token refresh before expiration
- Token caching for performance

### Request Security
- Rate limiting per IP address
- Security headers via Helmet.js
- CORS protection with configurable origins
- Input validation and sanitization

### Monitoring
- Comprehensive request logging
- Error logging with stack traces
- Health check endpoints
- Token and conversation statistics

## 📊 API Endpoints

### Health Check
```http
GET /health
```

### Authentication
```http
POST /api/auth/token          # Generate token
POST /api/auth/refresh        # Refresh token
DELETE /api/auth/token        # Revoke token
GET /api/auth/stats          # Token statistics
```

### Conversations
```http
POST /api/conversations                      # Create conversation
POST /api/conversations/:id/activities       # Send message
GET /api/conversations/:id/activities        # Get messages
GET /api/conversations/:id                   # Get conversation info
DELETE /api/conversations/:id                # Delete conversation
GET /api/conversations/stats                 # Conversation statistics
```

## 🧪 Testing

### 1. Proxy Server Tests

```bash
# Health check
curl http://localhost:3001/health

# Token generation (requires valid DirectLine secret)
curl -X POST http://localhost:3001/api/auth/token \
  -H "Content-Type: application/json" \
  -d '{"userId": "test-user", "sessionId": "test-session"}'

# Statistics
curl http://localhost:3001/api/auth/stats
curl http://localhost:3001/api/conversations/stats
```

### 2. Widget Integration Tests

Use the provided test pages:

- `test-proxy-integration.html` - Proxy integration testing
- `test-bot-framework.html` - Original DirectLine testing (for comparison)
- `test.html` - Standard widget testing

## 🚀 Production Deployment

### Environment Configuration

```bash
# Production environment variables
NODE_ENV=production
DIRECTLINE_SECRET=your_production_directline_secret
JWT_SECRET=strong_random_jwt_secret
PORT=3001

# Rate limiting (adjust as needed)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
```

### Security Considerations

1. **Use HTTPS** in production
2. **Configure proper CORS origins** (update `src/app.js`)
3. **Set strong JWT secrets**
4. **Configure firewall rules**
5. **Regular security updates**
6. **Monitor logs for suspicious activity**

### Scaling

- **Use Redis** for token/conversation caching in multi-instance deployments
- **Configure load balancer** health checks to use `/health` endpoint
- **Use reverse proxy** (nginx) for SSL termination
- **Consider rate limiting** at load balancer level

## 🔧 Client Integration

### Update Widget Configuration

The widget can be configured to use the proxy server:

```typescript
// Example: Proxy configuration in widget initialization
const proxyConfig: ProxyDirectLineConfig = {
  proxyUrl: 'https://your-proxy-server.com',
  apiKey: 'optional-api-key',
  debug: false
};

// Initialize widget with proxy
ChatbotWidget.push({
  action: 'init',
  args: {
    namespace: 'blue-namespace',
    context: { variables: { name: 'User' } },
    proxyConfig: proxyConfig
  }
});
```

### Migration Strategy

1. **Deploy proxy server** alongside existing setup
2. **Test proxy integration** with development environment
3. **Update widget configuration** to use proxy
4. **Monitor performance** and error rates
5. **Gradually migrate** production traffic
6. **Remove direct DirectLine integration** once stable

## 📈 Monitoring & Maintenance

### Health Monitoring

```bash
# Check server health
curl https://your-proxy-server.com/health

# Check statistics
curl https://your-proxy-server.com/api/auth/stats
curl https://your-proxy-server.com/api/conversations/stats
```

### Log Monitoring

Monitor logs for:
- Rate limit violations
- Authentication failures
- DirectLine API errors
- Unusual traffic patterns

### Maintenance Tasks

- **Regular token cleanup** (automatic)
- **Monitor token cache size**
- **Review error logs**
- **Update dependencies**
- **Rotate JWT secrets** periodically

## 🤝 Contributing

To extend or modify the proxy server:

1. **Add new endpoints** in controllers
2. **Update middleware** for additional security
3. **Extend logging** for new features
4. **Add tests** for new functionality
5. **Update documentation**

## 📚 Additional Resources

- [Bot Framework Documentation](https://docs.microsoft.com/en-us/azure/bot-service/)
- [DirectLine API v3.0](https://docs.microsoft.com/en-us/azure/bot-service/rest-api/bot-framework-rest-direct-line-3-0-concepts)
- [Express.js Security Best Practices](https://expressjs.com/en/advanced/best-practice-security.html)
- [Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)

---

## ✅ Implementation Complete!

The secure DirectLine proxy server is now implemented with:

- ✅ **Secure token management**
- ✅ **Complete DirectLine API proxy**
- ✅ **Rate limiting and security**
- ✅ **Comprehensive logging**
- ✅ **Health monitoring**
- ✅ **Production-ready architecture**
- ✅ **Integration test pages**
- ✅ **Documentation and setup guides**

The proxy server follows security best practices and provides a robust foundation for secure Bot Framework integration!