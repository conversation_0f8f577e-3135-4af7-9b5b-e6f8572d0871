# 🏗️ System Architecture

## Overview

Review Pulse Chatbot Widget is a React-based embeddable widget with complete style isolation and Bot Framework integration support.

## Core Architecture

```
Floating <PERSON><PERSON> (Document Body)
└── Click Handler
    ↓
Shadow DOM Container
├── Theme Context (Build-time Brand Themes: Eneco Red, Oxxio Blue)
├── ChatBotProvider (Bot Framework DirectLine WebSocket streaming)
├── Session Manager (localStorage persistence with Bot Framework data)
├── useMessageHandler Hook (unified DirectLine message processing)
├── DirectLine Proxy Service (secure token management)
└── UI Components (Individual folders with CSS)
    ├── Avatar Component (Brand-specific Eva image + user icon)
    ├── MessageTimestamp (HH:MM format)
    ├── SuggestedActions (Bot Framework interactive buttons)
    ├── Message Interface (with avatars and Bot Framework activities)
    └── Build-time Brand Theme System (Eneco/Oxxio with light/dark variants)
```

## Technology Stack

- **React 18** - UI framework with hooks and context
- **TypeScript** - Type safety and better DX
- **Vite** - Build tool and dev server
- **Shadow DOM** - Complete style isolation from host pages

## Design Patterns

- **Provider Pattern** - Context-based state management (ChatBotProvider for Bot Framework)
- **Service Layer** - Business logic separation with DirectLine proxy integration
- **Unified Messaging** - Shared message handling via `useMessageHandler` hook with WebSocket streaming
- **Suggested Actions** - Interactive Bot Framework activity buttons with click handlers
- **Welcome Message Handling** - Smart Bot Framework activity processing for new vs existing sessions
- **Build-time Theming** - Brand selection via environment variables (VITE_BRAND)
- **Component Architecture** - Individual component folders with CSS files
- **Shadow DOM Isolation** - Prevents CSS conflicts
- **Session Persistence** - localStorage-based state with Bot Framework integration data
- **Lazy Connection** - DirectLine only connects when user opens chat or manually triggered
- **Type Safety** - Comprehensive TypeScript integration with DirectLine activity types

## Project Structure

```
src/
├── main.tsx                     # Entry point, Shadow DOM setup
├── Chatbot.tsx                  # Main chat interface wrapper
├── api/                         # API communication layer
│   └── chatbot-api.ts          # External API for widget communication
├── components/                  # React UI components (individual folders)
│   ├── Avatar/                 # User and bot avatar component
│   │   ├── Avatar.tsx
│   │   ├── Avatar.css
│   │   └── index.ts
│   ├── ChatbotButton/          # Floating trigger button
│   │   ├── ChatbotButton.tsx
│   │   ├── ChatbotButton.css
│   │   └── index.ts
│   ├── ChatbotWidget/          # Main widget container
│   │   ├── ChatbotWidget.tsx
│   │   ├── ChatbotWidget.css
│   │   └── index.ts
│   ├── FormInput/              # Message input with send button
│   ├── IconButton/             # Reusable icon button component
│   ├── Link/                   # Link component
│   ├── MessageComponent/       # Memoized message renderer
│   ├── MessageTimestamp/       # Timestamp display component
│   ├── SuggestedActions/       # Bot Framework suggested actions buttons
│   ├── TypingIndicator/        # Typing indicator component
│   └── index.ts                # Component exports
├── contexts/                    # React contexts
│   ├── ChatBotProvider.tsx     # Bot Framework DirectLine provider with WebSocket streaming
│   ├── ThemeContext.tsx        # Theme management
│   └── chatbotContextDefinition.ts # Context type definitions
├── hooks/                       # Custom React hooks
│   ├── useChatbot.ts           # Hook for accessing chatbot state
│   ├── useMessageHandler.ts    # Unified message handling with DirectLine WebSocket streaming
│   └── index.ts                # Hook exports
├── constants/
│   └── index.ts                # Constants exports
├── services/                    # Business logic services
│   ├── sessionManager.ts       # Session persistence with Bot Framework data and 24h expiry
│   └── index.ts                # Service exports
├── theme/                       # Design system
│   ├── config.ts               # Theme definitions & build-time config
│   ├── shadowDomStyles.ts      # CSS generation for Shadow DOM
│   ├── components.css          # Component-specific styles
│   └── index.ts                # Theme exports
└── types/                       # TypeScript type definitions
    ├── api.ts                  # API-related types with window declarations
    ├── chatArgs.ts             # ChatArgs initialization types with proxy configuration
    ├── message.ts              # Message and SuggestedActions types
    ├── session.ts              # Session types with Bot Framework integration
    ├── theme.ts                # Theme-related types (NEW)
    ├── directLineActivity.ts   # DirectLine Bot Framework activity types
    ├── ui.ts                   # UI-related types
    ├── user.ts                 # User types
    └── index.ts                # Type exports
```

## Key Components

### 1. Main Entry (`main.tsx`)
- Creates Shadow DOM container for chatbot interface
- Creates floating button outside Shadow DOM for proper positioning
- Injects theme-aware CSS with avatar and timestamp styles
- Handles widget initialization and button/interface coordination

### 2. Message Handling System
- **useMessageHandler Hook**: Unified message processing with DirectLine WebSocket streaming
- **ChatBotProvider**: Bot Framework DirectLine provider with connection management
- **DirectLine Proxy Integration**: Secure communication with Bot Framework via proxy server

### 3. Session Management
- Persistent user sessions (24h expiry)
- Bot Framework integration data (DirectLine tokens, conversation IDs, proxy URLs)
- Message history storage with Bot Framework activities via localStorage
- Welcome message handling (different behavior for new vs existing sessions)
- Cross-session continuity with DirectLine connection state preservation

### 4. Brand Theme System
- Build-time brand selection (Eneco red, Oxxio blue)
- Light/dark variants for each brand
- Design token-based styling with CSS custom properties
- Brand-specific assets (fonts, avatars) from `/{brand}/` folders
- Environment-based configuration (VITE_BRAND, VITE_PROXY_URL)

## Integration Modes

### DirectLine Proxy Mode (Only Mode)
- Widget communicates exclusively via DirectLine proxy to Bot Framework
- WebSocket streaming for real-time message delivery (not polling)
- Suggested Actions support - interactive buttons from Bot Framework activities
- Welcome Message Intelligence - smart handling for new vs existing sessions
- Session persistence for DirectLine tokens, conversation IDs, and proxy configuration
- Lazy Connection - DirectLine only connects when user opens chat or manually triggered
- Build-time brand theming with light/dark variants
- Environment-based proxy configuration (VITE_PROXY_URL)
- Connection state management with auto-recovery
- Bot Framework activity processing (messages, typing indicators, suggested actions)
- Error recovery and fallback handling

## Build Output

- **Development**: ES modules with hot reloading
- **Production**: Single IIFE bundle (~210kB / 66kB gzipped)
- **Compatibility**: Works with any static file server
- **Integration**: Single script tag inclusion

## Security & Isolation

- **Shadow DOM**: Complete CSS and JS isolation
- **No Global Pollution**: All code scoped within Shadow DOM
- **Safe Integration**: Cannot conflict with host page styles or scripts
- **Session Security**: localStorage-based with expiration