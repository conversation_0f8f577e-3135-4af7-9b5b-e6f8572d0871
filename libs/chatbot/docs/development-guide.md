# Chatbot Widget Development Guide

## 📋 Project Overview

A fully encapsulated React chatbot widget that can be embedded in any website. Features include Shadow DOM isolation, theme system, personalization, and session management.

## 🏗️ Architecture

### Core Technologies
- **React 18** - UI framework with hooks and context
- **TypeScript** - Type safety and better DX
- **Vite** - Build tool and dev server
- **Shadow DOM** - Complete style isolation from host pages

### Design Patterns
- **Provider Pattern** - Context-based state management
- **Service Layer** - Business logic separation
- **WebSocket Streaming** - Real-time DirectLine communication via `useMessageHandler` hook
- **Build-time Theming** - Brand selection via environment variables
- **Component Architecture** - Individual folders with CSS files
- **Shadow DOM Isolation** - Prevents CSS conflicts
- **Session Persistence** - localStorage-based state with 24h expiry
- **Lazy Connection** - DirectLine only connects when needed
- **Type Safety** - Comprehensive TypeScript integration with centralized theme types

## 📁 Project Structure

**CURRENT ORGANIZED STRUCTURE** - Clean separation with individual component folders:

```
src/
├── main.tsx                     # Entry point, Shadow DOM setup, CSS injection, button creation
├── Chatbot.tsx                  # Main chat interface wrapper
├── api/                         # API communication layer
│   ├── chatbot-api.ts          # External API for widget communication
│   └── index.ts                # API exports
├── components/                  # React UI components (individual folders)
│   ├── Avatar/                 # User and bot avatar component (Brand-specific Eva image)
│   │   ├── Avatar.tsx
│   │   ├── Avatar.css
│   │   └── index.ts
│   ├── ChatbotButton/          # Floating trigger button
│   │   ├── ChatbotButton.tsx
│   │   ├── ChatbotButton.css
│   │   └── index.ts
│   ├── ChatbotWidget/          # Main widget container
│   │   ├── ChatbotWidget.tsx
│   │   ├── ChatbotWidget.css
│   │   └── index.ts
│   ├── MessageTimestamp/       # Timestamp display component
│   │   ├── MessageTimestamp.tsx
│   │   ├── MessageTimestamp.css
│   │   └── index.ts
│   └── index.ts                # Component exports
├── constants/                   # Application constants
│   ├── responses.ts            # Bot response templates
│   └── index.ts                # Constant exports
├── contexts/                    # React contexts
│   ├── ChatbotContext.tsx      # Message state management
│   ├── ThemeContext.tsx        # Theme state management
│   ├── chatbotContextDefinition.ts # Context type definitions
│   └── index.ts                # Context exports
├── hooks/                       # Custom React hooks
│   ├── useChatbot.ts          # Hook for accessing chatbot state
│   ├── useMessageHandler.ts   # WebSocket DirectLine streaming logic
│   └── index.ts                # Hook exports
├── services/                    # Business logic services
│   ├── sessionManager.ts       # Session persistence with 24h expiry
│   └── index.ts                # Service exports
├── utils/                       # Utility functions
│   ├── dom.ts                  # DOM manipulation utilities (scrollToBottom, getOverlayPositionStyle)
│   ├── session.ts              # Session utilities (checkForExistingSession)
│   ├── theme.ts                # Theme utilities (getThemeFromNamespace)
│   ├── time.ts                 # Time formatting utilities (formatTime)
│   ├── ui.ts                   # UI utilities (createFloatingButton)
│   └── index.ts                # Utility exports
├── theme/                       # Design system
│   ├── config.ts               # Theme definitions and build-time configuration
│   ├── shadowDomStyles.ts      # CSS generation for Shadow DOM
│   ├── components.css          # Component-specific styles
│   └── index.ts                # Theme exports
└── types/                       # TypeScript type definitions
    ├── api.ts                  # API-related types with window declarations
    ├── chatArgs.ts             # ChatArgs initialization types
    ├── message.ts              # Message and SuggestedActions types
    ├── session.ts              # Session types
    ├── theme.ts                # Theme-related types (NEW)
    ├── directLineActivity.ts   # DirectLine Bot Framework types
    ├── ui.ts                   # UI-related types
    ├── user.ts                 # User types
    └── index.ts                # Type exports

public/
├── eneco/                      # Eneco brand assets
│   ├── fonts/                 # Etelka font family (Light, Medium, Bold, Black)
│   └── eva.png                # Brand-specific bot avatar
└── oxxio/                      # Oxxio brand assets
    ├── fonts/                 # Etelka font family (Light, Medium, Bold, Black)
    └── eva.png                # Brand-specific bot avatar

dist/
└── chatbot-widget.iife.js      # Built widget for production

test.html                       # Development test page (supports Live Server)
package.json                    # Dependencies and scripts
vite.config.ts                 # Build configuration
```

### Key Improvements in New Structure

1. **Clear Separation**: UI, business logic, types, and services are clearly separated
2. **Type Safety**: Centralized type definitions in `src/types/` including theme types
3. **Service Layer**: Business logic extracted to `src/services/`
4. **Component Folders**: Each component has its own folder with .tsx, .css, and index.ts
5. **Build-time Themes**: Brand selection via environment variables
6. **Clean Imports**: Each directory has index.ts for clean importing

## 🎨 Design System

### Design Tokens (`src/tokens.ts`)
Centralized design values:

```typescript
{
  colors: { primary, secondary, background, surface, text, border },
  spacing: { xs: 4px, sm: 8px, md: 16px, lg: 24px, xl: 32px, xxl: 48px },
  typography: { fontFamily, fontSize, fontWeight, lineHeight },
  borderRadius: { sm: 4px, md: 8px, lg: 12px, xl: 16px, full: 50% },
  shadows: { sm, md, lg, xl },
  zIndex: { dropdown: 1000, modal: 1040, tooltip: 1060 },
  transitions: { fast: 0.15s, normal: 0.3s, slow: 0.5s }
}
```

### Brand Themes (`src/theme/config.ts`)
Build-time brand selection with light/dark variants:

- **Eneco Brand** (Red theme - `VITE_BRAND=eneco`)
  - `light` - Eneco red (#e30613) with light background
  - `dark` - Eneco red with dark background
- **Oxxio Brand** (Blue theme - `VITE_BRAND=oxxio`)
  - `light` - Oxxio blue (#0066cc) with light background  
  - `dark` - Oxxio blue with dark background

### CSS Architecture
Themes are applied using CSS custom properties with component-specific stylesheets:

```typescript
// CSS custom properties for theming
:host {
  --primary-color: ${tokens.colors.primary};
  --background-color: ${tokens.colors.background};
}

// Component styles use CSS variables
.component {
  background: var(--primary-color);
  color: var(--text-primary);
}
```

## 🔧 Key Components

### 1. Main Entry (`src/main.tsx`)
- Creates Shadow DOM container for chatbot interface
- Creates floating button outside Shadow DOM for proper positioning
- Injects theme-aware CSS with avatar and timestamp styles
- Handles widget initialization and button/interface coordination
- Exposes global `ChatbotWidget.initialize()` API

### 2. Widget Container (`src/ui/ChatbotWidget.tsx`)
- Renders chatbot interface inside Shadow DOM
- Manages session initialization
- Wraps components with context providers
- Handles close functionality

### 3. Chatbot Interface (`src/ui/App.tsx`)
- Message display with avatars and timestamps
- Send message functionality
- Typing indicators with bot avatar
- Close button in header
- Responsive message layout

### 4. UI Components (`src/ui/`)

#### Avatar Component (`Avatar.tsx`)
- **Purpose**: Displays user and bot avatars in messages
- **Bot Avatar**: Uses `eva.png` from public folder
- **User Avatar**: Shows placeholder user icon (👤)
- **Props**: `isUser?: boolean`, `className?: string`
- **Styling**: Theme-aware with proper sizing (32x32px)

#### SuggestedActions Component (`SuggestedActions.tsx`)
- **Purpose**: Renders interactive buttons from Bot Framework activities
- **Bot Framework Integration**: Processes `suggestedActions` from DirectLine activities
- **Click Handling**: Sends postback values to Bot Framework via DirectLine
- **Props**: `suggestedActions: SuggestedAction[]`, `onActionClick: (action) => void`
- **Styling**: Theme-aware button styling with hover states
- **Actions Support**: Text, postback, and other Bot Framework action types

#### MessageTimestamp Component (`MessageTimestamp.tsx`)
- **Purpose**: Shows timestamp above each message
- **Format**: HH:MM (24-hour format, no seconds)
- **Props**: `timestamp: Date`
- **Styling**: Small, centered, theme-aware text color

#### ChatbotWidget Component (`ChatbotWidget.tsx`)
- **Purpose**: Main container that wraps the chat interface
- **Props**: `initParams: ChatArgs`, `onClose?: () => void`
- **Features**: Session initialization, theme/context providers

#### Legacy Components
- **ChatbotButton.tsx**: Old floating button (replaced by vanilla DOM in main.tsx)

### 5. Enhanced Session Management (`src/services/sessionManager.ts`)
- **Persistent user sessions** (24h expiry)
- **Personalized greetings** with user context
- **Message history storage** across page visits
- **NEW: Bot Framework data persistence** (access tokens, conversation URLs)
- **NEW: Widget state persistence** (visibility, custom options)
- **NEW: Session Data API** for flexible integration storage
- localStorage integration

### 6. Theme System (`src/ThemeContext.tsx`)
- Runtime theme switching
- localStorage persistence
- CSS generation utilities
- Theme provider component

## 🗄️ Session Management Deep Dive

### Enhanced SessionManager API

The SessionManager now provides comprehensive session persistence for Bot Framework integration.

#### Core Session Data Storage
```typescript
// Store Bot Framework session data
const sessionManager = SessionManager.getInstance();

sessionManager.setSessionData({
  visibility: { window: "minimized" },
  options: { theme: "dark", customSetting: "value" },
  accessToken: "your-directline-access-token",
  conversationUrl: "/api/conversations/conversation-id",
  customData: { userPreference: "setting" }
});
```

#### Individual Field Management
```typescript
// Update specific fields
sessionManager.setAccessToken("new-directline-token");
sessionManager.setConversationUrl("/api/conversations/new-id");
sessionManager.updateVisibility({ window: "open" });
sessionManager.setCustomData("userSetting", "value");
```

#### Widget API Integration
```javascript
// Via ChatbotWidget API
ChatbotWidget.push({
  action: 'setSessionData',
  args: {
    visibility: { window: "minimized" },
    accessToken: "token",
    conversationUrl: "/api/conversations/id"
  }
});

// Retrieve session data
ChatbotWidget.push({
  action: 'getSessionData',
  callback: (sessionData) => {
    console.log('Persisted data:', sessionData);
  }
});
```

#### Automatic Bot Framework Integration
DirectLine services automatically persist:
- **Access tokens** when connections are established
- **Conversation IDs** when conversations are created
- **Conversation URLs** for proxy-based connections

#### Session Data Structure
```typescript
interface ChatbotSession {
  // Core session data
  id: string;
  authToken?: string;
  context?: ChatbotContext;
  topic?: string;
  messages: Message[];
  createdAt: Date;
  
  // Bot Framework integration
  visibility?: VisibilitySettings;
  options?: SessionOptions;
  accessToken?: string;
  conversationUrl?: string;
  conversationId?: string;
  customData?: Record<string, unknown>;
}
```

## 🚀 Development Workflow

### Setup
```bash
npm install
npm run dev                    # Start development server
```

### Testing

#### Development Testing (Vite)
```bash
npm run dev
# Open http://localhost:5173/test.html
```
- **Advantages**: Hot reload, full dev tools, source maps
- **File Loading**: Direct TypeScript/React source files
- **Use Case**: Active development and debugging

#### Production Testing (Live Server)
```bash
npm run build                    # Creates dist/chatbot-widget.iife.js
# Open test.html with any static server
```
- **Advantages**: Tests actual production bundle
- **File Loading**: Built IIFE bundle with optimizations
- **Use Case**: Integration testing, performance validation
- **Compatible Servers**: Live Server, Python HTTP, Apache, Nginx

#### Test Checklist
- ✅ **Theme variants work** (2 brands × 2 variants × all UI states)
- ✅ **Avatar display** (Eva image loads, user icon appears)
- ✅ **Timestamps** (HH:MM format above each message)
- ✅ **Shadow DOM isolation** (styles don't leak)
- ✅ **Button positioning** (visible in all 4 corners)
- ✅ **Personalization** (user info, topics work)
- ✅ **Session persistence** (refresh preserves state)
- ✅ **Cross-server compatibility** (Vite + Live Server)

### Building
```bash
npm run build                  # Build Eneco version (default)
npm run build:eneco            # Build Eneco brand (red theme)
npm run build:oxxio            # Build Oxxio brand (blue theme)
```

### Integration Example
```html
<!-- Widget creates its own container div automatically -->
<script src="dist/chatbot-widget.iife.js"></script>
<script>
  ChatbotWidget.push({
    action: 'init',
    args: {
      theme: "dark",  // light or dark variant
      context: {
        variables: { name: "John", event: "Customer Support" }
      },
      visibility: "minimized"
    }
  });
</script>
```

## 🎛️ Configuration API

### `ChatbotWidget.initialize(params)`

**Use ChatArgs type for initialization.**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `theme` | `ThemeVariant` | `'light'` | Theme variant: light or dark (brand selected at build time) |
| `context.variables.name` | `string` | `undefined` | User name for personalized greetings |
| `context.variables.event` | `string` | `undefined` | Support topic/event for context-aware responses |
| `visibility` | `Visibility` | `'minimized'` | Initial visibility: open, minimized, hidden |
| `layoutMode` | `LayoutMode` | `'window'` | Layout mode: window, inline |
| `topic` | `string` | `undefined` | Support topic for context-aware responses |
| `askText` | `string` | `undefined` | Custom placeholder text for message input |
| `fallbackMessage` | `string` | `undefined` | Message shown when bot is unavailable |
| `accountId` | `string` | `undefined` | Account identifier for analytics |
| `authToken` | `string` | `undefined` | Authentication token for API calls |

### ChatbotContext Interface
```typescript
interface ChatbotContext {
  contentLocale?: string;
  userLocale?: string;
  variables?: {
    name?: string;
    event?: string;
  };
}
```

## 🎨 Styling Architecture

### Shadow DOM Isolation
- Complete CSS encapsulation
- No conflicts with host page styles
- Self-contained theme system

### Dynamic Theming
- Real-time theme switching
- CSS regeneration on theme change
- Consistent design token application

### Responsive Design
- Fixed widget dimensions (350x500px)
- Scalable typography
- Mobile-optimized interactions

## 🔍 Debugging

### Development Tools
- Browser DevTools Shadow DOM inspection
- Console logging for initialization
- Theme switching verification
- Session storage inspection

### Common Issues

#### Development Issues
1. **Missing Styles** - Check Shadow DOM CSS injection in browser DevTools
2. **Theme Not Switching** - Verify CSS regeneration in `generateThemeCSS()`
3. **Session Issues** - Check localStorage permissions and browser console
4. **Button Not Appearing** - Button rendered outside Shadow DOM in document.body

#### Component Issues
5. **Avatar Not Loading** 
   - Check `public/eva.png` exists
   - Verify build copies static assets to `dist/`
   - Test image path in browser: `/eva.png`
6. **Timestamps Missing** - Verify `MessageTimestamp` component integration
7. **Message Layout Broken** - Check `.message-wrapper` and `.message` CSS

#### Server Compatibility Issues
8. **404 on Live Server** 
   - Run `npm run build` first
   - Check `dist/chatbot-widget.iife.js` exists
   - Verify test.html script path: `./dist/chatbot-widget.iife.js`
9. **ChatbotWidget Undefined**
   - Script failed to load - check browser console
   - Verify correct server and file paths
   - Check browser network tab for failed requests

## 🚧 Extending the Widget

### Adding New Brands
1. Define brand themes in `src/theme/config.ts`
2. Add to brand themes collection
3. Update build scripts in package.json
4. Create brand-specific asset folders in public/
5. Test both light and dark variants

### Adding Features
1. Update relevant interfaces in `src/types/`
2. Extend context providers in `src/contexts/`
3. Use `useMessageHandler` hook for message logic
4. Add CSS styles to component .css files or `shadowDomStyles.ts`
5. Update initialization parameters

### Message Handling Pattern
Use the unified `useMessageHandler` hook for consistent message processing:

```typescript
const { sendMessage } = useMessageHandler({
  messageService,     // MessageService or BotFrameworkMessageService
  messages,
  session,
  setIsTyping,
  setMessages,
  setSession,
  sessionManager
});
```

### Custom Styling
Modify component .css files or `src/theme/shadowDomStyles.ts` for styling changes.

#### Avatar Styling
```css
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.bot-avatar {
  background: var(--surface-color);
  border: 2px solid var(--border-color);
}

.user-avatar {
  background: var(--primary-color);
  color: var(--text-on-primary);
}
```

#### Message Layout with Avatars
```css
.message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.message.user {
  flex-direction: row-reverse; /* Avatar on right */
}

.message-timestamp {
  text-align: center;
  font-size: 11px;
  color: var(--text-secondary);
}
```

## 📝 Best Practices

### Code Organization
- Keep components focused and small
- Use TypeScript interfaces consistently
- Maintain design token consistency
- Document complex logic

### Performance
- Minimize Shadow DOM recreations
- Use React.memo for expensive components
- Optimize CSS injection
- Lazy load heavy features

### Accessibility
- Maintain keyboard navigation
- Use semantic HTML
- Ensure color contrast compliance
- Support screen readers

### Testing
- Test all theme combinations
- Verify cross-browser compatibility
- Check mobile responsiveness
- Validate API integration points