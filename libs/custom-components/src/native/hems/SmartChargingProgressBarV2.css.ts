import { keyframes } from '@vanilla-extract/css';

import { theme } from '@sparky/theme';
import tokens from '@sparky/themes/eneco/tokens';
import { style, conditionalRecipe, RecipeVariants } from '@sparky/vanilla-extract';

const gradientAnimation = keyframes({
  '0%': { backgroundPosition: '0 0' },
  '100%': { backgroundPosition: '100% 100%' },
});

const bounceAnimation = keyframes({
  '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
  '40%': { transform: 'translateY(-8px)' },
  '60%': { transform: 'translateY(-4px)' },
});

export const progressBarWrapper = style({
  width: '100%',
  height: '24px',
  position: 'relative',
});

export const progressBarContainer = style({
  width: '100%',
  height: '100%',
  backgroundColor: '#F3F0F0',
  borderRadius: '4px',
  overflow: 'hidden',
  position: 'relative',
});

export const progressBarRemainingRecipe = conditionalRecipe({
  base: {
    height: '100%',
    position: 'absolute',
    color: '#FFFFFF',
    fontSize: '12px',
    top: 0,
    left: 0,
    backgroundImage: `repeating-linear-gradient(135deg, #FFFFFF, #FFFFFF 3px, #84DC99 3px, #84DC99 6px)`,
    backgroundRepeat: 'repeat',
    backgroundSize: '60px 24px',
    backgroundPosition: '0 0',
  },
  variants: {
    status: {
      disabled: {},
      active: {
        animationName: gradientAnimation,
        animationDuration: '12s',
        animationIterationCount: 'infinite',
        animationTimingFunction: 'linear',
      },
      charging: {
        animationName: gradientAnimation,
        animationDuration: '6s',
        animationIterationCount: 'infinite',
        animationTimingFunction: 'linear',
      },
    },
  },
});

export type progressBarRemainingVariants = RecipeVariants<typeof progressBarRemainingRecipe>;

export const progressBarCurrent = style({
  height: '100%',
  backgroundColor: '#009B65',
  transition: 'width 0.3s ease-out',
  position: 'absolute',
  top: 0,
  left: 0,
});

export const progressBarFinishFlagRecipe = conditionalRecipe({
  base: {
    position: 'absolute',
    top: '0',
    width: '32px',
    height: '38px',
    marginLeft: '-16px',
    marginTop: '-19px',
    transition: 'left 0.3s ease-in-out',
  },
  variants: {
    finished: {
      true: {
        color: tokens.secondaryColors.enecoRed700,
        animationName: bounceAnimation,
        animationDuration: '2s',
        animationTimingFunction: 'ease',
        animationIterationCount: 'infinite',
      },
    },
  },
});

export const progressDataContainer = style({ position: 'relative', marginTop: '8px', height: '28px', width: '100%' });

export const progressDataOrigin = style({ position: 'absolute', transition: 'left 0.3s ease-in-out' });

export const progressDataDivider = style({
  width: '1px',
  height: '18px',
  position: 'relative',
  top: '4px',
  backgroundColor: theme.colors.neutral400,
});

export const progressDataCharge = style({
  position: 'absolute',
  width: 'max-content',
  transform: 'translateX(-100%)',
  top: 0,
  left: '-8px',
});

export const progressDataRange = style({ position: 'absolute', width: 'max-content', top: 0, left: '8px' });
