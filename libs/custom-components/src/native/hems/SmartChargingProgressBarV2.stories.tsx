import type { ComponentProps } from 'react';
import React from 'react';

import SmartChargingProgressBarV2 from './SmartChargingProgressBarV2';
type StatusProps = ComponentProps<typeof SmartChargingProgressBarV2>;

const Template = (args: StatusProps) => <SmartChargingProgressBarV2 {...args} />;

export default {
  title: 'SmartChargingProgressBarV2',
  component: SmartChargingProgressBarV2,
  args: {
    currentCharge: 50,
    targetCharge: 80,
    range: 325,
    status: 'charging',
    showProgressBarRemaining: true,
    showFinishFlag: true,
  },
};

export const BasicExample = {
  render: Template.bind({}),
  name: 'Basic Example',
};

export const ActiveStatusExample = {
  render: () => (
    <SmartChargingProgressBarV2
      currentCharge={60}
      targetCharge={80}
      range={425}
      status="active"
      showProgressBarRemaining={true}
      showFinishFlag={true}
    />
  ),
  name: 'Active Status Example',
};

export const DisabledStatusExample = {
  render: () => (
    <SmartChargingProgressBarV2
      currentCharge={40}
      targetCharge={70}
      range={225}
      status="disabled"
      showProgressBarRemaining={false}
      showFinishFlag={false}
    />
  ),
  name: 'Active Status Example',
};

export const CompletedExample = {
  render: () => (
    <SmartChargingProgressBarV2
      currentCharge={80}
      targetCharge={80}
      range={525}
      status="disabled"
      showProgressBarRemaining={false}
      showFinishFlag={true}
    />
  ),
  name: 'Completed Example',
};
