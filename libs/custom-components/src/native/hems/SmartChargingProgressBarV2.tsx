import { FC } from 'react';

import { Box, Heading, Stack, Text } from '@sparky';
import { getClassNames } from '@sparky/util/css';

import {
  progressBarContainer,
  progressBarCurrent,
  progressBarFinishFlagRecipe,
  progressBarRemainingRecipe,
  progressBarRemainingVariants,
  progressBarWrapper,
  progressDataCharge,
  progressDataContainer,
  progressDataDivider,
  progressDataOrigin,
  progressDataRange,
} from './SmartChargingProgressBarV2.css';
import { FinishFlagIcon } from '../icons/FinishFlagIcon';

type SmartChargingProgressBarV2Props = progressBarRemainingVariants & {
  currentCharge: number;
  targetCharge: number;
  range: number;
  status: 'disabled' | 'active' | 'charging';
  showProgressBarRemaining: boolean;
  showFinishFlag: boolean;
};

const nativeSmartChargingProgressBarClassName = 'native-smart-charging-progress-bar';

const SmartChargingProgressBarV2: FC<SmartChargingProgressBarV2Props> = ({
  currentCharge,
  targetCharge,
  range,
  status,
  showProgressBarRemaining,
  showFinishFlag,
}) => {
  const progressBarRemainingClassNames = getClassNames(progressBarRemainingRecipe({ status }));

  const progressBarFinishFlagClassNames = getClassNames(
    progressBarFinishFlagRecipe({ finished: currentCharge === targetCharge }),
  );

  return (
    <Box>
      <div className={progressBarWrapper}>
        <div className={progressBarContainer}>
          {showProgressBarRemaining && (
            <div className={progressBarRemainingClassNames} style={{ width: `${targetCharge}%` }} />
          )}
          <div className={progressBarCurrent} style={{ width: `${currentCharge}%` }} />
        </div>
        {showFinishFlag && (
          <div className={progressBarFinishFlagClassNames} style={{ left: `${targetCharge}%` }}>
            <FinishFlagIcon />
          </div>
        )}
      </div>
      <div className={progressDataContainer}>
        <div className={progressDataOrigin} style={{ left: `clamp(55px, ${currentCharge}%, calc(100% - 95px))` }}>
          <div className={progressDataCharge}>
            <Heading size="S" as="h3" color="textPrimary">
              <Stack direction="row" alignY="end" gap="1">
                <span>{currentCharge}</span>
                <Text size="BodyM" color="textLowEmphasis" weight="regular">
                  %
                </Text>
              </Stack>
            </Heading>
          </div>

          <div className={progressDataDivider} />

          <div className={progressDataRange}>
            <Heading size="S" as="h3" color="textPrimary">
              <Stack direction="row" alignY="end" gap="1">
                <span>{range}</span>
                <Text size="BodyM" color="textLowEmphasis" weight="regular">
                  km
                </Text>
              </Stack>
            </Heading>
          </div>
        </div>
      </div>
    </Box>
  );
};

SmartChargingProgressBarV2.toString = () => `.${nativeSmartChargingProgressBarClassName}`;
SmartChargingProgressBarV2.displayName = 'SmartChargingProgressBar';

export default SmartChargingProgressBarV2;
