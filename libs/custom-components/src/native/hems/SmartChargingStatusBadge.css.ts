import tokens from '@sparky/themes/eneco/tokens';
import { style, conditionalRecipe, RecipeVariants } from '@sparky/vanilla-extract';

export const container = style({
  position: 'relative',
});

export const badgeRecipe = conditionalRecipe({
  base: {
    position: 'absolute',
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    color: tokens.textColors.textInverted,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: tokens.neutralColors.neutralBlack,
  },

  variants: {
    variant: {
      disconnected: {
        backgroundColor: tokens.neutralColors.neutralBlack,
      },
      connected: {
        backgroundColor: tokens.neutralColors.neutralBlack,
      },
      charging: {
        backgroundColor: tokens.secondaryColors.accentGreen700,
      },
      chargingSolar: {
        backgroundColor: tokens.secondaryColors.yellow700,
      },
      completed: {
        backgroundColor: tokens.secondaryColors.accentGreen700,
      },
      warning: {
        backgroundColor: tokens.feedbackColors.feedbackError,
      },
      stopped: {
        backgroundColor: tokens.neutralColors.neutralBlack,
      },
    },
  },
});

export type badgeVariants = RecipeVariants<typeof badgeRecipe>;
