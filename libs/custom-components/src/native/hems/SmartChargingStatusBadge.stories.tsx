import type { ComponentProps } from 'react';
import React from 'react';

import SmartChargingStatusBadge from './SmartChargingStatusBadge';
type StatusProps = ComponentProps<typeof SmartChargingStatusBadge>;

const Template = (args: StatusProps) => <SmartChargingStatusBadge {...args} />;

export default {
  title: 'SmartChargingStatusBadge',
  component: SmartChargingStatusBadge,
  args: {
    variant: 'charging',
  },
};

export const BasicExample = {
  render: Template.bind({}),
  name: 'Basic Example',
};

export const DisconnectedExample = {
  render: () => <SmartChargingStatusBadge variant="disconnected" />,
  name: 'Disconnected example',
};

export const ConnectedExample = {
  render: () => <SmartChargingStatusBadge variant="connected" />,
  name: 'Connected example',
};

export const ChargingExample = {
  render: () => <SmartChargingStatusBadge variant="charging" />,
  name: 'Charging example',
};

export const ChargingSolarExample = {
  render: () => <SmartChargingStatusBadge variant="chargingSolar" />,
  name: 'Charging solar example',
};

export const CompletedExample = {
  render: () => <SmartChargingStatusBadge variant="completed" />,
  name: 'Completed example',
};

export const WarningExample = {
  render: () => <SmartChargingStatusBadge variant="warning" />,
  name: 'Warning example',
};
