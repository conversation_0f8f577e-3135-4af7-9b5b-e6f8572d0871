import { forwardRef } from 'react';

import { getStaticPrefix } from '@common/env';
import { Box, Image } from '@sparky';
import { getClassNames } from '@sparky/util/css';

import { container, badgeRecipe, badgeVariants } from './SmartChargingStatusBadge.css';
import IconBadge from '../icons/IconBadge';

const nativeSmartChargingStatusBadgeClassName = 'native-smart-charging-status-badge';

type SmartChargingStatusBadgeProps = badgeVariants & {
  variant: 'connected' | 'disconnected' | 'charging' | 'chargingSolar' | 'completed' | 'warning' | 'stopped';
};

const SmartChargingStatusBadge = forwardRef<HTMLDivElement, SmartChargingStatusBadgeProps>(({ variant }, ref) => {
  const badgeClassNames = getClassNames(badgeRecipe({ variant }));

  return (
    <div className={container}>
      <div className={badgeClassNames}>
        <IconBadge variant={variant} />
      </div>
      <Box paddingLeft="2">
        <Image
          src={`${getStaticPrefix()}/images/eneco/smart-charging/hems-ev.svg`}
          width="56"
          height="56"
          alt="EV"
          hasLazyLoad={false}
        />
      </Box>
    </div>
  );
});

SmartChargingStatusBadge.toString = () => `.${nativeSmartChargingStatusBadgeClassName}`;
SmartChargingStatusBadge.displayName = 'SmartChargingStatusBadge';

export default SmartChargingStatusBadge;
