export const FinishFlagIcon = () => {
  return (
    <svg width="32" height="38" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#finish-flag-filter)">
        <path d="M28 14c0 6.63-6 12-12 17.4C10 26 4 20.63 4 14a12 12 0 1 1 24 0Z" fill="currentColor" />
        <path
          d="M17.98 9.31c.33-.21.78-.11.95.24l2.2 4.49a.6.6 0 0 1-.17.76c-2.67 1.87-4.43.04-7.08 1.82l2.2 4.47.02.06c.11.3-.02.64-.3.78a.6.6 0 0 1-.8-.22l-.02-.06-4.92-10-.02-.07a.63.63 0 0 1 .3-.78.6.6 0 0 1 .77.2c2.58-1.74 4.32-.06 6.87-1.69Z"
          fill="#fff"
        />
      </g>
      <defs>
        <filter
          id="finish-flag-filter"
          x="0"
          y="0"
          width="32"
          height="37.4"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feColorMatrix values="0 0 0 0 0.101961 0 0 0 0 0.0901961 0 0 0 0 0.105882 0 0 0 0.2 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_14690_40265" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_14690_40265" result="shape" />
        </filter>
      </defs>
    </svg>
  );
};
