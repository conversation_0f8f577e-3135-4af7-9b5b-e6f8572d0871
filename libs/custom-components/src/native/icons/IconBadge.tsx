import { FC } from 'react';

import { iconBadge } from './IconBadge.css';

const IconBadge: FC<{ variant: string }> = ({ variant }) => {
  let iconPath;

  switch (variant) {
    case 'disconnected':
      iconPath = (
        <path
          d="M10 5.76 7.13 2.89C7.36 2.37 7.89 2 8.5 2c.83 0 1.5.67 1.5 1.5v2.26ZM20 14v-4c0-1.1-.9-2-2-2h-1V3.5c0-.83-.67-1.5-1.5-1.5S14 2.67 14 3.5V8h-1.76l7.54 7.54c.13-.49.22-1 .22-1.54Zm.71 5.29-1.82-1.82L7 5.59 4.71 3.3A.996.996 0 1 0 3.3 4.71L6.59 8H6c-1.1 0-2 .9-2 2v4c0 3.31 2.69 6 6 6v1c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-1c1.3 0 2.49-.41 3.47-1.11l1.82 1.82c.2.2.45.29.71.29.26 0 .51-.1.71-.29a.996.996 0 0 0 0-1.41v-.01Z"
          fill="currentColor"
        />
      );
      break;
    case 'connected':
    case 'stopped':
      iconPath = (
        <path
          d="M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10c.55 0 1-.45 1-1v-4.1a5 5 0 0 0 4-4.9v-1c0-.55-.45-1-1-1h-1V7c0-.55-.45-1-1-1s-1 .45-1 1v3h-2V7c0-.55-.45-1-1-1s-1 .45-1 1v3H8c-.55 0-1 .45-1 1v1a5 5 0 0 0 4 4.9v3.04c-3.94-.49-7-3.87-7-7.94 0-4.41 3.59-8 8-8s8 3.59 8 8c0 3.04-1.7 5.78-4.43 7.15-.49.25-.69.85-.45 1.34.25.49.84.69 1.34.45 3.41-1.71 5.53-5.13 5.53-8.94C22 6.49 17.51 2 12 2Z"
          fill="currentColor"
        />
      );
      break;
    case 'charging':
      iconPath = (
        <path
          fillRule="evenodd"
          d="M17.89 10.54a1 1 0 0 0-.893-.54H13.61l1.384-6.8a.998.998 0 0 0-.589-1.116 1.004 1.004 0 0 0-1.216.346l-7.016 10a.999.999 0 0 0-.07 1 1 1 0 0 0 .872.57h3.388l-1.383 6.8a.998.998 0 0 0 .591 1.12c.13.055.27.082.411.08a1.004 1.004 0 0 0 .822-.43l7.016-10a.999.999 0 0 0 .07-1.03Z"
          fill="currentColor"
        />
      );
      break;
    case 'chargingSolar':
      iconPath = (
        <path
          fillRule="evenodd"
          d="M4 12a1 1 0 0 1-1 1H2a1 1 0 0 1 0-2h1a1 1 0 0 1 1 1Zm18-1h-1a1 1 0 0 0 0 2h1a1 1 0 0 0 0-2ZM12 4a1 1 0 0 0 1-1V2a1 1 0 0 0-2 0v1a1 1 0 0 0 1 1ZM4.93 17.66l-.71.7a1.004 1.004 0 0 0 1.42 1.42l.7-.71a1 1 0 0 0-1.41-1.41ZM19.07 6.34l.71-.7a1.005 1.005 0 0 0-.71-1.714 1.004 1.004 0 0 0-.71.294l-.7.71a1 1 0 0 0 1.41 1.41Zm-14.14 0a1 1 0 0 0 1.41-1.41l-.7-.71a1.004 1.004 0 0 0-1.42 1.42l.71.7ZM18 12a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-3-.72a.51.51 0 0 0-.5-.28h-1.81L13 8.56a.5.5 0 0 0-.31-.56.5.5 0 0 0-.59.16l-3 4a.5.5 0 0 0 .4.8h1.81L11 15.44a.5.5 0 0 0 .31.52.429.429 0 0 0 .19 0 .511.511 0 0 0 .4-.2l3-4a.51.51 0 0 0 .1-.48Zm4.12 6.38a1 1 0 0 0-1.41 1.41l.7.71a1.004 1.004 0 1 0 1.42-1.42l-.71-.7ZM12 20a1 1 0 0 0-1 1v1a1 1 0 0 0 2 0v-1a1 1 0 0 0-1-1Z"
          fill="currentColor"
        />
      );
      break;
    case 'completed':
      iconPath = (
        <path
          fillRule="evenodd"
          d="M8.997 18a1 1 0 0 1-.71-.29l-3.997-4a1.004 1.004 0 0 1 1.42-1.42l3.287 3.3 9.286-9.3a1.003 1.003 0 1 1 1.419 1.42l-9.995 10a1 1 0 0 1-.71.29Z"
          fill="currentColor"
        />
      );
      break;
    case 'warning':
      iconPath = (
        <path
          d="M4.47 20.008h15.06c1.54 0 2.5-1.67 1.73-3l-7.53-13.01c-.77-1.33-2.69-1.33-3.46 0l-7.53 13.01c-.77 1.33.19 3 1.73 3Zm7.53-7c-.55 0-1-.45-1-1v-2c0-.55.45-1 1-1s1 .45 1 1v2c0 .55-.45 1-1 1Zm1 4h-2v-2h2v2Z"
          fill="currentColor"
        />
      );
      break;
    default:
      iconPath = null;
  }

  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className={iconBadge}>
      {iconPath}
    </svg>
  );
};

export default IconBadge;
