{"name": "customer-insights", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/customer-insights/src", "projectType": "library", "tags": ["lib:customer-insights"], "targets": {"lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["libs/customer-insights/src/**/*.{ts,tsx}"]}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "libs/customer-insights/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "runInBand": true}}}, "tsc": {"executor": "./tools/executors/tsc:tsc", "options": {"tsConfig": ["tsconfig.json"]}}}}