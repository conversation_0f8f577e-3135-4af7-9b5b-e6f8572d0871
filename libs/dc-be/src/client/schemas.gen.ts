// This file is auto-generated by @hey-api/openapi-ts

export const $AccessTypeDto = {
    enum: ['Read', 'Write', 'Owner'],
    type: 'string',
    description: 'Kind of access the account has'
} as const;

export const $AccountLinkOverviewDto = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AccountLinkOverviewRecordDto'
            },
            description: '',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains the overview of all linked accounts to this account'
} as const;

export const $AccountLinkOverviewLinkTypeDto = {
    enum: ['Read', 'Write', 'Owner'],
    type: 'string',
    description: 'Type of link established'
} as const;

export const $AccountLinkOverviewLoginDetailsDto = {
    type: 'object',
    properties: {
        displayName: {
            type: 'string',
            description: 'The display name the referenced user uses in the UI',
            nullable: true
        },
        loginUsername: {
            type: 'string',
            description: 'The username of the referenced user (email)',
            nullable: true
        },
        localLogin: {
            type: 'boolean',
            description: 'Whether the referenced user has local login credentials (username/password)'
        },
        itsmeLogin: {
            type: 'boolean',
            description: 'Whether the referenced user has ItsMe federated login credentials (ItsMe IDP)'
        }
    },
    additionalProperties: false,
    description: 'Body containing information regarding the login credentials for a given user account'
} as const;

export const $AccountLinkOverviewRecordDto = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            description: 'Id of the account link or invite',
            nullable: true
        },
        identityAlias: {
            type: 'string',
            description: '',
            nullable: true
        },
        inviteeEmail: {
            type: 'string',
            description: '',
            nullable: true
        },
        loginDetails: {
            '$ref': '#/components/schemas/AccountLinkOverviewLoginDetailsDto'
        },
        type: {
            '$ref': '#/components/schemas/AccountLinkOverviewLinkTypeDto'
        },
        linkingRequest: {
            '$ref': '#/components/schemas/AccountLinkOverviewRecordLinkingRequestDto'
        }
    },
    additionalProperties: false,
    description: 'Body containing a single linked account record'
} as const;

export const $AccountLinkOverviewRecordLinkingRequestDto = {
    type: 'object',
    properties: {
        inviteReference: {
            type: 'string',
            description: '',
            format: 'uuid'
        },
        status: {
            type: 'string',
            description: '',
            nullable: true
        },
        createdAt: {
            type: 'string',
            description: '',
            format: 'date-time'
        },
        lastMailSentAt: {
            type: 'string',
            description: '',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains link request details'
} as const;

export const $AccountLinkingTypeDto = {
    enum: ['Read', 'Write'],
    type: 'string',
    description: 'Defines the access control of the linking'
} as const;

export const $Address = {
    type: 'object',
    properties: {
        countryCode: {
            type: 'string',
            description: 'The countryCode property',
            nullable: true
        },
        city: {
            type: 'string',
            description: 'The city property',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'The email property',
            nullable: true
        },
        firstName: {
            type: 'string',
            description: 'The firstName property',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'The lastName property',
            nullable: true
        },
        line1: {
            type: 'string',
            description: 'The line1 property',
            nullable: true
        },
        line2: {
            type: 'string',
            description: 'The line2 property',
            nullable: true
        },
        line3: {
            type: 'string',
            description: 'The line3 property',
            nullable: true
        },
        postalCode: {
            type: 'string',
            description: 'The postalCode property',
            nullable: true
        },
        state: {
            type: 'string',
            description: 'The state property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Address response'
} as const;

export const $AddressDetailsDto = {
    type: 'object',
    properties: {
        street: {
            type: 'string',
            description: 'The name of the street where the address is located',
            nullable: true
        },
        houseNumber: {
            type: 'string',
            description: 'The number of the house or building on the street',
            nullable: true
        },
        bus: {
            type: 'string',
            description: 'Optional bus or additional unit information',
            nullable: true
        },
        postalCode: {
            type: 'string',
            description: 'The postal code of the address',
            nullable: true
        },
        municipality: {
            type: 'string',
            description: 'The municipality or city where the address is located',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Response containing address information in detail'
} as const;

export const $AdvanceAmountCollectionResponse = {
    type: 'object',
    properties: {
        advancePayments: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AdvanceAmountDto'
            },
            description: 'Collection of advance payment records',
            nullable: true
        },
        totals: {
            '$ref': '#/components/schemas/AdvanceAmountTotalsDto'
        }
    },
    additionalProperties: false,
    description: 'Collection of advance payment records'
} as const;

export const $AdvanceAmountDto = {
    type: 'object',
    properties: {
        contractDetails: {
            '$ref': '#/components/schemas/AdvanceContractDto'
        },
        meterDetails: {
            '$ref': '#/components/schemas/AdvanceMeterDto'
        },
        details: {
            '$ref': '#/components/schemas/AdvanceDetailsDto'
        }
    },
    additionalProperties: false,
    description: 'Details of an advance payment'
} as const;

export const $AdvanceAmountTotalsDto = {
    type: 'object',
    properties: {
        currentAmount: {
            type: 'integer',
            description: 'Total current amount',
            format: 'int32',
            nullable: true
        },
        minAmount: {
            type: 'integer',
            description: 'Total minimum amount',
            format: 'int32',
            nullable: true
        },
        maxAmount: {
            type: 'integer',
            description: 'Total maximum amount',
            format: 'int32',
            nullable: true
        },
        recommendedAmount: {
            type: 'integer',
            description: 'Total recommended amount',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Recommendation numbers for when a customer tries to change the total across multiple contracts and billing accounts'
} as const;

export const $AdvanceContractDto = {
    type: 'object',
    properties: {
        contractNumber: {
            type: 'string',
            description: 'Identifier of the contract',
            nullable: true
        },
        contractStartDate: {
            type: 'string',
            description: 'Contract start date',
            format: 'date'
        },
        contractExpiryDate: {
            type: 'string',
            description: 'Contract expiry date',
            format: 'date',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Details of the contract related to the advance payment'
} as const;

export const $AdvanceDetailsDto = {
    type: 'object',
    properties: {
        currentAmount: {
            type: 'integer',
            description: 'Current amount',
            format: 'int32',
            nullable: true
        },
        minAmount: {
            type: 'integer',
            description: 'Minimum amount allowed',
            format: 'int32',
            nullable: true
        },
        maxAmount: {
            type: 'integer',
            description: 'Maximum amount allowed',
            format: 'int32',
            nullable: true
        },
        recommendedAmount: {
            type: 'integer',
            description: 'The recommended amount',
            format: 'int32',
            nullable: true
        },
        isDefaulter: {
            type: 'boolean',
            description: 'Is defaulter'
        },
        notAvailable: {
            type: 'boolean',
            description: 'Not available'
        },
        changeRequested: {
            type: 'boolean',
            description: ''
        },
        vacancyAmount: {
            type: 'integer',
            description: 'Vacancy amount',
            format: 'int32',
            nullable: true
        },
        vacancyUntil: {
            type: 'string',
            description: 'Vacancy date until',
            format: 'date',
            nullable: true
        },
        vacancyChangeRequested: {
            type: 'boolean',
            description: 'Whether a vacancy request is ongoing'
        },
        monthsUntilMeterReadingMonth: {
            type: 'integer',
            description: 'Amount of months until meter reading month',
            format: 'int32',
            nullable: true
        },
        amountDueForMeterReadingMonth: {
            type: 'number',
            description: 'Amount due for meter reading month">',
            format: 'decimal',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Details of as'
} as const;

export const $AdvanceMeterDto = {
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'EAN of the meter',
            nullable: true
        },
        meterType: {
            '$ref': '#/components/schemas/MeterType'
        },
        energyType: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        serviceComponent: {
            '$ref': '#/components/schemas/MeterService'
        },
        invoiceFrequency: {
            '$ref': '#/components/schemas/InvoiceFrequency'
        },
        meterReadingMonth: {
            type: 'integer',
            description: 'Month in which the meter gets read',
            format: 'int32',
            nullable: true
        },
        yearlyBillMonth: {
            type: 'integer',
            description: 'Month in which the yearly bill occurs',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Details of the meter related to the advance payment'
} as const;

export const $AssetTypeDto = {
    enum: ['Vehicle'],
    type: 'string',
    description: 'Asset type'
} as const;

export const $AvailableProductsInfoResponse = {
    type: 'object',
    properties: {
        campaign: {
            type: 'string',
            description: 'The campaign property',
            nullable: true
        },
        couponCode: {
            type: 'string',
            description: 'The couponCode property',
            nullable: true
        },
        currencyCode: {
            type: 'string',
            description: 'The currencyCode property',
            nullable: true
        },
        customerInfo: {
            '$ref': '#/components/schemas/OrderCustomerInfo'
        },
        errorDetails: {
            '$ref': '#/components/schemas/EnecoErrorDetails'
        },
        locale: {
            type: 'string',
            description: 'The locale property',
            nullable: true
        },
        products: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoProduct'
            },
            description: 'The products property',
            nullable: true
        },
        productType: {
            type: 'string',
            description: 'The productType property',
            nullable: true
        },
        success: {
            type: 'boolean',
            description: 'The success property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Available products response.'
} as const;

export const $BillInsertDto = {
    type: 'object',
    properties: {
        reasonCode: {
            type: 'string',
            description: 'Reason code',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Bill insert with reason'
} as const;

export const $BillInsertListDto = {
    type: 'object',
    properties: {
        billInserts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/BillInsertDto'
            },
            description: 'List of the Reason codes',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'List of Bill inserts with reason'
} as const;

export const $BillingAccountRelatedMeterResponse = {
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'EAN of the meter',
            nullable: true
        },
        energyType: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        }
    },
    additionalProperties: false,
    description: 'Meters associated with a billing account'
} as const;

export const $CalculateMeterDeviationResponse = {
    type: 'object',
    properties: {
        summaryDelta: {
            '$ref': '#/components/schemas/RectificationDeltaDto'
        },
        details: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RectificationDeltaResultResponse'
            },
            description: 'Delta per contested values',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The calculate meter deviation response class'
} as const;

export const $ChargebeeStatus = {
    enum: [0, 1, 2],
    type: 'integer',
    description: 'Chargebee Status',
    format: 'int32'
} as const;

export const $ChildAccountLinkingInviteRequest = {
    type: 'object',
    properties: {
        alias: {
            type: 'string',
            description: 'Alias of the invitee',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'Email of the invitee',
            nullable: true
        },
        linkingType: {
            '$ref': '#/components/schemas/AccountLinkingTypeDto'
        }
    },
    additionalProperties: false,
    description: 'Request that contains the child account linking invite details'
} as const;

export const $ChildAccountLinkingRequest = {
    type: 'object',
    properties: {
        sessionToken: {
            type: 'string',
            description: 'Session token',
            nullable: true
        },
        alias: {
            type: 'string',
            description: 'Alias the user selected',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Body containing details link a child account'
} as const;

export const $ComfortBonusLevelDto = {
    enum: ['LEVEL0', 'LEVEL1', 'LEVEL2'],
    type: 'string',
    description: "Comfort bonus level based on the 'customer's lifetime'"
} as const;

export const $CompanyDataDto = {
    type: 'object',
    properties: {
        name: {
            type: 'string',
            description: 'Name of the company',
            nullable: true
        },
        formatType: {
            '$ref': '#/components/schemas/CompanyFormatTypeDto'
        },
        vatType: {
            '$ref': '#/components/schemas/CompanyVatTypeDto'
        },
        companyNumber: {
            type: 'string',
            description: 'Company number',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Company data for a crm account'
} as const;

export const $CompanyFormatTypeDto = {
    enum: ['BV', 'CV', 'CommV', 'Eenmanszaak', 'FV', 'TijdelijkeMaatschap', 'VOF', 'VZW', 'NV', 'Maatschap'],
    type: 'string',
    description: 'Format of the company'
} as const;

export const $CompanyVatTypeDto = {
    enum: ['VatRequired', 'VatNotRequired', 'CompanyNumberRequested'],
    type: 'string',
    description: 'VAT type of the company'
} as const;

export const $ConsumptionData = {
    type: 'object',
    properties: {
        energyType: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        direction: {
            '$ref': '#/components/schemas/DirectionTypeCodeDto'
        },
        meterTimeFrameType: {
            '$ref': '#/components/schemas/TimeframeTypeCodeDto'
        },
        reason: {
            '$ref': '#/components/schemas/ReasonDto'
        },
        basedOn: {
            '$ref': '#/components/schemas/MeterReadingType'
        },
        value: {
            type: 'number',
            description: 'Value of the consumption data',
            format: 'decimal'
        },
        unitOfMeasure: {
            '$ref': '#/components/schemas/UnitOfMeasureDto'
        }
    },
    additionalProperties: false,
    description: 'The consumption data for a specific period'
} as const;

export const $ConsumptionPeriod = {
    required: ['data'],
    type: 'object',
    properties: {
        startDate: {
            type: 'string',
            description: 'The start date of the period',
            format: 'date-time'
        },
        endDate: {
            type: 'string',
            description: 'The end date of the period',
            format: 'date-time'
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ConsumptionData'
            },
            description: 'The consumption data for this period',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The period for which the data gets fetched'
} as const;

export const $ConsumptionResponse = {
    type: 'object',
    properties: {
        consumptionType: {
            '$ref': '#/components/schemas/ConsumptionTypeDto'
        },
        timeFrame: {
            '$ref': '#/components/schemas/TimeFrameDto'
        },
        displayMode: {
            '$ref': '#/components/schemas/DisplayModeDto'
        },
        startDate: {
            type: 'string',
            description: 'The start date of the current view period',
            format: 'date-time'
        },
        endDate: {
            type: 'string',
            description: 'The end date of the current view period',
            format: 'date-time'
        },
        periods: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ConsumptionPeriod'
            },
            description: 'The periods for which consumption data is available',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The response for the consumption data'
} as const;

export const $ConsumptionTypeDto = {
    enum: ['Total', 'Electricity', 'Gas', 'Injection', 'PeakValues'],
    type: 'string',
    description: 'Represents the type of consumption data to be retrieved.'
} as const;

export const $ContactAddressDto = {
    type: 'object',
    properties: {
        street: {
            type: 'string',
            description: 'Street of the address',
            nullable: true
        },
        zipCode: {
            type: 'string',
            description: 'Zip code of the address',
            nullable: true
        },
        municipality: {
            type: 'string',
            description: 'Municipality of the address',
            nullable: true
        },
        houseNumber: {
            type: 'string',
            description: 'House number of the address',
            nullable: true
        },
        bus: {
            type: 'string',
            description: 'Bus of the address',
            nullable: true
        },
        country: {
            type: 'string',
            description: 'Country of the address',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Representation of the customers contact address'
} as const;

export const $ContactFormAttachmentDto = {
    type: 'object',
    properties: {
        ixosArchiveId: {
            type: 'string',
            description: `The unique identifier assigned to a file within the Ixos archive system, used for retrieval
and management.`,
            nullable: true
        },
        fileName: {
            type: 'string',
            description: `The name of the file as stored in the archive, excluding file extension, providing a
human-readable reference.`,
            nullable: true
        },
        fileExtension: {
            type: 'string',
            description: 'The extension of the file, indicating its format and type, such as .pdf, .docx, etc.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the metadata and identification details for a file archived in the system.'
} as const;

export const $ContactFormAttachmentResponse = {
    type: 'object',
    properties: {
        fileExtension: {
            type: 'string',
            nullable: true
        },
        fileName: {
            type: 'string',
            nullable: true
        },
        ixosArchiveId: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const $ContactFormDto = {
    type: 'object',
    properties: {
        attachments: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ContactFormAttachmentDto'
            },
            description: `A list of attachments related to the contact case, typically representing documents or files
provided by the contact.`,
            nullable: true
        },
        box: {
            type: 'string',
            description: 'The post office box number where the contact prefers to receive mail.',
            nullable: true
        },
        city: {
            type: 'string',
            description: 'The city of residence or work associated with the contact.',
            nullable: true
        },
        company: {
            type: 'boolean',
            description: `Indicates if the contact information pertains to a company or organization rather than an
individual.`,
            nullable: true
        },
        companyName: {
            type: 'string',
            description: 'The name of the company or organization the contact is associated with.',
            nullable: true
        },
        companyNumber: {
            type: 'string',
            description: 'The registration or identification number for the company associated with the contact.',
            nullable: true
        },
        customerNumber: {
            type: 'string',
            description: `A unique identifier or reference number assigned to the contact in the context of customer
relationships or services.`,
            nullable: true
        },
        email: {
            type: 'string',
            description: 'The email address used by the contact for electronic communication.',
            nullable: true
        },
        firstName: {
            type: 'string',
            description: 'The first name or given name of the contact.',
            nullable: true
        },
        gender: {
            '$ref': '#/components/schemas/GenderFormContactDto'
        },
        language: {
            '$ref': '#/components/schemas/ContactFormLanguageDto'
        },
        lastName: {
            type: 'string',
            description: 'The last name or family name of the contact.',
            nullable: true
        },
        question: {
            type: 'string',
            description: `A specific question or query posed by the contact, typically related to the subject or context
of communication.`,
            nullable: true
        },
        street: {
            type: 'string',
            description: 'The street address where the contact resides or works.',
            nullable: true
        },
        streetNumber: {
            type: 'string',
            description: 'The number associated with the street address of the contact.',
            nullable: true
        },
        subject: {
            type: 'string',
            description: 'The subject or main topic of communication or inquiry from the contact.',
            nullable: true
        },
        subLevelSubject: {
            type: 'string',
            description: `Additional subject details or subcategories related to the main subject of inquiry from
the contact.`,
            nullable: true
        },
        telephone: {
            type: 'string',
            description: 'The telephone number used by the contact for voice communication.',
            nullable: true
        },
        zipCode: {
            type: 'string',
            description: "The postal code or zip code corresponding to the contact's location.",
            nullable: true
        },
        recaptchaToken: {
            type: 'string',
            description: 'Token for reCaptcha validation.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: `Represents a contact's personal and professional information, including address details, communication preferences,
and identification numbers.`
} as const;

export const $ContactFormLanguageDto = {
    enum: ['DUTCH', 'FRENCH'],
    type: 'string',
    description: 'Kinds of language'
} as const;

export const $ContestedValue = {
    type: 'object',
    properties: {
        contestedDate: {
            type: 'string',
            description: 'Date entered by the user',
            format: 'date'
        },
        contestedIndex: {
            type: 'integer',
            description: 'Index entered by the user',
            format: 'int32'
        }
    },
    additionalProperties: false,
    description: 'Contested values entered by user'
} as const;

export const $ContractProductStatusDto = {
    enum: ['None', 'WaitingForApproval', 'InProgress', 'WaitingForDelivery', 'Active', 'RequestedTermination', 'Terminated'],
    type: 'string',
    description: 'Status that a contract can be in'
} as const;

export const $CorrespondenceAddressDto = {
    type: 'object',
    properties: {
        country: {
            type: 'string',
            description: 'Country of the address',
            nullable: true
        },
        postalCode: {
            type: 'string',
            description: 'Postal code of the address',
            nullable: true
        },
        municipality: {
            type: 'string',
            description: 'Municipality of the address',
            nullable: true
        },
        street: {
            type: 'string',
            description: 'Street of the address',
            nullable: true
        },
        houseNumber: {
            type: 'string',
            description: 'House number of the address',
            nullable: true
        },
        bus: {
            type: 'string',
            description: 'Bus of the address',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Move address'
} as const;

export const $CostCategoryTypeDto = {
    enum: ['EnergyCost', 'NetTariffCost', 'TaxationCost', 'Promotion', 'Total'],
    type: 'string',
    description: 'Represents the different types of cost categories used in price simulation.'
} as const;

export const $CostDto = {
    type: 'object',
    properties: {
        costCategoryType: {
            '$ref': '#/components/schemas/CostCategoryTypeDto'
        },
        value: {
            type: 'number',
            description: 'The monetary value of this cost.',
            format: 'decimal'
        },
        detailedCosts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/DetailedCostDto'
            },
            description: 'The detailed breakdown of this cost category.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents a cost category with its type, value and detailed cost breakdown.'
} as const;

export const $CreateAssetRequestDto = {
    required: ['assetType'],
    type: 'object',
    properties: {
        assetType: {
            '$ref': '#/components/schemas/AssetTypeDto'
        },
        externalAssetId: {
            type: 'string',
            description: 'Identifier in external system',
            nullable: true
        },
        vehicle: {
            '$ref': '#/components/schemas/VehicleAssetResourceDto'
        }
    },
    additionalProperties: false,
    description: 'Create a new asset'
} as const;

export const $CreatePaymentPlanRequest = {
    type: 'object',
    properties: {
        amountOfSlices: {
            type: 'integer',
            description: 'Amount of slices the user wants to pay in',
            format: 'int32'
        }
    },
    additionalProperties: false,
    description: 'Request to create a new payment plan'
} as const;

export const $CreateSubscriptionRequestDto = {
    required: ['consentVersion', 'startDate', 'tier', 'type'],
    type: 'object',
    properties: {
        type: {
            '$ref': '#/components/schemas/SubscriptionTypeDto'
        },
        tier: {
            '$ref': '#/components/schemas/SubscriptionTierDto'
        },
        startDate: {
            type: 'string',
            description: 'Subscription start date',
            format: 'date-time'
        },
        endDate: {
            type: 'string',
            description: 'Optional subscription end date',
            format: 'date-time',
            nullable: true
        },
        consentVersion: {
            minLength: 1,
            type: 'string',
            description: 'Consent version'
        }
    },
    additionalProperties: false,
    description: 'Create subscription'
} as const;

export const $CrmAccountClaimDto = {
    type: 'object',
    properties: {
        crmAccountNumber: {
            type: 'string',
            description: 'Account number of the account',
            nullable: true
        },
        alias: {
            type: 'string',
            description: 'Alias of the account',
            nullable: true
        },
        accessType: {
            '$ref': '#/components/schemas/AccessTypeDto'
        }
    },
    additionalProperties: false,
    description: 'Contains an account claim entry'
} as const;

export const $CsvLanguage = {
    enum: ['Dutch', 'French'],
    type: 'string',
    description: 'Language in which the csv should be'
} as const;

export const $CustomerInfoResponse = {
    type: 'object',
    properties: {
        enecoId: {
            type: 'string',
            description: 'Customer id',
            nullable: true
        },
        chargebeeStatus: {
            '$ref': '#/components/schemas/ChargebeeStatus'
        },
        customerPaymentStatus: {
            '$ref': '#/components/schemas/CustomerPaymentStatus'
        },
        totalAmountDue: {
            type: 'number',
            description: 'Total amount due',
            format: 'double',
            nullable: true
        },
        customer: {
            '$ref': '#/components/schemas/OrderCustomer'
        },
        dongle: {
            '$ref': '#/components/schemas/DongleInfo'
        },
        invoices: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/InvoiceInfo'
            },
            description: 'Invoice info',
            nullable: true
        },
        paymentMethod: {
            '$ref': '#/components/schemas/PaymentMethodInfo'
        },
        promotionalCreditsLeft: {
            type: 'number',
            description: 'The promotionalCreditsLeft property',
            format: 'double',
            nullable: true
        },
        promotionalCreditsTotal: {
            type: 'number',
            description: 'The promotionalCreditsTotal property',
            format: 'double',
            nullable: true
        },
        subscriptions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubscriptionInfo'
            },
            description: 'Subscriptions',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Customer info response'
} as const;

export const $CustomerLanguageDto = {
    enum: ['English', 'Dutch', 'French'],
    type: 'string',
    description: "The customer's language"
} as const;

export const $CustomerPaymentStatus = {
    enum: ['AllGood', 'InDunning'],
    type: 'string',
    description: 'Customer payment status'
} as const;

export const $CustomerTypeDto = {
    enum: ['Residential', 'SOHO'],
    type: 'string',
    description: 'Type of customer'
} as const;

export const $DetailCorrespondenceUpdateRequest = {
    type: 'object',
    properties: {
        customAddress: {
            '$ref': '#/components/schemas/CorrespondenceAddressDto'
        },
        selectedAddress: {
            '$ref': '#/components/schemas/SelectedCorrespondenceAddressDto'
        },
        correspondenceDate: {
            type: 'string',
            description: 'Date from which invoices should be sent to this selected address',
            format: 'date',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Request that allows the customer to update the correspondence address'
} as const;

export const $DetailMeterUpdateRequest = {
    type: 'object',
    properties: {
        energyType: {
            '$ref': '#/components/schemas/MoveEnergyTypeDto'
        },
        oldEAN: {
            type: 'string',
            description: "The old meter's EAN",
            nullable: true
        },
        ean: {
            type: 'string',
            description: "The meter's EAN",
            nullable: true
        },
        oldMeterNumber: {
            type: 'string',
            description: "The old meter's number",
            nullable: true
        },
        meterNumber: {
            type: 'string',
            description: "The meter's number",
            nullable: true
        },
        meterValue: {
            type: 'number',
            description: 'Value of the meter',
            format: 'decimal',
            nullable: true
        },
        dayMeterValue: {
            type: 'number',
            description: 'Day meter value',
            format: 'decimal',
            nullable: true
        },
        nightMeterValue: {
            type: 'number',
            description: 'Night meter value',
            format: 'decimal',
            nullable: true
        },
        nightExclusiveMeterValue: {
            type: 'number',
            description: 'Exclusive night meter value',
            format: 'decimal',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Request to update move meter details'
} as const;

export const $DetailOldAddressNewOwnerContactDetailsUpdateRequest = {
    type: 'object',
    properties: {
        representation: {
            '$ref': '#/components/schemas/Representation'
        },
        firstName: {
            type: 'string',
            description: 'First name of the new owner',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'Last name of the new owner',
            nullable: true
        },
        phoneNumber: {
            type: 'string',
            description: 'Phone number of the new owner',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'Email of the new owner',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Update the old address new owner'
} as const;

export const $DetailedCostDto = {
    type: 'object',
    properties: {
        costType: {
            '$ref': '#/components/schemas/DetailedCostTypeDto'
        },
        value: {
            type: 'number',
            description: 'The monetary value of this detailed cost.',
            format: 'decimal'
        }
    },
    additionalProperties: false,
    description: 'Represents a detailed cost item with its type and value.'
} as const;

export const $DetailedCostTypeDto = {
    enum: ['FixedFee', 'Usage', 'PurchaseRate', 'ProsumentRate', 'DataManagement', 'CapacityRate'],
    type: 'string',
    description: 'Represents the different types of detailed costs used in price simulation.'
} as const;

export const $DirectionTypeCodeDto = {
    enum: ['Consumption', 'Production', 'Injection', 'OffTake'],
    type: 'string',
    description: 'Direction type code'
} as const;

export const $DiscountStatus = {
    enum: ['None', 'Active', 'Expired'],
    type: 'string',
    description: 'Discount status'
} as const;

export const $DisplayModeDto = {
    enum: ['Consumption', 'Cost'],
    type: 'string',
    description: 'Represents the display mode for consumption data.'
} as const;

export const $DongleInfo = {
    type: 'object',
    properties: {
        dongleStatus: {
            '$ref': '#/components/schemas/DongleStatus'
        },
        orderDate: {
            type: 'string',
            description: 'The orderDate property',
            format: 'date-time',
            nullable: true
        },
        purchaseStatus: {
            '$ref': '#/components/schemas/PurchaseStatus'
        },
        shippingAddress: {
            '$ref': '#/components/schemas/Address'
        },
        shippingStatus: {
            '$ref': '#/components/schemas/ShippingStatus'
        }
    },
    additionalProperties: false,
    description: 'Dongle info response'
} as const;

export const $DongleStatus = {
    enum: ['Unknown', 'NotInstaled', 'Active', 'NotActive'],
    type: 'string',
    description: 'Dongle Status'
} as const;

export const $DunningStatus = {
    enum: ['InProgress', 'Success', 'Exhausted', 'Stopped'],
    type: 'string',
    description: 'Dunning status'
} as const;

export const $DynamicPriceRecord = {
    type: 'object',
    properties: {
        date: {
            type: 'string',
            description: 'The day on for which the price is valid',
            format: 'date'
        },
        time: {
            type: 'string',
            description: 'The time for which the price is valid',
            format: 'time'
        },
        price: {
            type: 'number',
            description: 'The price',
            format: 'decimal'
        }
    },
    additionalProperties: false,
    description: 'A dynamic price record'
} as const;

export const $DynamicPricingResponse = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/DynamicPriceRecord'
            },
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'A container with dynamic pricing records'
} as const;

export const $EODFileUploadResponse = {
    type: 'object',
    properties: {
        fileReference: {
            type: 'string',
            description: 'File reference',
            nullable: true
        },
        fileName: {
            type: 'string',
            description: 'File name',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Uploaded file details'
} as const;

export const $EligibilitiesResponse = {
    type: 'object',
    properties: {
        signedAgreements: {
            '$ref': '#/components/schemas/SignedAgreementsDto'
        }
    },
    additionalProperties: false,
    description: 'Response containing multiple kinds of eligibility characteristics'
} as const;

export const $EnecoDiscount = {
    type: 'object',
    properties: {
        campaignCodes: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'The campaignCodes property',
            nullable: true
        },
        detailsUrl: {
            type: 'string',
            description: 'The detailsUrl property',
            nullable: true
        },
        discountAmount: {
            type: 'number',
            description: 'The discountAmount property',
            format: 'double',
            nullable: true
        },
        discountPercentage: {
            type: 'number',
            description: 'The discountPercentage property',
            format: 'double',
            nullable: true
        },
        durationLength: {
            type: 'integer',
            description: 'The durationLength property',
            format: 'int32',
            nullable: true
        },
        durationUnit: {
            '$ref': '#/components/schemas/EnecoDiscountDurationUnit'
        },
        id: {
            type: 'string',
            description: 'The id property',
            nullable: true
        },
        invoiceName: {
            type: 'string',
            description: 'The invoiceName property',
            nullable: true
        },
        name: {
            type: 'string',
            description: 'The name property',
            nullable: true
        },
        notes: {
            type: 'string',
            description: 'The notes property',
            nullable: true
        },
        redemingCouponCode: {
            type: 'string',
            description: 'The redemingCouponCode property',
            nullable: true
        },
        requireCouponCode: {
            type: 'boolean',
            description: 'The requireCouponCode property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco discount.'
} as const;

export const $EnecoDiscountDurationUnit = {
    enum: ['OneTime', 'Day', 'Week', 'Month', 'Year', 'Forever'],
    type: 'string',
    description: 'Eneco discount duration unit.'
} as const;

export const $EnecoErrorDetails = {
    type: 'object',
    properties: {
        callerMessage: {
            type: 'string',
            description: 'The callerMessage property',
            nullable: true
        },
        customerMessage: {
            type: 'string',
            description: 'The customerMessage property',
            nullable: true
        },
        erroMessage: {
            type: 'string',
            description: 'The erroMessage property',
            nullable: true
        },
        errorCode: {
            type: 'string',
            description: 'The errorCode property',
            nullable: true
        },
        errorLocation: {
            type: 'string',
            description: 'The errorLocation property',
            nullable: true
        },
        message: {
            type: 'string',
            description: 'The primary error message.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco Error Details'
} as const;

export const $EnecoOrderDiscount = {
    type: 'object',
    properties: {
        couponCode: {
            type: 'string',
            description: 'The couponCode property',
            nullable: true
        },
        id: {
            type: 'string',
            description: 'The id property',
            nullable: true
        },
        redeemingCouponCode: {
            type: 'string',
            description: 'The redeemingCouponCode property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco order discount.'
} as const;

export const $EnecoOrderExtra = {
    type: 'object',
    properties: {
        discounts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoOrderDiscount'
            },
            description: 'The discounts property',
            nullable: true
        },
        productId: {
            type: 'string',
            description: 'The productId property',
            nullable: true
        },
        quantity: {
            type: 'integer',
            description: 'The quantity property',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco order extra.'
} as const;

export const $EnecoOrderProduct = {
    type: 'object',
    properties: {
        discounts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoOrderDiscount'
            },
            description: 'The discounts property',
            nullable: true
        },
        extras: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoOrderExtra'
            },
            description: 'The extras property',
            nullable: true
        },
        productId: {
            type: 'string',
            description: 'The productId property',
            nullable: true
        },
        quantity: {
            type: 'integer',
            description: 'The quantity property',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco order product.'
} as const;

export const $EnecoProduct = {
    type: 'object',
    properties: {
        billingPeriod: {
            type: 'integer',
            description: 'The billingPeriod property',
            format: 'int32',
            nullable: true
        },
        description: {
            type: 'string',
            description: 'The description property',
            nullable: true
        },
        discounts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoDiscount'
            },
            description: 'The discounts property',
            nullable: true
        },
        extras: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoProductExtra'
            },
            description: 'The extras property',
            nullable: true
        },
        familyId: {
            type: 'string',
            description: 'The familyId property',
            nullable: true
        },
        features: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'The features property',
            nullable: true
        },
        isMetered: {
            type: 'boolean',
            description: 'The isMetered property',
            nullable: true
        },
        isShippable: {
            type: 'boolean',
            description: 'The isShippable property',
            nullable: true
        },
        mainDescription: {
            type: 'string',
            description: 'The mainDescription property',
            nullable: true
        },
        mainId: {
            type: 'string',
            description: 'The mainId property',
            nullable: true
        },
        mainName: {
            type: 'string',
            description: 'The mainName property',
            nullable: true
        },
        maximumQuantity: {
            type: 'integer',
            description: 'The maximumQuantity property',
            format: 'int32',
            nullable: true
        },
        meteredPriceFormula: {
            type: 'string',
            description: 'The meteredPriceFormula property',
            nullable: true
        },
        minimumQuantity: {
            type: 'integer',
            description: 'The minimumQuantity property',
            format: 'int32',
            nullable: true
        },
        name: {
            type: 'string',
            description: 'The name property',
            nullable: true
        },
        periodUnit: {
            '$ref': '#/components/schemas/EnecoProductPeriodUnit'
        },
        priceIsTaxExcluded: {
            type: 'boolean',
            description: 'The priceIsTaxExcluded property',
            nullable: true
        },
        priceModel: {
            '$ref': '#/components/schemas/EnecoProductPriceModel'
        },
        productId: {
            type: 'string',
            description: 'The productId property',
            nullable: true
        },
        quantity: {
            type: 'integer',
            description: 'The quantity property',
            format: 'int32',
            nullable: true
        },
        taxPercentage: {
            type: 'number',
            description: 'The taxPercentage property',
            format: 'double',
            nullable: true
        },
        unitPrice: {
            type: 'number',
            description: 'The unitPrice property',
            format: 'double',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco product.'
} as const;

export const $EnecoProductExtra = {
    type: 'object',
    properties: {
        billingPeriod: {
            type: 'integer',
            description: 'The billingPeriod property',
            format: 'int32',
            nullable: true
        },
        description: {
            type: 'string',
            description: 'The description property',
            nullable: true
        },
        discounts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoDiscount'
            },
            description: 'The discounts property',
            nullable: true
        },
        familyId: {
            type: 'string',
            description: 'The familyId property',
            nullable: true
        },
        features: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'The features property',
            nullable: true
        },
        isMetered: {
            type: 'boolean',
            description: 'The isMetered property',
            nullable: true
        },
        isShippable: {
            type: 'boolean',
            description: 'The isShippable property',
            nullable: true
        },
        mainDescription: {
            type: 'string',
            description: 'The mainDescription property',
            nullable: true
        },
        mainId: {
            type: 'string',
            description: 'The mainId property',
            nullable: true
        },
        mainName: {
            type: 'string',
            description: 'The mainName property',
            nullable: true
        },
        maximumQuantity: {
            type: 'integer',
            description: 'The maximumQuantity property',
            format: 'int32',
            nullable: true
        },
        meteredPriceFormula: {
            type: 'string',
            description: 'The meteredPriceFormula property',
            nullable: true
        },
        minimumQuantity: {
            type: 'integer',
            description: 'The minimumQuantity property',
            format: 'int32',
            nullable: true
        },
        name: {
            type: 'string',
            description: 'The name property',
            nullable: true
        },
        periodUnit: {
            '$ref': '#/components/schemas/EnecoProductExtraPeriodUnit'
        },
        priceIsTaxExcluded: {
            type: 'boolean',
            description: 'The priceIsTaxExcluded property',
            nullable: true
        },
        priceModel: {
            '$ref': '#/components/schemas/EnecoProductExtraPriceModel'
        },
        productId: {
            type: 'string',
            description: 'The productId property',
            nullable: true
        },
        quantity: {
            type: 'integer',
            description: 'The quantity property',
            format: 'int32',
            nullable: true
        },
        taxPercentage: {
            type: 'number',
            description: 'The taxPercentage property',
            format: 'double',
            nullable: true
        },
        unitPrice: {
            type: 'number',
            description: 'The unitPrice property',
            format: 'double',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Eneco product.'
} as const;

export const $EnecoProductExtraPeriodUnit = {
    enum: ['OneTime', 'Day', 'Week', 'Month', 'Year', 'Forever'],
    type: 'string',
    description: 'Eneco product extra period unit.'
} as const;

export const $EnecoProductExtraPriceModel = {
    enum: ['FlatFee', 'PerUnit', 'Tiered', 'Volume', 'Stairstep'],
    type: 'string',
    description: 'Eneco product extra price model.'
} as const;

export const $EnecoProductPeriodUnit = {
    enum: ['OneTime', 'Day', 'Week', 'Month', 'Year', 'Forever'],
    type: 'string',
    description: 'Eneco product period unit.'
} as const;

export const $EnecoProductPriceModel = {
    enum: ['FlatFee', 'PerUnit', 'Tiered', 'Volume', 'Stairstep'],
    type: 'string',
    description: 'Eneco product price model.'
} as const;

export const $EnergyMonitorFrequency = {
    enum: ['Never', 'Weekly', 'Biweekly', 'Monthly'],
    type: 'string',
    description: 'The carrier for invoices'
} as const;

export const $EnergyTypeDto = {
    enum: ['Electricity', 'Gas'],
    type: 'string',
    description: 'Type of energy that can be delivered'
} as const;

export const $ForgotEmailResponse = {
    type: 'object',
    properties: {
        email: {
            type: 'string',
            description: 'Obfuscated email to which an email was sent',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains forgot email details'
} as const;

export const $GenderDto = {
    enum: ['Unspecified', 'Male', 'Female'],
    type: 'string',
    description: "Gender of the CRM accounts' user"
} as const;

export const $GenderFormContactDto = {
    enum: ['Male', 'Female'],
    type: 'string',
    description: 'Kinds of gender'
} as const;

export const $GetAccountContactDetailsResponse = {
    type: 'object',
    properties: {
        accountNumber: {
            type: 'string',
            description: 'Identifier of the account',
            nullable: true
        },
        firstName: {
            type: 'string',
            description: 'First name of the account',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'Last name of the account',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'Email of the account',
            nullable: true
        },
        dateOfBirth: {
            type: 'string',
            description: 'Creation date of the account',
            format: 'date-time',
            nullable: true
        },
        createdDate: {
            type: 'string',
            description: 'Created date',
            format: 'date-time'
        },
        telephoneNumber: {
            type: 'string',
            description: 'Telephone number of the account',
            nullable: true
        },
        mobileNumber: {
            type: 'string',
            description: 'Mobile number of the account',
            nullable: true
        },
        gezinsBondNumber: {
            type: 'string',
            description: 'Gezinsbond number of the account',
            nullable: true
        },
        customerType: {
            '$ref': '#/components/schemas/CustomerTypeDto'
        },
        gender: {
            '$ref': '#/components/schemas/GenderDto'
        },
        language: {
            '$ref': '#/components/schemas/CustomerLanguageDto'
        },
        contactAddress: {
            '$ref': '#/components/schemas/ContactAddressDto'
        },
        companyData: {
            '$ref': '#/components/schemas/CompanyDataDto'
        },
        comfortBonusLevel: {
            '$ref': '#/components/schemas/ComfortBonusLevelDto'
        }
    },
    additionalProperties: false,
    description: 'Representation a CRM accounts contact details'
} as const;

export const $GetAccountContactPreferencesResponse = {
    type: 'object',
    properties: {
        invoicePreferences: {
            '$ref': '#/components/schemas/InvoiceContactPreference'
        },
        advancePayments: {
            type: 'boolean',
            description: 'Does the user want Advance invoices'
        },
        enecoNews: {
            type: 'boolean',
            description: 'Does the user want more information about Eneco'
        },
        promotions: {
            type: 'boolean',
            description: 'Doest the user want more info about Promotions or third party partners'
        },
        energyMonitorFrequency: {
            '$ref': '#/components/schemas/EnergyMonitorFrequency'
        },
        hasEBillingPromotion: {
            type: 'boolean',
            description: 'Whether the user has any active product that has an E-Billing requirement'
        }
    },
    additionalProperties: false,
    description: 'Representation a CRM accounts contact preferences'
} as const;

export const $GetAccountDetailsResponse = {
    type: 'object',
    properties: {
        crmAccountNumber: {
            type: 'string',
            description: 'account number of the crm account',
            nullable: true
        },
        alias: {
            type: 'string',
            description: 'the configured alias for the crm account',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Details of a CRM account from the viewpoint of the logged in user'
} as const;

export const $GetAddressResponse = {
    type: 'object',
    properties: {
        addresses: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetAddressResponseAddress'
            },
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'A container with dynamic pricing records'
} as const;

export const $GetAddressResponseAddress = {
    type: 'object',
    properties: {
        zipcode: {
            type: 'string',
            description: 'The zipCode',
            nullable: true
        },
        cityId: {
            type: 'integer',
            description: 'the unique city identification',
            format: 'int32',
            nullable: true
        },
        city: {
            type: 'string',
            description: 'The city',
            nullable: true
        },
        streetId: {
            type: 'integer',
            description: 'The unique street identification',
            format: 'int32',
            nullable: true
        },
        street: {
            type: 'string',
            description: 'The street',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'A dynamic price record'
} as const;

export const $GetAssetResponse = {
    required: ['assetType', 'id'],
    type: 'object',
    properties: {
        id: {
            type: 'string',
            description: 'Asset identifier',
            format: 'uuid'
        },
        assetType: {
            '$ref': '#/components/schemas/AssetTypeDto'
        },
        externalAssetId: {
            type: 'string',
            description: 'Asset identifier in external system',
            nullable: true
        },
        vehicle: {
            '$ref': '#/components/schemas/VehicleAssetResourceDto'
        }
    },
    additionalProperties: false,
    description: 'Single asset response'
} as const;

export const $GetAssetsResponse = {
    type: 'object',
    properties: {
        assets: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetAssetResponse'
            },
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Single asset response'
} as const;

export const $GetContractDetailsForProductResponse = {
    type: 'object',
    properties: {
        activeFrom: {
            type: 'string',
            description: 'When the contract becomes active',
            format: 'date'
        },
        activeUntil: {
            type: 'string',
            description: 'When the contract ends',
            format: 'date',
            nullable: true
        },
        contractId: {
            type: 'string',
            description: 'Id of the contract',
            nullable: true
        },
        type: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        contractProductStatusExternal: {
            '$ref': '#/components/schemas/ContractProductStatusDto'
        },
        productName: {
            type: 'string',
            description: 'Name of the product',
            nullable: true
        },
        address: {
            type: 'string',
            description: 'Address to which the product is delivered',
            nullable: true
        },
        addressId: {
            type: 'string',
            description: 'Address Id of the address',
            nullable: true
        },
        tariff: {
            '$ref': '#/components/schemas/GetContractDetailsForProductResponseTariff'
        },
        meter: {
            '$ref': '#/components/schemas/GetContractDetailsForProductResponseMeter'
        },
        gridOperator: {
            '$ref': '#/components/schemas/GetContractDetailsForProductResponseGridOperator'
        },
        renewal: {
            '$ref': '#/components/schemas/RenewalResponse'
        },
        tariffChartStartDate: {
            type: 'string',
            description: 'The start date of the tariff chart',
            format: 'date',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Details for a product within a contract.'
} as const;

export const $GetContractDetailsForProductResponseGridOperator = {
    type: 'object',
    properties: {
        name: {
            type: 'string',
            description: 'Name of the grid operator',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Describes the grid operator for this product'
} as const;

export const $GetContractDetailsForProductResponseMeter = {
    type: 'object',
    properties: {
        meterType: {
            '$ref': '#/components/schemas/MeterType'
        },
        ean: {
            type: 'string',
            description: 'EAN code for the meter',
            nullable: true
        },
        meterNumber: {
            type: 'string',
            description: 'Number of the meter',
            nullable: true
        },
        service: {
            '$ref': '#/components/schemas/MeterService'
        },
        meterRegime: {
            '$ref': '#/components/schemas/MeterRegimeHolder'
        },
        invoiceFrequency: {
            '$ref': '#/components/schemas/InvoiceFrequencyHolder'
        },
        registers: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RegisterHolder'
            },
            description: 'The registers that the meter contains',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Meter details for a given product'
} as const;

export const $GetContractDetailsForProductResponseTariff = {
    type: 'object',
    properties: {
        product: {
            type: 'string',
            description: 'The name of the product',
            nullable: true
        },
        productType: {
            '$ref': '#/components/schemas/ProductType'
        },
        promotions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/Promotion'
            },
            description: 'Discounts active on this product',
            nullable: true
        },
        isSocialTariff: {
            type: 'boolean',
            description: 'Is social tariff'
        },
        currentTariff: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/TariffDetails'
            },
            description: 'Links to the tariff card by language, which is currently active',
            nullable: true
        },
        nextTariff: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/TariffDetails'
            },
            description: 'Links to the tariff card by language, which will be active once the current tariffs expire',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Tariff details for a given product'
} as const;

export const $GetInvoiceDto = {
    type: 'object',
    properties: {
        invoiceId: {
            type: 'string',
            description: 'The invoice identification',
            nullable: true
        },
        invoiceType: {
            '$ref': '#/components/schemas/InvoiceTypeDto'
        },
        advanceInvoiceMonth: {
            type: 'string',
            description: 'The month to which the advance invoice relates',
            nullable: true
        },
        dueDate: {
            type: 'string',
            description: 'Due date of the invoice',
            format: 'date-time',
            nullable: true
        },
        sentDate: {
            type: 'string',
            description: 'Sent to the customer date',
            format: 'date-time',
            nullable: true
        },
        lastPaymentDate: {
            type: 'string',
            description: 'Date of last payment of the customer',
            format: 'date-time',
            nullable: true
        },
        invoiceDate: {
            type: 'string',
            description: 'Date when the invoice is created',
            format: 'date-time',
            nullable: true
        },
        dueAmount: {
            type: 'number',
            description: 'Due amount to pay',
            format: 'decimal'
        },
        paidAmount: {
            type: 'number',
            description: 'What amount has already been paid for this invoice?',
            format: 'decimal'
        },
        openAmount: {
            type: 'number',
            description: 'The amount that is till open to pay',
            format: 'decimal'
        },
        invoiceStatus: {
            '$ref': '#/components/schemas/InvoiceStatusDto'
        },
        paymentMethod: {
            '$ref': '#/components/schemas/PaymentMethodDto'
        },
        billInserts: {
            '$ref': '#/components/schemas/BillInsertListDto'
        },
        invoiceEnergyType: {
            '$ref': '#/components/schemas/InvoiceEnergyTypeDto'
        },
        downloadLink: {
            type: 'string',
            description: 'The link to the digital invoice file',
            nullable: true
        },
        billingAccountNumber: {
            type: 'string',
            description: 'The billing account the invoice belongs to',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Representation of a single invoice row'
} as const;

export const $GetInvoicesOpenAmountSummaryRecord = {
    type: 'object',
    properties: {
        billingAccountNumber: {
            type: 'string',
            description: 'The billing account number associated with the invoice',
            nullable: true
        },
        amount: {
            type: 'number',
            description: 'The amount outstanding for the billing account',
            format: 'decimal'
        },
        amountOfUnpaidInvoices: {
            type: 'integer',
            description: 'The number of unpaid invoices for the billing account',
            format: 'int32'
        },
        iban: {
            type: 'string',
            description: 'The iban number linked to this billing account',
            nullable: true
        },
        paymentMethod: {
            '$ref': '#/components/schemas/PaymentMethodDto'
        }
    },
    additionalProperties: false,
    description: 'Record containing open amount information for a billing account'
} as const;

export const $GetInvoicesOpenAmountSummaryResponse = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetInvoicesOpenAmountSummaryRecord'
            },
            description: 'Collection of open amount summary records',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Response containing a summary of open invoice amounts'
} as const;

export const $GetInvoicesResponse = {
    type: 'object',
    properties: {
        invoices: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetInvoiceDto'
            },
            description: 'Multiple invoice rows',
            nullable: true
        },
        openAmountSummary: {
            '$ref': '#/components/schemas/GetInvoicesOpenAmountSummaryResponse'
        }
    },
    additionalProperties: false,
    description: 'Paged response containing multiple invoice rows'
} as const;

export const $GetMonthlyInvoicePeriodDtoMeter = {
    type: 'object',
    properties: {
        energyType: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        ean: {
            type: 'string',
            description: 'EAN of the meter',
            nullable: true
        },
        period: {
            '$ref': '#/components/schemas/MonthlyInvoicePeriodDto'
        },
        crmAccountNumber: {
            type: 'string',
            description: 'Number that signifies CRM account',
            nullable: true
        },
        billingAccountNumber: {
            type: 'string',
            description: 'Number that signifies Billing account',
            nullable: true
        },
        contractId: {
            type: 'string',
            description: 'Contract Id associated with the meter',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'A meter with monthly invoice period'
} as const;

export const $GetNewAddressDetailsResponse = {
    type: 'object',
    properties: {
        address: {
            '$ref': '#/components/schemas/MoveAddressDto'
        }
    },
    additionalProperties: false,
    description: 'Response containing possible already filled in address for the new address'
} as const;

export const $GetOverviewForAddressResponse = {
    type: 'object',
    properties: {
        activeProducts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetOverviewForAddressResponseItem'
            },
            description: '',
            nullable: true
        },
        expiredProducts: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetOverviewForAddressResponseItem'
            },
            description: '',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Collection of active and non-active products for a specific address'
} as const;

export const $GetOverviewForAddressResponseItem = {
    type: 'object',
    properties: {
        contractId: {
            type: 'string',
            description: 'Id of the contract',
            nullable: true
        },
        type: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        contractProductStatusExternal: {
            '$ref': '#/components/schemas/ContractProductStatusDto'
        },
        productName: {
            type: 'string',
            description: 'Name of the product',
            nullable: true
        },
        startTime: {
            type: 'string',
            description: 'Contract start date',
            format: 'date'
        },
        endTime: {
            type: 'string',
            description: 'Optional contract end date',
            format: 'date',
            nullable: true
        },
        meterReadingMonth: {
            type: 'integer',
            description: 'Month in which meter gets read',
            format: 'int32',
            nullable: true
        },
        meterType: {
            '$ref': '#/components/schemas/MeterTypeDto'
        },
        renewalStatus: {
            '$ref': '#/components/schemas/ProductSwitchStatusResponse'
        }
    },
    additionalProperties: false,
    description: 'Response containing information about the contract and product'
} as const;

export const $GetProfileResponse = {
    type: 'object',
    properties: {
        hasLocalLogin: {
            type: 'boolean',
            description: 'Whether the user has local login details (okta username/password)'
        },
        hasItsmeLogin: {
            type: 'boolean',
            description: 'Whether the user has social login details (third party login provider such as itsme)'
        },
        displayName: {
            type: 'string',
            description: 'The display name for the user, aka. the MyEneco Voornaam or ME Voornaam',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'The email for the user which is configured in okta',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains the profile details for a user'
} as const;

export const $GetServiceDeliveryPointOverviewForAddressResponse = {
    type: 'object',
    properties: {
        serviceDeliveryPoints: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetServiceDeliveryPointOverviewForAddressResponseItem'
            },
            description: '',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Collection of all active service delivery points for a specific address'
} as const;

export const $GetServiceDeliveryPointOverviewForAddressResponseItem = {
    type: 'object',
    properties: {
        contractId: {
            type: 'string',
            description: 'Id of the contract',
            nullable: true
        },
        ean: {
            type: 'string',
            description: 'The ean number',
            nullable: true
        },
        meterType: {
            '$ref': '#/components/schemas/MeterType'
        },
        type: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        meterRegisterTypes: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RegisterType'
            },
            description: 'Which registers the meter has',
            nullable: true
        },
        meterRegime: {
            '$ref': '#/components/schemas/MeterRegimeHolder'
        },
        invoiceFrequency: {
            '$ref': '#/components/schemas/InvoiceFrequencyHolder'
        }
    },
    additionalProperties: false,
    description: 'Response containing information about the contract and product'
} as const;

export const $GetSubscriptionResponse = {
    required: ['assets', 'id', 'startDate', 'status', 'tier', 'type'],
    type: 'object',
    properties: {
        id: {
            type: 'string',
            description: 'Subscription identifier',
            format: 'uuid'
        },
        type: {
            '$ref': '#/components/schemas/SubscriptionTypeDto'
        },
        tier: {
            '$ref': '#/components/schemas/SubscriptionTierDto'
        },
        startDate: {
            type: 'string',
            description: 'Start date of subscription',
            format: 'date-time'
        },
        endDate: {
            type: 'string',
            description: 'Optional end date of subscription',
            format: 'date-time',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/SubscriptionStatusDto'
        },
        assets: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetAssetResponse'
            },
            description: 'List of assets for this subscription'
        }
    },
    additionalProperties: false,
    description: 'Single subscription response'
} as const;

export const $GetSubscriptionsResponse = {
    type: 'object',
    properties: {
        subscriptions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetSubscriptionResponse'
            },
            description: 'List of subscriptions',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Get all subscriptions response'
} as const;

export const $GlobalAmountSimulationRecord = {
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'The EAN of the meter',
            nullable: true
        },
        contractNumber: {
            type: 'string',
            description: 'The contract number',
            nullable: true
        },
        amount: {
            type: 'integer',
            description: 'The simulated amount for the specified EAN and contract',
            format: 'int32'
        }
    },
    additionalProperties: false,
    description: 'Represents a record containing details of a global amount simulation.'
} as const;

export const $GlobalAmountSimulationResponse = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GlobalAmountSimulationRecord'
            },
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the response for a global amount simulation operation.'
} as const;

export const $ImpersonationRequest = {
    type: 'object',
    properties: {
        value: {
            type: 'string',
            nullable: true
        },
        type: {
            '$ref': '#/components/schemas/ImpersonationTypeDto'
        }
    },
    additionalProperties: false,
    description: 'Contains details about which person is being impersonated'
} as const;

export const $ImpersonationResponse = {
    type: 'object',
    properties: {
        actsAsIdentityProviderId: {
            type: 'string',
            description: 'Id in Okta of the account being impersonated',
            nullable: true
        },
        actsAsIdentityProviderDisplayName: {
            type: 'string',
            description: 'Display name in Okta of the account being impersonated',
            nullable: true
        },
        impersonatorSubject: {
            type: 'string',
            description: 'Email in Okta of the impersonator',
            nullable: true
        },
        impersonatorIdentityProviderId: {
            type: 'string',
            description: 'Id in Okta of the impersonator',
            nullable: true
        },
        impersonationType: {
            '$ref': '#/components/schemas/ImpersonationTypeDto'
        },
        impersonationRole: {
            '$ref': '#/components/schemas/ImpersonationRoleDto'
        },
        crmAccountClaims: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CrmAccountClaimDto'
            },
            description: 'Collection of account claim entries',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains the response of the impersonation flow'
} as const;

export const $ImpersonationRoleDto = {
    enum: ['Read', 'Write'],
    type: 'string',
    description: 'Role that the impersonator gets'
} as const;

export const $ImpersonationTypeDto = {
    enum: ['Email', 'Account'],
    type: 'string',
    description: 'The way of impersonation'
} as const;

export const $InvoiceContactPreference = {
    enum: ['Email', 'Post'],
    type: 'string',
    description: 'The carrier for invoices'
} as const;

export const $InvoiceEnergyTypeDto = {
    enum: ['Electricity', 'Gas', 'Both', 'Unknown'],
    type: 'string',
    description: 'Current status of the invoice'
} as const;

export const $InvoiceFrequency = {
    enum: ['Monthly', 'Yearly'],
    type: 'string',
    description: 'The Invoice frequency'
} as const;

export const $InvoiceFrequencyDto = {
    enum: ['Monthly', 'Yearly'],
    type: 'string',
    description: 'The frequency of the invoice'
} as const;

export const $InvoiceFrequencyHolder = {
    type: 'object',
    properties: {
        changeRequestPending: {
            type: 'boolean',
            description: 'Whether a invoice frequency change request is ongoing'
        },
        current: {
            '$ref': '#/components/schemas/InvoiceFrequency'
        },
        requested: {
            '$ref': '#/components/schemas/InvoiceFrequency'
        }
    },
    additionalProperties: false,
    description: 'Holder that has the current and requested invoice frequency and information about ongoing invoice frequency change requests'
} as const;

export const $InvoiceInfo = {
    type: 'object',
    properties: {
        dueAmount: {
            type: 'number',
            description: 'The dueAmount property',
            format: 'double',
            nullable: true
        },
        dueDate: {
            type: 'string',
            description: 'The dueDate property',
            format: 'date-time',
            nullable: true
        },
        dunningStatus: {
            '$ref': '#/components/schemas/DunningStatus'
        },
        invoiceAmount: {
            type: 'number',
            description: 'The invoiceAmount property',
            format: 'double',
            nullable: true
        },
        invoiceDate: {
            type: 'string',
            description: 'The invoiceDate property',
            format: 'date-time',
            nullable: true
        },
        invoiceNumber: {
            type: 'string',
            description: 'The invoiceNumber property',
            nullable: true
        },
        lines: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/InvoiceLineInfo'
            },
            description: 'The lines property',
            nullable: true
        },
        paymentFailureReasonCode: {
            type: 'string',
            description: 'The paymentFailureReasonCode property',
            nullable: true
        },
        paymentFailureReasonMessage: {
            type: 'string',
            description: 'The paymentFailureReasonMessage property',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/InvoiceStatus'
        }
    },
    additionalProperties: false,
    description: 'Invoice info response'
} as const;

export const $InvoiceLineInfo = {
    type: 'object',
    properties: {
        discountAmount: {
            type: 'number',
            description: 'The discountAmount property',
            format: 'double',
            nullable: true
        },
        itemName: {
            type: 'string',
            description: 'The itemName property',
            nullable: true
        },
        quantity: {
            type: 'integer',
            description: 'The quantity property',
            format: 'int32',
            nullable: true
        },
        servicePeriodEnd: {
            type: 'string',
            description: 'The servicePeriodEnd property',
            format: 'date-time',
            nullable: true
        },
        servicePeriodStart: {
            type: 'string',
            description: 'The servicePeriodStart property',
            format: 'date-time',
            nullable: true
        },
        subscriptionId: {
            type: 'string',
            description: 'The subscriptionId property',
            nullable: true
        },
        totalAmount: {
            type: 'number',
            description: 'The totalAmount property',
            format: 'double',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Invoice line response'
} as const;

export const $InvoiceStatus = {
    enum: ['Posted', 'Pending', 'Paid', 'Overdue', 'NotPaid', 'Voided'],
    type: 'string',
    description: 'Invoice Status'
} as const;

export const $InvoiceStatusDto = {
    enum: ['New', 'Paid', 'Unpaid', 'PartiallyPaid', 'PaymentInProgress'],
    type: 'string',
    description: 'Current status of the invoice'
} as const;

export const $InvoiceTypeDto = {
    enum: ['AdvanceInvoice', 'YearlyBill', 'ClosingBill', 'Invoice', 'CorrectionInvoice', 'IntermediaryInvoice', 'CreditNote', 'MonthlyBill', 'SelfBill'],
    type: 'string',
    description: 'Type of invoice'
} as const;

export const $LanguageCodeDto = {
    enum: ['NL', 'FR', 'DE'],
    type: 'string',
    description: 'Supported language codes'
} as const;

export const $LinkItsmeRequest = {
    type: 'object',
    properties: {
        itsmeId: {
            type: 'string',
            description: '',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Request which allows a user to link an itsme account to the okta account'
} as const;

export const $ListContractAddressesResponse = {
    type: 'object',
    properties: {
        addresses: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListContractAddressesResponseItem'
            },
            description: 'Collection of addresses',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Response containing a collection of addresses'
} as const;

export const $ListContractAddressesResponseItem = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            description: 'Internal id of the address',
            nullable: true
        },
        address: {
            type: 'string',
            description: 'The address',
            nullable: true
        },
        details: {
            '$ref': '#/components/schemas/AddressDetailsDto'
        }
    },
    additionalProperties: false,
    description: 'Response containing address information'
} as const;

export const $LocationDto = {
    required: ['latitude', 'longitude'],
    type: 'object',
    properties: {
        latitude: {
            type: 'number',
            description: 'Latitude',
            format: 'double'
        },
        longitude: {
            type: 'number',
            description: 'Longitude',
            format: 'double'
        }
    },
    additionalProperties: false,
    description: 'Location coordinates'
} as const;

export const $LocationType = {
    enum: ['Old', 'New'],
    type: 'string',
    description: 'Move location'
} as const;

export const $MeterDeviationRequest = {
    required: ['ean', 'meterValues'],
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'The ean of the meter to rectify',
            nullable: true
        },
        meterValues: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MeterRectificationValueRequest'
            },
            description: 'Gets or sets the value of the meter values',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The meter deviation request class'
} as const;

export const $MeterReadingIndexValue = {
    type: 'object',
    properties: {
        value: {
            type: 'integer',
            description: 'Gets or sets the value of the meter reading index value.',
            format: 'int32'
        },
        meterTimeFrameType: {
            '$ref': '#/components/schemas/TimeframeTypeCodeDto'
        },
        direction: {
            '$ref': '#/components/schemas/DirectionTypeCodeDto'
        },
        reason: {
            '$ref': '#/components/schemas/Reason'
        }
    },
    additionalProperties: false,
    description: 'Represents the index value of a meter reading, including the value itself and the associated time frame type.'
} as const;

export const $MeterReadingResponse = {
    type: 'object',
    properties: {
        ean: {
            maxLength: 50,
            type: 'string',
            description: 'Gets or sets the value of the ean',
            nullable: true
        },
        indexValues: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MeterReadingIndexValue'
            },
            description: 'Gets or sets the list of index values associated with the meter reading.',
            nullable: true
        },
        date: {
            type: 'string',
            description: 'Gets or sets the value of the date',
            format: 'date'
        },
        meterReadingType: {
            '$ref': '#/components/schemas/MeterReadingType'
        },
        registeredDate: {
            type: 'string',
            description: 'Gets or sets the value of the registered date',
            format: 'date'
        },
        utilityType: {
            '$ref': '#/components/schemas/UtilityType'
        }
    },
    additionalProperties: false,
    description: 'The meter reading response class'
} as const;

export const $MeterReadingType = {
    enum: ['Official', 'Informative', 'CustomerAdded', 'RLP'],
    type: 'string',
    description: 'The meter reading type enum'
} as const;

export const $MeterRectificationValueRequest = {
    type: 'object',
    properties: {
        contestedReadingValue: {
            type: 'integer',
            description: 'The rectified reading value',
            format: 'int32'
        },
        originalDate: {
            type: 'string',
            description: 'Date of the original observation of the rectified value',
            format: 'date'
        },
        contestedDate: {
            type: 'string',
            description: 'Date of observation of the rectified value',
            format: 'date'
        },
        meterTimeFrameType: {
            '$ref': '#/components/schemas/TimeframeTypeCodeDto'
        }
    },
    additionalProperties: false,
    description: 'Request containing the rectified values compared to the original values'
} as const;

export const $MeterRegime = {
    enum: ['NotApplicable', 'Smr1', 'Smr3'],
    type: 'string',
    description: 'The meter regime'
} as const;

export const $MeterRegimeHolder = {
    type: 'object',
    properties: {
        changeRequestPending: {
            type: 'boolean',
            description: 'Whether a meter regime change request is ongoing'
        },
        current: {
            '$ref': '#/components/schemas/MeterRegime'
        },
        requested: {
            '$ref': '#/components/schemas/MeterRegime'
        }
    },
    additionalProperties: false,
    description: 'Holder that has the current and requested meter regimes and information about ongoing meter regime change requests'
} as const;

export const $MeterService = {
    enum: ['PureOfftake', 'PureInjection', 'Compensation', 'ValorisationInjection', 'CommercialisationConstraintInjection', 'CommercialisationInjection'],
    type: 'string',
    description: 'Kind of service being delivered by the meter'
} as const;

export const $MeterType = {
    enum: ['Analogue', 'Digital', 'DigitalNightExclusive'],
    type: 'string',
    description: 'Kind of meter'
} as const;

export const $MeterTypeDto = {
    enum: ['Analogue', 'Digital', 'DigitalNightExclusive'],
    type: 'string',
    description: 'Physical Meter type'
} as const;

export const $MonthlyInvoicePeriodDto = {
    enum: ['NoPreference', 'Between4And11', 'Between11And18', 'Between21And25', 'Between28And4'],
    type: 'string',
    description: 'Current invoice period for the monthly invoices'
} as const;

export const $MonthlyInvoicePreferenceDto = {
    type: 'object',
    properties: {
        meters: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetMonthlyInvoicePeriodDtoMeter'
            },
            description: 'Meters to configure the preferences for',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Body containing Meters to configure the preferences for'
} as const;

export const $MoveAddressDto = {
    type: 'object',
    properties: {
        postalCode: {
            type: 'string',
            description: 'Postal code of the address',
            nullable: true
        },
        municipality: {
            type: 'string',
            description: 'Municipality of the address',
            nullable: true
        },
        street: {
            type: 'string',
            description: 'Street of the address',
            nullable: true
        },
        houseNumber: {
            type: 'string',
            description: 'House number of the address',
            nullable: true
        },
        bus: {
            type: 'string',
            description: 'Bus of the address',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Move address'
} as const;

export const $MoveEnergyTypeDto = {
    enum: ['Gas', 'Electricity'],
    type: 'string',
    description: 'Energy types that can get transferred'
} as const;

export const $MoveFileDetailResponse = {
    type: 'object',
    properties: {
        status: {
            '$ref': '#/components/schemas/MoveFileStatusDto'
        },
        flow: {
            '$ref': '#/components/schemas/MoveFlowDto'
        },
        reason: {
            '$ref': '#/components/schemas/MoveReasonDto'
        },
        moveRequestId: {
            type: 'string',
            description: 'External request id',
            nullable: true
        },
        statusHints: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/StatusHintDto'
            },
            description: 'Status hints',
            nullable: true
        },
        oldAddress: {
            '$ref': '#/components/schemas/MoveViewDetailOldAddressSectionResponse'
        },
        newAddress: {
            '$ref': '#/components/schemas/MoveViewDetailNewAddressSectionResponse'
        },
        contactDetails: {
            '$ref': '#/components/schemas/MoveViewDetailContactDetailsSectionResponse'
        },
        contractDetails: {
            '$ref': '#/components/schemas/MoveViewDetailContractDetailsSectionResponse'
        },
        lastSubmittedStep: {
            '$ref': '#/components/schemas/MoveStepDto'
        },
        nextStep: {
            '$ref': '#/components/schemas/MoveStepDto'
        },
        isLocked: {
            type: 'boolean',
            description: 'Is move file locked'
        }
    },
    additionalProperties: false,
    description: 'Details of the move file'
} as const;

export const $MoveFileStatusDto = {
    enum: ['Incomplete', 'InProgress', 'Closed', 'Cancelled', 'None'],
    type: 'string',
    description: 'Status of the move file'
} as const;

export const $MoveFlowDto = {
    enum: ['ContinueFlow', 'CancelFlow'],
    type: 'string',
    description: 'For which flow the move was initiated for'
} as const;

export const $MoveReasonDto = {
    enum: ['MoveAndKeepEneco', 'MoveInWithSomeoneElse', 'MoveIntoResidentialCareCenter', 'MoveToOtherCountry', 'MoveAndLeaveEneco', 'Other'],
    type: 'string',
    description: 'Reasons why people initiate a move'
} as const;

export const $MoveRegisterDto = {
    type: 'object',
    properties: {
        timeframeTypeCode: {
            '$ref': '#/components/schemas/TimeframeTypeCodeDto'
        },
        directionTypeCode: {
            '$ref': '#/components/schemas/DirectionTypeCodeDto'
        },
        meterReading: {
            type: 'number',
            description: 'Meter reading',
            format: 'decimal',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'A meter register'
} as const;

export const $MoveStepDto = {
    enum: ['MoveReasons', 'Eod', 'NewAddress', 'NewAddressKeyTransfer', 'OldAddressKeyTransfer', 'ConfirmationOverview', 'Done', 'OldAddressMeters', 'CorrespondenceAddress', 'EodNewAddress', 'EodOldAddress'],
    type: 'string',
    description: 'Collection of move file steps'
} as const;

export const $MoveViewDetailAddressMeterResponse = {
    type: 'object',
    properties: {
        energyType: {
            '$ref': '#/components/schemas/MoveEnergyTypeDto'
        },
        ean: {
            type: 'string',
            description: 'EAN of the meter',
            nullable: true
        },
        meterNumber: {
            type: 'string',
            description: 'Meter number',
            nullable: true
        },
        isDigital: {
            type: 'boolean',
            description: 'Is digital meter'
        },
        registers: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MoveRegisterDto'
            },
            description: 'Registers linked to the meter',
            nullable: true
        },
        isSealed: {
            type: 'boolean',
            description: 'Whether meters are sealed, set by PreSwitch Light',
            nullable: true
        },
        serviceComponent: {
            type: 'string',
            description: 'Service component, set by PreSwitch Light',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains information about a meter'
} as const;

export const $MoveViewDetailContactDetailsSectionResponse = {
    type: 'object',
    properties: {
        customCorrespondenceAddress: {
            '$ref': '#/components/schemas/CorrespondenceAddressDto'
        },
        contactCorrespondenceAddress: {
            '$ref': '#/components/schemas/CorrespondenceAddressDto'
        },
        selectedCorrespondenceAddress: {
            '$ref': '#/components/schemas/SelectedCorrespondenceAddressDto'
        },
        correspondenceDate: {
            type: 'string',
            description: 'Correspondence date',
            format: 'date',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains contact level information'
} as const;

export const $MoveViewDetailContactResponse = {
    type: 'object',
    properties: {
        representation: {
            '$ref': '#/components/schemas/Representation'
        },
        firstName: {
            type: 'string',
            description: 'First name',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'Last name',
            nullable: true
        },
        phoneNumber: {
            type: 'string',
            description: 'Phone number',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'Email',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains contact information of the old address'
} as const;

export const $MoveViewDetailContractDetailsSectionResponse = {
    type: 'object',
    properties: {
        firstName: {
            type: 'string',
            description: 'First name',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'Last name',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains contract level information'
} as const;

export const $MoveViewDetailNewAddressSectionResponse = {
    type: 'object',
    properties: {
        status: {
            '$ref': '#/components/schemas/MoveFileStatusDto'
        },
        newAddress: {
            '$ref': '#/components/schemas/MoveAddressDto'
        },
        deliveryDate: {
            type: 'string',
            description: 'Delivery date',
            format: 'date',
            nullable: true
        },
        vacancy: {
            type: 'string',
            description: 'Vacancy date',
            format: 'date',
            nullable: true
        },
        eod: {
            '$ref': '#/components/schemas/EODFileUploadResponse'
        },
        meters: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MoveViewDetailAddressMeterResponse'
            },
            description: 'Collection of meter details',
            nullable: true
        },
        contact: {
            '$ref': '#/components/schemas/MoveViewDetailContactResponse'
        },
        isLocked: {
            type: 'boolean',
            description: 'Is the new address section locked'
        },
        initiatingLeavingCustomerDate: {
            type: 'string',
            description: 'Initiating leaving customer date set PreSwitch light',
            format: 'date',
            nullable: true
        },
        suggestedEans: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SuggestedEanResponse'
            },
            description: 'List of the suggested EANs',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Detail section of new address'
} as const;

export const $MoveViewDetailOldAddressSectionResponse = {
    type: 'object',
    properties: {
        status: {
            '$ref': '#/components/schemas/MoveFileStatusDto'
        },
        oldAddress: {
            '$ref': '#/components/schemas/MoveAddressDto'
        },
        keyTransfer: {
            type: 'string',
            description: 'Date of key transfer',
            format: 'date',
            nullable: true
        },
        vacancy: {
            type: 'string',
            description: 'Vacancy date',
            format: 'date',
            nullable: true
        },
        eod: {
            '$ref': '#/components/schemas/EODFileUploadResponse'
        },
        meters: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MoveViewDetailAddressMeterResponse'
            },
            description: 'Collection of meter details',
            nullable: true
        },
        contact: {
            '$ref': '#/components/schemas/MoveViewDetailContactResponse'
        },
        isLocked: {
            type: 'boolean',
            description: 'Is the old address section locked'
        }
    },
    additionalProperties: false,
    description: 'Detail section of old address'
} as const;

export const $OrderCustomer = {
    type: 'object',
    properties: {
        billingAddress: {
            '$ref': '#/components/schemas/Address'
        },
        companyName: {
            type: 'string',
            description: 'The companyName property',
            nullable: true
        },
        customerId: {
            type: 'string',
            description: 'The customerId property',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'The email property',
            nullable: true
        },
        firstName: {
            type: 'string',
            description: 'The firstName property',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'The lastName property',
            nullable: true
        },
        phone: {
            type: 'string',
            description: 'The phone property',
            nullable: true
        },
        vatNumber: {
            type: 'string',
            description: 'The vatNumber property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Customer'
} as const;

export const $OrderCustomerInfo = {
    type: 'object',
    properties: {
        activeFeatures: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'The activeFeatures property',
            nullable: true
        },
        billingAddress: {
            '$ref': '#/components/schemas/Address'
        },
        companyName: {
            type: 'string',
            description: 'The companyName property',
            nullable: true
        },
        customerId: {
            type: 'string',
            description: 'The Eneco ID of the customer',
            nullable: true
        },
        dongleOrderDate: {
            type: 'string',
            description: 'The dongleOrderDate property',
            format: 'date-time',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'The email property',
            nullable: true
        },
        firstName: {
            type: 'string',
            description: 'The firstName property',
            nullable: true
        },
        hasUnpaidInvoices: {
            type: 'boolean',
            description: 'The hasUnpaidInvoices property',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'The lastName property',
            nullable: true
        },
        oldFeatures: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'The oldFeatures property',
            nullable: true
        },
        phone: {
            type: 'string',
            description: 'The phone property',
            nullable: true
        },
        smartStart: {
            type: 'string',
            description: 'The smartStart property',
            format: 'date-time',
            nullable: true
        },
        smartStatus: {
            '$ref': '#/components/schemas/OrderCustomerInfoSmartStatus'
        },
        vatNumber: {
            type: 'string',
            description: 'The vatNumber property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Order customer info.'
} as const;

export const $OrderCustomerInfoSmartStatus = {
    enum: ['Unknown', 'New', 'InTrial', 'Future', 'Active', 'Paused', 'NonRenewing', 'Cancelled', 'Archived'],
    type: 'string',
    description: 'Order customer info smart status.'
} as const;

export const $OrderInfoRequest = {
    type: 'object',
    properties: {
        campaign: {
            type: 'string',
            description: 'The campaign property',
            nullable: true
        },
        cancelUrl: {
            type: 'string',
            description: 'The cancelUrl property',
            nullable: true
        },
        couponCode: {
            type: 'string',
            description: 'The couponCode property',
            nullable: true
        },
        currencyCode: {
            type: 'string',
            description: 'The currencyCode property',
            nullable: true
        },
        customer: {
            '$ref': '#/components/schemas/OrderCustomer'
        },
        locale: {
            type: 'string',
            description: 'The locale property',
            nullable: true
        },
        orderReference: {
            type: 'string',
            description: 'The orderReference property',
            nullable: true
        },
        products: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoOrderProduct'
            },
            description: 'The products property',
            nullable: true
        },
        redirectUrl: {
            type: 'string',
            description: 'The redirectUrl property',
            nullable: true
        },
        shippingAddress: {
            '$ref': '#/components/schemas/Address'
        }
    },
    additionalProperties: false,
    description: 'Order info request'
} as const;

export const $OwnerAccountLinkingRequest = {
    type: 'object',
    properties: {
        lastName: {
            type: 'string',
            description: 'Last name of the account',
            nullable: true
        },
        alias: {
            type: 'string',
            description: 'Alias for the crm account',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Body containing details to link an owner account'
} as const;

export const $PaymentMethodByBillingAccountResponse = {
    type: 'object',
    properties: {
        billingAccountNumber: {
            type: 'string',
            description: 'Billing account number',
            nullable: true
        },
        hasEbillingRequirement: {
            type: 'boolean',
            description: 'Whether the user currently has an E-billing promo running and has the possibility of losing the promo when changing payment method'
        },
        hasDirectDebitRequirement: {
            type: 'boolean',
            description: 'Whether the user currently has an Direct Debit promo running and has the possibility of losing the promo when changing payment method'
        },
        currentPaymentMethod: {
            '$ref': '#/components/schemas/PaymentMethodDto'
        },
        iban: {
            type: 'string',
            description: 'IBAN details',
            nullable: true
        },
        signatureLocation: {
            type: 'string',
            description: 'Location on which signature was made',
            nullable: true
        },
        signatureDate: {
            type: 'string',
            description: 'Date on which signature was made',
            format: 'date',
            nullable: true
        },
        mandateId: {
            type: 'string',
            description: 'Mandate identifier',
            nullable: true
        },
        meters: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/BillingAccountRelatedMeterResponse'
            },
            description: 'Billing account related meters',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains payment method information per Billing account'
} as const;

export const $PaymentMethodDto = {
    enum: ['DirectDebit', 'BankTransfer', 'DirectDebitExcludingYearlyBill'],
    type: 'string',
    description: 'The way the invoice gets paid'
} as const;

export const $PaymentMethodInfo = {
    type: 'object',
    properties: {
        lastFourDigits: {
            type: 'string',
            description: 'The lastFourDigits property',
            nullable: true
        },
        methodType: {
            '$ref': '#/components/schemas/PaymentMethodType'
        },
        status: {
            '$ref': '#/components/schemas/PaymentStatus'
        }
    },
    additionalProperties: false,
    description: 'Payment method information'
} as const;

export const $PaymentMethodResponse = {
    type: 'object',
    properties: {
        items: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PaymentMethodByBillingAccountResponse'
            },
            description: 'Payment methods per billing account',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains payment method information'
} as const;

export const $PaymentMethodType = {
    enum: ['Creditcard', 'Bancontact', 'Other'],
    type: 'string',
    description: 'Payment method type'
} as const;

export const $PaymentPlanDto = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            description: 'Id of the payment plan',
            nullable: true
        },
        start: {
            type: 'string',
            description: 'Start of the payment plan',
            format: 'date'
        },
        end: {
            type: 'string',
            description: 'End of the payment plan',
            format: 'date'
        },
        openAmount: {
            type: 'number',
            description: 'Open amount of the payment plan',
            format: 'decimal'
        },
        amountOfSlices: {
            type: 'integer',
            description: 'Amount of slices in the payment plan',
            format: 'int32'
        },
        slices: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PaymentPlanSliceDto'
            },
            description: 'Slices in the payment plan',
            nullable: true
        },
        invoices: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/GetInvoiceDto'
            },
            description: 'Invoices to be paid through the payment plan',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/PaymentPlanStatusDto'
        },
        maxAmountOfSlices: {
            type: 'integer',
            description: 'Max amount of slices of the payment plan',
            format: 'int32',
            nullable: true
        },
        billingAccountNumber: {
            type: 'string',
            description: 'Billing account for which the payment plan has been created',
            nullable: true
        },
        paymentMethod: {
            '$ref': '#/components/schemas/PaymentMethodDto'
        }
    },
    additionalProperties: false,
    description: 'A payment plan'
} as const;

export const $PaymentPlanEligibilityConditions = {
    type: 'object',
    properties: {
        hasDeactivatedPaymentPlanInLastThreeMonths: {
            type: 'boolean',
            description: 'Whether there is a deactivated payment plan in the last three months'
        },
        moreThanMinimumAmount: {
            type: 'boolean',
            description: 'Whether the amount is more than the minimum amount'
        },
        moreThanMaximumAmount: {
            type: 'boolean',
            description: 'Whether the amount is more than the maximum amount'
        },
        hasExistingPaymentPlan: {
            type: 'boolean',
            description: 'Whether there is an existing payment plan'
        },
        hasDunning: {
            type: 'boolean',
            description: 'Whether the is a dunning level of 4'
        },
        hasNoClosingOrYearlyBill: {
            type: 'boolean',
            description: 'Whether there is no closing or yearly bill'
        }
    },
    additionalProperties: false,
    description: 'Conditions on which the payment plan eligibility gets determined'
} as const;

export const $PaymentPlanEligibilityResponse = {
    type: 'object',
    properties: {
        conditions: {
            '$ref': '#/components/schemas/PaymentPlanEligibilityConditions'
        },
        isEligible: {
            type: 'boolean',
            description: 'Whether the user is eligible'
        },
        billingAccountNumbers: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'Billing account numbers',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Body containing information on whether a user can request a payment plan or nots'
} as const;

export const $PaymentPlanSliceDto = {
    type: 'object',
    properties: {
        start: {
            type: 'string',
            description: 'Start of the slice',
            format: 'date'
        },
        end: {
            type: 'string',
            description: 'End of the slice',
            format: 'date'
        },
        amount: {
            type: 'number',
            description: 'Amount to be paid for the slice',
            format: 'decimal'
        },
        paid: {
            type: 'boolean',
            description: 'Whether the slice has been paid or not',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Slice of a payment plan'
} as const;

export const $PaymentPlanStatusDto = {
    enum: ['Activated', 'Revoked', 'Completed', 'Simulation', 'Pending'],
    type: 'string',
    description: 'Current status of the payment plan'
} as const;

export const $PaymentPlansResponse = {
    type: 'object',
    properties: {
        paymentPlans: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PaymentPlanDto'
            },
            description: 'Payment plans',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Response containing one or more payment plans'
} as const;

export const $PaymentStatus = {
    enum: ['Valid', 'Expiring', 'Expired', 'Invalid', 'PendingVerification'],
    type: 'string',
    description: 'Payment status'
} as const;

export const $PeakAverageResponse = {
    type: 'object',
    properties: {
        average: {
            type: 'number',
            description: 'Average value',
            format: 'decimal'
        }
    },
    additionalProperties: false,
    description: 'Response containing the average value of peak data.'
} as const;

export const $PeakPeriodDto = {
    type: 'object',
    properties: {
        periodStart: {
            type: 'string',
            description: 'Start of the period',
            format: 'date-time'
        },
        periodEnd: {
            type: 'string',
            description: 'End of the period',
            format: 'date-time'
        },
        date: {
            type: 'string',
            description: 'Time of the actual peak value',
            format: 'date-time'
        },
        value: {
            type: 'number',
            description: 'Value measured',
            format: 'decimal'
        }
    },
    additionalProperties: false,
    description: 'Period of peak values'
} as const;

export const $PeakResponse = {
    type: 'object',
    properties: {
        consumptionType: {
            '$ref': '#/components/schemas/ConsumptionTypeDto'
        },
        timeFrame: {
            '$ref': '#/components/schemas/TimeFrameDto'
        },
        startDate: {
            type: 'string',
            description: 'The start date of the current view period',
            format: 'date'
        },
        endDate: {
            type: 'string',
            description: 'The end date of the current view period',
            format: 'date'
        },
        unitOfMeasure: {
            '$ref': '#/components/schemas/UnitOfMeasureDto'
        },
        periods: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PeakPeriodDto'
            },
            description: 'Periods in which peak values are available',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Response containing peak values'
} as const;

export const $PlanPriceModel = {
    enum: ['FixedAmount', 'PerUnit', 'Tiered', 'PerUnitMetered', 'TieredMetered', 'FixedPercentage'],
    type: 'string',
    description: 'Plan price model'
} as const;

export const $PriceAggregationDto = {
    enum: ['Hourly', 'Daily', 'Monthly'],
    type: 'string',
    description: 'Type of price aggregation'
} as const;

export const $PriceModel = {
    enum: ['FixedAmount', 'PerUnit', 'Tiered', 'PerUnitMetered', 'TieredMetered', 'FixedPercentage'],
    type: 'string',
    description: 'Price model'
} as const;

export const $PriceSimulationCompensationTypeDto = {
    enum: ['Offtake', 'Compensation', 'Valorisation'],
    type: 'string',
    description: 'Represents the compensationtype (service component)'
} as const;

export const $PriceSimulationDetailsDto = {
    type: 'object',
    properties: {
        priceSimulationId: {
            type: 'string',
            format: 'uuid'
        },
        accountNumber: {
            type: 'string',
            nullable: true
        },
        isProspect: {
            type: 'boolean'
        },
        customerType: {
            '$ref': '#/components/schemas/CustomerTypeDto'
        },
        postalCode: {
            type: 'string',
            nullable: true
        },
        energyType: {
            '$ref': '#/components/schemas/PriceSimulationEnergyTypeDto'
        },
        usage: {
            '$ref': '#/components/schemas/PriceSimulationUsageDto'
        },
        features: {
            '$ref': '#/components/schemas/PriceSimulationFeaturesDto'
        }
    },
    additionalProperties: false,
    description: 'Represents the details of a price simulation.'
} as const;

export const $PriceSimulationElectricityDefaultUsageDto = {
    type: 'object',
    properties: {
        totalUsage: {
            type: 'integer',
            description: 'The total electricity usage.',
            format: 'int32'
        },
        exclusiveNightUsage: {
            type: 'integer',
            description: 'The electricity usage exclusively during night hours, nullable if not specified.',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the default electricity usage details for price simulation.'
} as const;

export const $PriceSimulationElectricityMultipleUsageDto = {
    type: 'object',
    properties: {
        peakUsage: {
            type: 'integer',
            description: 'The electricity usage during peak hours.',
            format: 'int32'
        },
        offPeakUsage: {
            type: 'integer',
            description: 'The electricity usage during off-peak hours.',
            format: 'int32'
        }
    },
    additionalProperties: false,
    description: 'Represents the electricity usage details for a multiple meter configuration in price simulation.'
} as const;

export const $PriceSimulationElectricitySingularUsageDto = {
    type: 'object',
    properties: {
        totalUsage: {
            type: 'integer',
            description: 'The total electricity usage.',
            format: 'int32'
        },
        exclusiveNightUsage: {
            type: 'integer',
            description: 'The electricity usage exclusively during night hours, nullable if not specified.',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the electricity usage details for a singular meter configuration in price simulation.'
} as const;

export const $PriceSimulationElectricityUsageDto = {
    type: 'object',
    properties: {
        defaultUsage: {
            '$ref': '#/components/schemas/PriceSimulationElectricityDefaultUsageDto'
        },
        singularMeterUsage: {
            '$ref': '#/components/schemas/PriceSimulationElectricitySingularUsageDto'
        },
        multipleMeterUsage: {
            '$ref': '#/components/schemas/PriceSimulationElectricityMultipleUsageDto'
        },
        peakPower: {
            type: 'number',
            description: 'The peak power usage, nullable if not specified.',
            format: 'decimal',
            nullable: true
        },
        takeAveragePeak: {
            type: 'boolean',
            description: 'Indicates whether to use average peak power in the simulation.'
        },
        solarPanelsProduction: {
            '$ref': '#/components/schemas/PriceSimulationSolarPanelsProductionDto'
        }
    },
    additionalProperties: false,
    description: 'Represents the electricity usage details for price simulation.'
} as const;

export const $PriceSimulationEmailRequest = {
    type: 'object',
    properties: {
        email: {
            type: 'string',
            description: 'The email address to send the price simulation to',
            nullable: true
        },
        promotionalContentOptIn: {
            type: 'boolean',
            description: 'Whether the user opted in for promotional content'
        },
        language: {
            '$ref': '#/components/schemas/CustomerLanguageDto'
        }
    },
    additionalProperties: false,
    description: 'Represents a request for initiating a price simulation email operation.'
} as const;

export const $PriceSimulationEnergyTypeDto = {
    enum: ['Electricity', 'Gas', 'Both'],
    type: 'string',
    description: 'Represents different energy types for price simulation.'
} as const;

export const $PriceSimulationFeaturesDto = {
    type: 'object',
    properties: {
        installations: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PriceSimulationInstallationDto'
            },
            description: 'A list of installations present at the location, such as solar panels or home battery.',
            nullable: true
        },
        heatingType: {
            '$ref': '#/components/schemas/PriceSimulationHeatingTypeDto'
        },
        housingType: {
            '$ref': '#/components/schemas/PriceSimulationHousingTypeDto'
        },
        houseHoldSize: {
            '$ref': '#/components/schemas/PriceSimulationHouseHoldSizeDto'
        },
        solarPanelInstallationDate: {
            '$ref': '#/components/schemas/SolarPanelInstallationDateDto'
        },
        compensationType: {
            '$ref': '#/components/schemas/PriceSimulationCompensationTypeDto'
        },
        hasBiDirectionalMeter: {
            type: 'boolean',
            description: 'Specifies if the location has a bi-directional meter.',
            nullable: true
        },
        meterType: {
            '$ref': '#/components/schemas/PriceSimulationMeterTypeDto'
        },
        meterSubType: {
            '$ref': '#/components/schemas/PriceSimulationMeterSubTypeDto'
        },
        hasExclusiveNightMeter: {
            type: 'boolean',
            description: 'Indicates if an exclusive night meter is present at the location.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents various features related to energy usage, installations, and housing for price simulation.'
} as const;

export const $PriceSimulationGasUsageDto = {
    type: 'object',
    properties: {
        totalUsage: {
            type: 'integer',
            description: 'The total gas usage measured in the simulation.',
            format: 'int32'
        }
    },
    additionalProperties: false,
    description: 'Represents the gas usage details for price simulation.'
} as const;

export const $PriceSimulationHeatingTypeDto = {
    enum: ['ElectricHeating', 'GasHeating', 'HeatPump', 'Other'],
    type: 'string',
    description: 'Represents different types of heating for price simulation.'
} as const;

export const $PriceSimulationHouseHoldSizeDto = {
    enum: ['One', 'Two', 'Three', 'Four', 'FiveOrMore'],
    type: 'string',
    description: 'Represents different household sizes used in price simulation.'
} as const;

export const $PriceSimulationHousingTypeDto = {
    enum: ['Apartment', 'FullyEnclosedBuilding', 'HalfOpenBuilding', 'OpenBuilding', 'Other'],
    type: 'string',
    description: 'Represents different types of housing used in price simulation.'
} as const;

export const $PriceSimulationInitiatedResponse = {
    type: 'object',
    properties: {
        priceSimulationId: {
            type: 'string',
            description: 'The unique identifier for the initiated price simulation.',
            format: 'uuid'
        }
    },
    additionalProperties: false,
    description: 'Represents the response for initiating a price simulation, containing the ID of the initiated price simulation.'
} as const;

export const $PriceSimulationInstallationDto = {
    enum: ['SolarPanels', 'HomeBattery', 'ChargingStation', 'HeatPump'],
    type: 'string',
    description: 'Represents different types of installations for price simulation.'
} as const;

export const $PriceSimulationMeterSubTypeDto = {
    enum: ['Single', 'Multiple'],
    type: 'string',
    description: 'Represents sub-types of meter configurations for price simulation.'
} as const;

export const $PriceSimulationMeterTypeDto = {
    enum: ['Analogue', 'Digital'],
    type: 'string',
    description: 'Represents different types of meters used in price simulation.'
} as const;

export const $PriceSimulationProductCombinationOptionDto = {
    type: 'object',
    properties: {
        electricityProduct: {
            '$ref': '#/components/schemas/PriceSimulationProductOptionDto'
        },
        gasProduct: {
            '$ref': '#/components/schemas/PriceSimulationProductOptionDto'
        }
    },
    additionalProperties: false,
    description: 'Represents a product combination option containing optional electricity and gas products.'
} as const;

export const $PriceSimulationProductOptionDto = {
    type: 'object',
    properties: {
        energyType: {
            '$ref': '#/components/schemas/PriceSimulationEnergyTypeDto'
        },
        technicalProductName: {
            type: 'string',
            description: 'The technical name of the product.',
            nullable: true
        },
        costs: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CostDto'
            },
            description: 'The list of costs associated with this product.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents an energy product option with its type, name and associated costs.'
} as const;

export const $PriceSimulationRequest = {
    type: 'object',
    properties: {
        accountNumber: {
            type: 'string',
            description: 'The account number of the customer. Can be null for prospects.',
            nullable: true
        },
        isProspect: {
            type: 'boolean',
            description: 'Indicates whether the request is from a prospective customer.'
        },
        customerType: {
            '$ref': '#/components/schemas/CustomerTypeDto'
        },
        postalCode: {
            type: 'string',
            description: "The postal code of the customer's location.",
            nullable: true
        },
        energyType: {
            '$ref': '#/components/schemas/PriceSimulationEnergyTypeDto'
        },
        usage: {
            '$ref': '#/components/schemas/PriceSimulationUsageDto'
        },
        features: {
            '$ref': '#/components/schemas/PriceSimulationFeaturesDto'
        }
    },
    additionalProperties: false,
    description: 'Represents the request for a price simulation.'
} as const;

export const $PriceSimulationResponse = {
    type: 'object',
    properties: {
        simulationDetails: {
            '$ref': '#/components/schemas/PriceSimulationDetailsDto'
        },
        productCombinationOptions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PriceSimulationProductCombinationOptionDto'
            },
            description: 'The list of available product combination options.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the response containing price simulation details and available product combination options.'
} as const;

export const $PriceSimulationSolarPanelsProductionDto = {
    type: 'object',
    properties: {
        peakHoursInjection: {
            type: 'integer',
            description: 'The amount of energy injected during peak hours.',
            format: 'int32',
            nullable: true
        },
        offPeakHoursInjection: {
            type: 'integer',
            description: 'The amount of energy injected during off-peak hours.',
            format: 'int32',
            nullable: true
        },
        totalInjection: {
            type: 'integer',
            description: 'The total amount of energy injected by the solar panels.',
            format: 'int32',
            nullable: true
        },
        installationTotalKva: {
            type: 'number',
            description: 'The total capacity of the solar panel installation in kVA.',
            format: 'decimal',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the production details of solar panels used for price simulation.'
} as const;

export const $PriceSimulationUsageDto = {
    type: 'object',
    properties: {
        electricity: {
            '$ref': '#/components/schemas/PriceSimulationElectricityUsageDto'
        },
        gas: {
            '$ref': '#/components/schemas/PriceSimulationGasUsageDto'
        }
    },
    additionalProperties: false,
    description: 'Represents energy usage details for both electricity and gas during a price simulation.'
} as const;

export const $ProblemDetails = {
    type: 'object',
    properties: {
        type: {
            type: 'string',
            nullable: true
        },
        title: {
            type: 'string',
            nullable: true
        },
        status: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        detail: {
            type: 'string',
            nullable: true
        },
        instance: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: {}
} as const;

export const $ProductSwitchCurrentProductResponse = {
    type: 'object',
    properties: {
        technicalProductName: {
            type: 'string',
            description: 'The technical name of the product',
            nullable: true
        },
        promoLoss: {
            type: 'boolean',
            description: 'Indicates whether there is a loss of promotional benefits when switching the product'
        },
        hasSocialTarriff: {
            type: 'boolean',
            description: 'Indicates whether the product has a social tariff applied'
        },
        isActive: {
            type: 'boolean',
            description: 'Indicates whether the product is currently active'
        },
        productSwitchStatus: {
            '$ref': '#/components/schemas/ProductSwitchStatusResponse'
        },
        ean: {
            type: 'string',
            description: 'EAN of the meter',
            nullable: true
        },
        energyType: {
            '$ref': '#/components/schemas/EnergyTypeDto'
        },
        tariffChartUrlNl: {
            type: 'string',
            description: 'URL of the tariff chart in Dutch',
            nullable: true
        },
        tariffChartUrlFr: {
            type: 'string',
            description: 'URL of the tariff chart in French',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Current product information available for switching'
} as const;

export const $ProductSwitchProductResponse = {
    type: 'object',
    properties: {
        switchId: {
            type: 'string',
            description: 'The ID of the switch',
            nullable: true
        },
        technicalProductName: {
            type: 'string',
            description: 'The technical name of the product',
            nullable: true
        },
        productType: {
            '$ref': '#/components/schemas/ProductType'
        },
        customerType: {
            '$ref': '#/components/schemas/CustomerTypeDto'
        },
        preferred: {
            type: 'boolean',
            description: 'Indicates whether the product is preferred'
        },
        tariffChartUrlNl: {
            type: 'string',
            description: 'URL of the tariff chart in Dutch',
            nullable: true
        },
        tariffChartUrlFr: {
            type: 'string',
            description: 'URL of the tariff chart in French',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Product available to switch to'
} as const;

export const $ProductSwitchRecordDto = {
    type: 'object',
    properties: {
        current: {
            '$ref': '#/components/schemas/ProductSwitchCurrentProductResponse'
        },
        switchOptions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ProductSwitchProductResponse'
            },
            description: 'A list of products available to switch to',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the options to switch to from a specific product'
} as const;

export const $ProductSwitchRequest = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ProductSwitchRequestDto'
            },
            description: 'Multiple product switch request records',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the request body containing the product switch ID.'
} as const;

export const $ProductSwitchRequestDto = {
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'The International Article Number (EAN) associated with the product. This is a unique identifier used for tracking and identification in retail and logistics.',
            nullable: true
        },
        productSwitchId: {
            type: 'string',
            description: 'The unique identifier for the product switch operation. This ID is used to reference the specific switch action being requested.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the data transfer object for a product switch request.'
} as const;

export const $ProductSwitchResponse = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ProductSwitchRecordDto'
            },
            description: 'List of product switch records',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the response for product switch options.'
} as const;

export const $ProductSwitchStatusResponse = {
    enum: ['PendingProductSwitch', 'TransitioningToProductSwitch', 'PendingProductSwitchCancellation', 'NoProductSwitch'],
    type: 'string',
    description: 'Represents the status of a product switch operation'
} as const;

export const $ProductType = {
    enum: ['Fixed', 'Variable', 'Dynamic'],
    type: 'string',
    description: 'Type of product. Indicates whether there is a fixed or a variable price across a given timespan or the timespan of the product.'
} as const;

export const $Promotion = {
    type: 'object',
    properties: {
        text: {
            type: 'object',
            additionalProperties: {
                type: 'string'
            },
            description: "Name of the promotion by language. E.g. 'Loyalty promo Elektriciteit'",
            nullable: true
        },
        disclaimer: {
            type: 'object',
            additionalProperties: {
                type: 'string'
            },
            description: 'Disclaimer of the promotion by language',
            nullable: true
        },
        eBillingRequired: {
            type: 'boolean',
            description: 'Whether E-Billing is required for this promotion to be valid'
        }
    },
    additionalProperties: false,
    description: 'Promotion active on a Product'
} as const;

export const $PurchaseStatus = {
    enum: ['Unknown', 'NotPurchased', 'Purchased'],
    type: 'string',
    description: 'Purchase status'
} as const;

export const $PutAccountDetailsRequest = {
    type: 'object',
    properties: {
        alias: {
            type: 'string',
            description: 'the CRM account alias',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Details to update on a CRM account from the viewpoint of the logged in user'
} as const;

export const $PutContractDetailsForProductMeterRequest = {
    type: 'object',
    properties: {
        meterRegime: {
            '$ref': '#/components/schemas/MeterRegime'
        }
    },
    additionalProperties: false,
    description: 'Request to update the meter details'
} as const;

export const $Reason = {
    enum: ['ADJ', 'TEMP', 'RECT', 'APPR', 'MISS', 'EST', 'OEST', 'DEF', 'VAL'],
    type: 'string',
    description: 'Quality of the reading'
} as const;

export const $ReasonDto = {
    enum: [0, 1, 2, 3, 4, 5, 6, 7, 8],
    type: 'integer',
    description: 'Quality of the reading',
    format: 'int32'
} as const;

export const $RectificationContestedMeter = {
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'EAN number identifying the meter',
            nullable: true
        },
        originalDate: {
            type: 'string',
            description: 'Original date of the reading',
            format: 'date-time'
        },
        correctedDate: {
            type: 'string',
            description: 'Date the user entered',
            format: 'date-time'
        },
        utilityType: {
            '$ref': '#/components/schemas/UtilityType'
        },
        rectificationContestedMeterReadingRecords: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RectificationContestedMeterReadingRecord'
            },
            description: 'List of contested meter reading records',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The information of a contested meter related to rectification.'
} as const;

export const $RectificationContestedMeterReadingRecord = {
    type: 'object',
    properties: {
        originalValue: {
            type: 'integer',
            description: 'The original provided meter reading value',
            format: 'int32'
        },
        correctedValue: {
            type: 'integer',
            description: 'The meter reading value corrected by the user',
            format: 'int32'
        },
        timeFrameType: {
            '$ref': '#/components/schemas/TimeframeTypeCodeDto'
        }
    },
    additionalProperties: false,
    description: 'The rectification meter information'
} as const;

export const $RectificationDeltaDto = {
    enum: ['HasDeviation', 'NoDeviation'],
    type: 'string',
    description: 'The rectification delta response'
} as const;

export const $RectificationDeltaResultResponse = {
    type: 'object',
    properties: {
        delta: {
            '$ref': '#/components/schemas/RectificationDeltaDto'
        },
        calculatedDelta: {
            type: 'number',
            description: 'Calculated delta',
            format: 'double'
        },
        contestedValue: {
            '$ref': '#/components/schemas/ContestedValue'
        }
    },
    additionalProperties: false,
    description: 'Record containing the deviation result'
} as const;

export const $RectificationDocuments = {
    type: 'object',
    properties: {
        ixosArchiveId: {
            type: 'string',
            description: 'Unique identifier for the archived document',
            nullable: true
        },
        fileName: {
            type: 'string',
            description: 'Name of the attached file',
            nullable: true
        },
        fileExtension: {
            type: 'string',
            description: 'File extension of the attached document',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The rectification attachment info'
} as const;

export const $RectificationEligibilityDto = {
    enum: ['Eligible', 'HasOngoingRectification', 'HasDigitalMeter'],
    type: 'string',
    description: 'Response type for eligibility check'
} as const;

export const $RectificationEligibilityRecordResponse = {
    type: 'object',
    properties: {
        ean: {
            type: 'string',
            description: 'EAN for which eligibility is checked',
            nullable: true
        },
        eligibility: {
            '$ref': '#/components/schemas/RectificationEligibilityDto'
        }
    },
    additionalProperties: false,
    description: 'Represents the eligibility per EAN'
} as const;

export const $RectificationEligibilityResponse = {
    type: 'object',
    properties: {
        records: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RectificationEligibilityRecordResponse'
            },
            description: 'Eligibility value per EAN',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents the response for a rectification eligibility check'
} as const;

export const $RectificationMeterReadingResponse = {
    type: 'object',
    properties: {
        ean: {
            maxLength: 50,
            type: 'string',
            description: 'Gets or sets the value of the ean',
            nullable: true
        },
        indexValues: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MeterReadingIndexValue'
            },
            description: 'Gets or sets the list of index values associated with the meter reading.',
            nullable: true
        },
        date: {
            type: 'string',
            description: 'Gets or sets the value of the date',
            format: 'date'
        },
        meterReadingType: {
            '$ref': '#/components/schemas/MeterReadingType'
        },
        registeredDate: {
            type: 'string',
            description: 'Gets or sets the value of the registered date',
            format: 'date'
        },
        utilityType: {
            '$ref': '#/components/schemas/UtilityType'
        },
        meterType: {
            '$ref': '#/components/schemas/MeterType'
        }
    },
    additionalProperties: false,
    description: 'The meter reading response class'
} as const;

export const $RegisterHolder = {
    type: 'object',
    properties: {
        registerType: {
            '$ref': '#/components/schemas/RegisterType'
        }
    },
    additionalProperties: false,
    description: 'Holder that has a meter register'
} as const;

export const $RegisterType = {
    enum: ['Single', 'Day', 'Night', 'ExclusiveNight'],
    type: 'string',
    description: 'Type of register'
} as const;

export const $RenewalResponse = {
    type: 'object',
    properties: {
        tariff: {
            '$ref': '#/components/schemas/GetContractDetailsForProductResponseTariff'
        },
        renewalType: {
            '$ref': '#/components/schemas/RenewalTypeDto'
        },
        nextRenewalDate: {
            type: 'string',
            description: 'Date of renewal',
            format: 'date-time',
            nullable: true
        },
        hasProductSwitchOptions: {
            type: 'boolean',
            description: 'Indicates if there are switch options available for the product'
        },
        status: {
            '$ref': '#/components/schemas/ProductSwitchStatusResponse'
        },
        productSwitchId: {
            type: 'string',
            description: 'Id of the product switch',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Response containing renewal information'
} as const;

export const $RenewalTypeDto = {
    enum: ['TacitNotChanged', 'TacitChanged', 'Regular', 'Migration', 'ProductSwitch'],
    type: 'string',
    description: 'Product renewal type'
} as const;

export const $Representation = {
    enum: ['Mr', 'Mrs', 'Ms'],
    type: 'string',
    description: 'Product renewal type'
} as const;

export const $SelectedCorrespondenceAddressDto = {
    enum: ['Old', 'New', 'Custom'],
    type: 'string',
    description: 'Customer selected correspondence address'
} as const;

export const $SessionTokenValidityResponse = {
    type: 'object',
    properties: {
        inviteReference: {
            type: 'string',
            description: 'Internal reference of the invite',
            format: 'uuid'
        },
        status: {
            '$ref': '#/components/schemas/SessionTokenValidityStatusDto'
        },
        displayNameOwner: {
            type: 'string',
            description: 'The ME voornaam of the owner of the account, given that the session token is in context of a crm account',
            nullable: true
        },
        emailOwner: {
            type: 'string',
            description: 'The email of the owner of the account',
            nullable: true
        },
        crmAccountNumber: {
            type: 'string',
            description: 'The Crm Account number of the account, given that the session token is in context of a crm account',
            nullable: true
        },
        crmAccountAliasCurrentUser: {
            type: 'string',
            description: 'The crm account alias of the current user for the account, given that the session token is in context of a crm account',
            nullable: true
        },
        createdAt: {
            type: 'string',
            description: 'Invite creation date',
            format: 'date-time'
        },
        expiresAt: {
            type: 'string',
            description: 'Expiration date of the session token',
            format: 'date-time',
            nullable: true
        },
        mailSentAt: {
            type: 'string',
            description: 'Date on which the mail was sent, if already sent',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The response of validating the session token validity'
} as const;

export const $SessionTokenValidityStatusDto = {
    enum: ['Redeemable', 'Redeemed', 'Expired', 'Revoked'],
    type: 'string',
    description: 'The validity status'
} as const;

export const $ShippingStatus = {
    enum: ['Unknown', 'NotShipped', 'Shipped'],
    type: 'string',
    description: 'Shipping status'
} as const;

export const $SignAgreementsRequest = {
    type: 'object',
    properties: {
        termsOfService: {
            type: 'boolean',
            description: 'Terms of service'
        },
        privacyPolicy: {
            type: 'boolean',
            description: 'Privacy policy'
        }
    },
    additionalProperties: false,
    description: 'Defines which agreements have been signed by the user'
} as const;

export const $SignedAgreementsDto = {
    type: 'object',
    properties: {
        signedTermsOfService: {
            type: 'string',
            description: 'Date of signing the terms of service',
            format: 'date-time',
            nullable: true
        },
        signedPrivacyPolicy: {
            type: 'string',
            description: 'Date of signing the privacy policy',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains the agreement dates'
} as const;

export const $SimulateGlobalAmountUpdateRequest = {
    type: 'object',
    properties: {
        amount: {
            type: 'integer',
            description: 'The proposed amount/>',
            format: 'int32'
        }
    },
    additionalProperties: false,
    description: 'Simulate global amount for an advance'
} as const;

export const $SmartEnergyDevice = {
    enum: ['EvCharger', 'HeatPump', 'ElectricalHeating', 'SwimmingPool'],
    type: 'string',
    description: 'Smart energy device'
} as const;

export const $SmartEnergyDeviceCombinationResponse = {
    type: 'object',
    properties: {
        devices: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SmartEnergyDevice'
            },
            description: 'Device combinations list',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Smart energy device combination response'
} as const;

export const $SmartEnergyDeviceCombinationResponseList = {
    type: 'object',
    properties: {
        deviceCombinations: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SmartEnergyDeviceCombinationResponse'
            },
            description: 'Valid device combination',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Smart energy device combination response list'
} as const;

export const $SmartEnergyOfferRequest = {
    required: ['deviceCombination', 'injectionVolume', 'inverterType', 'usageVolume'],
    type: 'object',
    properties: {
        injectionVolume: {
            type: 'integer',
            description: 'Number of kwh injected',
            format: 'int32'
        },
        usageVolume: {
            type: 'integer',
            description: 'Number of kwh used',
            format: 'int32'
        },
        inverterType: {
            type: 'string',
            description: 'Inverter type',
            nullable: true
        },
        deviceCombination: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SmartEnergyDevice'
            },
            description: 'Valid list of devices that can be combined into a valid combination',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Smart energy offer'
} as const;

export const $SmartEnergyOfferResponse = {
    required: ['offers'],
    type: 'object',
    properties: {
        offers: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SmartEnergyOfferResponseRow'
            },
            description: 'Offers',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Smart energy offer response'
} as const;

export const $SmartEnergyOfferResponseRow = {
    type: 'object',
    properties: {
        injectedVolume: {
            type: 'integer',
            description: 'Energy generated (kWh).',
            format: 'int32',
            nullable: true
        },
        usageVolume: {
            type: 'integer',
            description: 'Energy consumption from the grid (kWh).',
            format: 'int32',
            nullable: true
        },
        savedVolume: {
            type: 'integer',
            description: 'Storage capacity (kWh).',
            format: 'int32',
            nullable: true
        },
        numberOfModules: {
            type: 'integer',
            description: 'Number of modules (#).',
            format: 'int32',
            nullable: true
        },
        hasBatteryFive: {
            type: 'integer',
            description: 'Battery 5kWh (#).',
            format: 'int32',
            nullable: true
        },
        hasBatteryEight: {
            type: 'integer',
            description: 'Battery 8kWh (#).',
            format: 'int32',
            nullable: true
        },
        inventorType: {
            type: 'string',
            description: 'Inverter (kW).',
            nullable: true
        },
        investment: {
            type: 'integer',
            description: 'Investment at 0% (€).',
            format: 'int32',
            nullable: true
        },
        investmentSixPercent: {
            type: 'integer',
            description: 'Investment at 6% (€).',
            format: 'int32',
            nullable: true
        },
        investmentTwentyOnePercent: {
            type: 'integer',
            description: 'Investment at 21% (€).',
            format: 'int32',
            nullable: true
        },
        height: {
            type: 'integer',
            description: 'Height (cm).',
            format: 'int32',
            nullable: true
        },
        width: {
            type: 'integer',
            description: 'Width (cm).',
            format: 'int32',
            nullable: true
        },
        depth: {
            type: 'integer',
            description: 'Depth (cm).',
            format: 'int32',
            nullable: true
        },
        yearlySaving: {
            type: 'integer',
            description: 'Annual savings (€).',
            format: 'int32',
            nullable: true
        },
        extraSelfUsage: {
            type: 'number',
            description: 'Additional self-consumption (€).',
            format: 'decimal',
            nullable: true
        },
        selfUsageOptimalisation: {
            type: 'number',
            description: 'Self-consumption optimization (€).',
            format: 'decimal',
            nullable: true
        },
        capacityTarifReduction: {
            type: 'number',
            description: 'Capacity tariff reduction (€).',
            format: 'decimal',
            nullable: true
        },
        enecoSmartSavings: {
            type: 'integer',
            description: 'Savings with Eneco Smart Steering (€).',
            format: 'int32',
            nullable: true
        },
        payBackPeriodSixPercent: {
            type: 'integer',
            description: 'Payback period at 6% (€).',
            format: 'int32',
            nullable: true
        },
        payBackPeriodTwentyOnePercent: {
            type: 'integer',
            description: 'Payback period at 21% (€).',
            format: 'int32',
            nullable: true
        },
        roiSixPercent: {
            type: 'number',
            description: 'Return on Investment (ROI) for 20 years at 6%.',
            format: 'decimal',
            nullable: true
        },
        roiTwentyOnePercent: {
            type: 'number',
            description: 'Return on Investment (ROI) for 20 years at 21%.',
            format: 'decimal',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Represents a response row for a smart energy offer.'
} as const;

export const $SolarPanelInstallationDateDto = {
    enum: ['BeforeJanuary2024', 'AfterJanuary2024'],
    type: 'string',
    description: `Represents the date of solar panel installation for price simulation purposes.
The date is significant only for locations in Wallonia.`
} as const;

export const $StatusHintDto = {
    type: 'object',
    properties: {
        hint: {
            '$ref': '#/components/schemas/StatusHintOptionDto'
        },
        state: {
            '$ref': '#/components/schemas/StatusHintStateDto'
        }
    },
    additionalProperties: false,
    description: 'Record containing the state of a Dc.XAPI.Site.Web.Contracts.Move.StatusHintOptionDto'
} as const;

export const $StatusHintOptionDto = {
    enum: ['EmailConfirmation', 'MoveFileCreated', 'MoveInformationUpdated', 'MeterValuesOldAddressFilledIn', 'MeterValuesNewAddressFilledIn', 'EndNote', 'Completed'],
    type: 'string',
    description: 'All status hint options'
} as const;

export const $StatusHintStateDto = {
    enum: ['Todo', 'Completed'],
    type: 'string',
    description: 'The state in which a status hint resides for a move'
} as const;

export const $SubmitRectificationRequest = {
    type: 'object',
    properties: {
        contestedMeterReadings: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RectificationContestedMeter'
            },
            description: 'List of contested meter readings for the rectification',
            nullable: true
        },
        rectificationDocuments: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/RectificationDocuments'
            },
            description: 'List of documents attached to the rectification request',
            nullable: true
        },
        comment: {
            type: 'string',
            description: 'Comment attached by the user',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'The rectification info'
} as const;

export const $SubscriptionDetailInfo = {
    type: 'object',
    properties: {
        detailsUrl: {
            type: 'string',
            description: 'The detailsUrl property',
            nullable: true
        },
        detailType: {
            '$ref': '#/components/schemas/SubscriptionType'
        },
        discountStatus: {
            '$ref': '#/components/schemas/DiscountStatus'
        },
        endDate: {
            type: 'string',
            description: 'The endDate property',
            format: 'date-time',
            nullable: true
        },
        name: {
            type: 'string',
            description: 'The name property',
            nullable: true
        },
        percentage: {
            type: 'number',
            description: 'The percentage property',
            format: 'double',
            nullable: true
        },
        price: {
            type: 'number',
            description: 'The price property',
            format: 'double',
            nullable: true
        },
        priceModel: {
            '$ref': '#/components/schemas/PriceModel'
        },
        startDate: {
            type: 'string',
            description: 'The startDate property',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Subscription detail information'
} as const;

export const $SubscriptionInfo = {
    type: 'object',
    properties: {
        campaign: {
            type: 'string',
            description: 'The campaign property',
            nullable: true
        },
        details: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubscriptionDetailInfo'
            },
            description: 'The details property',
            nullable: true
        },
        endDate: {
            type: 'string',
            description: 'The endDate property',
            format: 'date-time',
            nullable: true
        },
        planDetailsUrl: {
            type: 'string',
            description: 'The planDetailsUrl property',
            nullable: true
        },
        planId: {
            type: 'string',
            description: 'The planId property',
            nullable: true
        },
        planName: {
            type: 'string',
            description: 'The planName property',
            nullable: true
        },
        planPrice: {
            type: 'number',
            description: 'The planPrice property',
            format: 'double',
            nullable: true
        },
        planPriceModel: {
            '$ref': '#/components/schemas/PlanPriceModel'
        },
        quantity: {
            type: 'integer',
            description: 'The quantity property',
            format: 'int32',
            nullable: true
        },
        serviceType: {
            type: 'string',
            description: 'The serviceType property',
            nullable: true
        },
        startDate: {
            type: 'string',
            description: 'The startDate property',
            format: 'date-time',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/SubscriptionStatus'
        },
        subscriptionId: {
            type: 'string',
            description: 'The subscriptionId property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Subscription info'
} as const;

export const $SubscriptionStatus = {
    enum: ['Active', 'Future', 'NonRenewing', 'InTrial', 'Cancelled'],
    type: 'string',
    description: 'Subscription status'
} as const;

export const $SubscriptionStatusDto = {
    enum: ['Created', 'LinkCompleted', 'Active', 'Expired'],
    type: 'string',
    description: 'Subscription status'
} as const;

export const $SubscriptionTierDto = {
    enum: ['Free', 'Premium'],
    type: 'string',
    description: 'Subscription tier'
} as const;

export const $SubscriptionType = {
    enum: ['RecurringAddon', 'OneTimeCharge', 'Discount'],
    type: 'string',
    description: 'Subscription type'
} as const;

export const $SubscriptionTypeDto = {
    enum: ['SmartCharging', 'SmartInsights'],
    type: 'string',
    description: 'Subscription type'
} as const;

export const $SuggestedEanResponse = {
    type: 'object',
    properties: {
        energyType: {
            '$ref': '#/components/schemas/MoveEnergyTypeDto'
        },
        ean: {
            type: 'string',
            description: 'EAN of the meter',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains EAN information returned by ThinkNext'
} as const;

export const $TariffDetails = {
    type: 'object',
    properties: {
        tariffCardByLanguage: {
            type: 'object',
            additionalProperties: {
                type: 'string'
            },
            description: 'Links to the tariff card by available languages. Languages are described as ISO 639-1 Alpha-2 codes.',
            nullable: true
        },
        startDate: {
            type: 'string',
            description: 'Date on which this tariff becomes active.',
            format: 'date-time'
        }
    },
    additionalProperties: false,
    description: 'Describes details in regards to a tariff for a given timeframe. (Usually monthly)'
} as const;

export const $TimeFrameDto = {
    enum: ['Monthly', 'Yearly', 'FifteenMin', 'Hourly', 'Daily'],
    type: 'string',
    description: 'Represents the time frame for data aggregation.'
} as const;

export const $TimeframeTypeCodeDto = {
    enum: ['TH', 'HI', 'LO', 'EX'],
    type: 'string',
    description: 'Type frame type code'
} as const;

export const $UnitOfMeasureDto = {
    enum: [0, 1],
    type: 'integer',
    description: 'Unit of measure of the value',
    format: 'int32'
} as const;

export const $UpdateAccountCompanyDetailsRequest = {
    type: 'object',
    properties: {
        name: {
            type: 'string',
            description: 'Name of the company',
            nullable: true
        },
        formatType: {
            '$ref': '#/components/schemas/CompanyFormatTypeDto'
        },
        vatType: {
            '$ref': '#/components/schemas/CompanyVatTypeDto'
        },
        companyNumber: {
            type: 'string',
            description: 'Company number',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Company data for a crm account'
} as const;

export const $UpdateAccountContactDetailsRequest = {
    type: 'object',
    properties: {
        firstName: {
            type: 'string',
            description: 'First name of the account',
            nullable: true
        },
        lastName: {
            type: 'string',
            description: 'Last name of the account',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'Email of the account',
            nullable: true
        },
        dateOfBirth: {
            type: 'string',
            description: 'Birth date of the account',
            format: 'date-time',
            nullable: true
        },
        telephoneNumber: {
            type: 'string',
            description: 'Telephone number of the account',
            nullable: true
        },
        mobileNumber: {
            type: 'string',
            description: 'Mobile number of the account',
            nullable: true
        },
        gezinsBondNumber: {
            type: 'string',
            description: 'Gezinsbond number of the account',
            nullable: true
        },
        gender: {
            '$ref': '#/components/schemas/GenderDto'
        },
        language: {
            '$ref': '#/components/schemas/CustomerLanguageDto'
        },
        contactAddress: {
            '$ref': '#/components/schemas/ContactAddressDto'
        }
    },
    additionalProperties: false,
    description: 'Update the accounts contact details'
} as const;

export const $UpdateAccountContactPreferencesRequest = {
    type: 'object',
    properties: {
        invoicePreferences: {
            '$ref': '#/components/schemas/InvoiceContactPreference'
        },
        advancePayments: {
            type: 'boolean',
            description: 'Does the user want Advance invoices'
        },
        enecoNews: {
            type: 'boolean',
            description: 'Does the user want more information about Eneco'
        },
        promotions: {
            type: 'boolean',
            description: 'Doest the user want more info about Promotions or third party partners'
        },
        energyMonitorFrequency: {
            '$ref': '#/components/schemas/EnergyMonitorFrequency'
        }
    },
    additionalProperties: false,
    description: 'Representation a CRM accounts contact preferences'
} as const;

export const $UpdateAdvancePaymentRequest = {
    type: 'object',
    properties: {
        type: {
            '$ref': '#/components/schemas/UpdateAdvancePaymentRequestType'
        },
        amount: {
            type: 'integer',
            description: 'The global amount if Dc.XAPI.Site.Web.Contracts.AdvanceAmount.UpdateAdvancePaymentRequestType.Global or amount for EAN if Dc.XAPI.Site.Web.Contracts.AdvanceAmount.UpdateAdvancePaymentRequestType.Granular',
            format: 'int32'
        },
        ean: {
            type: 'string',
            description: 'EAN if Dc.XAPI.Site.Web.Contracts.AdvanceAmount.UpdateAdvancePaymentRequestType.Granular',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Proposal for new advance request amount'
} as const;

export const $UpdateAdvancePaymentRequestType = {
    enum: ['Global', 'Granular'],
    type: 'string',
    description: 'Determines how the advance payment changes get handled'
} as const;

export const $UpdateEmailRequest = {
    type: 'object',
    properties: {
        sessionToken: {
            type: 'string',
            description: 'The session token',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Request which allows a user to change his password'
} as const;

export const $UpdateInvoiceFrequenciesDto = {
    type: 'object',
    properties: {
        serviceDeliveryPoints: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/UpdateInvoiceFrequencyDto'
            },
            description: 'Service delivery points to configure the invoice frequency for',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Body containing service delivery points to configure the invoice frequency for'
} as const;

export const $UpdateInvoiceFrequencyDto = {
    type: 'object',
    properties: {
        contractNumber: {
            type: 'string',
            description: 'The contract number to configure the invoice frequency for',
            nullable: true
        },
        invoiceFrequency: {
            '$ref': '#/components/schemas/InvoiceFrequencyDto'
        }
    },
    additionalProperties: false,
    description: 'A meter with monthly invoice period'
} as const;

export const $UpdateKeyTransferRequest = {
    type: 'object',
    properties: {
        keyTransferDate: {
            type: 'string',
            description: 'Date on which the key gets transferred',
            format: 'date',
            nullable: true
        },
        keyTransferDateUnknown: {
            type: 'boolean',
            description: 'Key transfer date is unknown right now'
        },
        vacancyDate: {
            type: 'string',
            description: 'Date of vacancy',
            format: 'date',
            nullable: true
        },
        reportedVacancy: {
            type: 'boolean',
            description: 'Whether there is going to be vacancy'
        }
    },
    additionalProperties: false,
    description: 'Body containing details about the key transfer on an address'
} as const;

export const $UpdateLinkFromAccountRequest = {
    type: 'object',
    properties: {
        alias: {
            type: 'string',
            description: 'Alias for the linked account',
            nullable: true
        },
        linkingType: {
            '$ref': '#/components/schemas/AccountLinkingTypeDto'
        }
    },
    additionalProperties: false,
    description: 'Details to update on a linked account'
} as const;

export const $UpdateLocalLogin = {
    type: 'object',
    properties: {
        displayName: {
            type: 'string',
            description: 'Display name (ME Voornaam) to configure for the user',
            nullable: true
        },
        email: {
            type: 'string',
            description: 'Email that the user can login with locally',
            nullable: true
        },
        password: {
            type: 'string',
            description: 'Password that the user can login with locally. If the password is empty ("") or not present, the password will not be updated.',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Updates the local login details for a user'
} as const;

export const $UpdateMonthlyInvoicePeriodDtoMeter = {
    type: 'object',
    properties: {
        billingAccountNumber: {
            type: 'string',
            description: 'Number that signifies the billing account',
            nullable: true
        },
        period: {
            '$ref': '#/components/schemas/MonthlyInvoicePeriodDto'
        }
    },
    additionalProperties: false,
    description: 'A meter with monthly invoice period'
} as const;

export const $UpdateMonthlyInvoicePreferenceDto = {
    type: 'object',
    properties: {
        meters: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/UpdateMonthlyInvoicePeriodDtoMeter'
            },
            description: 'Meters to configure the preferences for',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Body containing Meters to configure the preferences for'
} as const;

export const $UpdateMoveReasonRequest = {
    type: 'object',
    properties: {
        reasonDto: {
            '$ref': '#/components/schemas/MoveReasonDto'
        }
    },
    additionalProperties: false,
    description: 'Body containing details on how the user wants to move'
} as const;

export const $UpdateNewAddressRequest = {
    type: 'object',
    properties: {
        address: {
            '$ref': '#/components/schemas/MoveAddressDto'
        }
    },
    additionalProperties: false,
    description: 'Body containing new address details for a move'
} as const;

export const $UpdatePaymentMethodByBillingAccountResponse = {
    type: 'object',
    properties: {
        billingAccountNumber: {
            type: 'string',
            description: 'Billing account number',
            nullable: true
        },
        newPaymentMethod: {
            '$ref': '#/components/schemas/PaymentMethodDto'
        },
        iban: {
            type: 'string',
            description: 'IBAN details',
            nullable: true
        },
        directDebitConsentGiven: {
            type: 'boolean',
            description: 'Consent for direct debit given',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains updated payment method information per billing account'
} as const;

export const $UpdatePaymentMethodRequest = {
    type: 'object',
    properties: {
        items: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/UpdatePaymentMethodByBillingAccountResponse'
            },
            description: 'Payment methods per billing account',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Contains updated payment method information'
} as const;

export const $UpdateSubscriptionStatusRequest = {
    required: ['status'],
    type: 'object',
    properties: {
        status: {
            '$ref': '#/components/schemas/SubscriptionStatusDto'
        }
    },
    additionalProperties: false,
    description: 'Update subscription status request'
} as const;

export const $UpdateVacancyRequest = {
    type: 'object',
    properties: {
        vacancyUntil: {
            type: 'string',
            description: 'Date until vacancy has been requested. Should be the last day of one of the four next months',
            format: 'date'
        }
    },
    additionalProperties: false,
    description: 'Update the vacancy of multiple EANs'
} as const;

export const $UtilityType = {
    enum: ['Electricity', 'Gas'],
    type: 'string',
    description: 'The utility type enum'
} as const;

export const $ValidateOrderResponse = {
    type: 'object',
    properties: {
        checkoutExpiresAt: {
            type: 'string',
            description: 'The checkoutExpiresAt property',
            format: 'date-time',
            nullable: true
        },
        checkoutUrl: {
            type: 'string',
            description: 'The checkoutUrl property',
            nullable: true
        },
        currencyCode: {
            type: 'string',
            description: "The 3-character currency code to be used for the order. Currently Eneco only support value 'EUR'",
            nullable: true
        },
        customer: {
            '$ref': '#/components/schemas/OrderCustomer'
        },
        errorDetails: {
            '$ref': '#/components/schemas/EnecoErrorDetails'
        },
        initialInvoiceAmount: {
            type: 'number',
            description: 'The initialInvoiceAmount property',
            format: 'double',
            nullable: true
        },
        isOneTime: {
            type: 'boolean',
            description: 'The isOneTime property',
            nullable: true
        },
        orderReference: {
            type: 'string',
            description: 'The orderReference property',
            nullable: true
        },
        products: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EnecoProduct'
            },
            description: 'The products property',
            nullable: true
        },
        shippingAddress: {
            '$ref': '#/components/schemas/Address'
        },
        success: {
            type: 'boolean',
            description: 'The success property',
            nullable: true
        }
    },
    additionalProperties: false,
    description: 'Validate order response'
} as const;

export const $ValidationProblemDetails = {
    type: 'object',
    properties: {
        type: {
            type: 'string',
            nullable: true
        },
        title: {
            type: 'string',
            nullable: true
        },
        status: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        detail: {
            type: 'string',
            nullable: true
        },
        instance: {
            type: 'string',
            nullable: true
        },
        errors: {
            type: 'object',
            additionalProperties: {
                type: 'array',
                items: {
                    type: 'string'
                }
            },
            nullable: true
        }
    },
    additionalProperties: {}
} as const;

export const $VehicleAssetResourceDto = {
    required: ['brand', 'chargingLocation'],
    type: 'object',
    properties: {
        brand: {
            minLength: 1,
            type: 'string',
            description: 'Brand of the vehicle'
        },
        model: {
            type: 'string',
            description: 'Model of the vehicle',
            nullable: true
        },
        year: {
            type: 'integer',
            description: 'Year of the vehicle',
            format: 'int32',
            nullable: true
        },
        alias: {
            type: 'string',
            description: 'Vehicle alias',
            nullable: true
        },
        chargingLocation: {
            '$ref': '#/components/schemas/LocationDto'
        }
    },
    additionalProperties: false,
    description: 'Vehicle asset'
} as const;