// This file is auto-generated by @hey-api/openapi-ts

/**
 * Kind of access the account has
 */
export type AccessTypeDto = 'Read' | 'Write' | 'Owner';

/**
 * Contains the overview of all linked accounts to this account
 */
export type AccountLinkOverviewDto = {
    records?: Array<AccountLinkOverviewRecordDto>;
};

/**
 * Type of link established
 */
export type AccountLinkOverviewLinkTypeDto = 'Read' | 'Write' | 'Owner';

/**
 * Body containing information regarding the login credentials for a given user account
 */
export type AccountLinkOverviewLoginDetailsDto = {
    /**
     * The display name the referenced user uses in the UI
     */
    displayName?: string;
    /**
     * The username of the referenced user (email)
     */
    loginUsername?: string;
    /**
     * Whether the referenced user has local login credentials (username/password)
     */
    localLogin?: boolean;
    /**
     * Whether the referenced user has ItsMe federated login credentials (ItsMe IDP)
     */
    itsmeLogin?: boolean;
};

/**
 * Body containing a single linked account record
 */
export type AccountLinkOverviewRecordDto = {
    /**
     * Id of the account link or invite
     */
    id?: string;
    identityAlias?: string;
    inviteeEmail?: string;
    loginDetails?: AccountLinkOverviewLoginDetailsDto;
    type?: AccountLinkOverviewLinkTypeDto;
    linkingRequest?: AccountLinkOverviewRecordLinkingRequestDto;
};

/**
 * Contains link request details
 */
export type AccountLinkOverviewRecordLinkingRequestDto = {
    inviteReference?: string;
    status?: string;
    createdAt?: string;
    lastMailSentAt?: string | null;
};

/**
 * Defines the access control of the linking
 */
export type AccountLinkingTypeDto = 'Read' | 'Write';

/**
 * Details of an active product
 */
export type ActiveProductDetailsResponse = {
    /**
     * Name of the product
     */
    productName?: string;
    energyType?: EnergyTypeDto;
    status?: ContractProductStatusDto;
};

/**
 * Response model for active products card in next best action flow
 */
export type ActiveProductsCardResponse = NextBestActionCardResponse & {
    /**
     * List of active product details
     */
    activeProducts?: Array<ActiveProductDetailsResponse>;
};

/**
 * Add meter reading
 */
export type AddMeterReadingRequest = {
    /**
     * Number of the account
     */
    accountNumber?: string;
    /**
     * Value of the meter reading
     */
    readingValue?: number;
    /**
     * Ean for which the reading will be added
     */
    ean?: string;
    meterTimeFrameType?: TimeframeTypeCodeDto;
    /**
     * Date until vacancy has been requested. Should be the last day of one of the four next months
     */
    registeredDate?: string;
    utilityType?: EnergyTypeDto;
    direction?: DirectionTypeCodeDto;
};

/**
 * Address response
 */
export type Address = {
    /**
     * The countryCode property
     */
    countryCode?: string | null;
    /**
     * The city property
     */
    city?: string | null;
    /**
     * The email property
     */
    email?: string | null;
    /**
     * The firstName property
     */
    firstName?: string | null;
    /**
     * The lastName property
     */
    lastName?: string | null;
    /**
     * The line1 property
     */
    line1?: string | null;
    /**
     * The line2 property
     */
    line2?: string | null;
    /**
     * The line3 property
     */
    line3?: string | null;
    /**
     * The postalCode property
     */
    postalCode?: string | null;
    /**
     * The state property
     */
    state?: string | null;
};

/**
 * Response containing address information in detail
 */
export type AddressDetailsDto = {
    /**
     * The name of the street where the address is located
     */
    street?: string;
    /**
     * The number of the house or building on the street
     */
    houseNumber?: string;
    /**
     * Optional bus or additional unit information
     */
    bus?: string | null;
    /**
     * The postal code of the address
     */
    postalCode?: string;
    /**
     * The municipality or city where the address is located
     */
    municipality?: string;
};

/**
 * Response for advance amount changed card
 */
export type AdvanceAmountChangedCardResponse = NextBestActionCardResponse;

/**
 * Collection of advance payment records
 */
export type AdvanceAmountCollectionResponse = {
    /**
     * Collection of advance payment records
     */
    advancePayments?: Array<AdvanceAmountDto>;
    totals?: AdvanceAmountTotalsDto;
};

/**
 * Details of an advance payment
 */
export type AdvanceAmountDto = {
    contractDetails?: AdvanceContractDto;
    meterDetails?: AdvanceMeterDto;
    details?: AdvanceDetailsDto;
};

/**
 * Recommendation numbers for when a customer tries to change the total across multiple contracts and billing accounts
 */
export type AdvanceAmountTotalsDto = {
    /**
     * Total current amount
     */
    currentAmount?: number | null;
    /**
     * Total minimum amount
     */
    minAmount?: number | null;
    /**
     * Total maximum amount
     */
    maxAmount?: number | null;
    /**
     * Total recommended amount
     */
    recommendedAmount?: number | null;
};

/**
 * Details of the contract related to the advance payment
 */
export type AdvanceContractDto = {
    /**
     * Identifier of the contract
     */
    contractNumber?: string;
    /**
     * Contract start date
     */
    contractStartDate?: string;
    /**
     * Contract expiry date
     */
    contractExpiryDate?: string | null;
};

/**
 * Details of as
 */
export type AdvanceDetailsDto = {
    /**
     * Current amount
     */
    currentAmount?: number | null;
    /**
     * Minimum amount allowed
     */
    minAmount?: number | null;
    /**
     * Maximum amount allowed
     */
    maxAmount?: number | null;
    /**
     * The recommended amount
     */
    recommendedAmount?: number | null;
    /**
     * Is defaulter
     */
    isDefaulter?: boolean;
    /**
     * Not available
     */
    notAvailable?: boolean;
    changeRequested?: boolean;
    /**
     * Vacancy amount
     */
    vacancyAmount?: number | null;
    /**
     * Vacancy date until
     */
    vacancyUntil?: string | null;
    /**
     * Whether a vacancy request is ongoing
     */
    vacancyChangeRequested?: boolean;
    /**
     * Amount of months until meter reading month
     */
    monthsUntilMeterReadingMonth?: number | null;
    /**
     * Amount due for meter reading month">
     */
    amountDueForMeterReadingMonth?: number | null;
};

/**
 * Details of the meter related to the advance payment
 */
export type AdvanceMeterDto = {
    /**
     * EAN of the meter
     */
    ean?: string;
    meterType?: MeterType;
    energyType?: EnergyTypeDto;
    serviceComponent?: MeterService;
    invoiceFrequency?: InvoiceFrequency;
    /**
     * Month in which the meter gets read
     */
    meterReadingMonth?: number | null;
    /**
     * Month in which the yearly bill occurs
     */
    yearlyBillMonth?: number | null;
};

/**
 * Asset type
 */
export type AssetTypeDto = 'Vehicle';

/**
 * Available products response.
 */
export type AvailableProductsInfoResponse = {
    /**
     * The campaign property
     */
    campaign?: string | null;
    /**
     * The couponCode property
     */
    couponCode?: string | null;
    /**
     * The currencyCode property
     */
    currencyCode?: string | null;
    customerInfo?: OrderCustomerInfo;
    errorDetails?: EnecoErrorDetails;
    /**
     * The locale property
     */
    locale?: string | null;
    /**
     * The products property
     */
    products?: Array<EnecoProduct> | null;
    /**
     * The productType property
     */
    productType?: string | null;
    /**
     * The success property
     */
    success?: boolean | null;
};

/**
 * Bill insert with reason
 */
export type BillInsertDto = {
    /**
     * Reason code
     */
    reasonCode?: string;
};

/**
 * List of Bill inserts with reason
 */
export type BillInsertListDto = {
    /**
     * List of the Reason codes
     */
    billInserts?: Array<BillInsertDto>;
};

/**
 * Eligibility information for a single billing account
 */
export type BillingAccountPaymentPlanEligibilityDto = {
    /**
     * Billing account number
     */
    billingAccountNumber?: string;
    /**
     * Whether the billing account is eligible
     */
    isEligible?: boolean;
    conditions?: PaymentPlanEligibilityConditions;
};

/**
 * Meters associated with a billing account
 */
export type BillingAccountRelatedMeterResponse = {
    /**
     * EAN of the meter
     */
    ean?: string;
    energyType?: EnergyTypeDto;
};

/**
 * The calculate meter deviation response class
 */
export type CalculateMeterDeviationResponse = {
    summaryDelta?: RectificationDeltaDto;
    /**
     * Delta per contested values
     */
    details?: Array<RectificationDeltaResultResponse>;
};

/**
 * Chargebee Status
 */
export type ChargebeeStatus = 0 | 1 | 2;

/**
 * Request that contains the child account linking invite details
 */
export type ChildAccountLinkingInviteRequest = {
    /**
     * Alias of the invitee
     */
    alias?: string | null;
    /**
     * Email of the invitee
     */
    email?: string;
    linkingType?: AccountLinkingTypeDto;
};

/**
 * Body containing details link a child account
 */
export type ChildAccountLinkingRequest = {
    /**
     * Session token
     */
    sessionToken?: string;
    /**
     * Alias the user selected
     */
    alias?: string;
};

/**
 * Response for comfort bonus card
 */
export type ComfortBonusCardResponse = NextBestActionCardResponse;

/**
 * Comfort bonus level based on the 'customer's lifetime'
 */
export type ComfortBonusLevelDto = 'LEVEL0' | 'LEVEL1' | 'LEVEL2';

/**
 * Company data for a crm account
 */
export type CompanyDataDto = {
    /**
     * Name of the company
     */
    name?: string;
    formatType?: CompanyFormatTypeDto;
    vatType?: CompanyVatTypeDto;
    /**
     * Company number
     */
    companyNumber?: string;
};

/**
 * Format of the company
 */
export type CompanyFormatTypeDto = 'None' | 'BV' | 'CV' | 'CommV' | 'Eenmanszaak' | 'FV' | 'TijdelijkeMaatschap' | 'VOF' | 'VZW' | 'NV' | 'Maatschap';

/**
 * VAT type of the company
 */
export type CompanyVatTypeDto = 'VatRequired' | 'VatNotRequired' | 'CompanyNumberRequested';

/**
 * Response for complete move file card
 */
export type CompleteMoveFileCardResponse = NextBestActionCardResponse;

/**
 * Response for ItsMe configuration card
 */
export type ConfigureItsMeCardResponse = NextBestActionCardResponse;

/**
 * The consumption data for a specific period
 */
export type ConsumptionData = {
    energyType?: EnergyTypeDto;
    direction?: DirectionTypeCodeDto;
    meterTimeFrameType?: TimeframeTypeCodeDto;
    reason?: ReasonDto;
    basedOn?: MeterReadingType;
    /**
     * Value of the consumption data
     */
    value?: number;
    unitOfMeasure?: UnitOfMeasureDto;
};

/**
 * The period for which the data gets fetched
 */
export type ConsumptionPeriod = {
    /**
     * The start date of the period
     */
    startDate?: string;
    /**
     * The end date of the period
     */
    endDate?: string;
    /**
     * The consumption data for this period
     */
    data: Array<ConsumptionData>;
};

/**
 * The response for the consumption data
 */
export type ConsumptionResponse = {
    consumptionType?: ConsumptionTypeDto;
    timeFrame?: TimeFrameDto;
    displayMode?: DisplayModeDto;
    /**
     * The start date of the current view period
     */
    startDate?: string;
    /**
     * The end date of the current view period
     */
    endDate?: string;
    /**
     * The periods for which consumption data is available
     */
    periods?: Array<ConsumptionPeriod>;
};

/**
 * Represents the type of consumption data to be retrieved.
 */
export type ConsumptionTypeDto = 'Total' | 'Electricity' | 'Gas' | 'Injection' | 'PeakValues';

/**
 * Representation of the customers contact address
 */
export type ContactAddressDto = {
    /**
     * Street of the address
     */
    street?: string;
    /**
     * Zip code of the address
     */
    zipCode?: string;
    /**
     * Municipality of the address
     */
    municipality?: string;
    /**
     * House number of the address
     */
    houseNumber?: string;
    /**
     * Bus of the address
     */
    bus?: string | null;
    /**
     * Country of the address
     */
    country?: string;
};

/**
 * Represents the metadata and identification details for a file archived in the system.
 */
export type ContactFormAttachmentDto = {
    /**
     * The unique identifier assigned to a file within the Ixos archive system, used for retrieval
     * and management.
     */
    ixosArchiveId?: string;
    /**
     * The name of the file as stored in the archive, excluding file extension, providing a
     * human-readable reference.
     */
    fileName?: string;
    /**
     * The extension of the file, indicating its format and type, such as .pdf, .docx, etc.
     */
    fileExtension?: string;
};

export type ContactFormAttachmentResponse = {
    fileExtension?: string | null;
    fileName?: string | null;
    ixosArchiveId?: string | null;
};

/**
 * Represents a contact's personal and professional information, including address details, communication preferences,
 * and identification numbers.
 */
export type ContactFormDto = {
    /**
     * A list of attachments related to the contact case, typically representing documents or files
     * provided by the contact.
     */
    attachments?: Array<ContactFormAttachmentDto> | null;
    /**
     * The post office box number where the contact prefers to receive mail.
     */
    box?: string | null;
    /**
     * The city of residence or work associated with the contact.
     */
    city?: string | null;
    /**
     * Indicates if the contact information pertains to a company or organization rather than an
     * individual.
     */
    company?: boolean | null;
    /**
     * The name of the company or organization the contact is associated with.
     */
    companyName?: string | null;
    /**
     * The registration or identification number for the company associated with the contact.
     */
    companyNumber?: string | null;
    /**
     * A unique identifier or reference number assigned to the contact in the context of customer
     * relationships or services.
     */
    customerNumber?: string | null;
    /**
     * The email address used by the contact for electronic communication.
     */
    email?: string | null;
    /**
     * The first name or given name of the contact.
     */
    firstName?: string | null;
    gender?: GenderFormContactDto;
    language?: ContactFormLanguageDto;
    /**
     * The last name or family name of the contact.
     */
    lastName?: string | null;
    /**
     * A specific question or query posed by the contact, typically related to the subject or context
     * of communication.
     */
    question?: string | null;
    /**
     * The street address where the contact resides or works.
     */
    street?: string | null;
    /**
     * The number associated with the street address of the contact.
     */
    streetNumber?: string | null;
    /**
     * The subject or main topic of communication or inquiry from the contact.
     */
    subject?: string | null;
    /**
     * Additional subject details or subcategories related to the main subject of inquiry from
     * the contact.
     */
    subLevelSubject?: string | null;
    /**
     * The telephone number used by the contact for voice communication.
     */
    telephone?: string | null;
    /**
     * The postal code or zip code corresponding to the contact's location.
     */
    zipCode?: string | null;
    /**
     * Token for reCaptcha validation.
     */
    recaptchaToken?: string | null;
};

/**
 * Kinds of language
 */
export type ContactFormLanguageDto = 'DUTCH' | 'FRENCH';

/**
 * Contested values entered by user
 */
export type ContestedValue = {
    /**
     * Date entered by the user
     */
    contestedDate?: string;
    /**
     * Index entered by the user
     */
    contestedIndex?: number;
};

/**
 * Status that a contract can be in
 */
export type ContractProductStatusDto = 'None' | 'WaitingForApproval' | 'InProgress' | 'WaitingForDelivery' | 'Active' | 'RequestedTermination' | 'Terminated';

/**
 * Move address
 */
export type CorrespondenceAddressDto = {
    /**
     * Country of the address
     */
    country?: string;
    /**
     * Postal code of the address
     */
    postalCode?: string;
    /**
     * Municipality of the address
     */
    municipality?: string;
    /**
     * Street of the address
     */
    street?: string;
    /**
     * House number of the address
     */
    houseNumber?: string;
    /**
     * Bus of the address
     */
    bus?: string;
};

/**
 * Represents the different types of cost categories used in price simulation.
 */
export type CostCategoryTypeDto = 'EnergyCost' | 'NetTariffCost' | 'TaxationCost' | 'Promotion' | 'Total';

/**
 * Represents a cost category with its type, value and detailed cost breakdown.
 */
export type CostDto = {
    costCategoryType?: CostCategoryTypeDto;
    /**
     * The monetary value of this cost.
     */
    value?: number;
    /**
     * The detailed breakdown of this cost category.
     */
    detailedCosts?: Array<DetailedCostDto>;
};

/**
 * Create a new asset
 */
export type CreateAssetRequestDto = {
    assetType: AssetTypeDto;
    /**
     * Identifier in external system
     */
    externalAssetId?: string | null;
    vehicle?: VehicleAssetResourceDto;
};

/**
 * Request to create a new payment plan
 */
export type CreatePaymentPlanRequest = {
    /**
     * Amount of slices the user wants to pay in
     */
    amountOfSlices?: number;
};

/**
 * Create subscription
 */
export type CreateSubscriptionRequestDto = {
    type: SubscriptionTypeDto;
    tier: SubscriptionTierDto;
    /**
     * Subscription start date
     */
    startDate: string;
    /**
     * Optional subscription end date
     */
    endDate?: string | null;
    /**
     * Consent version
     */
    consentVersion: string;
};

/**
 * Contains an account claim entry
 */
export type CrmAccountClaimDto = {
    /**
     * Account number of the account
     */
    crmAccountNumber?: string;
    /**
     * Alias of the account
     */
    alias?: string;
    accessType?: AccessTypeDto;
};

/**
 * Language in which the csv should be
 */
export type CsvLanguage = 'Dutch' | 'French';

/**
 * Response for current advance amount vs recommended advance amount card
 */
export type CurrentAdvanceAmountVsRecommendedAvanceAmountCardResponse = NextBestActionCardResponse;

/**
 * Customer info response
 */
export type CustomerInfoResponse = {
    /**
     * Customer id
     */
    enecoId?: string | null;
    chargebeeStatus?: ChargebeeStatus;
    customerPaymentStatus?: CustomerPaymentStatus;
    /**
     * Total amount due
     */
    totalAmountDue?: number | null;
    customer?: OrderCustomer;
    dongle?: DongleInfo;
    /**
     * Invoice info
     */
    invoices?: Array<InvoiceInfo> | null;
    paymentMethod?: PaymentMethodInfo;
    /**
     * The promotionalCreditsLeft property
     */
    promotionalCreditsLeft?: number | null;
    /**
     * The promotionalCreditsTotal property
     */
    promotionalCreditsTotal?: number | null;
    /**
     * Subscriptions
     */
    subscriptions?: Array<SubscriptionInfo> | null;
};

/**
 * The customer's language
 */
export type CustomerLanguageDto = 'English' | 'Dutch' | 'French';

/**
 * Customer payment status
 */
export type CustomerPaymentStatus = 'AllGood' | 'InDunning';

/**
 * Type of customer
 */
export type CustomerTypeDto = 'Residential' | 'SOHO';

/**
 * Delete meter reading
 */
export type DeleteMeterReadingRequest = {
    /**
     * Ean of the meter reading
     */
    ean?: string;
    /**
     * Date of the meter reading
     */
    date?: string;
};

/**
 * Request that allows the customer to update the correspondence address
 */
export type DetailCorrespondenceUpdateRequest = {
    customAddress?: CorrespondenceAddressDto;
    selectedAddress?: SelectedCorrespondenceAddressDto;
    /**
     * Date from which invoices should be sent to this selected address
     */
    correspondenceDate?: string | null;
};

/**
 * Request to update move meter details
 */
export type DetailMeterUpdateRequest = {
    energyType?: MoveEnergyTypeDto;
    /**
     * The old meter's EAN
     */
    oldEAN?: string | null;
    /**
     * The meter's EAN
     */
    ean?: string | null;
    /**
     * The old meter's number
     */
    oldMeterNumber?: string | null;
    /**
     * The meter's number
     */
    meterNumber?: string | null;
    /**
     * Value of the meter
     */
    meterValue?: number | null;
    /**
     * Day meter value
     */
    dayMeterValue?: number | null;
    /**
     * Night meter value
     */
    nightMeterValue?: number | null;
    /**
     * Exclusive night meter value
     */
    nightExclusiveMeterValue?: number | null;
};

/**
 * Update the old address new owner
 */
export type DetailOldAddressNewOwnerContactDetailsUpdateRequest = {
    representation?: Representation;
    /**
     * First name of the new owner
     */
    firstName?: string | null;
    /**
     * Last name of the new owner
     */
    lastName?: string | null;
    /**
     * Phone number of the new owner
     */
    phoneNumber?: string | null;
    /**
     * Email of the new owner
     */
    email?: string | null;
};

/**
 * Represents a detailed cost item with its type and value.
 */
export type DetailedCostDto = {
    costType?: DetailedCostTypeDto;
    /**
     * The monetary value of this detailed cost.
     */
    value?: number;
};

/**
 * Represents the different types of detailed costs used in price simulation.
 */
export type DetailedCostTypeDto = 'FixedFee' | 'Usage' | 'PurchaseRate' | 'ProsumentRate' | 'DataManagement' | 'CapacityRate';

/**
 * Direction type code
 */
export type DirectionTypeCodeDto = 'Consumption' | 'Production' | 'Injection' | 'OffTake';

/**
 * Discount status
 */
export type DiscountStatus = 'None' | 'Active' | 'Expired';

/**
 * Represents the display mode for consumption data.
 */
export type DisplayModeDto = 'Consumption' | 'Cost';

/**
 * Dongle info response
 */
export type DongleInfo = {
    dongleStatus?: DongleStatus;
    /**
     * The orderDate property
     */
    orderDate?: string | null;
    purchaseStatus?: PurchaseStatus;
    shippingAddress?: Address;
    shippingStatus?: ShippingStatus;
};

/**
 * Dongle Status
 */
export type DongleStatus = 'Unknown' | 'NotInstaled' | 'Active' | 'NotActive';

/**
 * Dunning status
 */
export type DunningStatus = 'InProgress' | 'Success' | 'Exhausted' | 'Stopped';

/**
 * A dynamic price record
 */
export type DynamicPriceRecord = {
    /**
     * The day on for which the price is valid
     */
    date?: string;
    /**
     * The time for which the price is valid
     */
    time?: string;
    /**
     * The price
     */
    price?: number;
};

/**
 * A container with dynamic pricing records
 */
export type DynamicPricingResponse = {
    records?: Array<DynamicPriceRecord>;
};

/**
 * Uploaded file details
 */
export type EODFileUploadResponse = {
    /**
     * File reference
     */
    fileReference?: string;
    /**
     * File name
     */
    fileName?: string;
};

/**
 * Response containing multiple kinds of eligibility characteristics
 */
export type EligibilitiesResponse = {
    signedAgreements?: SignedAgreementsDto;
};

/**
 * Eneco discount.
 */
export type EnecoDiscount = {
    /**
     * The campaignCodes property
     */
    campaignCodes?: Array<(string)> | null;
    /**
     * The detailsUrl property
     */
    detailsUrl?: string | null;
    /**
     * The discountAmount property
     */
    discountAmount?: number | null;
    /**
     * The discountPercentage property
     */
    discountPercentage?: number | null;
    /**
     * The durationLength property
     */
    durationLength?: number | null;
    durationUnit?: EnecoDiscountDurationUnit;
    /**
     * The id property
     */
    id?: string | null;
    /**
     * The invoiceName property
     */
    invoiceName?: string | null;
    /**
     * The name property
     */
    name?: string | null;
    /**
     * The notes property
     */
    notes?: string | null;
    /**
     * The redemingCouponCode property
     */
    redemingCouponCode?: string | null;
    /**
     * The requireCouponCode property
     */
    requireCouponCode?: boolean | null;
};

/**
 * Eneco discount duration unit.
 */
export type EnecoDiscountDurationUnit = 'OneTime' | 'Day' | 'Week' | 'Month' | 'Year' | 'Forever';

/**
 * Eneco Error Details
 */
export type EnecoErrorDetails = {
    /**
     * The callerMessage property
     */
    callerMessage?: string | null;
    /**
     * The customerMessage property
     */
    customerMessage?: string | null;
    /**
     * The erroMessage property
     */
    erroMessage?: string | null;
    /**
     * The errorCode property
     */
    errorCode?: string | null;
    /**
     * The errorLocation property
     */
    errorLocation?: string | null;
    /**
     * The primary error message.
     */
    message?: string;
};

/**
 * Eneco order discount.
 */
export type EnecoOrderDiscount = {
    /**
     * The couponCode property
     */
    couponCode?: string | null;
    /**
     * The id property
     */
    id?: string | null;
    /**
     * The redeemingCouponCode property
     */
    redeemingCouponCode?: string | null;
};

/**
 * Eneco order extra.
 */
export type EnecoOrderExtra = {
    /**
     * The discounts property
     */
    discounts?: Array<EnecoOrderDiscount> | null;
    /**
     * The productId property
     */
    productId?: string | null;
    /**
     * The quantity property
     */
    quantity?: number | null;
};

/**
 * Eneco order product.
 */
export type EnecoOrderProduct = {
    /**
     * The discounts property
     */
    discounts?: Array<EnecoOrderDiscount> | null;
    /**
     * The extras property
     */
    extras?: Array<EnecoOrderExtra> | null;
    /**
     * The productId property
     */
    productId?: string | null;
    /**
     * The quantity property
     */
    quantity?: number | null;
};

/**
 * Eneco product.
 */
export type EnecoProduct = {
    /**
     * The billingPeriod property
     */
    billingPeriod?: number | null;
    /**
     * The description property
     */
    description?: string | null;
    /**
     * The discounts property
     */
    discounts?: Array<EnecoDiscount> | null;
    /**
     * The extras property
     */
    extras?: Array<EnecoProductExtra> | null;
    /**
     * The familyId property
     */
    familyId?: string | null;
    /**
     * The features property
     */
    features?: Array<(string)> | null;
    /**
     * The isMetered property
     */
    isMetered?: boolean | null;
    /**
     * The isShippable property
     */
    isShippable?: boolean | null;
    /**
     * The mainDescription property
     */
    mainDescription?: string | null;
    /**
     * The mainId property
     */
    mainId?: string | null;
    /**
     * The mainName property
     */
    mainName?: string | null;
    /**
     * The maximumQuantity property
     */
    maximumQuantity?: number | null;
    /**
     * The meteredPriceFormula property
     */
    meteredPriceFormula?: string | null;
    /**
     * The minimumQuantity property
     */
    minimumQuantity?: number | null;
    /**
     * The name property
     */
    name?: string | null;
    periodUnit?: EnecoProductPeriodUnit;
    /**
     * The priceIsTaxExcluded property
     */
    priceIsTaxExcluded?: boolean | null;
    priceModel?: EnecoProductPriceModel;
    /**
     * The productId property
     */
    productId?: string | null;
    /**
     * The quantity property
     */
    quantity?: number | null;
    /**
     * The taxPercentage property
     */
    taxPercentage?: number | null;
    /**
     * The unitPrice property
     */
    unitPrice?: number | null;
};

/**
 * Eneco product.
 */
export type EnecoProductExtra = {
    /**
     * The billingPeriod property
     */
    billingPeriod?: number | null;
    /**
     * The description property
     */
    description?: string | null;
    /**
     * The discounts property
     */
    discounts?: Array<EnecoDiscount> | null;
    /**
     * The familyId property
     */
    familyId?: string | null;
    /**
     * The features property
     */
    features?: Array<(string)> | null;
    /**
     * The isMetered property
     */
    isMetered?: boolean | null;
    /**
     * The isShippable property
     */
    isShippable?: boolean | null;
    /**
     * The mainDescription property
     */
    mainDescription?: string | null;
    /**
     * The mainId property
     */
    mainId?: string | null;
    /**
     * The mainName property
     */
    mainName?: string | null;
    /**
     * The maximumQuantity property
     */
    maximumQuantity?: number | null;
    /**
     * The meteredPriceFormula property
     */
    meteredPriceFormula?: string | null;
    /**
     * The minimumQuantity property
     */
    minimumQuantity?: number | null;
    /**
     * The name property
     */
    name?: string | null;
    periodUnit?: EnecoProductExtraPeriodUnit;
    /**
     * The priceIsTaxExcluded property
     */
    priceIsTaxExcluded?: boolean | null;
    priceModel?: EnecoProductExtraPriceModel;
    /**
     * The productId property
     */
    productId?: string | null;
    /**
     * The quantity property
     */
    quantity?: number | null;
    /**
     * The taxPercentage property
     */
    taxPercentage?: number | null;
    /**
     * The unitPrice property
     */
    unitPrice?: number | null;
};

/**
 * Eneco product extra period unit.
 */
export type EnecoProductExtraPeriodUnit = 'OneTime' | 'Day' | 'Week' | 'Month' | 'Year' | 'Forever';

/**
 * Eneco product extra price model.
 */
export type EnecoProductExtraPriceModel = 'FlatFee' | 'PerUnit' | 'Tiered' | 'Volume' | 'Stairstep';

/**
 * Eneco product period unit.
 */
export type EnecoProductPeriodUnit = 'OneTime' | 'Day' | 'Week' | 'Month' | 'Year' | 'Forever';

/**
 * Eneco product price model.
 */
export type EnecoProductPriceModel = 'FlatFee' | 'PerUnit' | 'Tiered' | 'Volume' | 'Stairstep';

/**
 * The carrier for invoices
 */
export type EnergyMonitorFrequency = 'Never' | 'Weekly' | 'Biweekly' | 'Monthly';

/**
 * Type of energy that can be delivered
 */
export type EnergyTypeDto = 'Electricity' | 'Gas';

export type Error = {
    readonly code?: string;
    readonly description?: string;
    type?: ErrorType;
    readonly numericType?: number;
    readonly metadata?: {
        [key: string]: unknown;
    } | null;
};

export type ErrorType = 0 | 1 | 2 | 3 | 4 | 5 | 6;

/**
 * Response for fallback card
 */
export type FallbackCardResponse = NextBestActionCardResponse;

/**
 * Contains forgot email details
 */
export type ForgotEmailResponse = {
    /**
     * Obfuscated email to which an email was sent
     */
    email?: string;
};

/**
 * Gender of the CRM accounts' user
 */
export type GenderDto = 'Unspecified' | 'Male' | 'Female';

/**
 * Kinds of gender
 */
export type GenderFormContactDto = 'Male' | 'Female';

/**
 * Representation a CRM accounts contact details
 */
export type GetAccountContactDetailsResponse = {
    /**
     * Identifier of the account
     */
    accountNumber?: string;
    /**
     * First name of the account
     */
    firstName?: string;
    /**
     * Last name of the account
     */
    lastName?: string;
    /**
     * Email of the account
     */
    email?: string;
    /**
     * Creation date of the account
     */
    dateOfBirth?: string | null;
    /**
     * Created date
     */
    createdDate?: string;
    /**
     * Telephone number of the account
     */
    telephoneNumber?: string | null;
    /**
     * Mobile number of the account
     */
    mobileNumber?: string | null;
    /**
     * Gezinsbond number of the account
     */
    gezinsBondNumber?: string | null;
    customerType?: CustomerTypeDto;
    gender?: GenderDto;
    language?: CustomerLanguageDto;
    contactAddress?: ContactAddressDto;
    companyData?: CompanyDataDto;
    comfortBonusLevel?: ComfortBonusLevelDto;
};

/**
 * Representation a CRM accounts contact preferences
 */
export type GetAccountContactPreferencesResponse = {
    invoicePreferences?: InvoiceContactPreference;
    /**
     * Does the user want Advance invoices
     */
    advancePayments?: boolean;
    /**
     * Does the user want more information about Eneco
     */
    enecoNews?: boolean;
    /**
     * Doest the user want more info about Promotions or third party partners
     */
    promotions?: boolean;
    energyMonitorFrequency?: EnergyMonitorFrequency;
    /**
     * Whether the user has any active product that has an E-Billing requirement
     */
    hasEBillingPromotion?: boolean;
};

/**
 * Details of a CRM account from the viewpoint of the logged in user
 */
export type GetAccountDetailsResponse = {
    /**
     * account number of the crm account
     */
    crmAccountNumber?: string;
    /**
     * the configured alias for the crm account
     */
    alias?: string;
};

/**
 * A container with dynamic pricing records
 */
export type GetAddressResponse = {
    addresses?: Array<GetAddressResponseAddress>;
};

/**
 * A dynamic price record
 */
export type GetAddressResponseAddress = {
    /**
     * The zipCode
     */
    zipcode?: string;
    /**
     * the unique city identification
     */
    cityId?: number | null;
    /**
     * The city
     */
    city?: string;
    /**
     * The unique street identification
     */
    streetId?: number | null;
    /**
     * The street
     */
    street?: string;
};

/**
 * Single asset response
 */
export type GetAssetResponse = {
    /**
     * Asset identifier
     */
    id: string;
    assetType: AssetTypeDto;
    /**
     * Asset identifier in external system
     */
    externalAssetId?: string | null;
    vehicle?: VehicleAssetResourceDto;
};

/**
 * Single asset response
 */
export type GetAssetsResponse = {
    assets?: Array<GetAssetResponse>;
};

/**
 * Details for a product within a contract.
 */
export type GetContractDetailsForProductResponse = {
    /**
     * When the contract becomes active
     */
    activeFrom?: string;
    /**
     * When the contract ends
     */
    activeUntil?: string | null;
    /**
     * Id of the contract
     */
    contractId?: string;
    type?: EnergyTypeDto;
    contractProductStatusExternal?: ContractProductStatusDto;
    /**
     * Name of the product
     */
    productName?: string;
    /**
     * Address to which the product is delivered
     */
    address?: string;
    /**
     * Address Id of the address
     */
    addressId?: string;
    tariff?: GetContractDetailsForProductResponseTariff;
    meter?: GetContractDetailsForProductResponseMeter;
    gridOperator?: GetContractDetailsForProductResponseGridOperator;
    renewal?: RenewalResponse;
    /**
     * The start date of the tariff chart
     */
    tariffChartStartDate?: string | null;
};

/**
 * Describes the grid operator for this product
 */
export type GetContractDetailsForProductResponseGridOperator = {
    /**
     * Name of the grid operator
     */
    name?: string;
};

/**
 * Meter details for a given product
 */
export type GetContractDetailsForProductResponseMeter = {
    meterType?: MeterType;
    /**
     * EAN code for the meter
     */
    ean?: string;
    /**
     * Number of the meter
     */
    meterNumber?: string;
    service?: MeterService;
    meterRegime?: MeterRegimeHolder;
    invoiceFrequency?: InvoiceFrequencyHolder;
    /**
     * The registers that the meter contains
     */
    registers?: Array<RegisterHolder>;
};

/**
 * Tariff details for a given product
 */
export type GetContractDetailsForProductResponseTariff = {
    /**
     * The name of the product
     */
    product?: string;
    productType?: ProductType;
    /**
     * Discounts active on this product
     */
    promotions?: Array<Promotion>;
    /**
     * Is social tariff
     */
    isSocialTariff?: boolean;
    /**
     * Links to the tariff card by language, which is currently active
     */
    currentTariff?: Array<TariffDetails>;
    /**
     * Links to the tariff card by language, which will be active once the current tariffs expire
     */
    nextTariff?: Array<TariffDetails> | null;
};

/**
 * Representation of a single invoice row
 */
export type GetInvoiceDto = {
    /**
     * The invoice identification
     */
    invoiceId?: string;
    invoiceType?: InvoiceTypeDto;
    /**
     * The month to which the advance invoice relates
     */
    advanceInvoiceMonth?: string | null;
    /**
     * Due date of the invoice
     */
    dueDate?: string | null;
    /**
     * Sent to the customer date
     */
    sentDate?: string | null;
    /**
     * Date of last payment of the customer
     */
    lastPaymentDate?: string | null;
    /**
     * Date when the invoice is created
     */
    invoiceDate?: string | null;
    /**
     * Due amount to pay
     */
    dueAmount?: number;
    /**
     * What amount has already been paid for this invoice?
     */
    paidAmount?: number;
    /**
     * The amount that is till open to pay
     */
    openAmount?: number;
    invoiceStatus?: InvoiceStatusDto;
    paymentMethod?: PaymentMethodDto;
    billInserts?: BillInsertListDto;
    invoiceEnergyType?: InvoiceEnergyTypeDto;
    /**
     * The link to the digital invoice file
     */
    downloadLink?: string;
    /**
     * The billing account the invoice belongs to
     */
    billingAccountNumber?: string;
};

/**
 * Record containing open amount information for a billing account
 */
export type GetInvoicesOpenAmountSummaryRecord = {
    /**
     * The billing account number associated with the invoice
     */
    billingAccountNumber?: string;
    /**
     * The amount outstanding for the billing account
     */
    amount?: number;
    /**
     * The number of unpaid invoices for the billing account
     */
    amountOfUnpaidInvoices?: number;
    /**
     * The iban number linked to this billing account
     */
    iban?: string;
    paymentMethod?: PaymentMethodDto;
};

/**
 * Response containing a summary of open invoice amounts
 */
export type GetInvoicesOpenAmountSummaryResponse = {
    /**
     * Collection of open amount summary records
     */
    records?: Array<GetInvoicesOpenAmountSummaryRecord>;
};

/**
 * Paged response containing multiple invoice rows
 */
export type GetInvoicesResponse = {
    /**
     * Multiple invoice rows
     */
    invoices?: Array<GetInvoiceDto>;
    openAmountSummary?: GetInvoicesOpenAmountSummaryResponse;
};

/**
 * A meter with monthly invoice period
 */
export type GetMonthlyInvoicePeriodDtoMeter = {
    energyType?: EnergyTypeDto;
    /**
     * EAN of the meter
     */
    ean?: string;
    period?: MonthlyInvoicePeriodDto;
    /**
     * Number that signifies CRM account
     */
    crmAccountNumber?: string;
    /**
     * Number that signifies Billing account
     */
    billingAccountNumber?: string;
    /**
     * Contract Id associated with the meter
     */
    contractId?: string;
};

/**
 * Response containing possible already filled in address for the new address
 */
export type GetNewAddressDetailsResponse = {
    address?: MoveAddressDto;
};

/**
 * Collection of active and non-active products for a specific address
 */
export type GetOverviewForAddressResponse = {
    activeProducts?: Array<GetOverviewForAddressResponseItem>;
    expiredProducts?: Array<GetOverviewForAddressResponseItem>;
};

/**
 * Response containing information about the contract and product
 */
export type GetOverviewForAddressResponseItem = {
    /**
     * Id of the contract
     */
    contractId?: string;
    type?: EnergyTypeDto;
    contractProductStatusExternal?: ContractProductStatusDto;
    /**
     * Name of the product
     */
    productName?: string;
    /**
     * Contract start date
     */
    startTime?: string;
    /**
     * Optional contract end date
     */
    endTime?: string | null;
    /**
     * Month in which meter gets read
     */
    meterReadingMonth?: number | null;
    meterType?: MeterTypeDto;
    renewalStatus?: ProductSwitchStatusResponse;
};

/**
 * Contains the profile details for a user
 */
export type GetProfileResponse = {
    /**
     * Whether the user has local login details (okta username/password)
     */
    hasLocalLogin?: boolean;
    /**
     * Whether the user has social login details (third party login provider such as itsme)
     */
    hasItsmeLogin?: boolean;
    /**
     * The display name for the user, aka. the MyEneco Voornaam or ME Voornaam
     */
    displayName?: string;
    /**
     * The email for the user which is configured in okta
     */
    email?: string;
};

/**
 * Collection of all active service delivery points for a specific address
 */
export type GetServiceDeliveryPointOverviewForAddressResponse = {
    serviceDeliveryPoints?: Array<GetServiceDeliveryPointOverviewForAddressResponseItem>;
};

/**
 * Response containing information about the contract and product
 */
export type GetServiceDeliveryPointOverviewForAddressResponseItem = {
    /**
     * Id of the contract
     */
    contractId?: string;
    /**
     * The ean number
     */
    ean?: string;
    meterType?: MeterType;
    serviceComponent?: MeterService;
    type?: EnergyTypeDto;
    /**
     * Which registers the meter has
     */
    meterRegisterTypes?: Array<RegisterType>;
    meterRegime?: MeterRegimeHolder;
    invoiceFrequency?: InvoiceFrequencyHolder;
};

/**
 * Single subscription response
 */
export type GetSubscriptionResponse = {
    /**
     * Subscription identifier
     */
    id: string;
    type: SubscriptionTypeDto;
    tier: SubscriptionTierDto;
    /**
     * Start date of subscription
     */
    startDate: string;
    /**
     * Optional end date of subscription
     */
    endDate?: string | null;
    status: SubscriptionStatusDto;
    /**
     * List of assets for this subscription
     */
    assets: Array<GetAssetResponse>;
};

/**
 * Get all subscriptions response
 */
export type GetSubscriptionsResponse = {
    /**
     * List of subscriptions
     */
    subscriptions?: Array<GetSubscriptionResponse>;
};

/**
 * Represents a record containing details of a global amount simulation.
 */
export type GlobalAmountSimulationRecord = {
    /**
     * The EAN of the meter
     */
    ean?: string;
    /**
     * The contract number
     */
    contractNumber?: string;
    /**
     * The simulated amount for the specified EAN and contract
     */
    amount?: number;
};

/**
 * Represents the response for a global amount simulation operation.
 */
export type GlobalAmountSimulationResponse = {
    records?: Array<GlobalAmountSimulationRecord>;
};

/**
 * Contains details about which person is being impersonated
 */
export type ImpersonationRequest = {
    value?: string;
    type?: ImpersonationTypeDto;
};

/**
 * Contains the response of the impersonation flow
 */
export type ImpersonationResponse = {
    /**
     * Id in Okta of the account being impersonated
     */
    actsAsIdentityProviderId?: string;
    /**
     * Display name in Okta of the account being impersonated
     */
    actsAsIdentityProviderDisplayName?: string;
    /**
     * Email in Okta of the impersonator
     */
    impersonatorSubject?: string;
    /**
     * Id in Okta of the impersonator
     */
    impersonatorIdentityProviderId?: string;
    impersonationType?: ImpersonationTypeDto;
    impersonationRole?: ImpersonationRoleDto;
    /**
     * Collection of account claim entries
     */
    crmAccountClaims?: Array<CrmAccountClaimDto>;
};

/**
 * Role that the impersonator gets
 */
export type ImpersonationRoleDto = 'Read' | 'Write';

/**
 * The way of impersonation
 */
export type ImpersonationTypeDto = 'Email' | 'Account';

/**
 * Response for intermediate settlement bill including reason card
 */
export type IntermediateSettlementBillInclReasonCardResponse = NextBestActionCardResponse;

/**
 * The carrier for invoices
 */
export type InvoiceContactPreference = 'Email' | 'Post';

/**
 * Current status of the invoice
 */
export type InvoiceEnergyTypeDto = 'Electricity' | 'Gas' | 'Both' | 'Unknown';

/**
 * The Invoice frequency
 */
export type InvoiceFrequency = 'Monthly' | 'Yearly';

/**
 * The frequency of the invoice
 */
export type InvoiceFrequencyDto = 'Monthly' | 'Yearly';

/**
 * Holder that has the current and requested invoice frequency and information about ongoing invoice frequency change requests
 */
export type InvoiceFrequencyHolder = {
    /**
     * Whether a invoice frequency change request is ongoing
     */
    changeRequestPending?: boolean;
    current?: InvoiceFrequency;
    requested?: InvoiceFrequency;
};

/**
 * Invoice info response
 */
export type InvoiceInfo = {
    /**
     * The dueAmount property
     */
    dueAmount?: number | null;
    /**
     * The dueDate property
     */
    dueDate?: string | null;
    dunningStatus?: DunningStatus;
    /**
     * The invoiceAmount property
     */
    invoiceAmount?: number | null;
    /**
     * The invoiceDate property
     */
    invoiceDate?: string | null;
    /**
     * The invoiceNumber property
     */
    invoiceNumber?: string | null;
    /**
     * The lines property
     */
    lines?: Array<InvoiceLineInfo> | null;
    /**
     * The paymentFailureReasonCode property
     */
    paymentFailureReasonCode?: string | null;
    /**
     * The paymentFailureReasonMessage property
     */
    paymentFailureReasonMessage?: string | null;
    status?: InvoiceStatus;
};

/**
 * Invoice line response
 */
export type InvoiceLineInfo = {
    /**
     * The discountAmount property
     */
    discountAmount?: number | null;
    /**
     * The itemName property
     */
    itemName?: string | null;
    /**
     * The quantity property
     */
    quantity?: number | null;
    /**
     * The servicePeriodEnd property
     */
    servicePeriodEnd?: string | null;
    /**
     * The servicePeriodStart property
     */
    servicePeriodStart?: string | null;
    /**
     * The subscriptionId property
     */
    subscriptionId?: string | null;
    /**
     * The totalAmount property
     */
    totalAmount?: number | null;
};

/**
 * Invoice Status
 */
export type InvoiceStatus = 'Posted' | 'Pending' | 'Paid' | 'Overdue' | 'NotPaid' | 'Voided';

/**
 * Current status of the invoice
 */
export type InvoiceStatusDto = 'New' | 'Paid' | 'Unpaid' | 'PartiallyPaid' | 'PaymentInProgress';

/**
 * Type of invoice
 */
export type InvoiceTypeDto = 'AdvanceInvoice' | 'YearlyBill' | 'ClosingBill' | 'Invoice' | 'CorrectionInvoice' | 'IntermediaryInvoice' | 'CreditNote' | 'MonthlyBill' | 'SelfBill';

/**
 * Supported language codes
 */
export type LanguageCodeDto = 'NL' | 'FR' | 'DE';

/**
 * Request which allows a user to link an itsme account to the okta account
 */
export type LinkItsmeRequest = {
    itsmeId?: string;
};

/**
 * Response containing a collection of addresses
 */
export type ListContractAddressesResponse = {
    /**
     * Collection of addresses
     */
    addresses?: Array<ListContractAddressesResponseItem>;
};

/**
 * Response containing address information
 */
export type ListContractAddressesResponseItem = {
    /**
     * Internal id of the address
     */
    id?: string;
    /**
     * The address
     */
    address?: string;
    details?: AddressDetailsDto;
};

/**
 * Location coordinates
 */
export type LocationDto = {
    /**
     * Latitude
     */
    latitude: number;
    /**
     * Longitude
     */
    longitude: number;
};

/**
 * Move location
 */
export type LocationType = 'Old' | 'New';

/**
 * The meter deviation request class
 */
export type MeterDeviationRequest = {
    /**
     * The ean of the meter to rectify
     */
    ean: string;
    /**
     * Gets or sets the value of the meter values
     */
    meterValues: Array<MeterRectificationValueRequest>;
};

/**
 * Represents the index value of a meter reading, including the value itself and the associated time frame type.
 */
export type MeterReadingIndexValue = {
    /**
     * Gets or sets the value of the meter reading index value.
     */
    value?: number;
    meterTimeFrameType?: TimeframeTypeCodeDto;
    direction?: DirectionTypeCodeDto;
    reason?: Reason;
};

/**
 * The meter reading response class
 */
export type MeterReadingResponse = {
    /**
     * Gets or sets the value of the ean
     */
    ean?: string;
    /**
     * Gets or sets the list of index values associated with the meter reading.
     */
    indexValues?: Array<MeterReadingIndexValue>;
    /**
     * Gets or sets the value of the date
     */
    date?: string;
    meterReadingType?: MeterReadingType;
    /**
     * Gets or sets the value of the registered date
     */
    registeredDate?: string;
    utilityType?: UtilityType;
};

/**
 * The meter reading type enum
 */
export type MeterReadingType = 'Official' | 'Informative' | 'CustomerAdded' | 'RLP';

/**
 * Request containing the rectified values compared to the original values
 */
export type MeterRectificationValueRequest = {
    /**
     * The rectified reading value
     */
    contestedReadingValue?: number;
    /**
     * Date of the original observation of the rectified value
     */
    originalDate?: string;
    /**
     * Date of observation of the rectified value
     */
    contestedDate?: string;
    meterTimeFrameType?: TimeframeTypeCodeDto;
};

/**
 * The meter regime
 */
export type MeterRegime = 'NotApplicable' | 'Smr1' | 'Smr3';

/**
 * Holder that has the current and requested meter regimes and information about ongoing meter regime change requests
 */
export type MeterRegimeHolder = {
    /**
     * Whether a meter regime change request is ongoing
     */
    changeRequestPending?: boolean;
    current?: MeterRegime;
    requested?: MeterRegime;
};

/**
 * Kind of service being delivered by the meter
 */
export type MeterService = 'PureOfftake' | 'PureInjection' | 'Compensation' | 'ValorisationInjection' | 'CommercialisationConstraintInjection' | 'CommercialisationInjection' | 'CompensatedOfftake';

/**
 * Kind of meter
 */
export type MeterType = 'Analogue' | 'Digital' | 'DigitalNightExclusive';

/**
 * Physical Meter type
 */
export type MeterTypeDto = 'Analogue' | 'Digital' | 'DigitalNightExclusive';

/**
 * Current invoice period for the monthly invoices
 */
export type MonthlyInvoicePeriodDto = 'NoPreference' | 'Between4And11' | 'Between11And18' | 'Between21And25' | 'Between28And4';

/**
 * Body containing Meters to configure the preferences for
 */
export type MonthlyInvoicePreferenceDto = {
    /**
     * Meters to configure the preferences for
     */
    meters?: Array<GetMonthlyInvoicePeriodDtoMeter>;
};

/**
 * Move address
 */
export type MoveAddressDto = {
    /**
     * Postal code of the address
     */
    postalCode?: string;
    /**
     * Municipality of the address
     */
    municipality?: string;
    /**
     * Street of the address
     */
    street?: string;
    /**
     * House number of the address
     */
    houseNumber?: string;
    /**
     * Bus of the address
     */
    bus?: string;
};

/**
 * Energy types that can get transferred
 */
export type MoveEnergyTypeDto = 'Gas' | 'Electricity';

/**
 * Response for move file created card
 */
export type MoveFileCreatedCardResponse = NextBestActionCardResponse;

/**
 * Details of the move file
 */
export type MoveFileDetailResponse = {
    status?: MoveFileStatusDto;
    flow?: MoveFlowDto;
    reason?: MoveReasonDto;
    /**
     * External request id
     */
    moveRequestId?: string | null;
    /**
     * Status hints
     */
    statusHints?: Array<StatusHintDto>;
    oldAddress?: MoveViewDetailOldAddressSectionResponse;
    newAddress?: MoveViewDetailNewAddressSectionResponse;
    contactDetails?: MoveViewDetailContactDetailsSectionResponse;
    contractDetails?: MoveViewDetailContractDetailsSectionResponse;
    lastSubmittedStep?: MoveStepDto;
    nextStep?: MoveStepDto;
    /**
     * Is move file locked
     */
    isLocked?: boolean;
};

/**
 * Status of the move file
 */
export type MoveFileStatusDto = 'Incomplete' | 'InProgress' | 'Closed' | 'Cancelled' | 'None';

/**
 * For which flow the move was initiated for
 */
export type MoveFlowDto = 'ContinueFlow' | 'CancelFlow';

/**
 * Reasons why people initiate a move
 */
export type MoveReasonDto = 'MoveAndKeepEneco' | 'MoveInWithSomeoneElse' | 'MoveIntoResidentialCareCenter' | 'MoveToOtherCountry' | 'MoveAndLeaveEneco' | 'Other';

/**
 * A meter register
 */
export type MoveRegisterDto = {
    timeframeTypeCode?: TimeframeTypeCodeDto;
    directionTypeCode?: DirectionTypeCodeDto;
    /**
     * Meter reading
     */
    meterReading?: number | null;
};

/**
 * Collection of move file steps
 */
export type MoveStepDto = 'MoveReasons' | 'Eod' | 'NewAddress' | 'NewAddressKeyTransfer' | 'OldAddressKeyTransfer' | 'ConfirmationOverview' | 'Done' | 'OldAddressMeters' | 'CorrespondenceAddress' | 'EodNewAddress' | 'EodOldAddress';

/**
 * Contains information about a meter
 */
export type MoveViewDetailAddressMeterResponse = {
    energyType?: MoveEnergyTypeDto;
    /**
     * EAN of the meter
     */
    ean?: string | null;
    /**
     * Meter number
     */
    meterNumber?: string | null;
    /**
     * Is digital meter
     */
    isDigital?: boolean;
    /**
     * Registers linked to the meter
     */
    registers?: Array<MoveRegisterDto>;
    /**
     * Whether meters are sealed, set by PreSwitch Light
     */
    isSealed?: boolean | null;
    /**
     * Service component, set by PreSwitch Light
     */
    serviceComponent?: string | null;
};

/**
 * Contains contact level information
 */
export type MoveViewDetailContactDetailsSectionResponse = {
    customCorrespondenceAddress?: CorrespondenceAddressDto;
    contactCorrespondenceAddress?: CorrespondenceAddressDto;
    selectedCorrespondenceAddress?: SelectedCorrespondenceAddressDto;
    /**
     * Correspondence date
     */
    correspondenceDate?: string | null;
};

/**
 * Contains contact information of the old address
 */
export type MoveViewDetailContactResponse = {
    representation?: Representation;
    /**
     * First name
     */
    firstName?: string | null;
    /**
     * Last name
     */
    lastName?: string | null;
    /**
     * Phone number
     */
    phoneNumber?: string | null;
    /**
     * Email
     */
    email?: string | null;
};

/**
 * Contains contract level information
 */
export type MoveViewDetailContractDetailsSectionResponse = {
    /**
     * First name
     */
    firstName?: string;
    /**
     * Last name
     */
    lastName?: string;
};

/**
 * Detail section of new address
 */
export type MoveViewDetailNewAddressSectionResponse = {
    status?: MoveFileStatusDto;
    newAddress?: MoveAddressDto;
    /**
     * Delivery date
     */
    deliveryDate?: string | null;
    /**
     * Vacancy date
     */
    vacancy?: string | null;
    eod?: EODFileUploadResponse;
    /**
     * Collection of meter details
     */
    meters?: Array<MoveViewDetailAddressMeterResponse>;
    contact?: MoveViewDetailContactResponse;
    /**
     * Is the new address section locked
     */
    isLocked?: boolean;
    /**
     * Initiating leaving customer date set PreSwitch light
     */
    initiatingLeavingCustomerDate?: string | null;
    /**
     * List of the suggested EANs
     */
    suggestedEans?: Array<SuggestedEanResponse> | null;
};

/**
 * Detail section of old address
 */
export type MoveViewDetailOldAddressSectionResponse = {
    status?: MoveFileStatusDto;
    oldAddress?: MoveAddressDto;
    /**
     * Date of key transfer
     */
    keyTransfer?: string | null;
    /**
     * Vacancy date
     */
    vacancy?: string | null;
    eod?: EODFileUploadResponse;
    /**
     * Collection of meter details
     */
    meters?: Array<MoveViewDetailAddressMeterResponse>;
    contact?: MoveViewDetailContactResponse;
    /**
     * Is the old address section locked
     */
    isLocked?: boolean;
};

/**
 * Available blocks for next best actions
 */
export type NextBestActionBlockDto = 'Pay' | 'Adv' | 'Usage' | 'Sit' | 'Eng' | 'Pers' | 'Any';

/**
 * Next Best Action Card. Possible key values: ActiveProducts, ConfigureItsMe, IntermediateSettlementBillInclReason, PaymentPlanInstallment, PayInvoice, ReferToAccountNumberIfRefund, PushDirectDebit, NoOpenAmount, AdvanceAmountChanged, CurrentAdvanceAmountVsRecommendedAvanceAmount, ShowUsage, CompleteMoveFile, MoveFileCreated, PaymentMethodChangedDueToFailedDirectDebit, ProductConfirmation, ComfortBonus, ShowPersonalInfo, Fallback
 */
export type NextBestActionCardResponse = {
    /**
     * Key of the card
     */
    key?: 'ActiveProducts' | 'ConfigureItsMe' | 'IntermediateSettlementBillInclReason' | 'PaymentPlanInstallment' | 'PayInvoice' | 'ReferToAccountNumberIfRefund' | 'PushDirectDebit' | 'NoOpenAmount' | 'AdvanceAmountChanged' | 'CurrentAdvanceAmountVsRecommendedAvanceAmount' | 'ShowUsage' | 'CompleteMoveFile' | 'MoveFileCreated' | 'PaymentMethodChangedDueToFailedDirectDebit' | 'ProductConfirmation' | 'ComfortBonus' | 'ShowPersonalInfo' | 'Fallback';
    block?: NextBestActionBlockDto;
};

/**
 * Key of the card
 */
export type key = 'ActiveProducts' | 'ConfigureItsMe' | 'IntermediateSettlementBillInclReason' | 'PaymentPlanInstallment' | 'PayInvoice' | 'ReferToAccountNumberIfRefund' | 'PushDirectDebit' | 'NoOpenAmount' | 'AdvanceAmountChanged' | 'CurrentAdvanceAmountVsRecommendedAvanceAmount' | 'ShowUsage' | 'CompleteMoveFile' | 'MoveFileCreated' | 'PaymentMethodChangedDueToFailedDirectDebit' | 'ProductConfirmation' | 'ComfortBonus' | 'ShowPersonalInfo' | 'Fallback';

/**
 * Represents the response for the next best action request, encapsulating
 * a collection of action cards and any related errors.
 */
export type NextBestActionResponse = {
    /**
     * Cards to show
     */
    cards?: Array<(ActiveProductsCardResponse | AdvanceAmountChangedCardResponse | ComfortBonusCardResponse | CompleteMoveFileCardResponse | ConfigureItsMeCardResponse | CurrentAdvanceAmountVsRecommendedAvanceAmountCardResponse | FallbackCardResponse | IntermediateSettlementBillInclReasonCardResponse | MoveFileCreatedCardResponse | NoOpenAmountCardResponse | PayInvoiceCardResponse | PaymentMethodChangedDueToFailedDirectDebitCardResponse | PaymentPlanInstallmentCardResponse | ProductConfirmationCardResponse | PushDirectDebitCardResponse | ReferToAccountNumberIfRefundCardResponse | ShowPersonalInfoCardResponse | ShowUsageCardResponse)>;
    /**
     * Errors while fetching data for cards
     */
    errors?: Array<Error>;
};

/**
 * Response for no open amount card
 */
export type NoOpenAmountCardResponse = NextBestActionCardResponse;

/**
 * Customer
 */
export type OrderCustomer = {
    billingAddress?: Address;
    /**
     * The companyName property
     */
    companyName?: string | null;
    /**
     * The customerId property
     */
    customerId?: string | null;
    /**
     * The email property
     */
    email?: string | null;
    /**
     * The firstName property
     */
    firstName?: string | null;
    /**
     * The lastName property
     */
    lastName?: string | null;
    /**
     * The phone property
     */
    phone?: string | null;
    /**
     * The vatNumber property
     */
    vatNumber?: string | null;
};

/**
 * Order customer info.
 */
export type OrderCustomerInfo = {
    /**
     * The activeFeatures property
     */
    activeFeatures?: Array<(string)> | null;
    billingAddress?: Address;
    /**
     * The companyName property
     */
    companyName?: string | null;
    /**
     * The Eneco ID of the customer
     */
    customerId?: string | null;
    /**
     * The dongleOrderDate property
     */
    dongleOrderDate?: string | null;
    /**
     * The email property
     */
    email?: string | null;
    /**
     * The firstName property
     */
    firstName?: string | null;
    /**
     * The hasUnpaidInvoices property
     */
    hasUnpaidInvoices?: boolean | null;
    /**
     * The lastName property
     */
    lastName?: string | null;
    /**
     * The oldFeatures property
     */
    oldFeatures?: Array<(string)> | null;
    /**
     * The phone property
     */
    phone?: string | null;
    /**
     * The smartStart property
     */
    smartStart?: string | null;
    smartStatus?: OrderCustomerInfoSmartStatus;
    /**
     * The vatNumber property
     */
    vatNumber?: string | null;
};

/**
 * Order customer info smart status.
 */
export type OrderCustomerInfoSmartStatus = 'Unknown' | 'New' | 'InTrial' | 'Future' | 'Active' | 'Paused' | 'NonRenewing' | 'Cancelled' | 'Archived';

/**
 * Order info request
 */
export type OrderInfoRequest = {
    /**
     * The campaign property
     */
    campaign?: string | null;
    /**
     * The cancelUrl property
     */
    cancelUrl?: string | null;
    /**
     * The couponCode property
     */
    couponCode?: string | null;
    /**
     * The currencyCode property
     */
    currencyCode?: string | null;
    customer?: OrderCustomer;
    /**
     * The locale property
     */
    locale?: string | null;
    /**
     * The orderReference property
     */
    orderReference?: string | null;
    /**
     * The products property
     */
    products?: Array<EnecoOrderProduct> | null;
    /**
     * The redirectUrl property
     */
    redirectUrl?: string | null;
    shippingAddress?: Address;
};

/**
 * Body containing details to link an owner account
 */
export type OwnerAccountLinkingRequest = {
    /**
     * Last name of the account
     */
    lastName?: string;
    /**
     * Alias for the crm account
     */
    alias?: string | null;
};

/**
 * Represents the details of a "Pay Invoice" card in the Next Best Actions system.
 * The card provides information about the total open invoice amount and the number of open invoices.
 */
export type PayInvoiceCardDetailsResponse = {
    /**
     * The total monetary value of all open invoices
     */
    totalOpenAmount?: number;
    /**
     * The count of open invoices
     */
    amountOfOpenInvoices?: number;
    /**
     * Amount of billing accounts present on the address
     */
    amountOfBillingAccounts?: number;
};

/**
 * Response for pay invoice card
 */
export type PayInvoiceCardResponse = NextBestActionCardResponse & {
    details?: PayInvoiceCardDetailsResponse;
};

/**
 * Contains payment method information per Billing account
 */
export type PaymentMethodByBillingAccountResponse = {
    /**
     * Billing account number
     */
    billingAccountNumber?: string;
    /**
     * Whether the user currently has an E-billing promo running and has the possibility of losing the promo when changing payment method
     */
    hasEbillingRequirement?: boolean;
    /**
     * Whether the user currently has an Direct Debit promo running and has the possibility of losing the promo when changing payment method
     */
    hasDirectDebitRequirement?: boolean;
    currentPaymentMethod?: PaymentMethodDto;
    /**
     * IBAN details
     */
    iban?: string | null;
    /**
     * Location on which signature was made
     */
    signatureLocation?: string | null;
    /**
     * Date on which signature was made
     */
    signatureDate?: string | null;
    /**
     * Mandate identifier
     */
    mandateId?: string | null;
    /**
     * Billing account related meters
     */
    meters?: Array<BillingAccountRelatedMeterResponse>;
};

/**
 * Response for payment method changed due to failed direct debit card
 */
export type PaymentMethodChangedDueToFailedDirectDebitCardResponse = NextBestActionCardResponse;

/**
 * The way the invoice gets paid
 */
export type PaymentMethodDto = 'DirectDebit' | 'BankTransfer' | 'DirectDebitExcludingYearlyBill';

/**
 * Payment method information
 */
export type PaymentMethodInfo = {
    /**
     * The lastFourDigits property
     */
    lastFourDigits?: string | null;
    methodType?: PaymentMethodType;
    status?: PaymentStatus;
};

/**
 * Contains payment method information
 */
export type PaymentMethodResponse = {
    /**
     * Payment methods per billing account
     */
    items?: Array<PaymentMethodByBillingAccountResponse>;
};

/**
 * Payment method type
 */
export type PaymentMethodType = 'Creditcard' | 'Bancontact' | 'Other';

/**
 * A payment plan
 */
export type PaymentPlanDto = {
    /**
     * Id of the payment plan
     */
    id?: string | null;
    /**
     * Start of the payment plan
     */
    start?: string;
    /**
     * End of the payment plan
     */
    end?: string;
    /**
     * Open amount of the payment plan
     */
    openAmount?: number;
    /**
     * Amount of slices in the payment plan
     */
    amountOfSlices?: number;
    /**
     * Slices in the payment plan
     */
    slices?: Array<PaymentPlanSliceDto>;
    /**
     * Invoices to be paid through the payment plan
     */
    invoices?: Array<GetInvoiceDto>;
    status?: PaymentPlanStatusDto;
    /**
     * Max amount of slices of the payment plan
     */
    maxAmountOfSlices?: number | null;
    /**
     * Billing account for which the payment plan has been created
     */
    billingAccountNumber?: string;
    paymentMethod?: PaymentMethodDto;
};

/**
 * Conditions on which the payment plan eligibility gets determined
 */
export type PaymentPlanEligibilityConditions = {
    /**
     * Whether there is a deactivated payment plan in the last three months
     */
    hasDeactivatedPaymentPlanInLastThreeMonths?: boolean;
    /**
     * Whether the amount is more than the minimum amount
     */
    moreThanMinimumAmount?: boolean;
    /**
     * Whether the amount is more than the maximum amount
     */
    moreThanMaximumAmount?: boolean;
    /**
     * Whether there is an existing payment plan
     */
    hasExistingPaymentPlan?: boolean;
    /**
     * Whether the is a dunning level of 4
     */
    hasDunning?: boolean;
    /**
     * Whether there is no closing or yearly bill
     */
    hasNoClosingOrYearlyBill?: boolean;
};

/**
 * Body containing per-billing-account information on whether a user can request a payment plan or not
 */
export type PaymentPlanEligibilityResponse = {
    /**
     * List of billing accounts with eligibility and conditions
     */
    billingAccounts?: Array<BillingAccountPaymentPlanEligibilityDto>;
};

/**
 * Response for payment plan installment card
 */
export type PaymentPlanInstallmentCardResponse = NextBestActionCardResponse;

/**
 * Slice of a payment plan
 */
export type PaymentPlanSliceDto = {
    /**
     * Start of the slice
     */
    start?: string;
    /**
     * End of the slice
     */
    end?: string;
    /**
     * Amount to be paid for the slice
     */
    amount?: number;
    /**
     * Whether the slice has been paid or not
     */
    paid?: boolean | null;
};

/**
 * Current status of the payment plan
 */
export type PaymentPlanStatusDto = 'Activated' | 'Revoked' | 'Completed' | 'Simulation' | 'Pending';

/**
 * Response containing one or more payment plans
 */
export type PaymentPlansResponse = {
    /**
     * Payment plans
     */
    paymentPlans?: Array<PaymentPlanDto>;
};

/**
 * Payment status
 */
export type PaymentStatus = 'Valid' | 'Expiring' | 'Expired' | 'Invalid' | 'PendingVerification';

/**
 * Response containing the average value of peak data.
 */
export type PeakAverageResponse = {
    /**
     * Average value
     */
    average?: number;
};

/**
 * Period of peak values
 */
export type PeakPeriodDto = {
    /**
     * Start of the period
     */
    periodStart?: string;
    /**
     * End of the period
     */
    periodEnd?: string;
    /**
     * Time of the actual peak value
     */
    date?: string;
    /**
     * Value measured
     */
    value?: number;
};

/**
 * Response containing peak values
 */
export type PeakResponse = {
    consumptionType?: ConsumptionTypeDto;
    timeFrame?: TimeFrameDto;
    /**
     * The start date of the current view period
     */
    startDate?: string;
    /**
     * The end date of the current view period
     */
    endDate?: string;
    unitOfMeasure?: UnitOfMeasureDto;
    /**
     * Periods in which peak values are available
     */
    periods?: Array<PeakPeriodDto>;
};

/**
 * Plan price model
 */
export type PlanPriceModel = 'FixedAmount' | 'PerUnit' | 'Tiered' | 'PerUnitMetered' | 'TieredMetered' | 'FixedPercentage';

/**
 * Type of price aggregation
 */
export type PriceAggregationDto = 'Hourly' | 'Daily' | 'Monthly';

/**
 * Price model
 */
export type PriceModel = 'FixedAmount' | 'PerUnit' | 'Tiered' | 'PerUnitMetered' | 'TieredMetered' | 'FixedPercentage';

/**
 * Represents the compensationtype (service component)
 */
export type PriceSimulationCompensationTypeDto = 'Offtake' | 'Compensation' | 'Valorisation';

/**
 * Represents the details of a price simulation.
 */
export type PriceSimulationDetailsDto = {
    priceSimulationId?: string;
    accountNumber?: string | null;
    isProspect?: boolean;
    customerType?: CustomerTypeDto;
    postalCode?: string;
    energyType?: PriceSimulationEnergyTypeDto;
    usage?: PriceSimulationUsageDto;
    features?: PriceSimulationFeaturesDto;
};

/**
 * Represents the default electricity usage details for price simulation.
 */
export type PriceSimulationElectricityDefaultUsageDto = {
    /**
     * The total electricity usage.
     */
    totalUsage?: number;
    /**
     * The electricity usage exclusively during night hours, nullable if not specified.
     */
    exclusiveNightUsage?: number | null;
};

/**
 * Represents the electricity usage details for a multiple meter configuration in price simulation.
 */
export type PriceSimulationElectricityMultipleUsageDto = {
    /**
     * The electricity usage during peak hours.
     */
    peakUsage?: number;
    /**
     * The electricity usage during off-peak hours.
     */
    offPeakUsage?: number;
};

/**
 * Represents the electricity usage details for a singular meter configuration in price simulation.
 */
export type PriceSimulationElectricitySingularUsageDto = {
    /**
     * The total electricity usage.
     */
    totalUsage?: number;
    /**
     * The electricity usage exclusively during night hours, nullable if not specified.
     */
    exclusiveNightUsage?: number | null;
};

/**
 * Represents the electricity usage details for price simulation.
 */
export type PriceSimulationElectricityUsageDto = {
    defaultUsage?: PriceSimulationElectricityDefaultUsageDto;
    singularMeterUsage?: PriceSimulationElectricitySingularUsageDto;
    multipleMeterUsage?: PriceSimulationElectricityMultipleUsageDto;
    /**
     * The peak power usage, nullable if not specified.
     */
    peakPower?: number | null;
    /**
     * Indicates whether to use average peak power in the simulation.
     */
    takeAveragePeak?: boolean;
    solarPanelsProduction?: PriceSimulationSolarPanelsProductionDto;
};

/**
 * Represents a request for initiating a price simulation email operation.
 */
export type PriceSimulationEmailRequest = {
    /**
     * The email address to send the price simulation to
     */
    email?: string;
    /**
     * Whether the user opted in for promotional content
     */
    promotionalContentOptIn?: boolean;
    language?: CustomerLanguageDto;
};

/**
 * Represents different energy types for price simulation.
 */
export type PriceSimulationEnergyTypeDto = 'Electricity' | 'Gas' | 'Both';

/**
 * Represents various features related to energy usage, installations, and housing for price simulation.
 */
export type PriceSimulationFeaturesDto = {
    /**
     * A list of installations present at the location, such as solar panels or home battery.
     */
    installations?: Array<PriceSimulationInstallationDto>;
    heatingType?: PriceSimulationHeatingTypeDto;
    housingType?: PriceSimulationHousingTypeDto;
    houseHoldSize?: PriceSimulationHouseHoldSizeDto;
    solarPanelInstallationDate?: SolarPanelInstallationDateDto;
    compensationType?: PriceSimulationCompensationTypeDto;
    /**
     * Specifies if the location has a bi-directional meter.
     */
    hasBiDirectionalMeter?: boolean | null;
    meterType?: PriceSimulationMeterTypeDto;
    meterSubType?: PriceSimulationMeterSubTypeDto;
    /**
     * Indicates if an exclusive night meter is present at the location.
     */
    hasExclusiveNightMeter?: boolean | null;
};

/**
 * Represents the gas usage details for price simulation.
 */
export type PriceSimulationGasUsageDto = {
    /**
     * The total gas usage measured in the simulation.
     */
    totalUsage?: number;
};

/**
 * Represents different types of heating for price simulation.
 */
export type PriceSimulationHeatingTypeDto = 'ElectricHeating' | 'GasHeating' | 'HeatPump' | 'Other';

/**
 * Represents different household sizes used in price simulation.
 */
export type PriceSimulationHouseHoldSizeDto = 'One' | 'Two' | 'Three' | 'Four' | 'FiveOrMore';

/**
 * Represents different types of housing used in price simulation.
 */
export type PriceSimulationHousingTypeDto = 'Apartment' | 'FullyEnclosedBuilding' | 'HalfOpenBuilding' | 'OpenBuilding' | 'Other';

/**
 * Represents the response for initiating a price simulation, containing the ID of the initiated price simulation.
 */
export type PriceSimulationInitiatedResponse = {
    /**
     * The unique identifier for the initiated price simulation.
     */
    priceSimulationId?: string;
};

/**
 * Represents different types of installations for price simulation.
 */
export type PriceSimulationInstallationDto = 'SolarPanels' | 'HomeBattery' | 'ChargingStation' | 'HeatPump';

/**
 * Represents sub-types of meter configurations for price simulation.
 */
export type PriceSimulationMeterSubTypeDto = 'Single' | 'Multiple';

/**
 * Represents different types of meters used in price simulation.
 */
export type PriceSimulationMeterTypeDto = 'Analogue' | 'Digital';

/**
 * Represents a product combination option containing optional electricity and gas products.
 */
export type PriceSimulationProductCombinationOptionDto = {
    electricityProduct?: PriceSimulationProductOptionDto;
    gasProduct?: PriceSimulationProductOptionDto;
};

/**
 * Represents an energy product option with its type, name and associated costs.
 */
export type PriceSimulationProductOptionDto = {
    energyType?: PriceSimulationEnergyTypeDto;
    /**
     * The technical name of the product.
     */
    technicalProductName?: string;
    /**
     * The list of costs associated with this product.
     */
    costs?: Array<CostDto>;
};

/**
 * Represents the request for a price simulation.
 */
export type PriceSimulationRequest = {
    /**
     * The account number of the customer. Can be null for prospects.
     */
    accountNumber?: string | null;
    /**
     * Indicates whether the request is from a prospective customer.
     */
    isProspect?: boolean;
    customerType?: CustomerTypeDto;
    /**
     * The postal code of the customer's location.
     */
    postalCode?: string;
    energyType?: PriceSimulationEnergyTypeDto;
    usage?: PriceSimulationUsageDto;
    features?: PriceSimulationFeaturesDto;
};

/**
 * Represents the response containing price simulation details and available product combination options.
 */
export type PriceSimulationResponse = {
    simulationDetails?: PriceSimulationDetailsDto;
    /**
     * The list of available product combination options.
     */
    productCombinationOptions?: Array<PriceSimulationProductCombinationOptionDto>;
};

/**
 * Represents the production details of solar panels used for price simulation.
 */
export type PriceSimulationSolarPanelsProductionDto = {
    /**
     * The amount of energy injected during peak hours.
     */
    peakHoursInjection?: number | null;
    /**
     * The amount of energy injected during off-peak hours.
     */
    offPeakHoursInjection?: number | null;
    /**
     * The total amount of energy injected by the solar panels.
     */
    totalInjection?: number | null;
    /**
     * The total capacity of the solar panel installation in kVA.
     */
    installationTotalKva?: number | null;
};

/**
 * Represents energy usage details for both electricity and gas during a price simulation.
 */
export type PriceSimulationUsageDto = {
    electricity?: PriceSimulationElectricityUsageDto;
    gas?: PriceSimulationGasUsageDto;
};

export type ProblemDetails = {
    type?: string | null;
    title?: string | null;
    status?: number | null;
    detail?: string | null;
    instance?: string | null;
    [key: string]: (unknown | string | number) | undefined;
};

/**
 * Response for product confirmation card
 */
export type ProductConfirmationCardResponse = NextBestActionCardResponse;

/**
 * Current product information available for switching
 */
export type ProductSwitchCurrentProductResponse = {
    /**
     * The technical name of the product
     */
    technicalProductName?: string;
    /**
     * Indicates whether there is a loss of promotional benefits when switching the product
     */
    promoLoss?: boolean;
    /**
     * Indicates whether the product has a social tariff applied
     */
    hasSocialTarriff?: boolean;
    /**
     * Indicates whether the product is currently active
     */
    isActive?: boolean;
    productSwitchStatus?: ProductSwitchStatusResponse;
    /**
     * EAN of the meter
     */
    ean?: string;
    energyType?: EnergyTypeDto;
    /**
     * URL of the tariff chart in Dutch
     */
    tariffChartUrlNl?: string;
    /**
     * URL of the tariff chart in French
     */
    tariffChartUrlFr?: string;
};

/**
 * Product available to switch to
 */
export type ProductSwitchProductResponse = {
    /**
     * The ID of the switch
     */
    switchId?: string;
    /**
     * The technical name of the product
     */
    technicalProductName?: string;
    productType?: ProductType;
    customerType?: CustomerTypeDto;
    /**
     * Indicates whether the product is preferred
     */
    preferred?: boolean;
    /**
     * URL of the tariff chart in Dutch
     */
    tariffChartUrlNl?: string;
    /**
     * URL of the tariff chart in French
     */
    tariffChartUrlFr?: string;
};

/**
 * Represents the options to switch to from a specific product
 */
export type ProductSwitchRecordDto = {
    current?: ProductSwitchCurrentProductResponse;
    /**
     * A list of products available to switch to
     */
    switchOptions?: Array<ProductSwitchProductResponse>;
};

/**
 * Represents the request body containing the product switch ID.
 */
export type ProductSwitchRequest = {
    /**
     * Multiple product switch request records
     */
    records?: Array<ProductSwitchRequestDto>;
};

/**
 * Represents the data transfer object for a product switch request.
 */
export type ProductSwitchRequestDto = {
    /**
     * The International Article Number (EAN) associated with the product. This is a unique identifier used for tracking and identification in retail and logistics.
     */
    ean?: string;
    /**
     * The unique identifier for the product switch operation. This ID is used to reference the specific switch action being requested.
     */
    productSwitchId?: string;
};

/**
 * Represents the response for product switch options.
 */
export type ProductSwitchResponse = {
    /**
     * List of product switch records
     */
    records?: Array<ProductSwitchRecordDto>;
};

/**
 * Represents the status of a product switch operation
 */
export type ProductSwitchStatusResponse = 'PendingProductSwitch' | 'TransitioningToProductSwitch' | 'PendingProductSwitchCancellation' | 'NoProductSwitch';

/**
 * Type of product. Indicates whether there is a fixed or a variable price across a given timespan or the timespan of the product.
 */
export type ProductType = 'Fixed' | 'Variable' | 'Dynamic';

/**
 * Promotion active on a Product
 */
export type Promotion = {
    /**
     * Name of the promotion by language. E.g. 'Loyalty promo Elektriciteit'
     */
    text?: {
        [key: string]: (string);
    };
    /**
     * Disclaimer of the promotion by language
     */
    disclaimer?: {
        [key: string]: (string);
    };
    /**
     * Whether E-Billing is required for this promotion to be valid
     */
    eBillingRequired?: boolean;
};

/**
 * Purchase status
 */
export type PurchaseStatus = 'Unknown' | 'NotPurchased' | 'Purchased';

/**
 * Response for push direct debit card
 */
export type PushDirectDebitCardResponse = NextBestActionCardResponse;

/**
 * Details to update on a CRM account from the viewpoint of the logged in user
 */
export type PutAccountDetailsRequest = {
    /**
     * the CRM account alias
     */
    alias?: string;
};

/**
 * Request to update the meter details
 */
export type PutContractDetailsForProductMeterRequest = {
    meterRegime?: MeterRegime;
};

/**
 * Quality of the reading
 */
export type Reason = 'ADJ' | 'TEMP' | 'RECT' | 'APPR' | 'MISS' | 'EST' | 'OEST' | 'DEF' | 'VAL';

/**
 * Quality of the reading
 */
export type ReasonDto = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

/**
 * The information of a contested meter related to rectification.
 */
export type RectificationContestedMeter = {
    /**
     * EAN number identifying the meter
     */
    ean?: string;
    /**
     * Original date of the reading
     */
    originalDate?: string;
    /**
     * Date the user entered
     */
    correctedDate?: string;
    utilityType?: UtilityType;
    /**
     * List of contested meter reading records
     */
    rectificationContestedMeterReadingRecords?: Array<RectificationContestedMeterReadingRecord>;
};

/**
 * The rectification meter information
 */
export type RectificationContestedMeterReadingRecord = {
    /**
     * The original provided meter reading value
     */
    originalValue?: number;
    /**
     * The meter reading value corrected by the user
     */
    correctedValue?: number;
    timeFrameType?: TimeframeTypeCodeDto;
};

/**
 * The rectification delta response
 */
export type RectificationDeltaDto = 'HasDeviation' | 'NoDeviation';

/**
 * Record containing the deviation result
 */
export type RectificationDeltaResultResponse = {
    delta?: RectificationDeltaDto;
    /**
     * Calculated delta
     */
    calculatedDelta?: number;
    contestedValue?: ContestedValue;
};

/**
 * The rectification attachment info
 */
export type RectificationDocuments = {
    /**
     * Unique identifier for the archived document
     */
    ixosArchiveId?: string;
    /**
     * Name of the attached file
     */
    fileName?: string;
    /**
     * File extension of the attached document
     */
    fileExtension?: string;
};

/**
 * Response type for eligibility check
 */
export type RectificationEligibilityDto = 'Eligible' | 'HasOngoingRectification' | 'HasDigitalMeter';

/**
 * Represents the eligibility per EAN
 */
export type RectificationEligibilityRecordResponse = {
    /**
     * EAN for which eligibility is checked
     */
    ean?: string;
    eligibility?: RectificationEligibilityDto;
};

/**
 * Represents the response for a rectification eligibility check
 */
export type RectificationEligibilityResponse = {
    /**
     * Eligibility value per EAN
     */
    records?: Array<RectificationEligibilityRecordResponse>;
};

/**
 * The meter reading response class
 */
export type RectificationMeterReadingResponse = {
    /**
     * Gets or sets the value of the ean
     */
    ean?: string;
    /**
     * Gets or sets the list of index values associated with the meter reading.
     */
    indexValues?: Array<MeterReadingIndexValue>;
    /**
     * Gets or sets the value of the date
     */
    date?: string;
    meterReadingType?: MeterReadingType;
    /**
     * Gets or sets the value of the registered date
     */
    registeredDate?: string;
    utilityType?: UtilityType;
    meterType?: MeterType;
};

/**
 * Response for refer to account number if refund card
 */
export type ReferToAccountNumberIfRefundCardResponse = NextBestActionCardResponse;

/**
 * Holder that has a meter register
 */
export type RegisterHolder = {
    registerType?: RegisterType;
};

/**
 * Type of register
 */
export type RegisterType = 'Single' | 'Day' | 'Night' | 'ExclusiveNight';

/**
 * Response containing renewal information
 */
export type RenewalResponse = {
    tariff?: GetContractDetailsForProductResponseTariff;
    renewalType?: RenewalTypeDto;
    /**
     * Date of renewal
     */
    nextRenewalDate?: string | null;
    /**
     * Indicates if there are switch options available for the product
     */
    hasProductSwitchOptions?: boolean;
    status?: ProductSwitchStatusResponse;
    /**
     * Id of the product switch
     */
    productSwitchId?: string | null;
};

/**
 * Product renewal type
 */
export type RenewalTypeDto = 'TacitNotChanged' | 'TacitChanged' | 'Regular' | 'Migration' | 'ProductSwitch';

/**
 * Product renewal type
 */
export type Representation = 'Mr' | 'Mrs' | 'Ms';

/**
 * Customer selected correspondence address
 */
export type SelectedCorrespondenceAddressDto = 'Old' | 'New' | 'Custom';

/**
 * The response of validating the session token validity
 */
export type SessionTokenValidityResponse = {
    /**
     * Internal reference of the invite
     */
    inviteReference?: string;
    status?: SessionTokenValidityStatusDto;
    /**
     * The ME voornaam of the owner of the account, given that the session token is in context of a crm account
     */
    displayNameOwner?: string | null;
    /**
     * The email of the owner of the account
     */
    emailOwner?: string | null;
    /**
     * The Crm Account number of the account, given that the session token is in context of a crm account
     */
    crmAccountNumber?: string | null;
    /**
     * The crm account alias of the current user for the account, given that the session token is in context of a crm account
     */
    crmAccountAliasCurrentUser?: string | null;
    /**
     * Invite creation date
     */
    createdAt?: string;
    /**
     * Expiration date of the session token
     */
    expiresAt?: string | null;
    /**
     * Date on which the mail was sent, if already sent
     */
    mailSentAt?: string | null;
};

/**
 * The validity status
 */
export type SessionTokenValidityStatusDto = 'Redeemable' | 'Redeemed' | 'Expired' | 'Revoked';

/**
 * Shipping status
 */
export type ShippingStatus = 'Unknown' | 'NotShipped' | 'Shipped';

/**
 * Response for show personal info card
 */
export type ShowPersonalInfoCardResponse = NextBestActionCardResponse;

/**
 * Response for show usage card
 */
export type ShowUsageCardResponse = NextBestActionCardResponse;

/**
 * Defines which agreements have been signed by the user
 */
export type SignAgreementsRequest = {
    /**
     * Terms of service
     */
    termsOfService?: boolean;
    /**
     * Privacy policy
     */
    privacyPolicy?: boolean;
};

/**
 * Contains the agreement dates
 */
export type SignedAgreementsDto = {
    /**
     * Date of signing the terms of service
     */
    signedTermsOfService?: string | null;
    /**
     * Date of signing the privacy policy
     */
    signedPrivacyPolicy?: string | null;
};

/**
 * Simulate global amount for an advance
 */
export type SimulateGlobalAmountUpdateRequest = {
    /**
     * The proposed amount/>
     */
    amount?: number;
};

/**
 * Smart energy device
 */
export type SmartEnergyDevice = 'EvCharger' | 'HeatPump' | 'ElectricalHeating' | 'SwimmingPool';

/**
 * Smart energy device combination response
 */
export type SmartEnergyDeviceCombinationResponse = {
    /**
     * Device combinations list
     */
    devices?: Array<SmartEnergyDevice>;
};

/**
 * Smart energy device combination response list
 */
export type SmartEnergyDeviceCombinationResponseList = {
    /**
     * Valid device combination
     */
    deviceCombinations?: Array<SmartEnergyDeviceCombinationResponse>;
};

/**
 * Smart energy offer
 */
export type SmartEnergyOfferRequest = {
    /**
     * Number of kwh injected
     */
    injectionVolume: number;
    /**
     * Number of kwh used
     */
    usageVolume: number;
    /**
     * Inverter type
     */
    inverterType: string;
    /**
     * Valid list of devices that can be combined into a valid combination
     */
    deviceCombination: Array<SmartEnergyDevice> | null;
};

/**
 * Smart energy offer response
 */
export type SmartEnergyOfferResponse = {
    /**
     * Offers
     */
    offers: Array<SmartEnergyOfferResponseRow>;
};

/**
 * Represents a response row for a smart energy offer.
 */
export type SmartEnergyOfferResponseRow = {
    /**
     * Energy generated (kWh).
     */
    injectedVolume?: number | null;
    /**
     * Energy consumption from the grid (kWh).
     */
    usageVolume?: number | null;
    /**
     * Storage capacity (kWh).
     */
    savedVolume?: number | null;
    /**
     * Number of modules (#).
     */
    numberOfModules?: number | null;
    /**
     * Battery 5kWh (#).
     */
    hasBatteryFive?: number | null;
    /**
     * Battery 8kWh (#).
     */
    hasBatteryEight?: number | null;
    /**
     * Inverter (kW).
     */
    inventorType?: string | null;
    /**
     * Investment at 0% (€).
     */
    investment?: number | null;
    /**
     * Investment at 6% (€).
     */
    investmentSixPercent?: number | null;
    /**
     * Investment at 21% (€).
     */
    investmentTwentyOnePercent?: number | null;
    /**
     * Height (cm).
     */
    height?: number | null;
    /**
     * Width (cm).
     */
    width?: number | null;
    /**
     * Depth (cm).
     */
    depth?: number | null;
    /**
     * Annual savings (€).
     */
    yearlySaving?: number | null;
    /**
     * Additional self-consumption (€).
     */
    extraSelfUsage?: number | null;
    /**
     * Self-consumption optimization (€).
     */
    selfUsageOptimalisation?: number | null;
    /**
     * Capacity tariff reduction (€).
     */
    capacityTarifReduction?: number | null;
    /**
     * Savings with Eneco Smart Steering (€).
     */
    enecoSmartSavings?: number | null;
    /**
     * Payback period at 6% (€).
     */
    payBackPeriodSixPercent?: number | null;
    /**
     * Payback period at 21% (€).
     */
    payBackPeriodTwentyOnePercent?: number | null;
    /**
     * Return on Investment (ROI) for 20 years at 6%.
     */
    roiSixPercent?: number | null;
    /**
     * Return on Investment (ROI) for 20 years at 21%.
     */
    roiTwentyOnePercent?: number | null;
};

/**
 * Represents the date of solar panel installation for price simulation purposes.
 * The date is significant only for locations in Wallonia.
 */
export type SolarPanelInstallationDateDto = 'BeforeJanuary2024' | 'AfterJanuary2024';

/**
 * Record containing the state of a Dc.XAPI.Site.Web.Contracts.Move.StatusHintOptionDto
 */
export type StatusHintDto = {
    hint?: StatusHintOptionDto;
    state?: StatusHintStateDto;
};

/**
 * All status hint options
 */
export type StatusHintOptionDto = 'EmailConfirmation' | 'MoveFileCreated' | 'MoveInformationUpdated' | 'MeterValuesOldAddressFilledIn' | 'MeterValuesNewAddressFilledIn' | 'EndNote' | 'Completed';

/**
 * The state in which a status hint resides for a move
 */
export type StatusHintStateDto = 'Todo' | 'Completed';

/**
 * The rectification info
 */
export type SubmitRectificationRequest = {
    /**
     * List of contested meter readings for the rectification
     */
    contestedMeterReadings?: Array<RectificationContestedMeter>;
    /**
     * List of documents attached to the rectification request
     */
    rectificationDocuments?: Array<RectificationDocuments>;
    /**
     * Comment attached by the user
     */
    comment?: string | null;
};

/**
 * Subscription detail information
 */
export type SubscriptionDetailInfo = {
    /**
     * The detailsUrl property
     */
    detailsUrl?: string | null;
    detailType?: SubscriptionType;
    discountStatus?: DiscountStatus;
    /**
     * The endDate property
     */
    endDate?: string | null;
    /**
     * The name property
     */
    name?: string | null;
    /**
     * The percentage property
     */
    percentage?: number | null;
    /**
     * The price property
     */
    price?: number | null;
    priceModel?: PriceModel;
    /**
     * The startDate property
     */
    startDate?: string | null;
};

/**
 * Subscription info
 */
export type SubscriptionInfo = {
    /**
     * The campaign property
     */
    campaign?: string | null;
    /**
     * The details property
     */
    details?: Array<SubscriptionDetailInfo> | null;
    /**
     * The endDate property
     */
    endDate?: string | null;
    /**
     * The planDetailsUrl property
     */
    planDetailsUrl?: string | null;
    /**
     * The planId property
     */
    planId?: string | null;
    /**
     * The planName property
     */
    planName?: string | null;
    /**
     * The planPrice property
     */
    planPrice?: number | null;
    planPriceModel?: PlanPriceModel;
    /**
     * The quantity property
     */
    quantity?: number | null;
    /**
     * The serviceType property
     */
    serviceType?: string | null;
    /**
     * The startDate property
     */
    startDate?: string | null;
    status?: SubscriptionStatus;
    /**
     * The subscriptionId property
     */
    subscriptionId?: string | null;
};

/**
 * Subscription status
 */
export type SubscriptionStatus = 'Active' | 'Future' | 'NonRenewing' | 'InTrial' | 'Cancelled';

/**
 * Subscription status
 */
export type SubscriptionStatusDto = 'Created' | 'LinkCompleted' | 'Active' | 'Expired';

/**
 * Subscription tier
 */
export type SubscriptionTierDto = 'Free' | 'Premium';

/**
 * Subscription type
 */
export type SubscriptionType = 'RecurringAddon' | 'OneTimeCharge' | 'Discount';

/**
 * Subscription type
 */
export type SubscriptionTypeDto = 'SmartCharging' | 'SmartInsights';

/**
 * Contains EAN information returned by ThinkNext
 */
export type SuggestedEanResponse = {
    energyType?: MoveEnergyTypeDto;
    /**
     * EAN of the meter
     */
    ean?: string;
};

/**
 * Describes details in regards to a tariff for a given timeframe. (Usually monthly)
 */
export type TariffDetails = {
    /**
     * Links to the tariff card by available languages. Languages are described as ISO 639-1 Alpha-2 codes.
     */
    tariffCardByLanguage?: {
        [key: string]: (string);
    };
    /**
     * Date on which this tariff becomes active.
     */
    startDate?: string;
};

/**
 * Represents the time frame for data aggregation.
 */
export type TimeFrameDto = 'Monthly' | 'Yearly' | 'FifteenMin' | 'Hourly' | 'Daily';

/**
 * Type frame type code
 */
export type TimeframeTypeCodeDto = 'TH' | 'HI' | 'LO' | 'EX';

/**
 * Unit of measure of the value
 */
export type UnitOfMeasureDto = 0 | 1;

/**
 * Company data for a crm account
 */
export type UpdateAccountCompanyDetailsRequest = {
    /**
     * Name of the company
     */
    name?: string;
    formatType?: CompanyFormatTypeDto;
    vatType?: CompanyVatTypeDto;
    /**
     * Company number
     */
    companyNumber?: string;
};

/**
 * Update the accounts contact details
 */
export type UpdateAccountContactDetailsRequest = {
    /**
     * First name of the account
     */
    firstName?: string;
    /**
     * Last name of the account
     */
    lastName?: string;
    /**
     * Email of the account
     */
    email?: string;
    /**
     * Birth date of the account
     */
    dateOfBirth?: string | null;
    /**
     * Telephone number of the account
     */
    telephoneNumber?: string | null;
    /**
     * Mobile number of the account
     */
    mobileNumber?: string | null;
    /**
     * Gezinsbond number of the account
     */
    gezinsBondNumber?: string | null;
    gender?: GenderDto;
    language?: CustomerLanguageDto;
    contactAddress?: ContactAddressDto;
};

/**
 * Representation a CRM accounts contact preferences
 */
export type UpdateAccountContactPreferencesRequest = {
    invoicePreferences?: InvoiceContactPreference;
    /**
     * Does the user want Advance invoices
     */
    advancePayments?: boolean;
    /**
     * Does the user want more information about Eneco
     */
    enecoNews?: boolean;
    /**
     * Doest the user want more info about Promotions or third party partners
     */
    promotions?: boolean;
    energyMonitorFrequency?: EnergyMonitorFrequency;
};

/**
 * Proposal for new advance request amount
 */
export type UpdateAdvancePaymentRequest = {
    type?: UpdateAdvancePaymentRequestType;
    /**
     * The global amount if Dc.XAPI.Site.Web.Contracts.AdvanceAmount.UpdateAdvancePaymentRequestType.Global or amount for EAN if Dc.XAPI.Site.Web.Contracts.AdvanceAmount.UpdateAdvancePaymentRequestType.Granular
     */
    amount?: number;
    /**
     * EAN if Dc.XAPI.Site.Web.Contracts.AdvanceAmount.UpdateAdvancePaymentRequestType.Granular
     */
    ean?: string | null;
};

/**
 * Determines how the advance payment changes get handled
 */
export type UpdateAdvancePaymentRequestType = 'Global' | 'Granular';

/**
 * Request which allows a user to change his password
 */
export type UpdateEmailRequest = {
    /**
     * The session token
     */
    sessionToken?: string;
};

/**
 * Body containing service delivery points to configure the invoice frequency for
 */
export type UpdateInvoiceFrequenciesDto = {
    /**
     * Service delivery points to configure the invoice frequency for
     */
    serviceDeliveryPoints?: Array<UpdateInvoiceFrequencyDto>;
};

/**
 * A meter with monthly invoice period
 */
export type UpdateInvoiceFrequencyDto = {
    /**
     * The contract number to configure the invoice frequency for
     */
    contractNumber?: string;
    invoiceFrequency?: InvoiceFrequencyDto;
};

/**
 * Body containing details about the key transfer on an address
 */
export type UpdateKeyTransferRequest = {
    /**
     * Date on which the key gets transferred
     */
    keyTransferDate?: string | null;
    /**
     * Key transfer date is unknown right now
     */
    keyTransferDateUnknown?: boolean;
    /**
     * Date of vacancy
     */
    vacancyDate?: string | null;
    /**
     * Whether there is going to be vacancy
     */
    reportedVacancy?: boolean;
};

/**
 * Details to update on a linked account
 */
export type UpdateLinkFromAccountRequest = {
    /**
     * Alias for the linked account
     */
    alias?: string;
    linkingType?: AccountLinkingTypeDto;
};

/**
 * Updates the local login details for a user
 */
export type UpdateLocalLogin = {
    /**
     * Display name (ME Voornaam) to configure for the user
     */
    displayName?: string;
    /**
     * Email that the user can login with locally
     */
    email?: string;
    /**
     * Password that the user can login with locally. If the password is empty ("") or not present, the password will not be updated.
     */
    password?: string;
};

/**
 * A meter with monthly invoice period
 */
export type UpdateMonthlyInvoicePeriodDtoMeter = {
    /**
     * Number that signifies the billing account
     */
    billingAccountNumber?: string;
    period?: MonthlyInvoicePeriodDto;
};

/**
 * Body containing Meters to configure the preferences for
 */
export type UpdateMonthlyInvoicePreferenceDto = {
    /**
     * Meters to configure the preferences for
     */
    meters?: Array<UpdateMonthlyInvoicePeriodDtoMeter>;
};

/**
 * Body containing details on how the user wants to move
 */
export type UpdateMoveReasonRequest = {
    reasonDto?: MoveReasonDto;
};

/**
 * Body containing new address details for a move
 */
export type UpdateNewAddressRequest = {
    address?: MoveAddressDto;
};

/**
 * Contains updated payment method information per billing account
 */
export type UpdatePaymentMethodByBillingAccountResponse = {
    /**
     * Billing account number
     */
    billingAccountNumber?: string;
    newPaymentMethod?: PaymentMethodDto;
    /**
     * IBAN details
     */
    iban?: string | null;
    /**
     * Consent for direct debit given
     */
    directDebitConsentGiven?: boolean | null;
};

/**
 * Contains updated payment method information
 */
export type UpdatePaymentMethodRequest = {
    /**
     * Payment methods per billing account
     */
    items?: Array<UpdatePaymentMethodByBillingAccountResponse>;
};

/**
 * Update subscription status request
 */
export type UpdateSubscriptionStatusRequest = {
    status: SubscriptionStatusDto;
};

/**
 * Update the vacancy of multiple EANs
 */
export type UpdateVacancyRequest = {
    /**
     * Date until vacancy has been requested. Should be the last day of one of the four next months
     */
    vacancyUntil?: string;
};

/**
 * The utility type enum
 */
export type UtilityType = 'Electricity' | 'Gas';

/**
 * Validate order response
 */
export type ValidateOrderResponse = {
    /**
     * The checkoutExpiresAt property
     */
    checkoutExpiresAt?: string | null;
    /**
     * The checkoutUrl property
     */
    checkoutUrl?: string | null;
    /**
     * The 3-character currency code to be used for the order. Currently Eneco only support value 'EUR'
     */
    currencyCode?: string | null;
    customer?: OrderCustomer;
    errorDetails?: EnecoErrorDetails;
    /**
     * The initialInvoiceAmount property
     */
    initialInvoiceAmount?: number | null;
    /**
     * The isOneTime property
     */
    isOneTime?: boolean | null;
    /**
     * The orderReference property
     */
    orderReference?: string | null;
    /**
     * The products property
     */
    products?: Array<EnecoProduct> | null;
    shippingAddress?: Address;
    /**
     * The success property
     */
    success?: boolean | null;
};

export type ValidationProblemDetails = {
    type?: string | null;
    title?: string | null;
    status?: number | null;
    detail?: string | null;
    instance?: string | null;
    errors?: {
        [key: string]: Array<(string)>;
    };
    [key: string]: (unknown | string | number) | undefined;
};

/**
 * Vehicle asset
 */
export type VehicleAssetResourceDto = {
    /**
     * Brand of the vehicle
     */
    brand: string;
    /**
     * Model of the vehicle
     */
    model?: string | null;
    /**
     * Year of the vehicle
     */
    year?: number | null;
    /**
     * Vehicle alias
     */
    alias?: string | null;
    chargingLocation: LocationDto;
};

export type GetEnecoBeXapiSiteApiV1ProfileResponse = {
    data?: GetProfileResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1ProfileError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1ProfileLocalData = {
    /**
     * Body containing profile update details
     */
    body?: UpdateLocalLogin;
};

export type PutEnecoBeXapiSiteApiV1ProfileLocalResponse = void;

export type PutEnecoBeXapiSiteApiV1ProfileLocalError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1ProfileItsmeData = {
    /**
     * Request containing link details
     */
    body?: LinkItsmeRequest;
};

export type PostEnecoBeXapiSiteApiV1ProfileItsmeResponse = unknown;

export type PostEnecoBeXapiSiteApiV1ProfileItsmeError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1ProfileItsmeResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1ProfileItsmeError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailData = {
    path: {
        /**
         * Account number for which the forgot email flow is initiated
         */
        accountNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailResponse = {
    data?: ForgotEmailResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1ProfileEmailData = {
    /**
     * Body containing a session token
     */
    body?: UpdateEmailRequest;
};

export type PutEnecoBeXapiSiteApiV1ProfileEmailResponse = void;

export type PutEnecoBeXapiSiteApiV1ProfileEmailError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberData = {
    path: {
        /**
         * crm account to fetch the details for
         */
        accountNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberResponse = {
    data?: GetAccountDetailsResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberData = {
    body?: PutAccountDetailsRequest;
    path: {
        accountNumber: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsData = {
    path: {
        /**
         * Crm account number to fetch the contact details for
         */
        accountNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsResponse = {
    data?: GetAccountContactDetailsResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsData = {
    /**
     * Requesting containing updated account contact details
     */
    body?: UpdateAccountContactDetailsRequest;
    path: {
        /**
         * Crm account to update the contact details for
         */
        accountNumber: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesData = {
    path: {
        /**
         * Crm account number to fetch the contact preferences for
         */
        accountNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesResponse = {
    data?: GetAccountContactPreferencesResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesData = {
    /**
     * Requesting containing updated account contact preferences
     */
    body?: UpdateAccountContactPreferencesRequest;
    path: {
        /**
         * Crm account to update the contact preferences for
         */
        accountNumber: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsData = {
    /**
     * Requesting containing updated account company details
     */
    body?: UpdateAccountCompanyDetailsRequest;
    path: {
        /**
         * Crm account to update the company details for
         */
        accountNumber: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerData = {
    /**
     * Body containing details required to link the owner account
     */
    body?: OwnerAccountLinkingRequest;
    path: {
        /**
         * Account number that is being linked to
         */
        accountNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteData = {
    /**
     * Request containing invitation details
     */
    body?: ChildAccountLinkingInviteRequest;
    path: {
        /**
         * Account number that the child is being linked to
         */
        accountNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsLinkChildData = {
    /**
     * Body containing child account link information
     */
    body?: ChildAccountLinkingRequest;
};

export type PutEnecoBeXapiSiteApiV1AccountsLinkChildResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsLinkChildError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityData = {
    path: {
        /**
         * Session token
         */
        sessionToken: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityResponse = {
    data?: SessionTokenValidityResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewResponse = {
    data?: AccountLinkOverviewDto;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Invite reference found on the overview
         */
        inviteReference: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfData = {
    path: {
        /**
         * the crm account to be unlinked from
         */
        accountNumber: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkData = {
    path: {
        /**
         * the crm account to be unlinked from
         */
        accountNumber: string;
        /**
         * the link to unlink
         */
        targetLink: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkData = {
    /**
     * the data to update
     */
    body?: UpdateLinkFromAccountRequest;
    path: {
        /**
         * the crm account to be unlinked from
         */
        accountNumber: string;
        /**
         * the id of the link
         */
        targetLink: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeData = {
    query?: {
        /**
         * Language (NL,FR,DE)
         */
        language?: LanguageCodeDto;
        /**
         * Zip code part
         */
        zipCodePart?: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeResponse = {
    data?: GetAddressResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameData = {
    query?: {
        /**
         * (partial) name of the city
         */
        cityName?: string;
        /**
         * Language (NL,FR,DE)
         */
        language?: LanguageCodeDto;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameResponse = {
    data?: GetAddressResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameData = {
    query?: {
        /**
         * The unique identificationnumber of the city. This parameter takes precedence over the zip code if it is filled in.
         */
        cityId?: number;
        /**
         * Language (NL,FR,DE)
         */
        language?: LanguageCodeDto;
        /**
         * A part of the street name
         */
        streetNamePart?: string;
        /**
         * The zipCode of the city where the street is located
         */
        zipCode?: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameResponse = {
    data?: GetAddressResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsData = {
    path: {
        /**
         * Id of the account
         */
        accountNumber: string;
        /**
         * Id of the address
         */
        addressIdentifier: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsResponse = {
    data?: AdvanceAmountCollectionResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsData = {
    /**
     * Request body with new approval
     */
    body?: UpdateAdvancePaymentRequest;
    path: {
        /**
         * Id of the account
         */
        accountNumber: string;
        /**
         * Id of the address
         */
        addressIdentifier: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalData = {
    /**
     * Request body with new approval
     */
    body?: SimulateGlobalAmountUpdateRequest;
    path: {
        /**
         * Id of the account
         */
        accountNumber: string;
        /**
         * Id of the address
         */
        addressIdentifier: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalResponse = {
    data?: GlobalAmountSimulationResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyData = {
    /**
     * Request body with new approval
     */
    body?: UpdateVacancyRequest;
    path: {
        /**
         * Id of the account
         */
        accountNumber: string;
        /**
         * Id of the address
         */
        addressIdentifier: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyData = {
    path: {
        /**
         * Id of the account
         */
        accountNumber: string;
        /**
         * Id of the address
         */
        addressIdentifier: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsData = {
    path: {
        /**
         * Customer id
         */
        customerId: string;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsResponse = {
    data?: GetAssetsResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsData = {
    /**
     * Request body with new approval
     */
    body?: CreateAssetRequestDto;
    path: {
        /**
         * Customer id
         */
        customerId: string;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsResponse = {
    data?: GetAssetResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdData = {
    path: {
        assetId: string;
        customerId: string;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdResponse = {
    data?: GetAssetResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdData = {
    path: {
        /**
         * Id of the asset to remove
         */
        assetId: string;
        customerId: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1ContactContactFormData = {
    /**
     * Contact form you want to submit
     */
    body?: ContactFormDto;
};

export type PostEnecoBeXapiSiteApiV1ContactContactFormResponse = unknown;

export type PostEnecoBeXapiSiteApiV1ContactContactFormError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentData = {
    body?: {
        /**
         * file body
         */
        form?: (Blob | File);
    };
};

export type PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentResponse = {
    data?: ContactFormAttachmentResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdData = {
    path: {
        /**
         * id of the file to remove
         */
        attachmentId: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsData = {
    path: {
        accountNumber: string;
        contractNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsResponse = {
    data?: GetContractDetailsForProductResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterData = {
    /**
     * request with the data to be modified
     */
    body?: PutContractDetailsForProductMeterRequest;
    path: {
        /**
         * id of the crm account
         */
        accountNumber: string;
        /**
         * id of the contract
         */
        contractNumber: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyData = {
    /**
     * The requested invoice frequency list
     */
    body?: UpdateInvoiceFrequenciesDto;
    path: {
        /**
         * The account number
         */
        accountNumber: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdData = {
    path: {
        /**
         * Customer id
         */
        customerId: string;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdResponse = {
    data?: CustomerInfoResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesData = {
    path: {
        accountNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesResponse = {
    data?: ListContractAddressesResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsData = {
    path: {
        accountNumber: string;
        addressIdentifier: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsResponse = {
    data?: GetOverviewForAddressResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsData = {
    path: {
        accountNumber: string;
        addressIdentifier: string;
    };
    query?: {
        includeMetersFromTerminatedAccounts?: boolean;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsResponse = {
    data?: GetServiceDeliveryPointOverviewForAddressResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1PricingDynamicData = {
    query: {
        /**
         * Type of aggregation
         */
        aggregation: PriceAggregationDto;
        /**
         * Last day of the date range
         */
        endDate: string;
        /**
         * First day of the date range
         */
        startDate: string;
    };
};

export type GetEnecoBeXapiSiteApiV1PricingDynamicResponse = {
    data?: DynamicPricingResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1PricingDynamicError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1EligibilitiesResponse = {
    data?: EligibilitiesResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1EligibilitiesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignData = {
    /**
     * Request containing signing details
     */
    body?: SignAgreementsRequest;
};

export type PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignResponse = unknown;

export type PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarData = {
    path: {
        /**
         * Identifier of the account
         */
        accountNumber: string;
        /**
         * ID of the delivery address
         */
        addressIdentifier: string;
    };
    query: {
        /**
         * Optional EAN to filter on
         */
        ean?: string;
        /**
         * End date
         */
        endDate: string;
        /**
         * Start date
         */
        startDate: string;
        /**
         * Time frame for data aggregation (Monthly, Yearly)
         */
        timeFrame?: TimeFrameDto;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarResponse = {
    data?: PeakResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarAverageData = {
    path: {
        /**
         * Identifier of the account
         */
        accountNumber: string;
        /**
         * ID of the delivery address
         */
        addressIdentifier: string;
    };
    query: {
        /**
         * Optional EAN to filter on
         */
        ean?: string;
        /**
         * End date
         */
        endDate: string;
        /**
         * Start date
         */
        startDate: string;
        /**
         * Time frame for data aggregation (Monthly, Yearly)
         */
        timeFrame?: TimeFrameDto;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarAverageResponse = {
    data?: PeakAverageResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarAverageError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionData = {
    path: {
        /**
         * Identifier of the account
         */
        accountNumber: string;
        /**
         * ID of the delivery address
         */
        deliveryAddressId: string;
    };
    query?: {
        /**
         * Type of consumption (Total, Electricity, Gas, Injection, PeakValues)
         */
        consumptionType?: ConsumptionTypeDto;
        /**
         * Display mode (Cost or Consumption)
         */
        displayMode?: DisplayModeDto;
        /**
         * Optional EAN to filter on
         */
        ean?: string;
        /**
         * Optional end date for the period. If not provided, current date will be used
         */
        endDate?: string;
        /**
         * Optional start date for the period. If not provided, it will be calculated based on timeFrame
         * (last 6 months or last 5 years)
         */
        startDate?: string;
        /**
         * Time frame for data aggregation (Monthly, Yearly)
         */
        timeFrame?: TimeFrameDto;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionResponse = {
    data?: ConsumptionResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsData = {
    path: {
        /**
         * account nr of the rectification
         */
        accountNumber: string;
    };
    query?: {
        /**
         * ean numbers of the meters to fetch
         */
        eans?: Array<(string)>;
        /**
         * End date
         */
        endDate?: string;
        /**
         * invoice nr of the rectification
         */
        invoiceNumber?: string;
        /**
         * Start date
         */
        startDate?: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsResponse = {
    data?: Array<MeterReadingResponse>;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsData = {
    /**
     * List of meter readings
     */
    body: Array<AddMeterReadingRequest>;
    path: {
        /**
         * Account number of user
         */
        accountNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsData = {
    /**
     * Data of the meter reading that will be deleted
     */
    body: DeleteMeterReadingRequest;
    path: {
        /**
         * Account number of user
         */
        accountNumber: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1FormsByFormNameData = {
    /**
     * Dynamic content of the form
     */
    body?: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Name of the form
         */
        formName: string;
    };
};

export type PostEnecoBeXapiSiteApiV1FormsByFormNameResponse = unknown;

export type PostEnecoBeXapiSiteApiV1FormsByFormNameError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryData = {
    /**
     * Dynamic content of the form
     */
    body?: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Name of the form
         */
        formName: string;
    };
};

export type PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryResponse = unknown;

export type PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1ImpersonationInitiateData = {
    /**
     * Contains details about which person is being impersonated
     */
    body?: ImpersonationRequest;
};

export type PostEnecoBeXapiSiteApiV1ImpersonationInitiateResponse = {
    data?: ImpersonationResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1ImpersonationInitiateError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesData = {
    path: {
        /**
         * The account number
         */
        accountNumber: string;
    };
    query?: {
        /**
         * Billing account number filter
         */
        BillingAccountNumber?: string;
        /**
         * Exclude invoice types
         */
        ExcludeInvoiceTypes?: Array<InvoiceTypeDto>;
        /**
         * Invoice from date filter
         */
        InvoiceFromDate?: string;
        /**
         * Invoice to date filter
         */
        InvoiceToDate?: string;
        /**
         * Filter invoices from terminated contracts
         */
        ShowActiveContractsOnly?: boolean;
        /**
         * Filter invoices that are eligible for payment plan only. This automatically filters out unpaid invoices
         */
        ShowPaymentPlanEligibleOnly?: boolean;
        /**
         * Filter invoices that are unpaid
         */
        ShowUnpaidOnly?: boolean;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesResponse = {
    data?: GetInvoicesResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesData = {
    path: {
        /**
         * The account number
         */
        accountNumber: string;
        /**
         * The delivery address identification
         */
        addressIdentifier: string;
    };
    query?: {
        /**
         * Invoice from date filter
         */
        InvoiceFromDate?: string;
        /**
         * Invoice to date filter
         */
        InvoiceToDate?: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesResponse = {
    data?: GetInvoicesResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodData = {
    path: {
        /**
         * The account number
         */
        accountNumber: string;
        /**
         * The delivery address identification
         */
        addressIdentifier: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodResponse = {
    data?: MonthlyInvoicePreferenceDto;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodData = {
    /**
     * Body containing monthly invoice preferences
     */
    body?: UpdateMonthlyInvoicePreferenceDto;
    path: {
        /**
         * The account number
         */
        accountNumber: string;
        /**
         * The delivery address identification
         */
        addressIdentifier: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailResponse = {
    data?: MoveFileDetailResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferData = {
    /**
     * Request containing key transfer information about the old address
     */
    body?: UpdateKeyTransferRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
        /**
         * Location type
         */
        locationType: LocationType;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterData = {
    /**
     * Request containing gas details
     */
    body?: DetailMeterUpdateRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
        /**
         * Location type
         */
        locationType: LocationType;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactData = {
    /**
     * Request containing contact details of the new owner of the old location
     */
    body?: DetailOldAddressNewOwnerContactDetailsUpdateRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
        /**
         * Location type
         */
        locationType: LocationType;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceData = {
    /**
     * Request that allows the customer to update the correspondence address
     */
    body?: DetailCorrespondenceUpdateRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveResponse = void;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsData = {
    /**
     * Request containing the move reason
     */
    body?: UpdateMoveReasonRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileData = {
    body?: {
        /**
         * Form with file
         */
        form?: (Blob | File);
    };
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
        /**
         * Old or new address
         */
        locationType: LocationType;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileResponse = {
    data?: EODFileUploadResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
        /**
         * Old or new address
         */
        locationType: LocationType;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileResponse = void;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressData = {
    /**
     * Request containing details on how the new address is supposed to be updated
     */
    body?: UpdateNewAddressRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressResponse = {
    data?: GetNewAddressDetailsResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Identifier that represents an address
         */
        addressIdentifier: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdNextBestActionsData = {
    path: {
        /**
         * The account number
         */
        accountNumber: string;
        /**
         * The delivery address identifier
         */
        deliveryAddressId: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdNextBestActionsResponse = {
    data?: NextBestActionResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdNextBestActionsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsData = {
    path: {
        /**
         * Customer id
         */
        customerId: string;
    };
    query: {
        campaign?: string;
        couponCode?: string;
        currencyCode?: string;
        locale?: string;
        productType: string;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsResponse = {
    data?: AvailableProductsInfoResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1OrdersValidateData = {
    body?: OrderInfoRequest;
    query: {
        prepareCheckout: boolean;
    };
};

export type PostEnecoBeXapiSiteApiV1OrdersValidateResponse = {
    data?: ValidateOrderResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1OrdersValidateError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Identifier of an address
         */
        addressIdentifier: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsResponse = {
    data?: PaymentMethodResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsData = {
    /**
     * The new payment method details
     */
    body?: UpdatePaymentMethodRequest;
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Identifier of an address
         */
        addressIdentifier: string;
    };
};

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsResponse = unknown;

export type PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportData = {
    path: {
        /**
         * The account number
         */
        accountNumber: string;
        /**
         * The delivery address identification
         */
        addressIdentifier: string;
    };
    query?: {
        /**
         * From date filter
         */
        FromDate?: string;
        /**
         * Language in which the csv should be generated
         */
        Language?: CsvLanguage;
        /**
         * To date filter
         */
        ToDate?: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportResponse = {
    data?: (Blob | File);
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansResponse = {
    data?: PaymentPlansResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
    };
    query?: {
        /**
         * Whether to include the conditions on which eligibility is based
         */
        includeConditions?: boolean;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityResponse = {
    data?: PaymentPlanEligibilityResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Account number that identifies a billing account
         */
        billingAccountNumber: string;
    };
    query?: {
        /**
         * Amount of slices the user wants
         */
        amountOfSlices?: number;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationResponse = {
    data?: PaymentPlanDto;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansData = {
    /**
     * Request body
     */
    body?: CreatePaymentPlanRequest;
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Account number that identifies a billing account
         */
        billingAccountNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1PriceSimulationLocationEligibilityData = {
    query: {
        postalCode: string;
    };
};

export type GetEnecoBeXapiSiteApiV1PriceSimulationLocationEligibilityResponse = unknown;

export type GetEnecoBeXapiSiteApiV1PriceSimulationLocationEligibilityError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1PriceSimulationData = {
    /**
     * Request body containing the product switch id
     */
    body?: PriceSimulationRequest;
};

export type PostEnecoBeXapiSiteApiV1PriceSimulationResponse = {
    data?: PriceSimulationInitiatedResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1PriceSimulationError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdData = {
    /**
     * Request body containing the product switch id
     */
    body?: PriceSimulationRequest;
    path: {
        /**
         * Identifier of the price simulation
         */
        priceSimulationId: string;
    };
};

export type PutEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdResponse = {
    data?: PriceSimulationInitiatedResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PutEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdData = {
    path: {
        /**
         * The price simulation id
         */
        priceSimulationId: string;
    };
};

export type GetEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdResponse = {
    data?: PriceSimulationResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdEmailData = {
    /**
     * Request body containing details on how to send the mail
     */
    body?: PriceSimulationEmailRequest;
    path: {
        /**
         * The price simulation id
         */
        priceSimulationId: string;
    };
};

export type PostEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdEmailResponse = unknown;

export type PostEnecoBeXapiSiteApiV1PriceSimulationByPriceSimulationIdEmailError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Identifier of the delivery address
         */
        addressIdentifier: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse = {
    data?: ProductSwitchResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData = {
    /**
     * Request body containing the product switch id
     */
    body?: ProductSwitchRequest;
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Identifier of the delivery address
         */
        addressIdentifier: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Identifier of the delivery address
         */
        addressIdentifier: string;
    };
    query: {
        /**
         * EAN to be switched
         */
        ean: string;
        /**
         * ID of the product switch request to be canceled
         */
        productSwitchId: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse = void;

export type DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdData = {
    path: {
        /**
         * Account number that identifies an account
         */
        accountNumber: string;
        /**
         * Order id
         */
        orderId: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdResponse = {
    data?: ProductSwitchResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsData = {
    path: {
        /**
         * account nr of the rectification
         */
        accountNumber: string;
        /**
         * invoice nr of the rectification
         */
        invoiceNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsResponse = {
    data?: Array<RectificationMeterReadingResponse>;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesData = {
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Invoice number
         */
        invoiceNumber: string;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesResponse = {
    data?: ListContractAddressesResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityData = {
    path: {
        /**
         * account nr of the rectification
         */
        accountNumber: string;
        /**
         * invoice nr of the rectification
         */
        invoiceNumber: string;
    };
    query?: {
        /**
         * Ean numbers of the meters
         */
        eans?: Array<(string)>;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityResponse = {
    data?: RectificationEligibilityResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationData = {
    /**
     * Request body
     */
    body?: MeterDeviationRequest;
    path: {
        /**
         * Account number
         */
        accountNumber: string;
        /**
         * Invoice number
         */
        invoiceNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationResponse = {
    data?: CalculateMeterDeviationResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationData = {
    /**
     * Request body containing rectification info
     */
    body?: SubmitRectificationRequest;
    path: {
        /**
         * account nr of the rectification
         */
        accountNumber: string;
        /**
         * invoice nr of the rectification
         */
        invoiceNumber: string;
    };
};

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationResponse = unknown;

export type PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1SmartEnergyOfferData = {
    body?: SmartEnergyOfferRequest;
};

export type PostEnecoBeXapiSiteApiV1SmartEnergyOfferResponse = {
    data?: SmartEnergyOfferResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1SmartEnergyOfferError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1SmartEnergyCombinationsResponse = {
    data?: SmartEnergyDeviceCombinationResponseList;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1SmartEnergyCombinationsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsData = {
    path: {
        /**
         * Customer id
         */
        customerId: string;
    };
    query?: {
        includeFutureSubscriptions?: boolean;
        tier?: SubscriptionTierDto;
        type?: SubscriptionTypeDto;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsResponse = {
    data?: GetSubscriptionsResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsData = {
    body?: CreateSubscriptionRequestDto;
    path: {
        /**
         * Customer id
         */
        customerId: string;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsResponse = {
    data?: GetSubscriptionResponse;
    errors?: unknown;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdData = {
    path: {
        customerId: string;
        /**
         * Id of the Subscription to remove
         */
        subscriptionId: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkData = {
    path: {
        assetId: string;
        /**
         * Customer id
         */
        customerId: string;
        subscriptionId: string;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkResponse = unknown;

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkData = {
    path: {
        assetId: string;
        /**
         * Customer id
         */
        customerId: string;
        subscriptionId: string;
    };
};

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkResponse = unknown;

export type DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusData = {
    body?: UpdateSubscriptionStatusRequest;
    path: {
        /**
         * Customer id
         */
        customerId: string;
        subscriptionId: string;
    };
};

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusResponse = unknown;

export type PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusError = {
    data?: unknown;
    errors?: ValidationProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
} | {
    data?: unknown;
    errors?: ProblemDetails;
    meta?: {
        [key: string]: unknown;
    };
};