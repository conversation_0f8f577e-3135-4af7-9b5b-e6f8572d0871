import { createDCHook, createMutationHook, collapseDataFromCall, collapseParams } from '../client';
import { getSessionsV2, getSessionsAggregateV2, getSessionDetailsV2 } from '../services/HemsV2Service';

export const useHemsV2GetSessionsV2 = createMutationHook(
  collapseDataFromCall(
    collapseParams(
      collapseParams(getSessionsV2, 'requestBody', 'data'),
      'data',
      'fromDate',
      'toDate',
      'offset',
      'limit',
    ),
  ),
  {
    injectables: ['label', 'customerId', 'businessUnit'],
    flattenData: true,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      { target: 'data', props: ['fromDate', 'toDate', 'offset', 'limit'] },
    ],
  },
);

export const useHemsV2GetSessionsAggregateV2 = createMutationHook(
  collapseDataFromCall(
    collapseParams(
      collapseParams(getSessionsAggregateV2, 'requestBody', 'data'),
      'data',
      'granularity',
      'fromDate',
      'toDate',
      'offset',
      'limit',
    ),
  ),
  {
    injectables: ['label', 'customerId', 'businessUnit'],
    flattenData: true,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      { target: 'data', props: ['granularity', 'fromDate', 'toDate', 'offset', 'limit'] },
    ],
  },
);

export const useHemsV2GetSessionDetailsV2 = createDCHook(
  'getSessionDetailsV2',
  collapseDataFromCall(getSessionDetailsV2),
  { injectables: ['label', 'customerId', 'businessUnit'], flattenData: true },
);
