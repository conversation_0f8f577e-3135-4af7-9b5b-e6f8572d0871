import { DC_Repositories_Base_Enumerations_BusinessUnit } from '@monorepo-types/dc';
import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';
import { DC_SAPI_Apigee_HemsSessionsV2_RequestModels_DetailedGranularity } from '@monorepo-types/dc';
import { RequestModels_Hems_SessionsAggregateV2PaginationRequestModel } from '@monorepo-types/dc';
import { RequestModels_Hems_SessionsV2RequestModel } from '@monorepo-types/dc';
import { ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_EvSessionAggregateResponse } from '@monorepo-types/dc';
import { ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel } from '@monorepo-types/dc';
import { ResponseModels_Hems_v2_EvSessionResponseModel } from '@monorepo-types/dc';

import { request } from '../client';
import type { ApiRequestConfig } from '../client/types';

type GetSessionsV2 = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  customerId: number;
  deviceId: string;
  requestBody?: RequestModels_Hems_SessionsV2RequestModel;
};
/**
 * GetSessionsV2
 * Gets the sessions for a specific device
 * @returns ResponseModels_Hems_v2_EvSessionResponseModel Success
 */
export function getSessionsV2(
  { businessUnit, label, customerId, deviceId, requestBody }: GetSessionsV2,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseModels_Hems_v2_EvSessionResponseModel> {
  return request(
    {
      method: 'POST',
      path: `/dxpweb/${businessUnit}/${label}/customers/${customerId}/hems/v2/sessions/${deviceId}`,
      body: requestBody,
      errors: { 400: 'Bad Request' },
    },
    requestConfig,
  );
}

type GetSessionsAggregateV2 = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  customerId: number;
  deviceId: string;
  requestBody: RequestModels_Hems_SessionsAggregateV2PaginationRequestModel;
};
/**
 * GetSessionsAggregateV2
 * Gets the sessions aggregate for a specific device
 * @returns ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_EvSessionAggregateResponse Success
 */
export function getSessionsAggregateV2(
  { businessUnit, label, customerId, deviceId, requestBody }: GetSessionsAggregateV2,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_EvSessionAggregateResponse> {
  return request(
    {
      method: 'POST',
      path: `/dxpweb/${businessUnit}/${label}/customers/${customerId}/hems/v2/sessions/aggregate/${deviceId}`,
      body: requestBody,
      errors: { 400: 'Bad Request' },
    },
    requestConfig,
  );
}

type GetSessionDetailsV2 = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  customerId: number;
  deviceId: string;
  sessionId: string;
  granularity: DC_SAPI_Apigee_HemsSessionsV2_RequestModels_DetailedGranularity;
};
/**
 * GetSessionDetailsV2
 * Gets the sessions aggregate for a specific device
 * @returns ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel Success
 */
export function getSessionDetailsV2(
  { businessUnit, label, customerId, deviceId, sessionId, granularity }: GetSessionDetailsV2,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel> {
  return request(
    {
      method: 'GET',
      path: `/dxpweb/${businessUnit}/${label}/customers/${customerId}/hems/v2/sessions/${deviceId}/${sessionId}/detailed`,
      query: { granularity },
      errors: { 400: 'Bad Request', 404: 'Not Found' },
    },
    requestConfig,
  );
}
