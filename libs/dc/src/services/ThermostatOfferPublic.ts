import { DC_Repositories_Base_Enumerations_BusinessUnit } from '@monorepo-types/dc';
import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';
import { RequestModels_Products_Offers_OfferProductsPublicRequest } from '@monorepo-types/dc';

import { request } from '../client';
import type { ApiRequestConfig } from '../client/types';

type GetSalesOffer = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  requestBody: RequestModels_Products_Offers_OfferProductsPublicRequest;
};
/**
 * GetSalesOffer
 * Get salesOffer public
 * @returns offerProduct Success
 */

export function getSalesOffer(
  { businessUnit, label, requestBody }: GetSalesOffer,
  requestConfig: ApiRequestConfig = {},
): Promise<{ data?: any }> {
  return request(
    {
      method: 'POST',
      path: `v1/smartthermostat/public/${businessUnit}/${label}/sales/offers`,
      body: requestBody,
      errors: { 400: 'Bad Request' },
    },
    requestConfig,
  );
}
