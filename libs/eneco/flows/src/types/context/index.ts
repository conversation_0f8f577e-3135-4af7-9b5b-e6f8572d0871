import { FlowContextBoilerComfort } from './flowContextBoilerComfort';
import { flowContextCancelation } from './flowContextCancelation';
import { FlowContextContractAdvice } from './flowContextContractAdvice';
import { FlowContextDeceased } from './flowContextDeceased';
import { FlowContextDongle } from './flowContextDongle';
import { FlowContextEnergy } from './flowContextEnergy';
import { FlowContextEnergyPlan } from './flowContextEnergyPlan';
import { FlowContextLegslationCompass } from './flowContextLegislationCompass';
import { flowContextRentalDevice } from './flowContextRentalDevice';
import { FlowContextServiceGemak } from './flowContextServiceGemak';
import { FlowContextSme } from './flowContextSme';
import { FlowContextThermostat } from './flowContextThermostat';
import { FlowContextToon } from './flowContextToon';
import { FlowContextUrgenEnergy } from './flowContextUrgentEnergy';

export type FlowContextExtensions = FlowContextServiceGemak &
  flowContextCancelation &
  FlowContextContractAdvice &
  FlowContextBoilerComfort &
  FlowContextDongle &
  FlowContextEnergy &
  FlowContextEnergyPlan &
  FlowContextLegslationCompass &
  flowContextRentalDevice &
  FlowContextSme &
  FlowContextThermostat &
  FlowContextToon &
  FlowContextDeceased &
  FlowContextUrgenEnergy;
