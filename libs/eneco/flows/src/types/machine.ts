/* eslint-disable @typescript-eslint/no-explicit-any */
import react from 'react';

import { AnyEventObject, AssignAction, StateConfig } from 'xstate';

import {
  DC_Repositories_Base_Enumerations_BusinessUnit,
  DC_Repositories_Base_Enumerations_Label,
  Products_Offers_V3_OfferResponseModel,
} from '@monorepo-types/dc';

import { FlowContextExtensions } from './context';
import { FlowRouteCategory } from './enums';

export interface MachineConfig {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
}

export interface FlowContext extends FlowContextExtensions {
  basketId?: string;

  path?: string;
  pathPrevious?: string;
  pathCategory?: string;

  isAuthenticated?: boolean;
  fetchedOffers?: Products_Offers_V3_OfferResponseModel;
  // Machines that you wish to persist through unmount/remount need to be listed here for typing

  machines?: {
    addressMachineState?: StateConfig<any, any>;
    energyChoiceMachineState?: StateConfig<any, any>;
    offerMachineState?: StateConfig<any, any>;
    offerMachineNonCommodityState?: StateConfig<any, any>;
    appointmentMachineState?: StateConfig<any, any>;
    usageMachineState?: StateConfig<any, any>;
    chamberOfCommerceSearchMachineState?: StateConfig<any, any>;
  };
}

export interface FlowEvent extends AnyEventObject {
  type: 'SET_INITIAL_CONTEXT' | 'SET_ROUTE' | 'UPDATE_ROUTE' | 'NEXT_PAGE' | 'PREVIOUS_PAGE' | 'UPDATE_VALUES' | string;
  values?: FlowContext;
}

export type FlowActions = Record<string, AssignAction<FlowContext, FlowEvent>>;

export interface FlowRoute {
  id: string;
  path: string;
  category: FlowRouteCategory;
  component: react.FC;
  actions?: string[];
  skippingFields?: string[];
  skippingRules?: ((context: FlowContext) => boolean)[];
}

export type FlowNavigationCategory = Pick<FlowRoute, 'id' | 'category'>;
