import { shouldSkipWhenUserIsLoggedIn } from '../shouldSkipWhenUserIsLoggedIn';

describe('Given a shouldSkipWhenUserIsLoggedIn', () => {
  describe('when context is provided', () => {
    it('should return true', () => {
      expect(
        shouldSkipWhenUserIsLoggedIn({
          isAuthenticated: true,
        }),
      ).toBeTruthy();
    });

    it('should return false', () => {
      expect(
        shouldSkipWhenUserIsLoggedIn({
          isAuthenticated: false,
        }),
      ).toBeFalsy();
      expect(
        shouldSkipWhenUserIsLoggedIn({
          isAuthenticated: false,
        }),
      ).toBeFalsy();
      expect(
        shouldSkipWhenUserIsLoggedIn({
          isAuthenticated: false,
        }),
      ).toBeFalsy();
    });
  });

  describe('when no context is provided', () => {
    it('should return false', () => {
      expect(
        shouldSkipWhenUserIsLoggedIn({
          isAuthenticated: undefined,
        }),
      ).toBeFalsy();
    });
  });
});
