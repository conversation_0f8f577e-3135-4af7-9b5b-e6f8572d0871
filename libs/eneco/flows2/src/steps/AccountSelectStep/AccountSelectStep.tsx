import { FC, useEffect } from 'react';

import AccountCard from '@components-next/AccountCard/AccountCard';
import useDC from '@dc/useDC';
import { useCustomerProfileAccount } from '@hooks/profile';
import { usePlaceholderContent } from '@sitecore/common';
import type { AccountSelectStepRendering } from '@sitecore/types/AccountSelectStep';
import { Button, Stack } from '@sparky';

import { useCachedAccount } from './useCachedAccount';
import { Header, Layout } from '../../components';
import { useFlowHooks } from '../../utils/FlowProvider';

export const AccountSelectStep: FC = () => {
  const isReady = useCachedAccount();
  const { accountId } = useDC();
  const { useFlowActorRef } = useFlowHooks();
  const sendToFlowMachine = useFlowActorRef().send;

  const { hasMultipleAccounts, isLoading: isProfileLoading } = useCustomerProfileAccount({ filterInactive: true });
  const { AccountSelectStep: { fields: textualData } = {} } = usePlaceholderContent<{
    AccountSelectStep: AccountSelectStepRendering;
  }>();
  const isDataLoading = !isReady && isProfileLoading;

  function submitAccount() {
    sendToFlowMachine({
      type: 'NEXT_PAGE',
      values: { accountId },
    });
  }

  useEffect(() => {
    if (!isProfileLoading && !hasMultipleAccounts) {
      submitAccount();
    }
  }, []);

  const customLabels = textualData?.accountSelect
    ? {
        ...textualData?.accountSelect,
        modalDialog: { value: { ...textualData.accountSelect.selectAccountDialog.value, subheading: '' } },
      }
    : undefined;

  return (
    <Layout.Content variant="A" isLoading={!isDataLoading}>
      <Header>
        <Header.Title>{textualData?.content?.title?.value}</Header.Title>
      </Header>
      <AccountCard compactLayout customLabels={customLabels} />
      <Stack direction={{ initial: 'column', md: 'row' }}>
        <Button onClick={submitAccount}>{textualData?.content?.nextStepText?.value}</Button>
      </Stack>
    </Layout.Content>
  );
};
