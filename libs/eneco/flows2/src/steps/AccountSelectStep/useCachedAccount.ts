import { useEffect, useState } from 'react';

import logger from '@common/log';
import { getAccountSwitchHref } from '@components-next/AccountCard/accountSwitchHelpers';
import useDC from '@dc/useDC';
import { useSession } from '@dxp-auth';
import { useRouter } from '@dxp-next';

import { useFlowHooks } from '../../utils/FlowProvider';

export const useCachedAccount = () => {
  const [isReady, setIsReady] = useState(false);
  const { reload } = useRouter();
  const { data: session } = useSession();
  const { accountId } = useDC();

  const { useFlowSelector } = useFlowHooks();
  const flowContext = useFlowSelector(state => state);

  const {
    context: { accountId: cachedAccountId },
  } = flowContext;

  useEffect(() => {
    async function switchToCachedAccount(cachedAccountId: number) {
      try {
        await fetch(getAccountSwitchHref(cachedAccountId.toString()), {
          method: 'POST',
          headers: { authorization: `Bearer ${session?.idToken}` },
        });
        reload();
      } catch (error) {
        logger.error('Wz6mag', `Unexpected error for switching accounts`, error);
      } finally {
        setIsReady(true);
      }
    }
    if (cachedAccountId && cachedAccountId !== accountId) {
      switchToCachedAccount(cachedAccountId);
    } else {
      setIsReady(true);
    }
  }, []);

  return isReady;
};
