import { useCallback, useEffect, useState } from 'react';

import { Route, RouterProvider, Routes, useRouter } from '@dxp-next';
import { useLayoutData } from '@sitecore/common';
import { SkipToMain } from '@sparky';
import { useTracking } from '@tracking';

import { useFlowHooks } from './FlowProvider';
import { FlowRedirect } from './FlowRedirect';
import { getEventTypeByPath } from './FlowUtils';
import { useFlowBasket } from '../hooks/useFlowBasket';
import { useFlowQueryParams } from '../hooks/useFlowQueryParams';
import { FlowContext } from '../types';
import type { FlowRoute } from '../types/machine';

interface Props<T extends FlowContext> {
  routes: FlowRoute<T>[];
  stickyQueryParams?: Array<string>;
}
const RouterConsumer = <T extends FlowContext>({ routes, stickyQueryParams }: Props<T>) => {
  useFlowQueryParams();
  useFlowBasket();

  const { trackPageView, trackFunnelStart, trackFunnelStep, trackFunnelCompleted } = useTracking();
  const { useFlowSelector, useFlowActorRef } = useFlowHooks();
  const { send } = useFlowActorRef();
  const flowState = useFlowSelector(state => state);
  const currentRoute = useFlowSelector(state => state.context.path);

  const previousRoute = useFlowSelector(state => state.context.pathPrevious);

  const {
    route: { fields: { frontEndRootPath, flowCompletionUrl, flowName } = {} },
    context: { experimentInfo: { variant } = {}, itemPath },
  } = useLayoutData();

  const router = useRouter();
  const { activeRoute, routerBasePath } = router;

  const [isInitialized, setIsInitialized] = useState(false);
  const [isCompleted, setIsCompleted] = useState(flowState.matches('COMPLETED'));
  const [isActive, setIsActive] = useState(false);

  // Capture initial query parameters once and preserve them throughout the flow
  const [initialQueryParams, setInitialQueryParams] = useState<string>('');

  useEffect(() => {
    if (typeof window !== 'undefined' && !initialQueryParams) {
      const search = window.location.search;
      setInitialQueryParams(search);
    }
  }, [initialQueryParams]);

  const redirect = useCallback(() => {
    const url = flowCompletionUrl?.value as unknown as { href: string };
    if (url?.href) {
      window.location.href = url.href;
    }
  }, [flowCompletionUrl]);

  useEffect(() => {
    setIsCompleted(flowState.matches('COMPLETED'));
  }, [flowState]);

  useEffect(() => {
    isInitialized &&
      trackFunnelStart({
        funnel: flowName?.value?.toString(),
        step: getEventTypeByPath(routes, currentRoute),
      });
  }, [isInitialized]);

  useEffect(() => {
    isCompleted &&
      isInitialized &&
      trackFunnelCompleted({
        funnel: flowName?.value?.toString(),
        step: 'STEP_THANK_YOU',
      });
    isCompleted && redirect();
  }, [isCompleted]);

  useEffect(() => {
    setIsActive(isInitialized && !isCompleted);
  }, [isInitialized, isCompleted]);

  useEffect(() => {
    if (currentRoute) {
      const basePath = routerBasePath.endsWith('/') ? routerBasePath.slice(0, -1) : routerBasePath;

      if (isInitialized) {
        trackPageView({
          pageName: `${basePath}${currentRoute}`,
          previousPage: `${window?.location?.origin}${basePath}${previousRoute ?? '/'}`,
        });
      }

      // Construct path with query params only when needed
      let path = currentRoute;

      const shouldAddVariantTracking =
        variant &&
        frontEndRootPath?.value
          ?.toString()
          .replace(/\//g, '')
          .endsWith(itemPath?.replace(/\//g, '') ?? '');

      const hasStickyParams = initialQueryParams && stickyQueryParams?.length;

      // Only create URL search params if we need to modify the path
      if (hasStickyParams || shouldAddVariantTracking) {
        const searchParams = new URLSearchParams();

        // Add sticky query parameters
        if (hasStickyParams) {
          const initialParams = new URLSearchParams(initialQueryParams);
          initialParams.forEach((value, key) => {
            if (stickyQueryParams.includes(key)) {
              searchParams.set(key, value);
            }
          });
        }

        // Add variant tracking if needed
        if (shouldAddVariantTracking) {
          searchParams.set('sc_trk', variant);
        }

        // Combine currentRoute with search parameters
        const searchString = searchParams.toString();
        path = searchString ? `${currentRoute}?${searchString}` : currentRoute;
      }

      if (isInitialized) {
        debugger;
        router.push(path);
      } else if (activeRoute !== currentRoute) {
        router.replace(path);
      }
    }
  }, [currentRoute, initialQueryParams, isInitialized]);

  useEffect(() => {
    currentRoute &&
      previousRoute &&
      currentRoute !== previousRoute &&
      trackFunnelStep({
        funnel: flowName?.value?.toString(),
        step: getEventTypeByPath(routes, currentRoute),
      });
  }, [currentRoute, previousRoute]);

  useEffect(() => {
    if (currentRoute && currentRoute !== activeRoute) {
      send({
        type: 'GOTO',
        stepName: getEventTypeByPath(routes, activeRoute?.toString()),
      });
    }

    if (isCompleted) {
      redirect();
    }
  }, [activeRoute]);

  useEffect(() => {
    if (activeRoute === currentRoute) {
      setIsInitialized(true);
    }
  }, [activeRoute, currentRoute]);

  return isActive ? (
    <Routes>
      {routes.map(({ path, component }) => (
        <Route key={path} route={path} component={component} />
      ))}
    </Routes>
  ) : isCompleted ? (
    <Routes>
      <Route route={activeRoute ?? '/'} component={FlowRedirect} />
    </Routes>
  ) : (
    <SkipToMain.Main />
  );
};

export const FlowRouter = <T extends FlowContext>({ routes, stickyQueryParams }: Props<T>) => (
  <RouterProvider>
    <RouterConsumer<T> routes={routes} stickyQueryParams={stickyQueryParams} />
  </RouterProvider>
);
