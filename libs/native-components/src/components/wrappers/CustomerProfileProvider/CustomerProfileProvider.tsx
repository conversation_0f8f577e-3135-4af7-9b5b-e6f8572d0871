import { createContext, FC, PropsWithChildren, useContext } from 'react';

import { KeyedMutator } from 'swr';

import { ApiError } from '@dc/client/ApiError';
import { useCustomerGetCustomerProfileBe } from '@dc/hooks';
import { Customers_Profile_BECustomerProfileResponse } from '@monorepo-types/dc';

type CustomerProfileContextType = {
  error: ApiError;
  isLoading: boolean;
  data: Customers_Profile_BECustomerProfileResponse | undefined;
  mutate: KeyedMutator<Customers_Profile_BECustomerProfileResponse | undefined>;
};

const CustomerProfileContext = createContext<CustomerProfileContextType | null>(null);

const CustomerProfileProvider: FC<PropsWithChildren> = ({ children }) => {
  const { error, isLoading, data, mutate } = useCustomerGetCustomerProfileBe();

  return (
    <CustomerProfileContext.Provider value={{ error, isLoading, data, mutate }}>
      {children}
    </CustomerProfileContext.Provider>
  );
};

const useCustomerProfileContext = () => {
  const context = useContext(CustomerProfileContext);

  if (!context) {
    throw new Error('useCustomerProfileContext must be used within a CustomerProfileProvider');
  }

  return context;
};

export { CustomerProfileProvider, useCustomerProfileContext };
