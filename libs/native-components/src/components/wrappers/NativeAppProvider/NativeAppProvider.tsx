import { FC, PropsWithChildren, useEffect } from 'react';

import { Capacitor, PluginListenerHandle } from '@capacitor/core';
import { Device } from '@capacitor/device';
import { Preferences } from '@capacitor/preferences';
import { StatusBar, Style } from '@capacitor/status-bar';
import { EdgeToEdge } from '@capawesome/capacitor-android-edge-to-edge-support';
import { Airship } from '@ua/capacitor-airship';
import { AppTrackingTransparency } from 'capacitor-plugin-app-tracking-transparency';
import { useRouter } from 'next/router';

import env from '@common/env';
import Logger from '@common/log';
import { AppPreferences } from '@common/preferences';
import { useTracking } from '@tracking';

const { FIRST_TIME_OPEN } = AppPreferences;

async function setupAirship() {
  await Airship.takeOff({
    development: {
      appKey: env('AIRSHIP_DEV_KEY'),
      appSecret: env('AIRSHIP_DEV_SECRET'),
      logLevel: 'verbose',
    },
    production: {
      appKey: env('AIRSHIP_PRODUCTION_KEY'),
      appSecret: env('AIRSHIP_PRODUCTION_SECRET'),
      logLevel: 'error',
    },
    inProduction: env('ENVIRONMENT') === 'production',
    site: 'eu',
    urlAllowList: ['*'],
    android: {
      notificationConfig: {
        icon: 'ic_notification',
        accentColor: '#D21242',
      },
    },
  }).finally(() => {
    Airship.channel.getChannelId().then(channelId => {
      if (channelId) {
        Preferences.set({ key: 'channelId', value: channelId });
      }
    });
  });
}

// All logic that should only be executed once is trigger here.
const NativeAppProvider: FC<PropsWithChildren> = ({ children }) => {
  const { trackFirstTimeOpen } = useTracking();
  const router = useRouter();

  useEffect(() => {
    if (Capacitor.isNativePlatform()) {
      setupAirship();
    }
  }, []);

  useEffect(() => {
    // TODO: find a proper solution to enable the edge-to-edge display/layout on Android version >= 35
    //  (https://developer.android.com/develop/ui/views/layout/edge-to-edge).
    //  Temporarily solution: enabled the Edge-to-Edge package and added a black background
    //  so that the looks stays the same in all android versions. Once we have a solution for the edge-to-edge,
    //  we should disable the Edge-To-Edge package on Android version >= 35
    async function setSystemBars() {
      const { platform, androidSDKVersion } = await Device.getInfo();

      const isIOS = platform === 'ios';
      const isAndroid = platform === 'android';

      // On iOS the screen is already edge-to-edge style that's why we set the status bar to style 'Light'
      await StatusBar.setStyle(isIOS ? { style: Style.Light } : { style: Style.Dark });

      if (!androidSDKVersion || !isAndroid) return;
      if (androidSDKVersion < 35) {
        await StatusBar.setBackgroundColor({ color: '#000000' });
      } else {
        // Only affects Android phones.
        // Edge-to-Edge plugin is enabled by default when installed.
        await EdgeToEdge.setBackgroundColor({ color: '#000000' });
      }
    }

    if (Capacitor.isNativePlatform()) {
      setSystemBars();
    }
  }, []);

  useEffect(() => {
    Preferences.get({ key: FIRST_TIME_OPEN }).then(({ value }) => {
      if (value !== 'true') {
        Preferences.set({ key: FIRST_TIME_OPEN, value: 'true' });
        trackFirstTimeOpen();
      }
    });
  }, [trackFirstTimeOpen]);

  useEffect(() => {
    async function setupTrackingPermissions() {
      try {
        const { status } = await AppTrackingTransparency.getStatus();
        if (status === 'notDetermined') {
          await AppTrackingTransparency.requestPermission();
        }
      } catch (e) {
        Logger.error('SrqwYX', 'Error getting insights app tracking status', e);
      }
    }

    setupTrackingPermissions();
  }, []);

  useEffect(() => {
    let listener: PluginListenerHandle;

    async function handlePushNotificationDeeplink() {
      listener = await Airship.push.onNotificationResponse(event => {
        // https://go.airship.eu/apps/li01SZWLTHaUMKxxX6UJiw/composer/2PVLqFJ7SBepoaCvy7wHmw/
        // PUSH_ID is different every time the push notification is sent. We can't rely on this.
        const alert = event.pushPayload.alert;
        const deeplink = JSON.parse(event.pushPayload.extras['com.urbanairship.actions'] as string)?.['^d'];

        if (deeplink) {
          const path = deeplink.toString().replace('enecobeinsights://', '');

          // TODO: investigate how can we identify the push notification ?
          // For now we just check if the alert contains 'gas' or 'elektriciteit'.
          if (alert) {
            if (alert.includes('gas')) {
              router.push(path + '/gas');
              return;
            } else if (alert.includes('elektriciteit')) {
              router.push(path + '/electricity');
              return;
            }
          }

          router.push(path);
        }
      });
    }

    if (Capacitor.isNativePlatform()) {
      handlePushNotificationDeeplink();
    }

    return () => {
      if (listener) {
        listener.remove();
      }
    };
  }, [router]);

  return children;
};

export default NativeAppProvider;
