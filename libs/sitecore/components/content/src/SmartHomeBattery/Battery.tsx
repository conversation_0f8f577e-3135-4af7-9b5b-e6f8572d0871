import { FC, Fragment } from 'react';

import { BatteryModuleStackItem, TransformStackItem, MaxWidthBox } from '@custom-components/SmartHomeBattery';
import { SmartEnergyOfferResponseRow } from '@dc-be/client/types.gen';
import { SmartHomeBatteryRendering } from '@sitecore/types/SmartHomeBattery';
import { Box, Stack, Stretch, Text } from '@sparky';

const OriginalBattery: FC<{
  offer: SmartEnergyOfferResponseRow;
  fields: SmartHomeBatteryRendering['fields'];
}> = ({ offer, fields }) => {
  const amount = offer.numberOfModules ?? 1;

  return (
    <Stack direction="row" gap="2" alignX="center">
      <Stack.Item>
        <Stack alignX="center" gap="2">
          <Stack gap="0" direction="column">
            <svg
              width="100%"
              height="auto"
              viewBox={`78 34 222 ${62 + 61 * amount}`}
              xmlns="http://www.w3.org/2000/svg"
              fill="none">
              {/* Start module */}
              <path
                d={`M 78 42 C 78 37 81 34 86 34 H 270 C 275 34 278 37 278 42 V ${95 + 61 * amount} H 78 Z`}
                fill="#8C8B8D"
              />
              <path d="M 79 42 C 79 37.75 81.75 35 86 35 H 260 C 265 35 268 38 268 43 V 94 H 79 Z" fill="#FFF" />
              <circle xmlns="http://www.w3.org/2000/svg" cx="246" cy="57.5" r="11.5" fill="#ECECEC" stroke="#8C8B8D" />
              <circle xmlns="http://www.w3.org/2000/svg" cx="246" cy="57.5" r="8" fill="#8C8B8D" />
              {fields.step3BatteryTransformerLabel?.value && (
                <foreignObject x="88" y="35" width="145" height="59">
                  <Stretch height="true">
                    <Stack alignY="center">
                      <Text size="BodyXS" color="textLowEmphasis">
                        {fields.step3BatteryTransformerLabel?.value}
                      </Text>
                    </Stack>
                  </Stretch>
                </foreignObject>
              )}
              <path
                d={`M 297 40 L 299 34 L 294 34 L 296 40 L 297 40 Z M 296 40 V ${90 + 61 * amount} H 297 V 40 Z M 296 ${90 + 61 * amount} L 294 ${95 + 61 * amount} L 299 ${95 + 61 * amount} L 297 ${90 + 61 * amount} L 296 ${90 + 61 * amount} Z`}
                fill="#000"
              />
              {/* Middle modules */}
              {[...Array(amount)].map((_, i) => (
                <Fragment key={`module-${i}}`}>
                  <path d={`M 79 ${95 + 61 * i} H 268 V ${155 + 61 * i} H 79 Z`} fill="#FFF" />
                  {fields.step3BatteryModuleLabel?.value && (
                    <foreignObject x="88" y={`${96 + 61 * i}`} width="180" height="59">
                      <Stretch height="true">
                        <Stack alignY="center">
                          <Text size="BodyXS" color="textLowEmphasis">
                            {fields.step3BatteryModuleLabel?.value} {i + 1}
                          </Text>
                        </Stack>
                      </Stretch>
                    </foreignObject>
                  )}
                </Fragment>
              ))}
            </svg>

            {/* End module */}
            <svg width="100%" height="auto" viewBox="78 335 222 30" xmlns="http://www.w3.org/2000/svg" fill="none">
              <path
                d="M 83 359 L 78 357 L 78 362 L 83 360 L 83 359 Z M 263 360 L 268 362 L 268 357 L 263 359 L 263 360 Z M 83 360 L 263 360 L 263 359 L 83 359 L 83 360 Z M 281.817 355.951 L 276.346 356.847 L 279.653 361.582 L 282.39 356.771 L 281.817 355.951 Z M 293.183 349.264 L 298.653 348.367 L 295.347 343.633 L 292.61 348.444 L 293.183 349.264 Z M 281.979 357.056 L 293.593 348.979 L 293.02 348.159 L 281.407 356.236 L 281.979 357.056 Z"
                fill="#000"
              />
            </svg>
          </Stack>
          <Box paddingRight="8">
            <Text>{`${offer.width || 0} cm`}</Text>
          </Box>
        </Stack>
      </Stack.Item>
      <Stack.Item shrink={false}>
        <Stretch height="true">
          <Stack alignY="justify" direction="column">
            <Stack.Item grow>
              <Stretch height="true">
                <Stack alignY="center">
                  <Box paddingBottom="8">
                    <Text>{`${offer.height || 0} cm`}</Text>
                  </Box>
                </Stack>
              </Stretch>
            </Stack.Item>
            <Text>{`${offer.depth || 0} cm`}</Text>
          </Stack>
        </Stretch>
      </Stack.Item>
    </Stack>
  );
};

const NewBattery: FC<{
  offer: SmartEnergyOfferResponseRow;
  fields: SmartHomeBatteryRendering['fields'];
}> = ({ offer, fields }) => {
  const amount = offer.numberOfModules ?? 1;

  return (
    <Stack alignX="center">
      <MaxWidthBox>
        <Stack alignX="center" alignY="center" direction="row" gap={{ initial: '2', md: '4' }}>
          <TransformStackItem>
            <svg width="100%" height="auto" viewBox="0 0 131 235" xmlns="http://www.w3.org/2000/svg" fill="none">
              <g>
                <path
                  d="M4.088 8.25073C4.088 3.83246 7.66972 0.250732 12.088 0.250732H121.727C126.145 0.250732 129.727 3.83245 129.727 8.25073V114.367C129.727 118.785 126.145 122.367 121.727 122.367H12.088C7.66972 122.367 4.088 118.785 4.088 114.367V8.25073Z"
                  fill="#CBCBCB"
                />
                <path
                  d="M12.0527 0.750732H117.639C121.781 0.750732 125.139 4.1086 125.139 8.25073V114.367C125.139 118.509 121.781 121.867 117.639 121.867H12.0527C7.91069 121.867 4.55288 118.509 4.55273 114.367V8.25073C4.55273 4.1086 7.9106 0.750732 12.0527 0.750732Z"
                  fill="white"
                  stroke="#CBCBCB"
                />
                <path
                  d="M62.8193 122.367H76.3289L75.774 140.201C75.7068 142.36 73.9366 144.076 71.7759 144.076H67.3723C65.2116 144.076 63.4414 142.36 63.3742 140.201L62.8193 122.367Z"
                  fill="#5B5B5B"
                />
                <path
                  d="M85.7861 122.367H96.5938V124.437C96.5938 125.542 95.6983 126.437 94.5938 126.437H87.7861C86.6816 126.437 85.7861 125.542 85.7861 124.437V122.367Z"
                  fill="#5B5B5B"
                />
                <path d="M87.8125 126.437H94.5673V137.292H87.8125V126.437Z" fill="#5B5B5B" />
                <rect x="86.4609" y="137.292" width="9.45669" height="4.07053" rx="1" fill="#5B5B5B" />
                <path d="M87.8125 141.362H94.5673V145.433H87.8125V141.362Z" fill="#5B5B5B" />
                <path
                  d="M49.3096 74.0577C49.3096 69.6394 52.8913 66.0576 57.3096 66.0576H74.3815C77.6953 66.0576 80.3815 68.7439 80.3815 72.0576V94.6924C80.3815 99.1107 76.7998 102.692 72.3815 102.692H57.3096C52.8913 102.692 49.3096 99.1107 49.3096 94.6924V74.0577Z"
                  fill="#7D7D7D"
                />
                <rect x="55.3896" y="78.9477" width="18.9134" height="9.4979" rx="2" fill="#5B5B5B" />
                <path
                  d="M12.8341 111.512C13.9532 111.512 14.8605 112.423 14.8605 113.547C14.8605 114.671 13.9532 115.582 12.8341 115.582C11.7149 115.582 10.8076 114.671 10.8076 113.547C10.8076 112.423 11.7149 111.512 12.8341 111.512Z"
                  fill="#B5B5B5"
                />
                <path
                  d="M116.858 111.512C117.977 111.512 118.884 112.423 118.884 113.547C118.884 114.671 117.977 115.582 116.858 115.582C115.738 115.582 114.831 114.671 114.831 113.547C114.831 112.423 115.738 111.512 116.858 111.512Z"
                  fill="#B5B5B5"
                />
                <path
                  d="M12.8341 7.03494C13.9532 7.03494 14.8605 7.94616 14.8605 9.07021C14.8605 10.1943 13.9532 11.1055 12.8341 11.1055C11.7149 11.1055 10.8076 10.1943 10.8076 9.07021C10.8076 7.94616 11.7149 7.03494 12.8341 7.03494Z"
                  fill="#B5B5B5"
                />
                <path
                  d="M116.858 7.03494C117.977 7.03494 118.884 7.94616 118.884 9.07021C118.884 10.1943 117.977 11.1055 116.858 11.1055C115.738 11.1055 114.831 10.1943 114.831 9.07021C114.831 7.94616 115.738 7.03494 116.858 7.03494Z"
                  fill="#B5B5B5"
                />
                <path
                  d="M42.5547 122.367H49.3095V124.437C49.3095 125.542 48.414 126.437 47.3095 126.437H44.5547C43.4501 126.437 42.5547 125.542 42.5547 124.437V122.367Z"
                  fill="#5B5B5B"
                />
                <path
                  d="M34.4492 122.367H41.204V124.437C41.204 125.542 40.3086 126.437 39.204 126.437H36.4492C35.3447 126.437 34.4492 125.542 34.4492 124.437V122.367Z"
                  fill="#5B5B5B"
                />
                <path
                  d="M26.3438 122.367H33.0985V124.437C33.0985 125.542 32.2031 126.437 31.0985 126.437H28.3438C27.2392 126.437 26.3438 125.542 26.3438 124.437V122.367Z"
                  fill="#5B5B5B"
                />
                {fields.step3BatteryTransformerLabel?.value && (
                  <foreignObject x="5" y="10" width="120" height="55">
                    <Stretch height="true">
                      <Stack alignY="center" alignX="center">
                        <Text size="BodyXS" color="textLowEmphasis">
                          {fields.step3BatteryTransformerLabel?.value}
                        </Text>
                      </Stack>
                    </Stretch>
                  </foreignObject>
                )}
              </g>
            </svg>
          </TransformStackItem>
          <Stack.Item>
            <Text size={{ initial: 'BodyXL', md: 'QuoteM' }} color="textLowEmphasis">
              +&nbsp;{amount}x
            </Text>
          </Stack.Item>
          <BatteryModuleStackItem shrink={false}>
            <Stretch height="true">
              <svg width="100%" height="auto" viewBox="142 0 200 235" xmlns="http://www.w3.org/2000/svg" fill="none">
                <g>
                  <path
                    d="M143.353 8.25072C143.353 3.83244 146.934 0.250732 151.353 0.250732H248.449C252.867 0.250732 256.449 3.83245 256.449 8.25073V176.103C256.449 180.521 252.867 184.103 248.449 184.103H151.353C146.934 184.103 143.353 180.521 143.353 176.103V8.25072Z"
                    fill="#CBCBCB"
                  />
                  <path
                    d="M151.353 0.750732H244.36C248.502 0.750732 251.86 4.1086 251.86 8.25073V176.103C251.86 180.245 248.502 183.603 244.36 183.603H151.353C147.21 183.603 143.853 180.245 143.853 176.103V8.25073C143.853 4.1086 147.21 0.750732 151.353 0.750732Z"
                    fill="white"
                    stroke="#CBCBCB"
                  />
                  <path
                    d="M152.891 184.103H162.429C162.429 184.852 161.821 185.46 161.072 185.46H154.247C153.498 185.46 152.891 184.852 152.891 184.103Z"
                    fill="#5B5B5B"
                  />
                  <path
                    d="M233.284 184.103H242.822C242.822 184.852 242.215 185.46 241.466 185.46H234.641C233.892 185.46 233.284 184.852 233.284 184.103Z"
                    fill="#5B5B5B"
                  />
                  {fields.step3BatteryModuleLabel?.value && (
                    <foreignObject x="144" y="10" width="107" height="55">
                      <Stretch height="true">
                        <Stack alignY="center" alignX="center">
                          <Text size="BodyXS" color="textLowEmphasis">
                            {fields.step3BatteryModuleLabel?.value}
                          </Text>
                        </Stack>
                      </Stretch>
                    </foreignObject>
                  )}
                  {/* Height */}
                  <path
                    d="M 275.088 4 L 277.088 0 L 272.088 0 L 274.088 4 L 275.088 4 Z M 274.088 4 V 182 H 275.088 V 4 Z M 274.088 182 L 272.088 186 L 277.088 186 L 275.088 182 L 274.088 182 Z"
                    fill="#000"
                  />
                  <foreignObject x="276" y="0" width="66" height="188">
                    <Stretch height="true">
                      <Stack alignY="center" alignX="center">
                        <Text>{`${offer.height || 0} cm`}</Text>
                      </Stack>
                    </Stretch>
                  </foreignObject>
                  {/* Depth */}
                  <path
                    d="M 258.905 205.951 L 253.434 206.847 L 256.741 211.582 L 259.478 206.771 L 258.905 205.951 Z M 270.271 199.264 L 275.741 198.367 L 272.435 193.633 L 269.698 198.444 L 270.271 199.264 Z M 259.067 207.056 L 270.681 198.979 L 270.108 198.159 L 258.495 206.236 L 259.067 207.056 Z"
                    fill="#000"
                  />
                  <foreignObject x="276" y="210" width="66" height="25">
                    <Stretch height="true">
                      <Stack alignY="center" alignX="center">
                        <Text>{`${offer.depth || 0} cm`}</Text>
                      </Stack>
                    </Stretch>
                  </foreignObject>
                  {/* Width */}
                  <path
                    d="M 147.088 209 L 143.088 207 L 143.088 212 L 147.088 210 Z M 241.088 210 L 245.088 212 L 245.088 207 L 241.088 209 Z M 147.088 210 L 241.088 210 L 241.088 209 L 147.088 209 Z"
                    fill="#000"
                  />
                  <foreignObject x="144" y="210" width="100" height="25">
                    <Stretch height="true">
                      <Stack alignY="center" alignX="center">
                        <Text>{`${offer.width || 0} cm`}</Text>
                      </Stack>
                    </Stretch>
                  </foreignObject>
                </g>
              </svg>
            </Stretch>
          </BatteryModuleStackItem>
        </Stack>
      </MaxWidthBox>
    </Stack>
  );
};

const Battery: FC<{
  offer: SmartEnergyOfferResponseRow;
  fields: SmartHomeBatteryRendering['fields'];
}> = ({ offer, fields }) => {
  if (offer.savedVolume && [5, 10, 15, 20].includes(offer.savedVolume)) {
    return <NewBattery offer={offer} fields={fields} />;
  }

  return <OriginalBattery offer={offer} fields={fields} />;
};

export default Battery;
