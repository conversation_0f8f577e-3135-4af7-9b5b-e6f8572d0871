/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/MyZone/Consumption/ConsumptionGraphicalOverview
 */
export interface ConsumptionGraphicalOverviewRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;

  params: {};
  uid: string;
}
export interface Fields {
  data: Data;
  legend: Legend;
  meterReadingsTable: MeterReadingsTable;
  advanceCard: AdvanceCard;
}
export interface Data {
  addressFilterLabel: TextField;
  consumptionTypeLabel: TextField;
  consumptionTypeOptionsList: NameLabelListField;
  description: TextField;
  disclaimerInfoDescription: TextField;
  disclaimerText: TextField;
  graphTitle: TextField;
  periodLabel: TextField;
  periodOptionsList: NameLabelListField;
  title: TextField;
  noDigitalMeterText: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface NameLabelListField {
  editable?: string;
  value: {
    enum: {
      label: string;
      name: string;
      value: string;
    }[];
  };
}
export interface Legend {
  electricityLabel: TextField;
  fixedCostDescription: TextField;
  fixedCostInformationDescription: TextField;
  fixedCostLabel: TextField;
  injectionLabel: TextField;
  title: TextField;
  totalLabel: TextField;
  peakValuesTitle: TextField;
  averagePeakValueLabel: TextField;
  capacityTariffLabel: TextField;
  gasLabel: TextField;
}
export interface MeterReadingsTable {
  addMeterReadingButtonText: TextField;
  dateTableHeaderText: TextField;
  deleteText: TextField;
  deleteValueText: TextField;
  energyOptionsList: NameLabelListField;
  meterReadingElectricityTableText: TextField;
  meterReadingGasTableText: TextField;
  meterReadingTableTitle: TextField;
  originInformativeText: TextField;
  originMoreInfoPopoverContent: TextField;
  originMoreInfoText: TextField;
  originOfficialText: TextField;
  originText: TextField;
  originUserText: TextField;
  originRlpText: TextField;
  unableToDeleteValueText: TextField;
}
export interface AdvanceCard {
  title: TextField;
  acceptRecommendedAmountLink: GeneralLinkField;
  currencySignLabel: TextField;
  currentAmountTitle: TextField;
  eachMonthLabel: TextField;
  recommendedAmountTitle: TextField;
  recommendedAmountTooHighDisclaimerLabel: TextField;
  recommendedAmountTooLowDisclaimerLabel: TextField;
  editAdvanceAmountLink: GeneralLinkField;
  recommendedAmountNotification: NotificationField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
  };
}
