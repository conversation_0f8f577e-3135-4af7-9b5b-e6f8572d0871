/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Project/Grootzakelijk/Flows/DecarbonizationLead/CompanyDetailsStep
 */
export interface DecarbonizationLeadFlowCompanyDetailsStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  companyDetails: CompanyDetails;
  contactDetails: ContactDetails;
  content: Content;
  isBusinessCustomer: IsBusinessCustomer;
  personalDetails: PersonalDetails;
}
export interface CompanyDetails {
  chamberOfCommerceNumberFormField: CustomFormField;
  companyNameFormField: CustomFormField;
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
export interface ContactDetails {
  emailAddressFormField: CustomFormField;
  phoneNumberFormField: CustomFormField;
}
export interface Content {
  companySearchLoadingText: TextField;
  nextStepText: TextField;
  notification: NotificationField;
  text: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: "info" | "success" | "warning" | "error" | "chat";
  };
}
export interface IsBusinessCustomer {
  isBusinessCustomerRadioGroupField: RadioGroupField;
}
export interface RadioGroupField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    options: {
      description: string;
      label: string;
      name: string;
      value: string;
    }[];
    requiredMessage: string;
  };
}
export interface PersonalDetails {
  initialsFormField: CustomFormFieldMinMax;
  salutationRadioGroupField: RadioGroupField;
  surnameFormField: CustomFormField;
  surnamePrepositionFormField: CustomFormField;
}
export interface CustomFormFieldMinMax {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMaxValueMessage: string;
    validationMessage: string;
    validationMinValueMessage: string;
  };
}
