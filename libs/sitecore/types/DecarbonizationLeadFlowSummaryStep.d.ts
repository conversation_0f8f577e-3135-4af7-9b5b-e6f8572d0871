/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Project/Grootzakelijk/Flows/DecarbonizationLead/DecarbonizationLeadFlowSummaryStep
 */
export interface DecarbonizationLeadFlowSummaryStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  companyDetails: CompanyDetails;
  content: Content;
  disclaimer: Disclaimer;
  edit: Edit;
  genericError: GenericError;
  isBusinessCustomer: IsBusinessCustomer;
  notifications: Notifications;
  paymentDetails: PaymentDetails;
  personalDetailsOverview: PersonalDetailsOverview;
  productsOverview: ProductsOverview;
  settings: Settings;
}
export interface CompanyDetails {
  companyTypeLabel: TextField;
  sectionTitle: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface Content {
  nextStepText: TextField;
  notification: NotificationField;
  priceIncludingText: TextField;
  text: TextField;
  title: TextField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: "info" | "success" | "warning" | "error" | "chat";
  };
}
export interface Disclaimer {
  disclaimerContent: TextField;
  disclaimerTitle: TextField;
}
export interface Edit {
  editButtonLabel: TextField;
}
export interface GenericError {
  errorNotAuthenticatedNotification: NotificationField;
  errorNotAvailableNotification: NotificationField;
  errorNotFoundNotification: NotificationField;
  fetchOfferErrorNotification: NotificationField;
  loginButtonText: TextField;
  sendOrderErrorNotification: NotificationField;
  tryAgainButtonText: TextField;
}
export interface IsBusinessCustomer {
  sectionTitle: TextField;
}
export interface Notifications {
  dynamicPricingContractNotification: NotificationField;
  hybridContractNotification: NotificationField;
}
export interface PaymentDetails {
  description: TextField;
  ibanDialog: DialogField;
  ibanFormField: CustomFormField;
  title: TextField;
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
export interface PersonalDetailsOverview {
  addressDetailsLabel: TextField;
  birthDateLabel: TextField;
  contactDetailsLabel: TextField;
  editDetailsLabel: TextField;
  emailAddressLabel: TextField;
  nameLabel: TextField;
  personalDetailsLabel: TextField;
  phoneNumberLabel: TextField;
  situationDetailsLabel: TextField;
  title: TextField;
  willBeMovingLabel: TextField;
  wontBeMovingLabel: TextField;
}
export interface ProductsOverview {
  contractEndDateLabel: TextField;
  contractStartDateLabel: TextField;
  costDetailsDialog: DialogField;
  title: TextField;
  totalFirstYearLabel: TextField;
}
export interface Settings {
  senderIdOption: DropList;
}
export interface DropList {
  editable?: string;
  value: string;
}
