/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/AccessManagement/RegistrationAccountCreate
 */
export interface RegistrationAccountCreateRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  placeholders: {
    "jss-registration-bottom": {};
  };
  uid: string;
}
export interface Fields {
  canRegisterAddress: CanRegisterAddress;
  canRegisterCustomerID: CanRegisterCustomerID;
  header: Header;
  registerOrgEmail: RegisterOrgEmail;
  registerOrgPassword: RegisterOrgPassword;
  registerPerson: RegisterPerson;
  registerSuccess: RegisterSuccess;
}
export interface CanRegisterAddress {
  addressUnknownText: TextField;
  backLink: GeneralLinkField;
  continueButtonText: TextField;
  houseNumberFormField: CustomFormField;
  houseNumberSuffixFormField: CustomFormField;
  introductionText: TextField;
  isLoadingAddressText: TextField;
  modalDialog: DialogField;
  postalCodeFormField: CustomFormField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
export interface CanRegisterCustomerID {
  backLinkText: TextField;
  continueButtonText: TextField;
  customerIdFormField: CustomFormField;
  errorNotificationField: NotificationField;
  inEmailModalDescription: TextField;
  inEmailModalLabelText: TextField;
  introductionText: TextField;
  modalDialog: DialogField;
  onBankStatementModalDescription: TextField;
  onBankStatementModalLabelText: TextField;
  onYearnoteModalDescription: TextField;
  onYearnoteModalLabelText: TextField;
  title: TextField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: "info" | "success" | "warning" | "error" | "chat";
  };
}
export interface Header {
  title: TextField;
}
export interface RegisterOrgEmail {
  backLinkText: TextField;
  confirmButtonText: TextField;
  conflictErrorNotification: NotificationField;
  emailFormField: CustomFormField;
  emailOverrideCloseLinkText: TextField;
  emailOverrideLinkText: TextField;
  introductionDescription: TextField;
  modalDialog: DialogField;
  overrideDescription: TextField;
  overrideEmailForLoginCodeHintLabel: TextField;
  overrideEmailForLoginCodeLabel: TextField;
  overrideEmailForLoginCodeModalDialog: DialogField;
  overrideEmailForUsernameHintLabel: TextField;
  overrideEmailForUsernameLabel: TextField;
  technicalErrorNotificationField: NotificationField;
  title: TextField;
}
export interface RegisterOrgPassword {
  confirmButtonText: TextField;
  introductionText: TextField;
  passwordFormField: CustomFormField;
  title: TextField;
}
export interface RegisterPerson {
  backLinkText: TextField;
  conflictErrorNotification: NotificationField;
  continueButtonText: TextField;
  emailFormField: CustomFormField;
  introductionText: TextField;
  modalDialog: DialogField;
  passwordFormField: CustomFormField;
  technicalErrorNotificationField: NotificationField;
  title: TextField;
}
export interface RegisterSuccess {
  introductionText: TextField;
  link: GeneralLinkField;
  title: TextField;
}
