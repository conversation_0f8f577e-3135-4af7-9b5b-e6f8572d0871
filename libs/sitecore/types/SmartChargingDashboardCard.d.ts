/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

export type InsightsApp = {
  displayName: string;
  fields: {};
  id: string;
  name: string;
  url: string;
  [k: string]: unknown;
}[];

/**
 * /sitecore/layout/Renderings/Project/Eneco Insights App/Dashboard/SmartChargingDashboardCard
 */
export interface SmartChargingDashboardCardRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  featureScopeList: InsightsApp;
}
