/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/Flows/ThermostatFlow/ThermostatFlowOfferStep
 */
export interface ThermostatFlowOfferStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  content: Content;
  productDetails: ProductDetails;
}
export interface Content {
  errorNotification: NotificationField;
  nextStepText: TextField;
  text: TextField;
  title: TextField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: "info" | "success" | "warning" | "error" | "chat";
  };
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface ProductDetails {
  discountLabel: TextField;
  firstYearFreeLabel: TextField;
  fixedCostsLabel: TextField;
  image: FocalPointImageField;
  oneOffCostsLabel: TextField;
  perMonthLabel: TextField;
  text: TextField;
  title: TextField;
}
export interface FocalPointImageField {
  editable?: string;
  value: {
    alt: string;
    formats: ThumbSchema[];
  };
}
export interface ThumbSchema {
  format: string;
  height: string;
  src: string;
  width: string;
}
