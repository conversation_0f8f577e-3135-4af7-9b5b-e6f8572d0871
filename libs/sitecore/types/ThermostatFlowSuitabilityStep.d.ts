/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/Flows/ThermostatFlow/ThermostatFlowSuitabilityStep
 */
export interface ThermostatFlowSuitabilityStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  content: Content;
  formfields: Formfields;
  unsuitable: Unsuitable;
}
export interface Content {
  nextStepText: TextField;
  suitableNotification: NotificationField;
  text: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: "info" | "success" | "warning" | "error" | "chat";
  };
}
export interface Formfields {}
export interface Unsuitable {
  emailDestinationCodeText: TextField;
  emailErrorNotification: NotificationField;
  emailSignupButtonText: TextField;
  emailSuccessNotification: NotificationField;
  multipleReasonsText: TextField;
  signupContent: TextField;
  unsuitableNotification: NotificationField;
}
