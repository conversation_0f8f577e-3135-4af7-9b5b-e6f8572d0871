export interface DashboardRendering {
  fields: Fields;
  uid: string;
  componentName: string;
  dataSource: string;
  params: {};
  datasourceRequired: boolean;
}

export interface Fields {
  payBlockOrderNumber: TextField;
  advBlockOrderNumber: TextField;
  usageBlockOrderNumber: TextField;
  sitBlockOrderNumber: TextField;
  engBlockOrderNumber: TextField;
  persBlockOrderNumber: TextField;
}

export interface CustomFormField {
  value: {
    label: string;
    hint: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
  editable?: string;
}

export interface TextField {
  value: string;
  editable?: string;
}

export interface NotificationField {
  value: {
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
    title: string;
    content: string;
  };
  editable?: string;
}

export interface GeneralLinkField {
  value: {
    href: string;
    linktype: string;
    url: string;
    anchor: string;
    target: string;
    text: string;
    class: string;
    title: string;
    querystring: string;
    id: string;
  };
  editable?: string;
}
