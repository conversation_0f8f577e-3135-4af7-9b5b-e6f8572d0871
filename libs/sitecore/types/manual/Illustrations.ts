/* Code to generate typing */
// import * as Illustrations from '@sparky/illustrations'
// let arrayOfNames = Object.keys(Illustrations);
// arrayOfNames = arrayOfNames.map(name => `"${name}"`);
// console.log(arrayOfNames.join(' | '));

export type Illustrations =
  | 'Apartment'
  | 'Aquarium'
  | 'Attention'
  | 'Belgium'
  | 'Boiler'
  | 'Calculator'
  | 'Calendar'
  | 'CalendarDate'
  | 'CarHybrid'
  | 'Certificate'
  | 'Certified'
  | 'Chart'
  | 'Chat'
  | 'CheckedBoiler'
  | 'CircleCheckmark'
  | 'ComfortCat'
  | 'Compass'
  | 'FloorConvector'
  | 'Cooking'
  | 'CornerHouse'
  | 'DetachedHouse'
  | 'Dishwasher'
  | 'DoubleMeter'
  | 'Dryer'
  | 'EBike'
  | 'EVChargingStation'
  | 'ElectricCar'
  | 'ElectricalOutlet'
  | 'Electricity'
  | 'ElectricityAndGas'
  | 'ElectricityAndHeating'
  | 'ElectricityGasHeating'
  | 'Email'
  | 'EmailCheckmark'
  | 'EnergyBill'
  | 'EuroThermometer'
  | 'Eye'
  | 'FivePeople'
  | 'FourPeople'
  | 'Freezer'
  | 'Gas'
  | 'GasBoiler'
  | 'Germany'
  | 'Grid'
  | 'Heating'
  | 'Heatpump'
  | 'HeatpumpBehind'
  | 'HeatpumpFront'
  | 'HeatpumpShed'
  | 'HeatpumpSide'
  | 'HomeBattery'
  | 'House'
  | 'HouseCheckmark'
  | 'HouseHeat'
  | 'HouseInsulated'
  | 'Idea'
  | 'InsulationCavityWall'
  | 'InsulationCrawlSpace'
  | 'InsulationFloor'
  | 'InsulationRoof'
  | 'InsulationWindow'
  | 'LabelEuro'
  | 'Leaf'
  | 'Leaves'
  | 'Magnifier'
  | 'Meter'
  | 'Microwave'
  | 'Moving'
  | 'MultipleLocations'
  | 'Netherlands'
  | 'OfficeBuilding'
  | 'OnePerson'
  | 'OnePlanet'
  | 'OtherHouse'
  | 'Oven'
  | 'Payback'
  | 'PaybackElectricity'
  | 'PaybackHeat'
  | 'Placeholder'
  | 'Profile'
  | 'Question'
  | 'Quooker'
  | 'Radiator'
  | 'SwimmingPool'
  | 'ReturnOnInvestment'
  | 'Savings'
  | 'SemiDetachedHouse'
  | 'Service'
  | 'Shower'
  | 'SingleMeter'
  | 'SmallBusiness'
  | 'SmartMeter'
  | 'Smartlamp'
  | 'Smartphone'
  | 'Smile'
  | 'SolarEnergy'
  | 'SolarPanel'
  | 'Suitcase'
  | 'Sun'
  | 'SwitchContract'
  | 'Tap'
  | 'Tariffs'
  | 'Teamwork'
  | 'TerracedHouse'
  | 'ThreePeople'
  | 'Thermostat'
  | 'Timer'
  | 'Toon'
  | 'ToonGraphics'
  | 'Tree'
  | 'TwoPeople'
  | 'UnitedKingdom'
  | 'Washmachine'
  | 'WiFi'
  | 'Wind'
  | 'WindPower';
