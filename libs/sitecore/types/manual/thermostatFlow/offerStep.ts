import { FocalPointImage } from '../../lib';

export interface OfferStepRendering {
  fields: Fields;
  uid: string;
  componentName: string;
  dataSource: string;
  params: {};
  datasourceRequired: boolean;
}
interface Fields {
  content: Content;
  productDetails: ProductDetails;
}
interface Content {
  nextStepText: TextField;
  text: TextField;
  title: TextField;
  errorNotification: NotificationField;
}

interface ProductDetails {
  discountLabel: TextField;
  image: FocalPointImage;
  title: TextField;
  text: TextField;
  perMonthLabel: TextField;
  fixedCostsLabel: TextField;
  firstYearFreeLabel: TextField;
  oneOffCostsLabel: TextField;
}

interface TextField {
  value: string;
  editable?: string;
}

interface NotificationField {
  value: {
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
    title: string;
    content: string;
  };
  editable?: string;
}
