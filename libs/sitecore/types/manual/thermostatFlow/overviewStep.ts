export interface OverviewStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  content: Content;
  notifications: Notifications;
  personalDetails: PersonalDetails;
  productDetails: ProductDetails;
}
export interface Content {
  nextStepText: TextField;
  text: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface Notifications {
  offerErrorNotification: NotificationField;
  orderErrorNotification: NotificationField;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
  };
}
export interface PersonalDetails {
  addressLabel: TextField;
  contactLabel: TextField;
  content: TextField;
  emailAddressLabel: TextField;
  mobilePhoneNumberLabel: TextField;
  nameLabel: TextField;
  phoneNumberLabel: TextField;
  title: TextField;
}
export interface ProductDetails {
  conditionContent: TextField;
  contractEndDateLabel: TextField;
  contractStartDateLabel: TextField;
  discountInfoLabel: TextField;
  discountLabel: TextField;
  firstYearFreeLabel: TextField;
  firstYearInfoText: TextField;
  fixedCostsLabel: TextField;
  oneOffCostsLabel: TextField;
  perMonthLabel: TextField;
  privacyLink: GeneralLinkField;
  salesConditionsDialog: DialogField;
  thermostatInfoLabel: TextField;
  title: TextField;
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
