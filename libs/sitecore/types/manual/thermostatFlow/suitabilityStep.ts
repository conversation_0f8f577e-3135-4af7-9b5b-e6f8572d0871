import { Illustrations } from '../Illustrations';

export interface SuitabilityStepRendering {
  fields: Fields;
  uid: string;
  componentName: string;
  dataSource: string;
  params: {};
  datasourceRequired: boolean;
}
interface Fields {
  content: Content;
  formfields: FormFields;
  unsuitable: Unsuitable;
}

interface Content {
  text: TextField;
  title: TextField;
  nextStepText: TextField;
  suitableNotification: NotificationField;
}

type FormFields = {
  requiredCheckedItemsList: CheckboxItem[];
  requiredUncheckedItemsList: CheckboxItem[];
};

interface Unsuitable {
  emailSignupButtonText: TextField;
  emailDestinationCode: TextField;
  emailSuccessNotification: NotificationField;
  emailErrorNotification: NotificationField;
  unsuitableNotification: NotificationField;
  multipleReasonsText: TextField;
  signupContent: TextField;
}

interface TextField {
  value: string;
  editable?: string;
}

interface NotificationField {
  value: {
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
    title: string;
    content: string;
  };
  editable?: string;
}

interface CheckboxItem {
  id: string;
  url: string;
  name: string;
  displayName: string;
  fields: {
    checkbox: {
      illustration: {
        value: Illustrations;
      };
      name: {
        value: string;
      };
      label: {
        value: string;
      };
      requiredMessageText: {
        value: string;
      };
    };
  };
}
