import { useState, type Dispatch, type SetStateAction } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { useArgs } from '@storybook/preview-api';
import { invisibleStoryCanvas } from '@storybook-tools';

import { Button, Accordion } from '..';

/**
 *  A stack of interactive headings that each reveal an associated section of content. Differs from
 * [Expandable](/docs/content-expandable--basic-example) in that the trigger can only contain text, and it is meant to be
 * used with multiple items.
 */
const meta: Meta<typeof Accordion> = {
  component: Accordion,
  args: { value: [] },
  argTypes: { defaultValue: { control: { type: 'text' } }, setValue: { control: { type: 'text' } } },
  title: 'Components/Content/Accordion',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/proto/naqKumGbHqh3NE6gbIu8v9/Components-Eneco---Sparky?node-id=17804%3A274762&scaling=min-zoom&page-id=17804%3A274760',
    },
  },
};

const AccordionItems = (
  <>
    <Accordion.Item heading="FAQ Vraag 1" id="q1">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
      magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
      consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
    </Accordion.Item>
    <Accordion.Item heading="FAQ Vraag 2" id="q2">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
      magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
      consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
    </Accordion.Item>
    <Accordion.Item heading="FAQ Vraag 3" id="q3">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
      magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
      consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
    </Accordion.Item>
  </>
);

export default meta;
type Story = StoryObj<typeof Accordion>;

export const BasicExample: Story = {
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const setValue = ((newValue: string[]) => {
      updateArgs({ value: newValue });
    }) as Dispatch<SetStateAction<string[]>>;

    return (
      <Accordion {...args} value={value} setValue={setValue}>
        {AccordionItems}
      </Accordion>
    );
  },
};

/**
 * By passing a `defaultValue` to the `Accordion` component, one or more items can be opened automatically when the page
 * loads. Using an array of strings, as many items can be passed as needed. The passed value must match the ID of the item
 * you want to open.
 */
export const DefaultValue: Story = {
  args: {
    defaultValue: ['q2'],
  },
  render: function Render(args) {
    return <Accordion defaultValue={args.defaultValue}>{AccordionItems}</Accordion>;
  },
};

const ControlledView = () => {
  const [value, setValue] = useState(['']);

  return (
    <>
      <Button
        onClick={() => {
          setValue(['item2']);
        }}>
        Learn more
      </Button>
      <Accordion value={value} setValue={setValue}>
        <Accordion.Item id="item1" heading="Is it accessible?">
          Yes. It adheres to the WAI-ARAI design pattern.
        </Accordion.Item>
        <Accordion.Item id="item2" heading="Is it unstyled?">
          Yes. Its unstyled by default, giving you freedom over the look and feel.
        </Accordion.Item>
        <Accordion.Item id="item3" heading="Can it be animated?">
          Yes! You can animate the Accordion with CSS or JavaScript.
        </Accordion.Item>
      </Accordion>
    </>
  );
};

/** If you need more control over the `Accordion`, for example, if you need to include opening items from a function, the
 * `Accordion` can be controlled. Here's an example of how this can be done. You need to pass both the `value` and
 * `setValue` props, so that the internal state setters also work.
 */

export const Controlled: Story = {
  parameters: {
    docs: {
      canvas: {
        sourceState: 'shown',
      },
      source: {
        code: `
import { useState } from 'react';

import { Accordion, Button } from '@sparky';

export default function Component(): JSX.Element {
  const [openItems, setOpenItems] = useState(['']);
  return (
    <>
      <Button
        onClick={() => {
          setOpenItems(['item2']);
        }}>
        Learn more
      </Button>
      <Accordion value={openItems} setValue={setOpenItems}>
        <Accordion.Item id="item1" heading="Is it accessible?">
          Yes. It adheres to the WAI-ARAI design pattern.
        </Accordion.Item>
        <Accordion.Item id="item2" heading="Is it unstyled?">
          Yes. Its unstyled by default, giving you freedom over the look and feel.
        </Accordion.Item>
        <Accordion.Item id="item3" heading="Can it be animated?">
          Yes! You can animate the Accordion with CSS or JavaScript.
        </Accordion.Item>
      </Accordion>
    </>
  );
}`,
      },
    },
  },
  render: function Render(args) {
    return <ControlledView />;
  },
};

/**
 * The Sparky `Accordion` is derived from
 * [Radix-ui's Accordion component](https://www.radix-ui.com/docs/primitives/components/accordion), which adheres to the
 * [Accordion WAI-ARIA design pattern](https://www.w3.org/TR/wai-aria-practices-1.1/#accordion). It is fully
 * keyboard-navigable. After focusing on a trigger, pressing the space or enter key can open and close it. Using the up and
 * down arrow, the user can move through the list, and `Home` and `End` can be used to reach the respective first and last
 * item.
 */
export const Accessibility: Story = invisibleStoryCanvas();
