import { <PERSON><PERSON>bj, <PERSON>a } from '@storybook/nextjs';
import { useArgs } from '@storybook/preview-api';

import { PageGrid, Card, Box, Bleed, Text, Carousel, Stack, Button } from '..';

const CarouselItem = () => (
  <Carousel.Item>
    <Box paddingY="16" backgroundColor="backgroundVarTwo">
      <Text align="center">Carousel item</Text>
    </Box>
  </Carousel.Item>
);

/**
 * Strongly configurable container that can be used to display a set of items in a horizontal row.
 * The user can view the items by scrolling or swiping horizontally, or optionally using controls.
 */
export default {
  title: 'Components/Utilities/Carousel',
  component: Carousel,
  argTypes: {
    scrollBehavior: {
      options: ['native', 'snap', 'vertical'],
    },
    scrollMarginX: {
      options: ['undefined', 'gridGutter'],
    },
    hasCardPadding: {
      control: { type: 'boolean' },
    },
    layout: {
      options: ['default', 'peekNext', 'fullWidth', undefined],
      control: { type: 'select' },
    },
    showIndicator: {
      control: { type: 'boolean' },
    },
    hideArrows: {
      control: { type: 'boolean' },
    },
    prevLabel: {
      control: { type: 'text' },
    },
    nextLabel: {
      control: { type: 'text' },
    },
    startAt: {
      description: 'Index of the carousel item should display on initial load',
      control: { type: 'number' },
    },
    scrollTo: {
      description: 'Index of the carousel item that should be scrolled into the view',
      control: { type: 'number' },
    },
  },
  render: function Render(args) {
    return (
      <Carousel {...args}>
        {Array.from(Array(8)).map((value, index) => (
          <CarouselItem key={index} />
        ))}
      </Carousel>
    );
  },
  parameters: {
    layout: 'fullscreen',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/naqKumGbHqh3NE6gbIu8v9/Components-Eneco---Sparky?type=design&node-id=23631-252286&mode=dev',
    },
  },
} as Meta<typeof Carousel>;

type Story = StoryObj<typeof Carousel>;

export const BasicExample: Story = {
  args: {
    hasCardPadding: false,
    layout: 'default',
    showIndicator: false,
    scrollTo: 0,
  },
};

/**
 * Add `scrollBehavior: "snap"` to enable scroll snap. The user can navigate the carousel by scrolling horizontally. The
 * carousel stops on every item before the user can continue to scroll.
 */
export const ScrollSnap: Story = {
  args: {
    hasCardPadding: false,
    layout: 'default',
    showIndicator: false,
    scrollBehavior: 'snap',
  },
};

/**
 * When adding cards as carousel items, the box shadow might be cut off. If this is the case, add the property
 * `hasCardPadding`.
 */

export const CardWithBoxShadow: Story = {
  args: {
    hasCardPadding: true,
    layout: 'default',
    showIndicator: false,
  },
  render: function Render(args) {
    return (
      <Carousel {...args}>
        {Array.from(Array(5)).map((value, index) => (
          <Carousel.Item key={index}>
            <Card>
              <Card.Content />
            </Card>
          </Carousel.Item>
        ))}
      </Carousel>
    );
  },
};

/**
 * The carousel should be able to take up the entire viewport width, even when placed inside the PageGrid. In this case,
 * wrap the component with `<Bleed horizontal="gridGutter" />` and use the `scrollMarginX` prop to add a scroll margin.
 */
export const UsageWithinPageGrid: Story = {
  args: {
    scrollMarginX: 'gridGutter',
    scrollBehavior: 'vertical',
  },
  render: function Render(args) {
    return (
      <PageGrid>
        <PageGrid.Item gridColumn="1/-1">
          <Bleed horizontal="gridGutter">
            <Carousel {...args}>
              {Array.from(Array(5)).map((value, index) => (
                <CarouselItem key={index} />
              ))}
            </Carousel>
          </Bleed>
        </PageGrid.Item>
      </PageGrid>
    );
  },
};

/**
 * The `scrollBehavior` property makes it possible to have a different scroll behavior on mobile than on desktop. For
 * example, you might want to enable scroll snap on mobile, but vertical scrolling on desktop.
 */
export const ResponsiveBehavior: Story = {
  args: {
    scrollBehavior: { initial: 'snap', md: 'vertical' },
  },
};

/**
 * The carousel indicator is an optional prop that enables a visual representation and controls the navigation of the
 * Carousel.
 *
 * When there are multiple carousel items visible within the viewport, the indicator will show up as **dots**. Otherwise
 * you will see a **bar**.
 *
 * The indicator arrows can be hidden by enabling the `hideArrows` prop. The `prevLabel` and `nextLabel` props can be used
 * to display a text label instead of arrows.
 */
export const CarouselIndicator: Story = {
  args: {
    showIndicator: true,
  },
};

/**
 *  By default, there will be 3 items visible on breakpoint XL, 2 on MD and 1 on SM. By setting the `layout` prop to
 * `peekNext` or `fullWidth`, the carousel will display a single item on all breakpoints.
 * Try interacting with this story on story mode.
 */
export const LayoutProp: Story = {
  args: {
    layout: 'peekNext',
  },
};

/**
 * By using the scrollTo property you can force an item to scroll
 * into view. This can be useful when you want to make items visible
 * based on other interactions. Each time the scrollTo prop is updated,
 * the carousel will scroll to the item at the given index (zero-based).
 */
export const ScrollToProp: Story = {
  args: {
    scrollTo: 0,
  },
  render: function Render(args) {
    const [__, updateArgs] = useArgs();
    const scrollToFour = () => {
      updateArgs({ scrollTo: 3 });
    };
    return (
      <Stack gap="4">
        <Stack.Item>
          <Button onClick={scrollToFour}>Scroll to fourth item</Button>
        </Stack.Item>
        <Carousel {...args}>
          {Array.from(Array(7)).map((value, index) => (
            <CarouselItem key={index} />
          ))}
        </Carousel>
      </Stack>
    );
  },
};
