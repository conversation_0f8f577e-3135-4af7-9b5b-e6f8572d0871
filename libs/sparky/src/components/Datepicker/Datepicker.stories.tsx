/* eslint-disable no-console */
import { type Dispatch, type SetStateAction } from 'react';

import { addDays } from '@common/date';
import type { Meta, StoryObj } from '@storybook/nextjs';
import { useArgs } from '@storybook/preview-api';
import { invisibleStoryCanvas } from '@storybook-tools';

import { Datepicker } from '..';

/**
 * Inner datepicker component.
 */
const meta: Meta<typeof Datepicker> = {
  component: Datepicker,
  title: 'Components/Form Components/Datepicker',

  argTypes: {
    mode: {
      table: {
        type: { summary: 'string' },
      },
      control: { type: 'select', options: ['single', 'range', 'year'] },
    },
    numberOfMonths: {
      table: {
        type: { summary: 'number' },
      },
      control: { type: 'number' },
    },
    resetButtonLabel: {
      table: {
        type: { summary: 'string' },
      },
      control: { type: 'text' },
    },
    saveButtonLabel: {
      table: {
        type: { summary: 'string' },
      },
      control: { type: 'text' },
    },
    selected: {
      table: {
        type: { summary: 'Date' },
      },
      control: { type: 'date' },
    },
    onReset: {
      table: {
        type: { summary: '() => void' },
      },
      control: {},
    },
    onSave: {
      table: {
        type: { summary: '() => void' },
      },
      control: {},
    },
    onSelect: {
      table: {
        type: { summary: '() => void' },
      },
      control: {},
    },
  },
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/naqKumGbHqh3NE6gbIu8v9/Components-Eneco---Sparky?node-id=25492-285791&t=z24cUoeaqdjTnh7h-0',
    },
  },
};
export default meta;

type Story = StoryObj<typeof Datepicker>;

export const BasicExample: Story = {
  args: {
    mode: 'single',
    numberOfMonths: 1,
    resetButtonLabel: 'Reset',
    saveButtonLabel: 'Save',
    onReset: console.log,
    onSave: console.log,
    onSelect: console.log,
    selected: new Date(),
  },
  render: function Render(args) {
    const [{ selected }, updateArgs] = useArgs();
    const updateSelected = ((newValue: Date) => {
      updateArgs({ selected: newValue });
    }) as Dispatch<SetStateAction<Date | undefined>>;
    return <Datepicker {...args} selected={new Date(selected)} onSelect={updateSelected} />;
  },
};

/**
 * The Datepicker component doesn't keep track of its state. To handle date selection, create your own state and state
 * setter and pass these to the Datepicker.
 *
 * ```tsx
 *  const MyCustomDatepicker = () => {
 *   const [selected, setSelected] = useState(new Date());
 *   return <Datepicker onSelect={setSelected} selected={selected} />;
 * };
 * ```
 */
export const StateInstructions: Story = invisibleStoryCanvas();

export const DisabledWeekends: Story = {
  args: { disableWeekends: true },
};

export const DisabledExample: Story = {
  args: { disabled: [addDays(new Date(), -3), addDays(new Date(), -1)] },
};

export const FromDate: Story = {
  args: { fromDate: addDays(new Date(), -2) },
};

export const ToDate: Story = {
  args: { toDate: addDays(new Date(), -2) },
};
