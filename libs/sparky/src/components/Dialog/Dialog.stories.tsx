import { Dispatch, SetStateAction } from 'react';

import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs';
import { useArgs } from '@storybook/preview-api';
import { invisibleStoryCanvas } from '@storybook-tools';

import { Dialog, TextLink, Text, Box, Stack, ButtonLink, Button } from '..';

/**
 * `Dialog` is our modal component. When the `Dialog` is open, it generates a backdrop that has a dark overlay (and is
 * blurred if the browser supports this). On top of this backdrop is a simple card that can contain any content. As long as
 * the `Dialog` is open, the normal content of the website is inaccessible. The `Dialog` is not part of the normal document
 * flow and will be portalled to its own `<div>` root.
 */
const meta: Meta<typeof Dialog> = {
  component: Dialog,
  args: {
    trigger: <TextLink emphasis="high">Click to open modal</TextLink>,
    title: 'Mijn mooie string titel',
    description:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Quidem at cum ullam, placeat ipsum provident soluta excepturi fugit ex ad? Dolorem aut et fuga quibusdam vel tenetur, accusamus voluptatibus doloribus.',
    width: 'regular',
    fullscreen: false,
  },
  argTypes: {
    description: {
      control: { type: 'text' },
    },
    isOpen: {
      control: { type: 'boolean' },
    },
    onClose: {
      control: false,
      description: 'Function called when closed through backdrop, button or esc',
    },
    setOpen: {
      control: false,
    },
    title: {
      control: { type: 'text' },
    },
    trigger: {
      control: false,
    },
    width: {
      options: ['wide', 'regular'],
      control: { type: 'radio' },
      table: { defaultValue: { summary: 'regular' } },
    },
    fullscreen: {
      control: { type: 'boolean' },
      description: 'Enable fullscreen (only on viewport sizes up until `@md`)',
    },
  },
  title: 'Components/Content/Dialog',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/proto/naqKumGbHqh3NE6gbIu8v9/Components-Eneco---Sparky?node-id=15768%3A193913&scaling=min-zoom&page-id=15768%3A193913',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Dialog>;

export const BasicExample: Story = {
  args: {
    isOpen: false,
  },
  render: function Render(args) {
    const [{ isOpen }, updateArgs] = useArgs();
    const updateOpen = ((newValue: boolean) => {
      updateArgs({ isOpen: newValue });
    }) as Dispatch<SetStateAction<boolean>>;
    return (
      <Dialog {...args} isOpen={isOpen} setOpen={updateOpen}>
        <Text>
          Pariatur veritatis id consequuntur, necessitatibus expedita laudantium eligendi sequi officiis ut quia vero,
          commodi illo harum vitae voluptatibus sint perferendis aspernatur laborum iure unde quis, accusantium
          dignissimos. Neque, facilis asperiores?
        </Text>
      </Dialog>
    );
  },
};

/**
 * To use the `Dialog` uncontrolled, a trigger prop is required. This needs to be an interactable element, such as a
 * `Button` or a `TextLink`. If a non-interactable element, such as a plain string is used, the trigger will not show up
 * and the component will be unusable. Triggers will be part of the normal flow of the document.
 */

export const DialogTrigger: Story = invisibleStoryCanvas();

/**
 * Anything passed as children between the `Dialog` tags will be rendered as the content of the Dialog. If the content does
 * not fit on the viewport, it will become scrollable (while the backdrop remains static).
 */

export const DialogContentScrollable: Story = {
  args: {
    description:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Quidem at cum ullam, placeat ipsum provident soluta excepturi fugit ex ad? Dolorem aut et fuga quibusdam vel tenetur, accusamus voluptatibus doloribus.',
  },
  render: function (args) {
    return (
      <Dialog {...args}>
        <Text size={{ initial: 'BodyL', md: 'BodyXL' }}>
          Pariatur veritatis id consequuntur, necessitatibus expedita laudantium eligendi sequi officiis ut quia vero,
          commodi illo harum vitae voluptatibus sint perferendis aspernatur laborum iure unde quis, accusantium
          dignissimos. Neque, facilis asperiores? Pariatur veritatis id consequuntur, necessitatibus expedita laudantium
          eligendi sequi officiis ut quia vero, commodi illo harum vitae voluptatibus sint perferendis aspernatur
          laborum iure unde quis, accusantium dignissimos. Neque, facilis asperiores? Pariatur veritatis id
          consequuntur, necessitatibus expedita laudantium eligendi sequi officiis ut quia vero, commodi illo harum
          vitae voluptatibus sint perferendis aspernatur laborum iure unde quis, accusantium dignissimos. Neque, facilis
          asperiores? Pariatur veritatis id consequuntur, necessitatibus expedita laudantium eligendi sequi officiis ut
          quia vero, commodi illo harum vitae voluptatibus sint perferendis aspernatur laborum iure unde quis,
          accusantium dignissimos. Neque, facilis asperiores? Pariatur veritatis id consequuntur, necessitatibus
          expedita laudantium eligendi sequi officiis ut quia vero, commodi illo harum vitae voluptatibus sint
          perferendis aspernatur laborum iure unde quis, accusantium dignissimos. Neque, facilis asperiores? Pariatur
          veritatis id consequuntur, necessitatibus expedita laudantium eligendi sequi officiis ut quia vero, commodi
          illo harum vitae voluptatibus sint perferendis aspernatur laborum iure unde quis, accusantium dignissimos.
          Neque, facilis asperiores? Pariatur veritatis id consequuntur, necessitatibus expedita laudantium eligendi
          sequi officiis ut quia vero, commodi illo harum vitae voluptatibus sint perferendis aspernatur laborum iure
          unde quis, accusantium dignissimos. Neque, facilis asperiores? Pariatur veritatis id consequuntur,
          necessitatibus expedita laudantium eligendi sequi officiis ut quia vero, commodi illo harum vitae voluptatibus
          sint perferendis aspernatur laborum iure unde quis, accusantium dignissimos. Neque, facilis asperiores?
          Pariatur veritatis id consequuntur, necessitatibus expedita laudantium eligendi sequi officiis ut quia vero,
          commodi illo harum vitae voluptatibus sint perferendis aspernatur laborum iure unde quis, accusantium
          dignissimos. Neque, facilis asperiores? Pariatur veritatis id consequuntur, necessitatibus expedita laudantium
          eligendi sequi officiis ut quia vero, commodi illo harum vitae voluptatibus sint perferendis aspernatur
          laborum iure unde quis, accusantium dignissimos. Neque, facilis asperiores?
        </Text>
      </Dialog>
    );
  },
};
/**
 * The `Dialog` has a built-in closing button that will close the `Dialog`. It can also be closed by clicking the backdrop
 * and by pressing `ESC`, `enter` or `space` on the keyboard.
 */
export const ClosingTheDialog: Story = invisibleStoryCanvas();

/**
 * You can use the `Dialog.Close` compound component to add your own buttons that can close the modal. If you use it to
 * wrap a button element make sure the `Dialog.Close` gets the `asChild` prop.
 */

export const CustomClose: Story = {
  render: function Render(args) {
    const [{ isOpen }, updateArgs] = useArgs();
    const updateOpen = ((newValue: boolean) => {
      updateArgs({ isOpen: newValue });
    }) as Dispatch<SetStateAction<boolean>>;
    return (
      <Dialog {...args} isOpen={isOpen} setOpen={updateOpen}>
        <Box paddingBottom="6">
          <Text>
            It can for example be used for Dialogs that start an action such as filling out a form, which can be
            canceled.
          </Text>
        </Box>
        <Stack gap="5" direction={{ initial: 'column', sm: 'row' }}>
          <ButtonLink href="/">OK</ButtonLink>
          <Dialog.Close asChild>
            <Button action="secondary">Cancel</Button>
          </Dialog.Close>
        </Stack>
      </Dialog>
    );
  },
};

/**
 * If you need more control over the `Dialog`, for example, if you need to include closing the Dialog inside of a function,
 * the `Dialog` can be controlled. Here's an example of how this can be done. You need to pass both the `isOpen` and
 * `setOpen` props, so that the internal state setters also work.
 *
 * ```tsx
 * import { useState } from 'react';
 *
 * import { Button, Dialog, Text } from '@sparky';
 *
 * export default function Page(): JSX.Element {
 *   const [isOpen, setOpen] = useState(false);
 *
 *   const handleSubmit = () => {
 *     console.log('Submitting');
 *     setOpen(false);
 *   };
 *
 *   return (
 *     <Dialog title="This modal is controlled" isOpen={isOpen} setOpen={setOpen} trigger={<Button>Open Dialog</Button>}>
 *       <Text>Dialog content goes here.</Text>
 *       <Button onClick={handleSubmit}>Submit</Button>
 *     </Dialog>
 *   );
 * }
 * ```
 */
export const Controlled: Story = invisibleStoryCanvas();

/**
 * The `Dialog` component is an implementation of `radix-ui`'s `Dialog` component, which takes into account key
 * accessibility concerns and follows the
 * [Dialog WAI-ARIA design pattern](https://www.w3.org/TR/wai-aria-practices-1.2/#dialog_modal). Focus is automatically
 * trapped when the `Dialog` is opened. The `title` and `description` will be announced to screen reader users. This way,
 * the `Dialog` is also usable for people with vision or mobility limitations.
 */
export const Accessibility: Story = invisibleStoryCanvas();
