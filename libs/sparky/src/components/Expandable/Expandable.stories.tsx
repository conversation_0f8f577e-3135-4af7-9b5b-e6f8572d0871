import type { SetStateAction, Dispatch } from 'react';

import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs';
import { useArgs } from '@storybook/preview-api';
import { invisibleStoryCanvas } from '@storybook-tools';

import { Button, Expandable, Grid, Stack, Text, TextLink } from '..';

/**
 * The `Expandable` component is used to show or hide content whenever the user interacts with it. Essentially, it's a
 * button that controls the visibility of the content. This type of component is also known as a collapsible or (according
 * to [WAI-ARIA](https://www.w3.org/TR/wai-aria-practices-1.1/#disclosure) spec) disclosure. If you need multiple
 * Expandables that stack, consider using an [Accordion](/docs/content-accordion--basic-example) instead.
 */
export default {
  title: 'Components/Content/Expandable',
  component: Expandable,
  argTypes: { isOpen: { control: { type: 'boolean' } }, setOpen: { control: { type: 'object' } } },
  render: function Render(args) {
    return (
      <Expandable {...args}>
        <Expandable.Trigger label="Voorschotfactuur">
          <Grid alignY="center">
            <Grid.Item gridColumn="1">
              <Stack>
                <Text weight="bold">Voorschotfactuur januari 2022</Text>
                <Text size="BodyS" color="textLowEmphasis">
                  Betaal voor 24 januari
                </Text>
              </Stack>
            </Grid.Item>
            <Grid.Item gridColumn="2">
              <Text>€ 180,00</Text>
            </Grid.Item>
            <Grid.Item gridColumn="3">
              <TextLink emphasis="high" href="#jkl">
                Betalen
              </TextLink>
            </Grid.Item>
          </Grid>
        </Expandable.Trigger>
        <Expandable.Content>
          <Text>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus tincidunt odio sed volutpat molestie.
            Aliquam vel pharetra nisl. Etiam sagittis dui et congue commodo. Vestibulum tempor hendrerit neque, non
            interdum dolor faucibus at. Nulla consectetur nisl ac diam consequat pellentesque. In iaculis felis at elit
            semper laoreet. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae;
            Integer euismod sagittis augue, mattis tincidunt felis sagittis ac. Nulla commodo, urna ut egestas
            tristique, erat nisl lacinia felis, nec condimentum augue neque vitae urna. Vivamus odio odio, tincidunt
            eget blandit vel, lacinia eget lorem.
          </Text>
        </Expandable.Content>
      </Expandable>
    );
  },
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/proto/naqKumGbHqh3NE6gbIu8v9/Components-Eneco---Sparky?node-id=13524%3A139813&scaling=min-zoom&page-id=13524%3A139784',
    },
  },
} as Meta<typeof Expandable>;

type Story = StoryObj<typeof Expandable>;

export const BasicExample: Story = {};

/**
 * The implementation of the component is fairly straightforward. You need one wrapper component: `Expandable`. This
 * component controls the state of the whole thing. To make it work you need the `Trigger` and `Content` compound
 * components. `Expandable.Content` will contain all the content that should be shown when teh Expandable is open. This is not limited to plain text.
 * To learn more about the `Trigger`, please check the subpage.
 */

export const MinimalExample: Story = {
  render: function Render() {
    return (
      <Expandable>
        <Expandable.Trigger label="open me">Anything</Expandable.Trigger>
        <Expandable.Content>Is possible</Expandable.Content>
      </Expandable>
    );
  },
  parameters: {
    docs: {
      canvas: {
        sourceState: 'shown',
      },
    },
  },
};

/**
 * If you need more control over the `Expandable`, for example, if you need to include opening the Expandable inside of a
 * function, the `Expandable` can be controlled. Here's an example of how this can be done. You need to pass both the
 * `isOpen` and `setOpen` props, so that the internal state setters also work.
 * To see this story in action, switch to the story tab and pay attention to the controls.
 */

export const Controlled: Story = {
  parameters: {
    docs: {
      canvas: {
        sourceState: 'shown',
      },
      source: {
        code: `
import { useState } from 'react';

import { Expandable, Button } from '@sparky';

export default function Page(): JSX.Element {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <Button onClick={() => setIsOpen(!isOpen)}>Change state </Button>
      <Expandable isOpen={isOpen} setOpen={setIsOpen}>
        <Expandable.Trigger label="trigger">Trigger</Expandable.Trigger>
        <Expandable.Content>Content</Expandable.Content>
      </Expandable>
    </>
  );
}
    `,
      },
    },
  },
  render: function Render(_args) {
    const [{ isOpen }, setArgs] = useArgs();
    const updateState = ((newArgs: boolean) => setArgs({ isOpen: newArgs })) as Dispatch<SetStateAction<boolean>>;
    return (
      <>
        <Button onClick={() => updateState(!isOpen)}>Change state </Button>
        <Expandable isOpen={isOpen} setOpen={updateState}>
          <Expandable.Trigger label="trigger">Trigger</Expandable.Trigger>
          <Expandable.Content>Content</Expandable.Content>
        </Expandable>
      </>
    );
  },
};

/**
 * You can also set the `defaultOpen` prop to `true` to make the `Expandable` open by default.
 * This can not be combined with a controlled state, in that case you can set the default value of the passed state.
 */
export const DefaultOpen: Story = {
  render: function Render(args) {
    return (
      <Expandable {...args}>
        <Expandable.Trigger label="open me">Trigger</Expandable.Trigger>
        <Expandable.Content>Content</Expandable.Content>
      </Expandable>
    );
  },
  args: {
    defaultOpen: true,
  },
};

/**
 * The `Expandable` folows the [WAI-ARIA disclosure](https://www.w3.org/WAI/ARIA/apg/patterns/disclosure/) pattern. This means that
 * the `Expandable` is accessible by default. The `Expandable` is also keyboard navigable. We use [Radix's `Disclosure`
 * component](https://www.radix-ui.com/primitives/docs/components/collapsible) under the hood, which takes care of all of these accessibility concerns.
 */
export const Accessibility: Story = invisibleStoryCanvas();
