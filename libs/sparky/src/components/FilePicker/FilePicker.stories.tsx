import type { Dispatch, SetStateAction } from 'react';

import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { useArgs } from '@storybook/preview-api';
import { invisibleStoryCanvas } from '@storybook-tools';

import { FilePicker } from '..';

const mockBlob = new Blob(['test'], { type: 'image/png' });
const mockFiles = [
  new File([mockBlob], 'File 1.png'),
  new File([mockBlob], 'File 2.png'),
  new File([mockBlob], 'File 3.png'),
];
/**
 * An input component that allows users to select files by pressing a button or dragging files. Includes basic validation
 * of the selected files.
 */
export default {
  title: 'Components/Form Components/FilePicker',
  component: FilePicker,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/9fDXpPYAEi1pieuYVL0I2L/Exploration?type=design&node-id=2337-82595&t=znZ8SPk6HC7bRUOv-0',
    },
  },
  args: {
    files: mockFiles,
    maxFileSize: 5,
    maxTotalSize: 10,
    maxAmount: 7,
    hasError: false,
    accept: ['PDF', 'DOC', 'DOCX', 'JPG', 'JPEG', 'PNG', 'GIF'],
    headingLevel: 'h2',
    name: 'passport',
  },
  argTypes: {
    files: { control: { type: 'file' } },
    hasError: {},
    setFiles: {
      control: {
        disable: true,
      },
    },
    setHasError: {
      control: {
        disable: true,
      },
    },
  },
  render: function Render(args) {
    const [_, setArgs] = useArgs();
    const setHasError = ((value: boolean) => setArgs({ hasError: value })) as Dispatch<SetStateAction<boolean>>;
    const setFiles = ((value: File[]) => setArgs({ files: value })) as Dispatch<SetStateAction<File[]>>;
    return <FilePicker {...args} setHasError={setHasError} setFiles={setFiles} />;
  },
} as Meta<typeof FilePicker>;

type Story = StoryObj<typeof FilePicker>;

export const BasicExample: Story = {};

/**
 * This component requires two states and their setter functions to work. This is to ensure that input can be verified
 * properly. The component can only be used in a controlled setting.
 *
 * When a user selects files to upload, they are checked based on the accepted file extensions and maximum file size. If
 * these are ok, the files are passed to the files array. If the combined size of the files or the amount of files is too
 * high, an error will be shown. `hasError` will also be set to true, which can be used to prevent form submission. As long
 * as no more than `maxAmount + 10` files are selected, they will always be stored in the array of files, so that the user
 * can select which ones to keep after uploading.
 *
 * ```tsx
 * import { FilePicker } from '@sparky';
 * const MyImplementation = () => {
 *   const [files, setFiles] = useState<File[]>([]);
 *   const [hasError, setHasError] = useState(false);
 *   return (
 *     <FilePicker files={files} hasError={hasError} name="myFilePicker" setFiles={setFiles} setHasError={setHasError} />
 *   );
 * };
 * ```
 */

export const RequiredStates: Story = invisibleStoryCanvas();

/**
 * This component has built-in support for English, Dutch, Flemish and French labels. If desired, custom labels can be
 * passed as seen below.
 *
 * The schema of customLabels is available under `FilePickerProps['customLabels']`, which can be imported from
 * `@sparky/types`. Pay special attention to the required
 * [arguments](https://formatjs.io/docs/core-concepts/icu-syntax/#simple-argument) that will be used to inject data into
 * the strings.
 */
export const CustomCopy: Story = {
  args: {
    customLabels: {
      selectFile: 'Kies een bestand',
      orDrag: 'of sleep hem in het vak',
      maxFileSize: 'maximaal {maxFileSize} MB groot.',
      uploadedFiles: 'Geupload: ({amount})',
      errors: {
        fileTooBig: 'Dit bestand is te groot',
        incorrectType: 'Dit bestandstype staat niet in de lijst',
        maxAmountReached: {
          title: 'Te veel bestanden geupload.',
          message: 'Je kan maximaal {maxAmount} bestanden tegelijk kiezen.',
        },
        maxTotalSizeReached: {
          title: 'Maximale totale grootte bereikt',
          message: 'Je kan niet meer dan {maxTotalSize} MB aan bestanden tegelijk kiezen.',
        },
      },
    },
  },
};

/**
 * This component uses a hidden `<input type='file' multiple>` element, which has built-in accessibility. Users can click
 * anywhere in the drag area to open a native popup that handles file selection. The popup can also be opened with standard
 * keyboard navigation. Drag-and-drop is available as well.
 */
export const Accessibility: Story = invisibleStoryCanvas();
