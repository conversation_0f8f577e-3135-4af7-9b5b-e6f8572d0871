import '@testing-library/jest-dom';
import React from 'react';

import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { InputDatepicker } from './InputDatepicker';
import { renderWithProviders } from '../../test-utils/render-with-providers';

describe('Given an InputDatepicker', () => {
  // Reuse userEvent instance across tests for better performance
  let user: ReturnType<typeof userEvent.setup>;

  // Common test data to avoid recreating dates
  const testDate = new Date(2023, 0, 1);
  const selectedDate1 = new Date(2023, 0, 3);
  const selectedDate2 = new Date(2023, 0, 4);
  const selectedDate3 = new Date(2023, 0, 8);
  const selectedDate4 = new Date(2023, 0, 10);

  beforeEach(() => {
    user = userEvent.setup();
  });

  // Helper functions to reduce repetitive code
  const openCalendar = async () => {
    await user.click(screen.getByRole('button', { name: 'Open calendar' }));
  };

  const closeCalendar = async () => {
    await user.click(screen.getByRole('button', { name: 'Close calendar' }));
  };

  const clickDate = async (dateLabel: string) => {
    await user.click(screen.getByRole('button', { name: dateLabel }));
  };

  const getTextbox = () => screen.getByRole('textbox');

  // Lightweight render function for simple tests that don't need full provider setup
  const renderDatepicker = (props: React.ComponentProps<typeof InputDatepicker>) => {
    return renderWithProviders(<InputDatepicker {...props} />);
  };

  describe('when in the default state', () => {
    it('should have no accessibility violations', async () => {
      const { container } = renderDatepicker({ label: 'Pick a date', name: 'datepicker' });

      await openCalendar();
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('when date selected', () => {
    it('calls the onSelect function with the selected date', async () => {
      const onSelect = jest.fn();

      renderDatepicker({
        label: 'Pick a date',
        name: 'datepicker',
        onDaySelect: onSelect,
        fromDate: testDate,
        defaultMonth: testDate,
      });

      await openCalendar();
      await clickDate('Tuesday, 3 January 2023');

      expect(onSelect).toHaveBeenCalledWith(selectedDate1);
      expect(getTextbox()).toHaveValue('03-01-2023');
    });

    it('selects a date range', async () => {
      const onSelect = jest.fn();

      renderDatepicker({
        label: 'Pick a date',
        name: 'datepicker',
        onDaySelect: onSelect,
        fromDate: testDate,
        defaultMonth: testDate,
        mode: 'range',
      });

      await openCalendar();
      await clickDate('Wednesday, 4 January 2023');
      await clickDate('Sunday, 8 January 2023');
      await closeCalendar();

      expect(getTextbox()).toHaveValue('04-01-2023 / 08-01-2023');
      expect(onSelect).toHaveBeenLastCalledWith({ from: selectedDate2, to: selectedDate3 });
    });

    it('selects multiple dates', async () => {
      const onSelect = jest.fn();

      renderDatepicker({
        label: 'Pick a date',
        name: 'datepicker',
        onDaySelect: onSelect,
        fromDate: testDate,
        defaultMonth: testDate,
        mode: 'multiple',
      });

      await user.click(getTextbox());
      await clickDate('Wednesday, 4 January 2023');
      await clickDate('Sunday, 8 January 2023');
      await clickDate('Tuesday, 10 January 2023');
      await closeCalendar();

      expect(getTextbox()).toHaveValue('04-01-2023, 08-01-2023, 10-01-2023');
      expect(onSelect).toHaveBeenLastCalledWith([selectedDate2, selectedDate3, selectedDate4]);
    });

    it('hides the reset button unless onReset is passed down', async () => {
      const onSelect = jest.fn();

      renderDatepicker({
        label: 'Pick a date',
        name: 'datepicker',
        onDaySelect: onSelect,
        fromDate: testDate,
        defaultMonth: testDate,
        mode: 'range',
      });

      await openCalendar();

      expect(screen.queryByRole('button', { name: 'Reset' })).not.toBeInTheDocument();
    });

    it('resets the date selection if onReset is passed down', async () => {
      const onSelect = jest.fn();
      const onReset = jest.fn();

      renderDatepicker({
        label: 'Pick a date',
        name: 'datepicker',
        onDaySelect: onSelect,
        fromDate: testDate,
        defaultMonth: testDate,
        mode: 'range',
        onReset: onReset,
      });

      // Open calendar and verify reset button is present
      await openCalendar();
      expect(screen.getByRole('button', { name: 'Reset' })).toBeInTheDocument();

      // Select date range
      await clickDate('Wednesday, 4 January 2023');
      await clickDate('Sunday, 8 January 2023');

      // Close calendar and verify the selected range
      await closeCalendar();
      expect(getTextbox()).toHaveValue('04-01-2023 / 08-01-2023');

      // Reopen calendar for reset operation
      await openCalendar();

      // Click reset and verify reset was called
      await user.click(screen.getByRole('button', { name: 'Reset' }));
      expect(onReset).toHaveBeenCalled();

      // Close calendar and verify the input is cleared
      await closeCalendar();
      await waitFor(() => {
        expect(getTextbox()).toHaveValue('');
      });
    });
  });
});
