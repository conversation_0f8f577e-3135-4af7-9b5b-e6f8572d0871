import '@testing-library/jest-dom';
import React from 'react';

import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import { InputDatepicker } from './InputDatepicker';
import { renderWithProviders } from '../../test-utils/render-with-providers';

describe('Given an InputDatepicker', () => {
  const user = userEvent.setup();

  describe('when in the default state', () => {
    it('should have no accessibility violations', async () => {
      const { container } = renderWithProviders(<InputDatepicker label="Pick a date" name="datepicker" />);

      await user.click(
        screen.getByRole('button', {
          name: 'Open calendar',
        }),
      );
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('when date selected', () => {
    it('calls the onSelect function with the selected date', async () => {
      const onSelect = jest.fn();
      const fromDate = new Date(2023, 0, 1);

      renderWithProviders(
        <InputDatepicker
          label="Pick a date"
          name="datepicker"
          onDaySelect={onSelect}
          fromDate={fromDate}
          defaultMonth={fromDate}
        />,
      );

      await user.click(
        screen.getByRole('button', {
          name: 'Open calendar',
        }),
      );
      await user.click(
        screen.getByRole('button', {
          name: 'Tuesday, 3 January 2023',
        }),
      );

      const selectedDate = new Date(2023, 0, 3);

      expect(onSelect).toHaveBeenCalledWith(selectedDate);
      expect(screen.getByRole('textbox')).toHaveValue('03-01-2023');
    });

    it('selects a date range', async () => {
      const onSelect = jest.fn();
      const fromDate = new Date(2023, 0, 1);

      renderWithProviders(
        <InputDatepicker
          label="Pick a date"
          name="datepicker"
          onDaySelect={onSelect}
          fromDate={fromDate}
          defaultMonth={fromDate}
          mode="range"
        />,
      );

      await user.click(
        screen.getByRole('button', {
          name: 'Open calendar',
        }),
      );

      await user.click(
        screen.getByRole('button', {
          name: 'Wednesday, 4 January 2023',
        }),
      );
      await user.click(
        screen.getByRole('button', {
          name: 'Sunday, 8 January 2023',
        }),
      );

      await user.click(
        screen.getByRole('button', {
          name: 'Close calendar',
        }),
      );

      const selectedDateFrom = new Date(2023, 0, 4);
      const selectedDateTo = new Date(2023, 0, 8);

      expect(screen.getByRole('textbox')).toHaveValue('04-01-2023 / 08-01-2023');
      expect(onSelect).toHaveBeenLastCalledWith({ from: selectedDateFrom, to: selectedDateTo });
    });

    it('selects multiple dates', async () => {
      const onSelect = jest.fn();
      const fromDate = new Date(2023, 0, 1);

      renderWithProviders(
        <InputDatepicker
          label="Pick a date"
          name="datepicker"
          onDaySelect={onSelect}
          fromDate={fromDate}
          defaultMonth={fromDate}
          mode="multiple"
        />,
      );

      await user.click(screen.getByRole('textbox'));

      await user.click(
        screen.getByRole('button', {
          name: 'Wednesday, 4 January 2023',
        }),
      );
      await user.click(
        screen.getByRole('button', {
          name: 'Sunday, 8 January 2023',
        }),
      );
      await user.click(
        screen.getByRole('button', {
          name: 'Tuesday, 10 January 2023',
        }),
      );
      await user.click(
        screen.getByRole('button', {
          name: 'Close calendar',
        }),
      );

      const selectedDates = [new Date(2023, 0, 4), new Date(2023, 0, 8), new Date(2023, 0, 10)];

      expect(screen.getByRole('textbox')).toHaveValue('04-01-2023, 08-01-2023, 10-01-2023');
      expect(onSelect).toHaveBeenLastCalledWith(selectedDates);
    });

    it('hides the reset button unless onReset is passed down', async () => {
      const onSelect = jest.fn();
      const fromDate = new Date(2023, 0, 1);

      renderWithProviders(
        <InputDatepicker
          label="Pick a date"
          name="datepicker"
          onDaySelect={onSelect}
          fromDate={fromDate}
          defaultMonth={fromDate}
          mode="range"
        />,
      );

      await user.click(
        screen.getByRole('button', {
          name: 'Open calendar',
        }),
      );

      expect(
        screen.queryByRole('button', {
          name: 'Reset',
        }),
      ).not.toBeInTheDocument();
    });

    it('resets the date selection if onReset is passed down', async () => {
      const onSelect = jest.fn();
      const onReset = jest.fn();
      const fromDate = new Date(2023, 0, 1);

      renderWithProviders(
        <InputDatepicker
          label="Pick a date"
          name="datepicker"
          onDaySelect={onSelect}
          fromDate={fromDate}
          defaultMonth={fromDate}
          mode="range"
          onReset={onReset}
        />,
      );

      // Open calendar and verify reset button is present
      await user.click(
        screen.getByRole('button', {
          name: 'Open calendar',
        }),
      );

      expect(
        screen.getByRole('button', {
          name: 'Reset',
        }),
      ).toBeInTheDocument();

      // Select date range
      await user.click(
        screen.getByRole('button', {
          name: 'Wednesday, 4 January 2023',
        }),
      );
      await user.click(
        screen.getByRole('button', {
          name: 'Sunday, 8 January 2023',
        }),
      );

      // Close calendar and verify the selected range
      await user.click(
        screen.getByRole('button', {
          name: 'Close calendar',
        }),
      );

      expect(screen.getByRole('textbox')).toHaveValue('04-01-2023 / 08-01-2023');

      // Reopen calendar for reset operation
      await user.click(
        screen.getByRole('button', {
          name: 'Open calendar',
        }),
      );

      // Click reset and wait for the reset to complete
      await user.click(
        screen.getByRole('button', {
          name: 'Reset',
        }),
      );

      // Verify reset was called
      expect(onReset).toHaveBeenCalled();

      // Wait for the input to be cleared after reset
      await waitFor(() => {
        expect(screen.getByRole('textbox')).toHaveValue('');
      });

      // Close calendar
      await user.click(
        screen.getByRole('button', {
          name: 'Close calendar',
        }),
      );

      // Final verification that the input remains cleared
      expect(screen.getByRole('textbox')).toHaveValue('');
    });
  });
});
