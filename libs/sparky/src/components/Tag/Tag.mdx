import { <PERSON>vas, Meta, Controls } from '@storybook/addon-docs/blocks';
import { Tag } from '..';
import * as TagStories from './Tag.stories';

import { useArgs } from '@storybook/preview-api';

<Meta of={TagStories} />

# Tag

A tag is an interactive element that allows the user to find content that has been marked with a specific keyword or
theme. They are often used to filter content.

export const Template = args => {
  const [_, updateArgs] = useArgs();
  const handle = (e, f) => {
    updateArgs({ ...args, isCurrent: !args.isCurrent });
  };
  return (
    <Tag {...args} onClick={handle}>
      Click me
    </Tag>
  );
};

<Canvas of={TagStories.BasicExample} />

## API (props)

<Controls />

## Usage

```js
import { Tag } from '@sparky';
```

Depending on whether or not a `href` is passed, the `Tag` will be rendered as a `button` or an `a` tag.

## Accessibility

When the component is used as a button, the `aria-pressed` property will be set. This transforms the button into a
toggle button, which tells users of assistive technology that the button can be turned on and off. Aditionally, the
`ariaControls` property will be required. This property should be set to the id of the element that is controlled by the
button, which will usually be a container of content that will be filtered by the `Tag`.
