import type { ComponentProps } from 'react';

import { useArgs } from '@storybook/preview-api';

import { Tag } from '..';

type TagProps = ComponentProps<typeof Tag>;

const Template = (args: TagProps) => {
  const [_, updateArgs] = useArgs();
  const handle = () => {
    updateArgs({ ...args, isCurrent: !args.isCurrent });
  };
  return (
    <Tag {...args} onClick={handle}>
      Click me
    </Tag>
  );
};

export default {
  title: 'Components/Links/Tag',
  component: Tag,

  args: {
    variant: 'regular',
  },

  argTypes: {
    isCurrent: {
      control: {
        type: 'boolean',
      },
    },
  },
};

export const BasicExample = {
  render: Template.bind(this),

  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/naqKumGbHqh3NE6gbIu8v9/Components-Eneco---Sparky?type=design&node-id=28458-20148&mode=dev',
    },
  },
};
