import React, { FC } from 'react';

import { Illustration, IllustrationProps } from '../IllustrationWrapper';

export const Thermostat: FC<IllustrationProps> = ({ size, color }) => (
  <Illustration size={size} color={color}>
    <path d="M57 7H23C14.16 7 7 14.16 7 23V57C7 65.84 14.16 73 23 73H57C65.84 73 73 65.84 73 57V23C73 14.16 65.84 7 57 7ZM70 57C70 64.17 64.17 70 57 70H23C15.83 70 10 64.17 10 57V23C10 15.83 15.83 10 23 10H57C64.17 10 70 15.83 70 23V57ZM35 43.79V22C35 18.69 32.31 16 29 16C25.69 16 23 18.69 23 22V43.79C19.99 45.75 18 49.14 18 53C18 59.08 22.92 64 29 64C35.08 64 40 59.08 40 53C40 49.14 38.01 45.75 35 43.79ZM29 19C30.65 19 32 20.35 32 22V30H26V22C26 20.35 27.35 19 29 19ZM29 61C24.59 61 21 57.41 21 53C21 50.29 22.36 47.79 24.64 46.3L26 45.41V33H32V45.41L33.36 46.3C35.64 47.79 37 50.29 37 53C37 57.41 33.41 61 29 61ZM60 17.5C60 18.33 59.33 19 58.5 19H47.5C46.67 19 46 18.33 46 17.5C46 16.67 46.67 16 47.5 16H58.5C59.33 16 60 16.67 60 17.5ZM60 31.5C60 32.33 59.33 33 58.5 33H47.5C46.67 33 46 32.33 46 31.5C46 30.67 46.67 30 47.5 30H58.5C59.33 30 60 30.67 60 31.5ZM60 45.5C60 46.33 59.33 47 58.5 47H47.5C46.67 47 46 46.33 46 45.5C46 44.67 46.67 44 47.5 44H58.5C59.33 44 60 44.67 60 45.5ZM46 24.5C46 23.67 46.67 23 47.5 23H52.5C53.33 23 54 23.67 54 24.5C54 25.33 53.33 26 52.5 26H47.5C46.67 26 46 25.33 46 24.5ZM46 38.5C46 37.67 46.67 37 47.5 37H52.5C53.33 37 54 37.67 54 38.5C54 39.33 53.33 40 52.5 40H47.5C46.67 40 46 39.33 46 38.5Z" />
  </Illustration>
);
