export const brandColors = {
  brandRed: '#E5384C',
  brandOrange: '#EA714F',
  brandDarkRed: '#D21242',
  brandLightRed: '#F9C7CC',
};

export const brandGradients = {
  brandGradientStart: '{{ brandColors.brandRed }}',
  brandGradientEnd: '{{ brandColors.brandOrange }}',
  brandGradientDark: 'linear-gradient(90deg, #B4334E 0%, #B65A4E 100%)',
  brandGradient: `linear-gradient(90deg, {{ brandGradients.brandGradientStart }} 0%, {{ brandGradients.brandGradientEnd }} 100%)`,
};

export const brandSupport = {
  darkerRed: '#A63B3C',
  darkPurple: '#300C38',
};

export const neutralColors = {
  neutralWhite: '#FFF',
  neutral25: '#FCFAFA', // deprecated since rebranding but still used in some places, so we set it to neutral50
  neutral50: '#FCFAFA',
  neutral100: '#F8F6F6',
  neutral200: '#F3F0F0', // deprecated since rebranding but still used in some places, so we set it to neutral300
  neutral300: '#F3F0F0',
  neutral400: '#DFDCDC',
  neutral500: '#7B7575', // deprecated since rebranding but still used in some places, so we set it to neutral800
  neutral800: '#716A6A',
  neutral900: '#2F2D2D',
  neutralBlack: '#000',
};

// For a hex to percentage opacity conversion, see https://davidwalsh.name/hex-opacity
export const opacityColors = {
  whiteOpacity15: `{{ neutralColors.neutralWhite }}15`,
  whiteOpacity30: `{{ neutralColors.neutralWhite }}30`,
  blackOpacity40: `#00000040`,
  blackOpacity70: `{{ neutralColors.neutral900 }}70`,
};

export const secondaryColors = {
  // Accentgreen
  accentGreen100: '#E3FAEA',
  accentGreen200: '#C0EACA',
  accentGreen300: '#84DC99',
  accentGreen600: '#009B65',
  accentGreen700: '#007250',
  accentGreen800: '#00593F',

  // Blue
  blue50: '#F0FAF8',
  blue100: '#E1F4F1',
  blue300: '#B1DDDF',
  blue500: '#72BDCE',
  blue700: '#3E798D',
  blue900: '#09354B',

  // Bluegray
  blueGray50: '#EBF6F0',
  blueGray100: '#D7EDE1',
  blueGray300: '#BDE0D7',
  blueGray500: '#85BAB0',
  blueGray700: '#557C77',
  blueGray900: '#243D3D',

  // Brown / new color added for rebranding
  brown_100: '#F8F2E1',
  brown_300: '#E7D19E',
  brown_600: '#B77E00',
  brown_700: '#694900',

  // Eneco red
  enecoRed600: '#E5384C',
  enecoRed700: '#D21242',
  enecoRed800: '#BF0639',
  enecoRed900: '#821034',

  // Green
  green50: '#F2F7EC',
  green100: '#E4EFD8',
  green300: '#CDE3BB',
  green500: '#7EC389',
  green700: '#009B65',
  green800: '#2C6F49',
  green900: '#0A4033',

  // Lilac / new color added for rebranding
  lilac_100: '#FDE1FD',
  lilac_300: '#F4B7F5',
  lilac_600: '#A648C8',
  lilac_700: '#6926B2',

  // Moss / new color added for rebranding
  moss_100: '#F1F6CF',
  moss_300: '#D7E19B',
  moss_600: '#809400',
  moss_700: '#586600',

  // Ochre / new color added for rebranding
  ochre_100: '#FCF0CC',
  ochre_300: '#FFD87D',
  ochre_600: '#D76B00',
  ochre_700: '#783E00',

  // Orange
  orange100: '#FFE7DC',
  orange300: '#FFBA8F',
  orange400: '#FF9363',
  orange500: '#EA714F',

  // Pink
  pink50: '#FDEDF1',
  pink100: '#FBDBE3',
  pink300: '#F6B1C6',
  pink500: '#EC6C87',
  pink700: '#C44B6B',
  pink800: '#A04967',
  pink900: '#552748',

  // Purple
  purple50: '#F4F3FA',
  purple100: '#E8E6F4',
  purple300: '#C3C6E5',
  purple500: '#8D8CC6',
  purple700: '#8586CF',
  purple800: '#655790',
  purple900: '#3E235B',

  // Yellow
  yellow50: '#FFFAF0',
  yellow100: '#FEF4E0',
  yellow300: '#FDE8B6',
  yellow500: '#FCCA6D',
  yellow700: '#CE7731',
  yellow900: '#501318',
};

// General decision tokens

export const backgroundColors = {
  backgroundPrimary: '{{ neutralColors.neutralWhite }}',
  backgroundSecondary: '{{ neutralColors.neutral100 }}',
  backgroundTertiary: '{{ neutralColors.neutral200 }}',
  backgroundBrand: '{{ brandGradients.brandGradient }}',
  backgroundScrim: '{{ opacityColors.blackOpacity70 }}',
  backgroundDark: '{{ neutralColors.neutral900 }}',
  backgroundPressed: '{{ neutralColors.neutral100 }}',
};

export const backgroundColoredColors = {
  backgroundVarOne: '{{ secondaryColors.purple100 }}',
  backgroundVarTwo: '{{ secondaryColors.pink100 }}',
  backgroundVarThree: '{{ secondaryColors.blue100 }}',
  backgroundVarFour: '{{ secondaryColors.blueGray100 }}',
  backgroundVarFive: '{{ secondaryColors.green100 }}',
  backgroundVarSix: '{{ secondaryColors.yellow100 }}',
};

export const backgroundSitecoreColors = {
  backgroundCMSVarOne: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarTwo: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarThree: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarFour: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarFive: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarSix: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarSeven: '{{ neutralColors.neutral100 }}',
  backgroundCMSVarEight: '{{ neutralColors.neutral100 }}',
};

export const textColors = {
  textPrimary: '{{ neutralColors.neutral900 }}',
  textInverted: '{{ neutralColors.neutralWhite }}',
  textBrand: '{{ secondaryColors.accentGreen700 }}',
  textOnBackgroundVarOne: '{{ secondaryColors.purple900 }}',
  textOnBackgroundVarTwo: '{{ secondaryColors.pink900 }}',
  textOnBackgroundVarThree: '{{ secondaryColors.blue900 }}',
  textOnBackgroundVarFour: '{{ secondaryColors.blueGray900 }}',
  textOnBackgroundVarFive: '{{ secondaryColors.green900 }}',
  textOnBackgroundVarSix: '{{ secondaryColors.yellow900 }}',
  textLowEmphasis: '{{ neutralColors.neutral800 }}',
  textHighlightVarOne: '{{ secondaryColors.purple700 }}',
  textHighlightVarTwo: '{{ secondaryColors.pink700 }}',
  textHighlightVarThree: '{{ secondaryColors.blue700 }}',
  textHighlightVarFour: '{{ secondaryColors.blueGray700 }}',
  textHighlightVarFive: '{{ secondaryColors.green700 }}',
  textHighlightVarSix: '{{ secondaryColors.yellow700 }}',
};

export const iconColors = {
  iconPrimary: '{{ neutralColors.neutral900 }}',
  iconSecondary: '{{ neutralColors.neutral800 }}',
  iconTertiary: '{{ neutralColors.neutral900 }}',
  iconInverted: '{{ neutralColors.neutralWhite }}',
  iconBrand: '{{ brandColors.brandDarkRed }}',
  iconCooling: '{{ secondaryColors.blue700 }}',
  iconElectricity: '{{ secondaryColors.green700 }}',
  iconGas: '{{ secondaryColors.purple700 }}',
  iconHeat: '{{ secondaryColors.pink700 }}',
  iconReview: '{{ secondaryColors.accentGreen600 }}',
  iconSolar: '{{ secondaryColors.yellow700 }}',
  iconTotal: '{{ secondaryColors.blueGray700 }}',
  iconWater: '{{ secondaryColors.blue700 }}',
  iconOnBackgroundVarOne: '{{ secondaryColors.purple900 }}',
  iconOnBackgroundVarTwo: '{{ secondaryColors.pink900 }}',
  iconOnBackgroundVarThree: '{{ secondaryColors.blue900 }}',
  iconOnBackgroundVarFour: '{{ secondaryColors.blueGray900 }}',
  iconOnBackgroundVarFive: '{{ secondaryColors.green900 }}',
  iconOnBackgroundVarSix: '{{ secondaryColors.yellow900 }}',
  currentColor: 'currentColor',
};

export const borderColors = {
  borderDividerLowEmphasis: '{{ neutralColors.neutral200 }}',
  borderDividerMediumEmphasis: '{{ neutralColors.neutral400 }}',
  borderDividerHighEmphasis: '{{ neutralColors.neutral900 }}',
  borderFocus: '{{ neutralColors.neutral900 }}',
  borderSelected: '{{ secondaryColors.green500 }}',
  outlineHover: '{{ neutralColors.neutral300 }}',
};

export const linkColors = {
  linkBrand: '{{ textColors.textBrand }}',
  linkPrimary: '{{ textColors.textPrimary }}',
  linkSecondary: '{{ textColors.textLowEmphasis }}',
  linkDisabled: '{{ neutralColors.neutral400 }}',
  linkInverted: '{{ textColors.textInverted }}',
};

export const controlColors = {
  controlsActive: '{{ secondaryColors.green700 }}',
  controlsInactive: '{{ neutralColors.neutral200 }}',
  controlsKnob: '{{ neutralColors.neutralWhite }}',
};

export const feedbackColors = {
  feedbackError: '{{ brandColors.brandDarkRed }}',
  feedbackSuccess: '{{ secondaryColors.green700 }}',
  feedbackWarning: '{{ secondaryColors.yellow700 }}',
  feedbackInfo: '{{ secondaryColors.blue700 }}',
  feedbackBackgroundError: '{{ neutralColors.neutral100 }}',
  feedbackBackgroundSuccess: '{{ secondaryColors.green100 }}',
  feedbackBackgroundWarning: '{{ secondaryColors.yellow100 }}',
  feedbackBackgroundInfo: '{{ secondaryColors.blue100 }}',
};

export const formColors = {
  formBorderDefault: '{{ neutralColors.neutral800 }}',
  formBorderError: '{{ brandColors.brandDarkRed }}',
  formBorderHover: '{{ neutralColors.neutral900 }}',
  formErrorMessageBackground: '{{ brandSupport.darkerRed }}',
  formOutlineError: '{{ brandColors.brandLightRed }}',
};

export const graphsColors = {
  graphsTotal: '{{ secondaryColors.blueGray500 }}',
  graphsGasPrimary: '{{ secondaryColors.purple800 }}',
  graphsGasSecondary: '{{ secondaryColors.purple900 }}',
  graphsElectricityPrimary: '{{ secondaryColors.green500 }}',
  graphsElectricitySecondary: '{{ secondaryColors.green800 }}',
  graphsSolarPrimary: '{{ secondaryColors.yellow500 }}',
  graphsSolarSecondary: '{{ secondaryColors.yellow300 }}',
  graphsWarmthPrimary: '{{ secondaryColors.pink800 }}',
  graphsWaterPrimary: '{{ secondaryColors.blue700 }}',
  graphsFixedCosts: '{{ neutralColors.neutral400 }}',
  graphsEstimatedPrimary: '{{ neutralColors.neutral100 }}',
  graphsEstimatedSecondary: '{{ neutralColors.neutral800 }}',
  graphComparePrimary: '{{ secondaryColors.pink800 }}',
  graphCompareSecondary: '{{ secondaryColors.blue900 }}',
  graphCompareStickerPositive: '{{ secondaryColors.green700 }}',
  graphCompareStickerNeutral: '{{ secondaryColors.blue700 }}',
  graphCompareStickerNegative: '{{ secondaryColors.yellow700 }}',
  graphCompareInnerTextColor: '{{ textColors.textInverted }}',
};

export const globalThemeColors = {
  ...neutralColors,
  ...opacityColors,
  ...brandColors,
  ...brandGradients,
  ...backgroundColors,
  ...backgroundColoredColors,
  ...backgroundSitecoreColors,
  ...textColors,
  ...iconColors,
  ...borderColors,
  ...linkColors,
  ...controlColors,
  ...feedbackColors,
  ...formColors,
  ...graphsColors,
};
