import { DefaultBodyType, http, HttpResponse, HttpResponseResolver } from 'msw';

import { apiHostPrefix, apiPrefix } from '../utils';

const mockSmartEnergyOffer: HttpResponseResolver<never, DefaultBodyType, undefined> = ({ params }) => {
  try {
    return HttpResponse.json({
      data: {
        offers: [
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 20,
            numberOfModules: 5,
            hasBatteryFive: 4,
            hasBatteryEight: 0,
            inventorType: 'type 4,6',
            investment: 11238,
            investmentSixPercent: 11912,
            investmentTwentyOnePercent: 13598,
            height: 153,
            width: 85,
            depth: 26,
            yearlySaving: 914,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 502,
            capacityTarifReduction: 162,
            enecoSmartSavings: 250,
            payBackPeriodSixPercent: 14,
            payBackPeriodTwentyOnePercent: 17,
            roiSixPercent: 15226,
            roiTwentyOnePercent: 15226,
          },
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 15,
            numberOfModules: 4,
            hasBatteryFive: 3,
            hasBatteryEight: 0,
            inventorType: 'type 4,6',
            investment: 9120,
            investmentSixPercent: 9667,
            investmentTwentyOnePercent: 11035,
            height: 123,
            width: 85,
            depth: 26,
            yearlySaving: 834,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 459,
            capacityTarifReduction: 150,
            enecoSmartSavings: 225,
            payBackPeriodSixPercent: 12,
            payBackPeriodTwentyOnePercent: 14,
            roiSixPercent: 14500,
            roiTwentyOnePercent: 14500,
          },
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 10,
            numberOfModules: 3,
            hasBatteryFive: 2,
            hasBatteryEight: 0,
            inventorType: 'type 4,6',
            investment: 7002,
            investmentSixPercent: 7422,
            investmentTwentyOnePercent: 8472,
            height: 93,
            width: 85,
            depth: 26,
            yearlySaving: 754,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 415,
            capacityTarifReduction: 139,
            enecoSmartSavings: 200,
            payBackPeriodSixPercent: 10,
            payBackPeriodTwentyOnePercent: 12,
            roiSixPercent: 13876,
            roiTwentyOnePercent: 13876,
          },
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 16,
            numberOfModules: 3,
            hasBatteryFive: 0,
            hasBatteryEight: 2,
            inventorType: 'type 4,6',
            investment: 8919,
            investmentSixPercent: 9454,
            investmentTwentyOnePercent: 10792,
            height: 93,
            width: 85,
            depth: 28,
            yearlySaving: 807,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 459,
            capacityTarifReduction: 148,
            enecoSmartSavings: 200,
            payBackPeriodSixPercent: 12,
            payBackPeriodTwentyOnePercent: 14,
            roiSixPercent: 13284,
            roiTwentyOnePercent: 13284,
          },
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 24,
            numberOfModules: 4,
            hasBatteryFive: 0,
            hasBatteryEight: 3,
            inventorType: 'type 4,6',
            investment: 11995,
            investmentSixPercent: 12715,
            investmentTwentyOnePercent: 14514,
            height: 123,
            width: 85,
            depth: 28,
            yearlySaving: 962,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 535,
            capacityTarifReduction: 177,
            enecoSmartSavings: 250,
            payBackPeriodSixPercent: 14,
            payBackPeriodTwentyOnePercent: 17,
            roiSixPercent: 15374,
            roiTwentyOnePercent: 15374,
          },
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 5,
            numberOfModules: 2,
            hasBatteryFive: 1,
            hasBatteryEight: 0,
            inventorType: 'type 4,6',
            investment: 4884,
            investmentSixPercent: 5177,
            investmentTwentyOnePercent: 5910,
            height: 63,
            width: 85,
            depth: 26,
            yearlySaving: 625,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 359,
            capacityTarifReduction: 116,
            enecoSmartSavings: 150,
            payBackPeriodSixPercent: 8,
            payBackPeriodTwentyOnePercent: 9,
            roiSixPercent: 11572,
            roiTwentyOnePercent: 11572,
          },
          {
            injectedVolume: 3500,
            usageVolume: 4000,
            savedVolume: 8,
            numberOfModules: 2,
            hasBatteryFive: 0,
            hasBatteryEight: 1,
            inventorType: 'type 4,6',
            investment: 5843,
            investmentSixPercent: 6194,
            investmentTwentyOnePercent: 7070,
            height: 63,
            width: 85,
            depth: 28,
            yearlySaving: 700,
            extraSelfUsage: 0,
            selfUsageOptimalisation: 424,
            capacityTarifReduction: 126,
            enecoSmartSavings: 150,
            payBackPeriodSixPercent: 9,
            payBackPeriodTwentyOnePercent: 10,
            roiSixPercent: 13218,
            roiTwentyOnePercent: 13218,
          },
        ],
      },
      errors: null,
      meta: null,
    });
  } catch {
    return new HttpResponse(null, { status: 404 });
  }
};

export default [
  http.post(`${apiPrefix}/smart-energy/offer`, mockSmartEnergyOffer),
  http.post(`${apiHostPrefix}/smart-energy/offer`, mockSmartEnergyOffer),
];
