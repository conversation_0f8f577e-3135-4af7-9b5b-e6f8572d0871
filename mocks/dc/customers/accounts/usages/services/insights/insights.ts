import { http, HttpResponse } from 'msw';

import env from '@common/env';

const host = env('DC_HOST');

const loadCustomerData = async (customerId: string) => {
  try {
    const module = await import(`./response/${customerId}`);
    return module.default;
  } catch {
    try {
      const defaultModule = await import('./response/default');
      return defaultModule.default;
    } catch {
      return { error: 'No mock data available' };
    }
  }
};

export default [
  http.get(
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/accounts/:accountId/usages/services/insights`,
    async ({ params }) => {
      const { customerId } = params;
      if (customerId) {
        const data = await loadCustomerData(customerId as string);
        return HttpResponse.json(data);
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    },
  ),
  http.put(
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/accounts/:accountId/usages/services/insights`,
    ({ params }) => {
      const { customerId } = params;

      if (customerId) {
        return new HttpResponse(null, { status: 201 });
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    },
  ),
  http.delete(
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/accounts/:accountId/usages/services/insights`,
    ({ params }) => {
      const { customerId } = params;

      if (customerId) {
        return new HttpResponse(null, { status: 204 });
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    },
  ),
];
