import appointmentsLeads from './appointmentsLeads/appointmentsLeads';
import averagereviewscore from './averagereviewscore/averagereviewscore';
import bannerClosedAction from './banner/bannerClosedAction/bannerClosedAction';
import getCallToActionBanner from './banner/getCallToActionBanner/getCallToActionBanner';
import advancepayment from './customers/accounts/advancepayment/advancepayment';
import contactpreferences from './customers/accounts/contactpreferences/contactpreferences';
import correspondenceaddress from './customers/accounts/correspondenceaddress';
import dashboard from './customers/accounts/dashboard/dashboard';
import downloadinvoice from './customers/accounts/downloadinvoice/downloadinvoice';
import dynamicPricing from './customers/accounts/dynamic/tarrifs/dynamicpricing';
import dynamicusage from './customers/accounts/dynamic/usage/dynamicusage';
import energyprofile from './customers/accounts/energyprofile/energyprofile';
import externalmandate from './customers/accounts/externalmandate/externalmandate';
import financialoverview from './customers/accounts/financialoverview/financialoverview';
import financialpreferences from './customers/accounts/financialpreferences/financialpreferences';
import invoicesoverview from './customers/accounts/invoicesoverview/invoicesoverview';
import meterstatus from './customers/accounts/meterstatus/meterstatus';
import motivations from './customers/accounts/motivations/motivations';
import paymentarrangement from './customers/accounts/paymentarrangement/paymentarrangement';
import paymentplanbreakdown from './customers/accounts/paymentplanbreakdown';
import payments from './customers/accounts/payments';
import productsForAccounts from './customers/accounts/products';
import reading from './customers/accounts/reading';
import relocations from './customers/accounts/relocations';
import usagecap from './customers/accounts/usagecap/usagecap';
import energyreports from './customers/accounts/usages/energyreports';
import monthsummary from './customers/accounts/usages/monthsummary/monthsummary';
import insights from './customers/accounts/usages/services/insights/insights';
import isma from './customers/accounts/usages/services/isma/isma';
import usages from './customers/accounts/usages/usages';
import activeUsers from './customers/active-users';
import address from './customers/address';
import banks from './customers/banks';
import customerContracts from './customers/contracts/customerDocuments/';
import latestContract from './customers/contracts/latestContract';
import gdpr from './customers/gdpr/gdpr';
import nextBestActionFeedback from './customers/nextbestaction/feedback/feedback';
import desiredadvancepayment from './customers/orders/desiredadvancepayment/desiredadvancepayment';
import status from './customers/orders/status/status';
import trackAndTrace from './customers/orders/trackandtrace';
import products from './customers/products';
import profile from './customers/profile';
import readingsummary from './customers/readingsummary';
import experiments from './experiments/experiments';
import chargeSettings from './hems/chargeSettings/chargeSettings';
import chargeSettingsOverride from './hems/chargeSettingsOverride/chargeSettingsOverride';
import deviceState from './hems/deviceState/deviceState';
import linkDevice from './hems/linkDevice/linkDevice';
import onboardDevice from './hems/onboardDevice/onboardDevice';
import getSessions from './hems/sessionsV2/getSessions';
import sessionsAggregate from './hems/sessionsV2Aggregate/sessionsAggregate';
import getSessionDetailsV2 from './hems/sessionsV2details/getSessionDetailsV2';
import startSchedule from './hems/startSchedule/startSchedule';
import stopSchedule from './hems/stopSchedule/stopSchedule';
import toggleSchedule from './hems/toggleSchedule/toggleSchedule';
import confirmation from './orders/confirmation';
import p1DongleInstallationReminder from './p1Dongle/installationReminder/installationReminder';
import powerNow from './p1DongleUsage/powerNow/powerNow';
import usageFlow from './p1DongleUsage/usageFlow/usageFlow';
import preferences from './preferences/preferences';
import appointmentInformationV2 from './products/appointmentsV2/appointments';
import appointmentTimeslotsV2 from './products/appointmentsV2/timeslots';
import productTypeCombinations from './products/productTypeCombinations/productTypeCombinations';
import publicReading from './reading';
import serviceLocation from './serviceLocation/serviceLocation';
import shopWindow from './shopWindow/shopWindow';
import cancelActiveSubscription from './subscriptions/cancelActiveSubscription/cancelActiveSubscription';
import getAvailableProducts from './subscriptions/getAvailableProducts/getAvailableProducts';
import subscriptionsGetCustomerInfo from './subscriptions/getCustomerInfo/getCustomerInfo';
import getRegisteredProducts from './subscriptions/getRegisteredProducts/getRegisteredProducts';
import lookupSubscriptions from './subscriptions/lookupSubscriptions/lookupSubscriptions';
import registerSubscription from './subscriptions/registerSubscription/registerSubscription';
import updateRegisteredProductStatus from './subscriptions/updateRegisteredProductStatus/updateRegisteredProductStatus';
import validateOrder from './subscriptions/validateOrder/validateOrder';
import createMandate from './userAccounts/createMandate';
import register from './userAccounts/register';
import username from './userAccounts/username';
import basket from '../sitecore/apps/flows/mocks/basket';
import contractExtensionV3 from '../sitecore/apps/flows/mocks/contractExtensionV3';
import productOffer from '../sitecore/apps/flows/mocks/productOffer';
import productOfferV3 from '../sitecore/apps/flows/mocks/productOfferV3';
import productOrder from '../sitecore/apps/flows/mocks/productOrder';
import produtcOrderV2 from '../sitecore/apps/flows/mocks/productOrderV2';
import switchType from '../sitecore/apps/flows/mocks/switchType';

export const handlers = [
  ...paymentplanbreakdown,
  ...subscriptionsGetCustomerInfo,
  ...advancepayment,
  ...averagereviewscore,
  ...banks,
  ...chargeSettings,
  ...chargeSettingsOverride,
  ...contactpreferences,
  ...correspondenceaddress,
  ...desiredadvancepayment,
  ...deviceState,
  ...downloadinvoice,
  ...dashboard,
  ...energyprofile,
  ...energyreports,
  ...externalmandate,
  ...financialoverview,
  ...financialpreferences,
  ...getSessions,
  ...invoicesoverview,
  ...meterstatus,
  ...appointmentInformationV2,
  ...appointmentTimeslotsV2,
  ...experiments,
  ...linkDevice,
  ...lookupSubscriptions,
  ...motivations,
  ...monthsummary,
  ...nextBestActionFeedback,
  ...onboardDevice,
  ...products,
  ...profile,
  ...publicReading,
  ...readingsummary,
  ...reading,
  ...register,
  ...relocations,
  ...status,
  ...address,
  ...activeUsers,
  ...isma,
  ...insights,
  ...gdpr,
  ...usagecap,
  ...usages,
  ...username,
  ...createMandate,
  ...paymentarrangement,
  ...preferences,
  ...productOffer,
  ...productOfferV3,
  ...productTypeCombinations,
  ...basket,
  ...productOrder,
  ...produtcOrderV2,
  ...payments,
  ...productsForAccounts,
  ...startSchedule,
  ...stopSchedule,
  ...switchType,
  ...contractExtensionV3,
  ...shopWindow,
  ...sessionsAggregate,
  ...dynamicPricing,
  ...dynamicusage,
  ...appointmentsLeads,
  ...confirmation,
  ...serviceLocation,
  ...registerSubscription,
  ...trackAndTrace,
  ...cancelActiveSubscription,
  ...toggleSchedule,
  ...getAvailableProducts,
  ...latestContract,
  ...customerContracts,
  ...bannerClosedAction,
  ...getCallToActionBanner,
  ...getRegisteredProducts,
  ...powerNow,
  ...usageFlow,
  ...p1DongleInstallationReminder,
  ...validateOrder,
  ...updateRegisteredProductStatus,
  ...getSessionDetailsV2,
];
