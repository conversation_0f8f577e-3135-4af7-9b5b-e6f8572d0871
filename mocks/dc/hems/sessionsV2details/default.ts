import { ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel } from '@monorepo-types/dc';

export const response: ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel = {
  data: {
    sessionId: '375f8d3c-8fef-44c7-81a8-0bcf8f63f3f5',
    startingStateOfCharge: 27,
    timestamps: [
      '2025-08-01T08:05:00Z',
      '2025-08-01T08:10:00Z',
      '2025-08-01T08:15:00Z',
      '2025-08-01T08:20:00Z',
      '2025-08-01T08:25:00Z',
      '2025-08-01T08:30:00Z',
      '2025-08-01T08:35:00Z',
      '2025-08-01T08:40:00Z',
      '2025-08-01T08:45:00Z',
      '2025-08-01T08:50:00Z',
      '2025-08-01T08:55:00Z',
      '2025-08-01T09:00:00Z',
      '2025-08-01T09:05:00Z',
      '2025-08-01T09:10:00Z',
      '2025-08-01T09:15:00Z',
      '2025-08-01T09:20:00Z',
    ],
    chargeBlockPlanning: [
      0.5833333333333334, 0.5833333333333334, 0.5833333333333334, 0.5833333333333334, 0.5833333333333334,
      0.5833333333333334, 0.5833333333333334, 0.5833333333333334, 0.515333, 0, 0, 0, 0, 0, 0, 0,
    ],
    chargePlanning: [
      0.5833333333333334, 0.5833333333333334, 0.5833333333333334, 0.5833333333333334, 0.5833333333333334,
      0.5833333333333334, 0.5833333333333334, 0.5833333333333334, 0.515333, 0, 0, 0, 0, 0, 0, 0,
    ],
    chargeBlockActual: [
      0.22800000000000006, 0.22800000000000006, 0.22800000000000006, 0.22800000000000006, 0.22800000000000006,
      0.22800000000000006, 0.22800000000000006, 0.22800000000000006, 0.22800000000000006, 0.22800000000000006,
      0.22800000000000006, 0.22800000000000006, 0.22800000000000006, 0.22800000000000006, 0.22800000000000006,
      0.22800000000000006,
    ],
    chargeActual: [
      0.03294117647058803, 0.032941176470588474, 0.032941176470588474, 0.13797647058823514, 0.23919999999999986,
      0.26000000000000023, 0.20800000000000018, 0.25999999999999934, 0.15600000000000058, 0.3120000000000003,
      0.2079999999999993, 0.5200000000000005, 0.20800000000000018, 0.2079999999999993, 0.41600000000000037,
      0.41600000000000037,
    ],
    chargeMode: [
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
      'Smart',
    ],
    customerOfftakePrice: [
      0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929,
      0.25929, 0.25929, 0.25929, 0.25929,
    ],
  },
};
