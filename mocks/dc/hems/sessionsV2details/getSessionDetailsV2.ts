import { http, HttpResponse } from 'msw';

import env from '@common/env';

import { cuResponse } from './cu';
import { response } from './default';
import { hourResponse } from './hour';
import { ptuResponse } from './ptu';

const host = env('DC_HOST');

export default [
  // See the service in `/libs/dc/services/HemsV2Service` to find the endpoint
  http.get(
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/hems/v2/sessions/:deviceId/:sessionId/detailed`,
    ({ params }) => {
      const { customerId, granularity } = params;
      if (customerId) {
        switch (granularity) {
          case 'Cu':
            return HttpResponse.json(cuResponse);
          case 'Ptu':
            return HttpResponse.json(ptuResponse);
          case 'Hour':
            return HttpResponse.json(hourResponse);
          default:
            return HttpResponse.json(response);
        }
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    },
  ),
];
