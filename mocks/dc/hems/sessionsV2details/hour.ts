import { ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel } from '@monorepo-types/dc';

export const hourResponse: ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel = {
  data: {
    sessionId: '375f8d3c-8fef-44c7-81a8-0bcf8f63f3f5',
    startingStateOfCharge: 27,
    timestamps: ['2025-08-01T08:00:00Z', '2025-08-01T09:00:00Z'],
    chargeBlockPlanning: [5.181999666666667, 0],
    chargePlanning: [5.181999666666667, 0],
    chargeBlockActual: [2.508000000000001, 1.1400000000000003],
    chargeActual: [1.88, 1.7680000000000007],
    chargeMode: ['Smart', 'Smart'],
    customerOfftakePrice: [0.25929, 0.25929],
  },
};
