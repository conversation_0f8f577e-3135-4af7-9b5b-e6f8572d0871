import { ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel } from '@monorepo-types/dc';

export const ptuResponse: ResponseDataDC_SAPI_Apigee_HemsSessionsV2_ResponseModels_SessionDetailsResponseModel = {
  data: {
    sessionId: '375f8d3c-8fef-44c7-81a8-0bcf8f63f3f5',
    startingStateOfCharge: 27,
    timestamps: [
      '2025-08-01T08:00:00Z',
      '2025-08-01T08:15:00Z',
      '2025-08-01T08:30:00Z',
      '2025-08-01T08:45:00Z',
      '2025-08-01T09:00:00Z',
      '2025-08-01T09:15:00Z',
    ],
    chargeBlockPlanning: [1.1666666666666667, 1.75, 1.75, 0.515333, 0, 0],
    chargePlanning: [1.1666666666666667, 1.75, 1.75, 0.515333, 0, 0],
    chargeBlockActual: [
      0.4560000000000001, 0.6840000000000002, 0.6840000000000002, 0.6840000000000002, 0.6840000000000002,
      0.4560000000000001,
    ],
    chargeActual: [
      0.0658823529411765, 0.4101176470588235, 0.7279999999999998, 0.6760000000000002, 0.9359999999999999,
      0.8320000000000007,
    ],
    chargeMode: ['Smart', 'Smart', 'Smart', 'Smart', 'Smart', 'Smart'],
    customerOfftakePrice: [0.25929, 0.25929, 0.25929, 0.25929, 0.25929, 0.25929],
  },
};
