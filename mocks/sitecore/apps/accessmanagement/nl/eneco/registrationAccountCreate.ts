const component = (label: string) => ({
  params: { anchor: { value: '' } },
  componentName: 'RegistrationAccountCreate',
  placeholders: {
    'jss-registration-bottom': [
      {
        uid: 'b5710b26-ce86-4902-a43d-b08467306567',
        componentName: 'ContentCard',
        dataSource: '',
        params: {
          setElevation: '1',
          desktopVariant: 'default',
          mobileVariant: 'compact',
        },
        datasourceRequired: false,
        fields: {
          buttonLink: {
            value: {
              href: '',
            },
          },
          cardNumber: {
            value: '',
          },
          content: {
            value: 'Altijd je verbruik bij de hand? Registreer je via de app.',
          },
          image: {
            value: {
              alt: 'meisje met rol isolatiemateriaal',
              fields: {
                default: {
                  value: {
                    src: 'https://www.eneco.nl/-/media/eneco-nl/productlineup/meisje_met_rol_isolatie.jpg?cx=0.59&cy=0.26&cw=650&ch=400&hash=05C42A338AAB5F90708CCBC38C9AA396',
                    height: '400px',
                    width: '650px',
                  },
                },
                extraLarge: {
                  value: {
                    src: 'https://www.eneco.nl/-/media/eneco-nl/text_image/besparingsexpert.jpg?cx=0.55&cy=0.53&cw=1024&ch=576&hash=24877F742806D8D67C6A0919B1F82F38',
                    height: '576px',
                    width: '1024px',
                  },
                },
                large: {
                  value: {
                    src: 'https://www.eneco.nl/-/media/eneco-nl/text_image/besparingsexpert.jpg?cx=0.55&cy=0.53&cw=1024&ch=768&hash=9035A9235B4C919070F204B693E38609',
                    height: '768px',
                    width: '1024px',
                  },
                },
                medium: {
                  value: {
                    src: 'https://www.eneco.nl/-/media/eneco-nl/text_image/besparingsexpert.jpg?cx=0.55&cy=0.53&cw=1024&ch=576&hash=24877F742806D8D67C6A0919B1F82F38',
                    height: '576px',
                    width: '1024px',
                  },
                },
                small: {
                  value: {
                    src: 'https://www.eneco.nl/-/media/eneco-nl/text_image/besparingsexpert.jpg?cx=0.55&cy=0.53&cw=768&ch=576&hash=B10396070F180B3C5CED051327CD769E',
                    height: '576px',
                    width: '768px',
                  },
                },
              },
            },
          },
          link: {
            value: {
              href: '/some-dynamic-link-for-this/',
              text: 'Download de app',
              linktype: 'external',
              url: '/some-dynamic-link-for-this/',
              anchor: '',
              target: '_blank',
            },
          },
          title: {
            value: 'Eneco app',
          },
        },
      },
    ],
  },
  fields: {
    content: {
      content: {
        value: '',
      },
    },
    canRegisterAddress: {
      title: {
        value: 'Account aanmaken',
      },
      addressUnknownText: {
        value: 'Adres niet gevonden. Controleer postcode en huisnummer.',
      },
      backLink: {
        value: {
          href: `https://inloggen.${label}.nl`,
          linktype: 'internal',
          url: '',
          anchor: '',
          target: '',
          text: 'Inloggen',
          class: '',
          title: '',
          queryString: '',
          id: '{c48d2ae2-44f1-4ce7-9fdc-6e893f534b6d}',
        },
      },
      continueButtonText: {
        value: 'Ga verder',
      },
      houseNumberFormField: {
        value: {
          label: 'Huisnummer',
          hint: '',
          placeholder: '123',
          requiredMessage: 'Vul je huisnummer in',
          validationMessage: '',
        },
      },
      houseNumberSuffixFormField: {
        value: {
          label: 'Toevoeging',
          hint: '',
          placeholder: '',
          requiredMessage: '',
          validationMessage: 'Vul een geldige toevoeging in',
        },
      },
      introductionText: {
        value: 'Vul je postcode en huisnummer in. Zo kunnen we de juiste informatie aan je account koppelen.',
      },
      isLoadingAddressText: {
        value: 'Bezig met het ophalen van adres.',
      },
      modalDialog: {
        value: {
          title: `${label}-account aanmaken`,
          content: `<p>Heb je een actief energiecontract bij ${label}? Dan kan je met je postcode, huisnummer en klantnummer eenvoudig een account aanmaken voor Mijn ${label}. In sommige gevallen kun je (nog) geen gebruikmaken van Mijn ${label}. Wanneer niet?</p><ul><li>Als je alleen bronwarmte of warmte via een eco-project of via EKV hebt.</li><li>Als je een buitenlands post-/factuuradres of vakantiewoning hebt.</li><li>Als je langer dan een jaar geen klant meer bent.</li><li>Je onderneming een aanvullende omschrijving bevat.</li></ul><p>Heb je nog geen contract bij ${label} en wil je graag klant worden? Dat kan natuurlijk! We vertellen je graag meer over ons aanbod.</p>`,
          triggerText: 'Kan ik een account aanmaken?',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      postalCodeFormField: {
        value: {
          label: 'Postcode',
          hint: '',
          placeholder: '1111AA',
          requiredMessage: 'Vul een postcode in',
          validationMessage: 'Vul een geldige postcode in',
        },
      },
    },
    canRegisterCustomerID: {
      title: {
        value: 'Account aanmaken',
      },
      backLinkText: {
        value: 'Adres',
      },
      continueButtonText: {
        value: 'Ga verder',
      },
      customerIdFormField: {
        value: {
          label: 'Klantnummer',
          hint: '',
          placeholder: '',
          requiredMessage: 'Vul je klantnummer in.',
          validationMessage: 'Je klantnummer bestaat uit 2 tot 10 cijfers.',
        },
      },
      errorNotificationField: {
        value: {
          variant: 'error',
          title: '',
          content: `De combinatie tussen je adres en klantnummer herkennen we niet. Controleer je gegevens. Kom je er niet uit? We helpen je graag verder via onze <a href='https://www.${label}.nl/klantenservice'>Klantenservice.</a>`,
        },
      },
      introductionText: {
        value: `Vul je klantnummer in. Je vindt je klantnummer in e-mails van ${label}, in de transactieomschrijving op je bankafschrift en bovenaan je jaarnota.`,
      },
      modalDialog: {
        value: {
          title: 'Hier vind je jouw klantnummer',
          content: '',
          triggerText: 'Waar vind ik mijn klantnummer?',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      inEmailModalDescription: {
        value: `<p><strong>In e-mails</strong></p><p>In de e-mails die je ontvangt van ${label}, kun je bovenin je klantnummer terugvinden.</p>`,
      },
      onBankStatementModalDescription: {
        value: `<p><strong>Op je bankafschrift</strong></p><p>Je klantnummer staat in de omschrijving van je bankafschrift van ${label}.</p>`,
      },
      onYearnoteModalDescription: {
        value: `<p><strong>Op je jaarnota</strong></p><p>Je vindt jouw klantnummer aan de rechterkant van je jaarnota, boven de inhoud van de brief.</p>`,
      },
      inEmailModalLabelText: {
        value: 'Voorbeeld waar je je klantnummer vindt in onze e-mails',
      },
      onBankStatementModalLabelText: {
        value: 'Voorbeeld waar je je klantnummer vindt in op je bankafschrift',
      },
      onYearnoteModalLabelText: {
        value: 'Voorbeeld waar je je klantnummer vindt op je jaarnota',
      },
    },
    registerOrgEmail: {
      title: {
        value: 'Account aanmaken',
      },
      backLinkText: {
        value: 'Klantnummer',
      },
      confirmButtonText: {
        value: 'Bevestigen',
      },
      emailFormField: {
        value: {
          label: 'E-mailadres',
          hint: '',
          placeholder: '<EMAIL>',
          requiredMessage: 'Vul een e-mailadres in.',
          validationMessage: 'Vul een geldig e-mailadres in.',
        },
      },
      emailOverrideCloseLinkText: {
        value: 'Extra e-mailadres verwijderen',
      },
      emailOverrideLinkText: {
        value: 'Ander e-mailadres voor inlogcode',
      },
      introductionDescription: {
        value: `<p>Vul het e-mailadres in waarmee je wilt inloggen in Mijn ${label} en de ${label} app.</p><p>Je ontvangt op dit e-mailadres de inlogcode. Je kan ervoor kiezen om de code op een ander e-mailadres te ontvangen.</p>`,
      },
      modalDialog: {
        value: {
          title: 'Inloggen met een inlogcode',
          content: `<p>Tijdens het inloggen op een Mijn ${label}-account sturen we een code naar je e-mailadres. De code is eenmalig te gebruiken en tot 15 minuten geldig. Het is daarom belangrijk dat je een e-mailadres invult waar je toegang tot hebt tijdens het inloggen.</p><p>Heb je meerdere accounts bij Mijn ${label}? Dan is het belangrijk dat je voor allemaal een verschillend e-mailadres gebruikt om in te loggen.</p><p>De inlogcodes kan je daarentegen wel allemaal op een zelfde e-mailadres ontvangen. Bijvoorbeeld op het e-mailadres dat je dagelijks gebruikt. Wil je dit? Klik dan op 'ander e-mailadres' om het in te stellen.</p>`,
          triggerText: 'Hoe werkt een inlogcode?',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      overrideDescription: {
        value: `<p>Vul de e-mailadressen in waarmee je wilt inloggen in Mijn ${label} en de ${label} app en waarop je de inlogcode wilt ontvangen.</p></p>`,
      },
      overrideEmailForLoginCodeHintLabel: {
        value: 'Op dit e-mailadres ontvang je de inlogcode.',
      },
      overrideEmailForLoginCodeLabel: {
        value: 'E-mailadres (inlogcode)',
      },
      overrideEmailForLoginCodeModalDialog: {
        value: {
          title: 'Een extra e-mailadres',
          content: `<p>Tijdens het inloggen sturen we een code naar je e-mailadres. De code is eenmalig te gebruiken en tot 15 minuten geldig. Het is daarom belangrijk dat je een e-mailadres invult waar je toegang tot hebt tijdens het inloggen.</><p>Heb je meerdere accounts bij Mijn ${label}? Dan is het belangrijk dat je voor allemaal een verschillend e-mailadres gebruikt om in te loggen (gebruikersnaam).</><p>De inlogcodes kan je daarentegen wel allemaal op een zelfde e-mailadres ontvangen. Bijvoorbeeld op het e-mailadres dat je dagelijks gebruikt. Wil je toch liever hetzelfde e-mailadres gebruiken als je gebruikersnaam? Klik dan op 'extra e-mailadres verwijderen'.</p>`,
          triggerText: '',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      overrideEmailForUsernameHintLabel: {
        value: 'Dit e-mailadres gebruik je om in te loggen.',
      },
      overrideEmailForUsernameLabel: {
        value: 'E-mailadres (gebruikersnaam)',
      },
      technicalErrorNotificationField: {
        value: {
          variant: 'error',
          title: 'Er gaat iets niet goed',
          content: 'Oeps, we hebben even last van een technische storing. Probeer het opnieuw.',
        },
      },
    },
    registerOrgPassword: {
      title: {
        value: 'Account aanmaken',
      },
      confirmButtonText: {
        value: 'Bevestigen',
      },
      introductionText: {
        value: `Kies een wachtwoord waarmee je straks kan inloggen in Mijn ${label} en de ${label} app.`,
      },
      passwordFormField: {
        value: {
          label: 'Wachtwoord',
          hint: 'Een wachtwoord bestaat uit minimaal 8 karakters, waarvan minimaal 1 hoofdletter, 1 kleine letter en 1 cijfer.',
          placeholder: '',
          requiredMessage: 'Vul een wachtwoord in.',
          validationMessage:
            'Vul een wachtwoord in dat bestaat uit minstens 8 karakters, 1 hoofdletter, 1 kleine letter en 1 cijfer.',
        },
      },
    },
    registerPerson: {
      title: {
        value: 'Account aanmaken',
      },
      backLinkText: {
        value: 'Klantnummer',
      },
      continueButtonText: {
        value: 'Bevestigen',
      },
      introductionText: {
        value: `Met dit e-mailadres en wachtwoord kun je straks inloggen in Mijn ${label} en de ${label} app. Je ontvangt op dit e-mailadres ook de code waarmee je kunt inloggen.`,
      },
      modalDialog: {
        value: {
          title: 'Inloggen met een inlogcode',
          content:
            '<p>Om je account beter te beveiligen, sturen we tijdens het inloggen een code naar je e-mailadres. Het is daarom belangrijk dat je een e-mailadres invult waar je toegang tot hebt tijdens het inloggen. De code is eenmalig te gebruiken en tot 15 minuten geldig.</p>',
          triggerText: 'Hoe werkt een inlogcode?',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      technicalErrorNotificationField: {
        value: {
          variant: 'error',
          title: 'Er gaat iets niet goed',
          content: 'Oeps, we hebben even last van een technische storing. Probeer het opnieuw.',
        },
      },
      conflictErrorNotification: {
        value: {
          variant: 'error',
          title: 'E-mailadres niet geldig',
          content: 'Controleer of je e-mailadres juist is en probeer het opnieuw.',
        },
      },
      emailFormField: {
        value: {
          label: 'E-mailadres',
          hint: '',
          placeholder: '<EMAIL>',
          requiredMessage: 'Vul een e-mailadres in.',
          validationMessage: 'Vul een geldig e-mailadres in.',
        },
      },
      passwordFormField: {
        value: {
          label: 'Wachtwoord',
          hint: 'Een wachtwoord bestaat uit minimaal 8 karakters, waarvan minimaal 1 hoofdletter, 1 kleine letter en 1 cijfer.',
          placeholder: '',
          requiredMessage: 'Vul een wachtwoord in.',
          validationMessage:
            'Vul een wachtwoord in dat bestaat uit minstens 8 karakters, 1 hoofdletter, 1 kleine letter en 1 cijfer.',
        },
      },
    },
    registerSuccess: {
      title: {
        value: 'Je account is aangemaakt',
      },
      introductionText: {
        value: `We sturen je een bevestiging hiervan via de mail. Je kunt direct bij ${label} inloggen met je e-mailadres en wachtwoord. De code die je nodig hebt om in te loggen, ontvang je ook op je e-mailadres.`,
      },
      link: {
        value: {
          href: '/',
          linktype: 'internal',
          url: '',
          anchor: '',
          target: '',
          text: 'Naar inloggen',
          class: '',
          title: '',
          queryString: '',
          id: '{CCBF0B23-E83A-43CA-8725-F979F710E06F}',
        },
      },
    },
  },
});

export default component;
