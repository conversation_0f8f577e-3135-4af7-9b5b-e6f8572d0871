import navigationHeader from './navigation';

const mockupData = [
  navigationHeader,
  {
    uid: '91346eeb-8888-434e-82f8-0feca05bca65',
    componentName: 'OfferStep',
    dataSource: '{0A31DC53-E75F-48D9-9A82-A140A665377E}',
    params: {},
    fields: {
      content: {
        text: {
          value: '',
        },
        nextStepText: {
          value: 'Volgende',
        },
        notification: {
          value: {
            variant: 'info',
            title: '',
            content: '',
          },
        },
        contractTypeTitle: {
          value: 'Kies je type leveringstarief',
        },
        title: {
          value: 'Welke contract kies je?',
        },
        noOfferSelectedNotification: {
          value: {
            variant: 'info',
            title: 'Geen aanbod geselecteerd',
            content: 'Selecteer een aabod',
          },
        },

        moreInfoDialog: {
          value: {
            title: 'Welk contract past bij mij?',
            content:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque tempus ultricies justo vel elementum. Duis at dui feugiat nulla fringilla venenatis et id diam. Duis sed ultrices purus. Sed eleifend dignissim ornare. Suspendisse a nunc enim. Praesent sollicitudin euismod ante, luctus efficitur orci consectetur dignissim. Cras rutrum augue nisi, ut placerat elit tristique dignissim. Integer id consectetur nulla. Donec pulvinar risus eu erat auctor, id dapibus ante dignissim. Mauris aliquam elit at nisi efficitur aliquam eget a mi. Suspendisse a est arcu. Donec tempus, nisl ac pellentesque faucibus, orci nisi maximus magna, in tempor neque erat a dui. In pellentesque mollis sapien nec pretium. Phasellus et nisi vitae mauris consequat tristique in a tortor. Ut suscipit sapien vel nunc convallis scelerisque.',
            triggerText: 'Welk contract past bij mij?',
            submitButtonText: '',
            cancelButtonText: '',
          },
        },
        timeOfUseInfoDialog: {
          value: {
            title: 'Eneco VoordeelMomenten',
            content:
              '<p>Profiteer elke dag van twee vaste kortingsmomenten en bespaar op je energierekening!&nbsp;</p>\n<p><img height="813" alt="30% korting in voordeelmomenten" width="1600" src="https://cdn-assets-eu.frontify.com/s3/frontify-enterprise-files-eu/eyJvYXV0aCI6eyJjbGllbnRfaWQiOiJzaXRlY29yZSJ9LCJwYXRoIjoiZW5lY29cL2ZpbGVcL2dXY0FZQUJjU2N4bmNSNXFlcDkzLnBuZyJ9:eneco:iSSyhRC34zDp-uukF7L3mnVfZcWhK5bdhHaWK3N1_G0?width=2000" style="left: 968.867px; top: 92.2422px; width: 466.984px; height: 190.359px;" /></p>\n<p><strong>Een vast tarief met dagelijkse korting</strong><br />\nBen jij klaar om slimmer met energie om te gaan en je kosten te verlagen? Ontdek Eneco Voordeelmomenten! Door je wasmachine aan te zetten of je elektrische auto op te laden tussen 10 &ndash; 17 uur en tussen 22 &ndash; 5 uur, krijg je automatisch 30% korting op de stroom die je tijdens deze voordeelmomenten gebruikt.&nbsp;<br />\n<br />\n</p>\n<br />',
            triggerText: 'meer info',
            submitButtonText: '',
            cancelButtonText: '',
          },
        },
        timeOfUseInfoDialogUspList: {
          value: {
            enum: [
              {
                name: 'usp_1',
                value: 'usp_1',
                label: 'Je hebt een een contract met een vaste looptijd en vaste leveringstarieven voor een jaar.',
              },
              {
                name: 'usp_2',
                value: 'usp_2',
                label:
                  'Elke dag heb je twee vaste voordeelmomenten waarin je 30% korting krijgt op het standaardtarief. ',
              },
              {
                name: 'usp_3',
                value: 'usp_3',
                label: 'De voordeelmomenten zijn elke dag tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur ',
              },
            ],
          },
        },
      },
      costDetails: {
        fixedCostsLabel: {
          value: 'Vaste kosten',
        },
        totalCostsLabel: {
          value: 'Totaal',
        },
        title: {
          value: 'Prijsopbouw',
        },
        variableCostsLabel: {
          value: 'Variabele kosten',
        },
        perMonthLabel: {
          value: 'per maand',
        },
        text: {
          value: '',
        },
        perYearLabel: {
          value: 'per jaar',
        },
        totalCostsFirstYearLabel: {
          value: 'Eerste jaar',
        },
        dynamicPricingNotification: {
          value: {
            variant: 'info',
            title: 'Variabele looptijd Dynamic',
            content:
              'De leveringskosten zijn de verwachte leveringskosten en zijn inclusief de inkoopvergoeding van € 0,02232 per kWh en € 0,07225 per m3.',
          },
        },
        variableProductNotification: {
          value: {
            variant: 'info',
            title: 'Overweging bij een variabel contract',
            content:
              'Bij een variabel contract kunnen je tarieven meerdere keren per jaar stijgen of dalen. Dat betekent dat de prijs soms heel hoog kan worden, zoals tijdens de energiecrisis. Dit kan grote financiële gevolgen hebben. Als je tarieven wijzigen, laten we je dit minstens 30 dagen van tevoren weten. Je kunt altijd kosteloos je contract opzeggen. Ook als je het niet eens bent met de hoogte van je nieuwe tarieven.',
          },
        },
        hybridNotification: {
          value: {
            variant: 'info',
            title: 'Variabele looptijd Hybrid',
            content:
              'De leveringskosten zijn de verwachte leveringskosten en zijn inclusief de inkoopvergoeding van € 0,02232 per kWh en € 0,07225 per m3.',
          },
        },
        usageLabel: {
          value: 'Verbruik',
        },
        monthlyTermLabel: {
          value: 'Maandbedrag',
        },
        yearlyTermLabel: {
          value: 'Jaarbedrag',
        },
      },
      costDetailsExplanation: {
        monthlyFeesHybridParagraph: {
          value: {
            title: 'Maandbedrag en actuele tarieven',
            content:
              '<p>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re. In dit prijsoverzicht staan altijd:</p>\n<ul>\n    <li> de actuele verwachte tarieven voor stroom. De getoonde leveringstarieven voor het dynamische stroomcontract gelden voor de situatie in je woning op basis van het aantal (actieve) telwerken. De tarieven voor elektriciteit vari&euml;ren per uur. Je vindt de actuele tarieven een dag van tevoren (rond 15.00 uur) terug in de Eneco app.&nbsp;</li>\n</ul>\n<ul>\n    <li>de actuele tarieven voor het vaste contract voor gas. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd</li>\n</ul>\n<br />\n<p><strong>Verwachte leveringskosten</strong></p>\n<p><span style="background-color: #ffffff; color: #000000;">De leveringskosten zijn de verwachte leveringskosten en zijn inclusief de inkoopvergoeding van &euro; 0,02923 per kWh tot en met 31 juli 2025 (&euro; 0,02398 vanaf 1 augustus 2025) voor stroom. </span></p>',
          },
        },
        redeliveryHybridParagraph: {
          value: {
            title: 'Teruglevering',
            content:
              'Als je zelf stroom opwekt - en dit niet direct zelf gebruikt - lever je deze opgewekte stroom terug. Dit kan alleen als je aangemeld bent op&nbsp;<a rel="noopener noreferrer" href="https://energieleveren.nl/" target="_blank">energieleveren.nl</a>. Je ontvangt voor teruggeleverde stroom het werkelijke uurtarief per kWh op het moment van terugleveren, inclusief de inkoopvergoeding en overheidsheffingen en btw. Lever je over het gehele verbruiksjaar, waar de (jaar)nota op betrekking heeft, meer terug dan dat je verbruikt, dan ontvang je, in afwijking van artikel 3 van het Voorwaardenoverzicht, voor het netto teruggeleverde volume de marktprijs per kWh, die in het uur van teruglevering geldt (exclusief de inkoopvergoeding en overheidsheffingen en btw) min de verkoopvergoeding (<span style="background-color: #ffffff; color: #000000;">&euro; 0,02923 per kWh tot en met 31 juli 2025 (&euro; 0,02398 vanaf 1 augustus 2025).</span>&nbsp;De verkoopvergoeding is ter dekking van de kosten die Eneco maakt voor de verkoop en afhandeling van stroom op de energiemarkt en kan wijzigen, waardoor de vergoeding voor netto teruggeleverde stroom kan fluctueren.',
          },
        },
        monthlyFeesDynamicPricingProductParagraph: {
          value: {
            title: 'Maandbedrag en actuele tarieven',
            content:
              '<p>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. De getoonde leveringstarieven gelden voor de situatie in je woning op basis van het aantal (actieve) telwerken. De tarieven voor elektriciteit vari&euml;ren per uur. Je vindt de actuele tarieven een dag van tevoren (rond 15.00 uur) terug in de Eneco app. In dit prijsoverzicht staan altijd de actuele verwachte tarieven.&nbsp;<span style="background: white; line-height: 107%; color: black;">Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re.</span></p>\n<br />\n<p><span style="background: white; line-height: 107%; color: black;"><strong>Verwachte leveringskosten</strong></span></p>\n<p><span style="background: white; line-height: 107%; color: black;">De leveringskosten zijn de verwachte leveringskosten en zijn inclusief de inkoopvergoeding van <span style="background-color: #ffffff; color: #000000;"><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">&euro; 0,</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">02923</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> per kWh tot en met 31 juli 2025 </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">(</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">&euro; 0,</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">02398</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> vanaf 1 augustus 2025)</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> vo</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">o</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">r</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">s</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">t</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">r</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">o</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">o</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">m</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">.</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> Heb je ook g</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">as</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">? De Inkoopvergoeding daarvoor </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">i</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">s</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">&euro; 0,08246 per m3 tot en met 31 juli 2025, &euro; 0,08289 vanaf 1 augustus 2025.</span></span></span></span></p>',
          },
        },
        monthlyFeesVariablePricingProductParagraph: {
          value: {
            title: 'Maandbedrag en actuele tarieven',
            content:
              '<p>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. De getoonde leveringstarieven gelden voor de situatie in je woning op basis van het aantal (actieve) telwerken. De tarieven voor elektriciteit vari&euml;ren per uur. Je vindt de actuele tarieven een dag van tevoren (rond 15.00 uur) terug in de Eneco app. In dit prijsoverzicht staan altijd de actuele verwachte tarieven.&nbsp;<span style="background: white; line-height: 107%; color: black;">Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re.</span></p>',
          },
        },
        contractEndDateParagraph: {
          value: {
            title: 'Verwachte einddatum',
            content:
              'De verwachte einddatum contract is een indicatie gebaseerd op de gemiddelde doorlooptijd van een aanmelding en de gekozen looptijd.',
          },
        },
        currentRatesParagraph: {
          value: {
            title: '',
            content: '',
          },
        },
        deliveryCostsParagraph: {
          value: {
            title: 'Netbeheerkosten',
            content:
              'Netbeheerkosten zijn de kosten voor je aansluiting en voor het transport door de netbeheerder. Het bedrag wordt door ons namens de netbeheerder bij je in rekening gebracht.',
          },
        },
        explanationTitle: {
          value: 'Toelichting prijsopbouw',
        },
        fixedCostsParagraph: {
          value: {
            title: 'Vaste leveringskosten',
            content:
              'Vaste leveringskosten zijn kosten waarbij het niet uitmaakt hoeveel energie je verbruikt. Eerder heette dit vastrecht.&nbsp;',
          },
        },
        governmentAndLeviesParagraph: {
          value: {
            title: 'Overheidsheffingen en belastingen',
            content:
              'Met overheidsheffingen bedoelen we de energiebelasting. De leveringstarieven van stroom en gas zijn inclusief energiebelasting. De overheid heft per kWh stroom en m&sup3; gas energiebelasting om verbruikers te stimuleren zuinig en bewust met energie om te gaan.<br />\n&nbsp;<br />\nOver de leveringstarieven, overheidsheffingen en energiebelasting, en over de netbeheerkosten, betaal je btw. De energiebelasting en btw dragen wij af aan de Belastingdienst.&nbsp;',
          },
        },
        governmentLeviesParagraph: {
          value: {
            title: '',
            content: '',
          },
        },
        monthlyFeesParagraph: {
          value: {
            title: 'Maandbedrag en actuele tarieven',
            content:
              'In dit prijsoverzicht staan altijd de actuele tarieven. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd. Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. Omdat daarvoor het historisch verbruik van je woning wordt gebruikt. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re.',
          },
        },
        priceDifferencesParagraph: {
          value: {
            title: 'Prijsverschillen',
            content:
              'Bij het berekenen van je kosten ronden wij de tarieven af. Soms ontstaan hierdoor kleine afrondingsverschillen op het prijsoverzicht en de energienota.',
          },
        },
        residenceFunctionParagraph: {
          value: {
            title: 'Verblijfsfunctie',
            content:
              'Alleen als er sprake is van een woon-, werk- of andere verblijfsfunctie, heb je recht op vermindering energiebelasting. Dit is een door de overheid vastgesteld kortingbedrag op de energiebelasting.',
          },
        },
        variableCostsParagraph: {
          value: {
            title: 'Variabele kosten',
            content:
              'De getoonde variabele kosten zijn op basis van de door jou opgegeven hoeveelheid verbruik van kWh / m&sup3; / GJ.',
          },
        },
        redeliveryParagraph: {
          value: {
            title: 'Lever je terug uit zonnepanelen?',
            content:
              '<p><strong>Terugleverkosten&nbsp;</strong><br />\nAls je stroom opwekt, verbruiken je apparaten in huis ook meteen een deel van deze opgewekte stroom. De stroom die je zelf niet verbruikt, lever je terug aan het elektriciteitsnet. Hierover betaal je per kWh terugleverkosten (exclusief overheidsheffingen en inclusief btw).&nbsp;&nbsp;<br />\n&nbsp;<br />\n<strong>Salderen&nbsp;&nbsp;</strong><br />\nOp je jaarnota verminderen we je jaarverbruik met wat je teruglevert. Dat heet &lsquo;salderen&rsquo;. Heb je een slimme meter? Dan trekken we de door jou teruggeleverde stroom eerst af van je normaal- en daarna van je dalverbruik. Salderen kan alleen als je je zonnepanelen op energieleveren.nl hebt aangemeld.&nbsp;De salderingsregeling wordt per 1 januari 2027 afgeschaft. Vanaf dan kun je de stroom die je teruglevert met zonnepanelen niet meer verrekenen (salderen) met de stroom die je afneemt.&nbsp;<br />\n&nbsp;<br />\n<strong>Terugleververgoeding&nbsp;&nbsp;</strong><br />\nLever je meer stroom terug dan je totale stroomverbruik? Dan ontvang je een terugleververgoeding per kWh (exclusief overheidsheffingen en btw). De terugleververgoeding zie je op je jaarnota.&nbsp;<br />\n<br />\n<span style="color: #2f2d2d;">Bij een contract met vaste looptijd van 3 jaar gebruiken we twee tariefperiodes voor terugleverkosten en terugleververgoeding. Dit is vanwege de afschaffing van de salderingsregeling. De eerste periode begint bij de start van je contract en loopt tot 1 januari 2027. De tweede periode begint op 1 januari 2027 en loopt tot de vaste looptijd van je contract voorbij is.&nbsp;<span data-teams="true">De terugleverkosten en -vergoeding wijzigen in je contract op 1 januari 2027. Verder veranderen deze tarieven niet tijdens de vaste looptijd van het contract. De andere voorwaarden van het contract blijven gedurende de hele looptijd van het contract gelijk.</span></span></p>',
          },
        },
        redeliveryDynamicPricingParagraph: {
          value: {
            title: 'Teruglevering',
            content:
              'Als je zelf stroom opwekt - en dit niet direct zelf gebruikt - lever je deze opgewekte stroom terug. Dit kan alleen als je aangemeld bent op <a rel="noopener noreferrer" href="https://energieleveren.nl" target="_blank">energieleveren.nl</a>. Je ontvangt voor teruggeleverde stroom het werkelijke uurtarief per kWh op het moment van terugleveren, inclusief de inkoopvergoeding en overheidsheffingen en btw. Lever je over het gehele verbruiksjaar, waar de (jaar)nota op betrekking heeft, meer terug dan dat je verbruikt, dan ontvang je, in afwijking van artikel 3 van het Voorwaardenoverzicht, voor het netto teruggeleverde volume de marktprijs per kWh, die in het uur van teruglevering geldt (exclusief de inkoopvergoeding en overheidsheffingen en btw) min de verkoopvergoeding (<span style="background-color: #ffffff; color: #000000;">&euro; 0,02923 per kWh tot en met 31 juli 2025 ,&euro; 0,02398 vanaf 1 augustus 2025)</span>. De verkoopvergoeding is ter dekking van de kosten die Eneco maakt voor de verkoop en afhandeling van stroom op de energiemarkt en kan wijzigen, waardoor de vergoeding voor netto teruggeleverde stroom kan fluctueren.',
          },
        },
        lowHighTariffDifferenceParagraph: {
          value: {
            title: 'Normaal- en daltarief',
            content:
              'We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat normaal- en daltarieven steeds dichter bij elkaar liggen of gelijk zijn.',
          },
        },
        timeOfUseLowHighTariffDifferenceParagraph: {
          value: {
            title: 'Standaard- en voordeeltarieven',
            content:
              'We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat standaard- en voordeeltarieven steeds dichter bij elkaar liggen of gelijk zijn.',
          },
        },
      },
      electricityCostDetails: {
        lowTarifflLabel: {
          value: 'Leveringskosten (daltarief)',
        },
        redeliveryLabel: {
          value: 'Teruglevering',
        },
        taxDiscountLabel: {
          value: 'Verminderde energiebelasting',
        },
        redeliveryMoreNotification: {
          value: {
            variant: 'info',
            title: 'Je levert meer terug dan je verbruikt',
            content:
              '<p>Je totale stroomverbruik ({electricityTotal} kWh) is verminderd met de teruggeleverde stroom van je zonnepanelen ({electricityRedelivery} kWh). Dat heet salderen.&nbsp;</p>\n<p>We verrekenen dit eerst met je stroomverbruik tegen normaaltarief, en daarna tegen het dal tarief.</p>',
          },
        },
        tariffLabel: {
          value: 'Leveringskosten (normaaltarief)',
        },
        redeliveryLessNotification: {
          value: {
            variant: 'info',
            title: 'Je verbruikt meer dan dat je teruglevert',
            content:
              '<p>Je totale stroomverbruik ({electricityTotal} kWh) is verminderd met de teruggeleverde stroom van je zonnepanelen ({electricityRedelivery} kWh). Dat heet salderen.&nbsp;</p>\n<p>We verrekenen dit eerst met je stroomverbruik tegen normaaltarief, en daarna tegen het dal tarief.</p>',
          },
        },
        deliveryCostsLabel: {
          value: 'Netbeheerkosten (3 x 25 Ampère aansluiting)',
        },
        redeliveryTariffLabel: {
          value: 'Tarief voor je opwek na saldering (geen btw)',
        },
        redeliveryCostLabel: {
          value: 'Terugleverkosten',
        },
        redeliveryDynamicPricingProductTariffLabel: {
          value: 'Tarief je dat betaalt voor verhandeling van netto teruglevervolume op de energiemarkt',
        },
        energyTaxLabel: {
          value: 'Energiebelasting',
        },
        tariffDynamicPricingProductDeliveryCostsLabel: {
          value: 'Verwachte gemiddelde tarief excl. inkoopvergoeding',
        },
        tariffDeliveryCostsLabel: {
          value: 'Leveringskosten',
        },
        storageSustainableEnergyLabel: {
          value: 'Opslag duurzame energie',
        },
        standingChargeLabel: {
          value: 'Vaste leveringskosten',
        },
        governmentLeviesLabel: {
          value: 'Overheidsheffingen',
        },
        taxBracket1Label: {
          value: 'per jaar',
        },
        taxBracket2Label: {
          value: '2.901 - 10.000 per jaar',
        },
        taxBracket3Label: {
          value: 'Meer dan 10.000 per jaar',
        },
      },
      gasCostDetails: {
        storageSustainableEnergyLabel: {
          value: 'Opslag duurzame energie',
        },
        tariffLabel: {
          value: 'Leveringskosten',
        },
        energyTaxLabel: {
          value: 'Energiebelasting',
        },
        standingChargeLabel: {
          value: 'Leveringskosten',
        },
        deliveryCostsLabel: {
          value: 'Netbeheerkosten',
        },
        governmentLeviesLabel: {
          value: 'Overheidsheffingen',
        },
        taxBracket1Label: {
          value: 'per jaar',
        },
        taxBracket2Label: {
          value: '1.001 - 170.000 per jaar',
        },
      },
      warmthCostDetails: {
        tariffWarmWaterLabel: {
          value: 'Verbruik warmte voor warm water',
        },
        variableCostsWaterLabel: {
          value: 'Variabele kosten water',
        },
        deliveryCostsLabel: {
          value: 'Meettarief',
        },
        tariffColdWaterLabel: {
          value: 'Verbruik koud water t.b.v. warm water',
        },
        standingChargeLabel: {
          value: 'Vastrecht',
        },
        taxColdWaterLabel: {
          value: 'Belasting op leidingwater',
        },
        tariffLabel: {
          value: 'Verbruik warmte',
        },
        rentWarmthSet: {
          value: 'Huur warmteafleverset',
        },
        variableCostsWarmthLabel: {
          value: 'Variabele kosten warmte',
        },
        standingChargeGeothermalHeatingLabel: {
          value: 'Vastrecht bronwarmte (basistarief voor 3 kW)',
        },
        geothermalHeatingPompCostsLabel: {
          value: 'Huur warmtepomp',
        },
        variableCostsGeothermalHeatingLabel: {
          value: 'Opslag vastrecht bronwarmte (per kW boven 3 kW)',
        },
      },
      footer: {
        footerDescription: {
          value: 'Aanbod voor <strong>{address}</strong> op basis van',
        },
        redeliveryLabel: {
          value: 'Teruglevering',
        },
        gasLabel: {
          value: 'Gas',
        },
        waterLabel: {
          value: 'Water',
        },
        warmthLabel: {
          value: 'Warmte',
        },
        electricityLabel: {
          value: 'Stroom',
        },
        electricityLowLabel: {
          value: 'Dal',
        },
        electricityHighLabel: {
          value: 'Normaal',
        },
        modifyTriggerText: {
          value: 'Mijn verbruik aanpassen',
        },
      },
      genericError: {
        errorNotAvailableNotification: {
          value: {
            variant: 'error',
            title: 'Bel ons om een contract af te sluiten',
            content:
              'Op dit moment kun je bij Eneco geen contract afsluiten. Dringend stroom en/of gas nodig? Sluit dan telefonisch ons modelcontract af. Vanwege de onzekere energiemarkt wijzigt ons aanbod wekelijks. Houd voor het actuele aanbod onze website in de gaten, of lees meer over onze&nbsp;<a href="https://www.eneco.nl/duurzame-energie/energieprijzen/">energieprijzen</a>.',
          },
        },
        errorNotFoundNotification: {
          value: {
            variant: 'warning',
            title: 'We kunnen je op dit moment geen offers laten zien.',
            content:
              'We helpen je graag via onze klantenservice. We zijn op maandag tot en met vrijdag van 08.00 tot 16.00 bereikbaar. Telefoon 088-8 955 955 (gebruikelijke belkosten).',
          },
        },
        errorNotification: {
          value: {
            variant: 'error',
            title: 'We kunnen je op dit moment de looptijden niet laten zien.',
            content:
              'Er is een technisch probleem ontstaan. Laad de pagina opnieuw, of neem contact op met de&nbsp;<a rel="noopener noreferrer" href="https://www.eneco.nl/klantenservice/" target="_blank">Klantenservice</a>.',
          },
        },
        errorNotAuthenticatedNotification: {
          value: {
            variant: 'error',
            title: 'Session verlopen',
            content: 'Helaas is de sessie verlopen',
          },
        },
        loginButtonText: {
          value: 'Opnieuw inloggen',
        },
        tryAgainButtonText: {
          value: 'Laad de pagina opnieuw',
        },
      },
      productData: {
        contractTypeRadio: {
          label: 'Contracttype',
          value: {
            requiredMessage: 'Kies een type contract',
            options: [
              {
                name: 'fixed',
                description:
                  'Zekerheid. Tarieven staan vast voor de periode die je hieronder kiest. Daarna vrij om over te stappen.',
                label: 'Vast',
              },
              {
                name: 'variable',
                description: 'Tarieven kunnen veranderen. Altijd opzegbaar zonder boete.',
                label: 'Variabel',
              },
              {
                name: 'dynamic',
                description:
                  'Beweeg mee met de marktprijs voor stroom en gas. Of zet je gastarief vast. Altijd opzegbaar zonder boete.',
                label: 'Dynamisch',
              },
            ],
          },
        },
        tariffsTriggerText: {
          value: 'Bekijk tarieven',
        },
        yearLabel: {
          value: 'jaar',
        },
        perMonthLabel: {
          value: 'geschat per maand',
        },
        perMonthEstimatedLabel: {
          value: 'verwacht per maand',
        },
        monthsLabel: {
          value: 'maanden',
        },
        variablePeriodLabel: {
          value: 'Dynamisch energiecontract',
        },
        productDataList: [
          {
            id: '3310795c-89e8-4070-a8a4-51d81cd7b63d',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity/',
            name: '1 Year Electricity',
            displayName: '1 Year Electricity',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '3310795c-89e8-4070-a8a4-51d81cd7b63d',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity/',
            name: '1 Year Electricity',
            displayName: '1 Year Electricity',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '0-m-electricity-0-m-gas-variable',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: 'd945f727-15fb-4b6b-94de-81dd3440ca52',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-gas/',
            name: '1 Year Electricity and Gas',
            displayName: '1 Year Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom en gas',
                },
                usp2Text: {
                  value: ' Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: 'ade3ae39-ad22-4bfb-b0a1-18cc6f363992',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-warmth/',
            name: '1 Year Electricity and Warmth',
            displayName: '1 Year Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom en warmte',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '59214497-eb66-4607-adee-21b50a84da42',
            url: '/library/commodity-offer-data-library-folder/1-year-gas/',
            name: '1 Year Gas',
            displayName: '1 Year Gas',
            fields: {
              data: {
                name: {
                  value: '1 jaar gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '1-y-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '6c99217b-f3cf-42bc-bdaa-518c65e92fa3',
            url: '/library/commodity-offer-data-library-folder/1-year-warmth/',
            name: '1 Year Warmth',
            displayName: '1 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '1 jaar warmte',
                },
                usp2Text: {
                  value: 'Duurzamere manier van verwarmen en flinke CO2 reductie ten opzichte van gas ',
                },
                id: {
                  value: '1-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '90e7d404-7b01-46cd-8fee-cdc0ccbaacaf',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity/',
            name: '3 Months Electricity',
            displayName: '3 Months Electricity',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'b6f55e56-4385-47d8-a6bb-c511e63473f7',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity-and-gas/',
            name: '3 Months Electricity and Gas',
            displayName: '3 Months Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'a1fef1ba-ccdd-4500-9fbf-9ebe90bb6152',
            url: '/library/commodity-offer-data-library-folder/3-months-gas/',
            name: '3 Months Gas',
            displayName: '3 Months Gas',
            fields: {
              data: {
                name: {
                  value: '3 maanden gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '3-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '81aad285-6224-489d-9149-5797a89a80a1',
            url: '/library/commodity-offer-data-library-folder/3-years-electricity/',
            name: '3 Years Electricity',
            displayName: '3 Years Electricity',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-y-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '3 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '92717078-96b6-4f81-9432-6c181e6acb4b',
            url: '/library/commodity-offer-data-library-folder/3-years-electricity-and-gas/',
            name: '3 Years Electricity and Gas',
            displayName: '3 Years Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom en gas',
                },
                usp2Text: {
                  value: 'Voor gemak en duidelijkheid',
                },
                id: {
                  value: '3-y-electricity-gas',
                },
                usp3Text: {
                  value: 'Na 3 jaar vrij opzeggen',
                },
                usp1Text: {
                  value: 'Vaste tarieven voor 3 jaar',
                },
              },
            },
          },
          {
            id: '826a65d0-88fa-40ef-9d10-963172865f28',
            url: '/library/commodity-offer-data-library-folder/3-years-gas/',
            name: '3 Years Gas',
            displayName: '3 Years Gas',
            fields: {
              data: {
                name: {
                  value: '3 jaar gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '3-y-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '3 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '89a1bf62-5a73-43a5-ab9c-3e555ab85c64',
            url: '/library/commodity-offer-data-library-folder/3-year-electricity-and-warmth/',
            name: '3 Year Electricity and Warmth',
            displayName: '3 Year Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom en warmte',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-y-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '185505f0-9244-4802-9794-8d95ae589ebe',
            url: '/library/commodity-offer-data-library-folder/3-year-warmth/',
            name: '3 Year Warmth',
            displayName: '3 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '3 jaar warmte',
                },
                usp2Text: {
                  value: 'Duurzamere manier van verwarmen en flinke CO2 reductie ten opzichte van gas ',
                },
                id: {
                  value: '3-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '27516bae-f2c9-4385-87b0-9f581cb233f4',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity/',
            name: '6 Months Electricity',
            displayName: '6 Months Electricity',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity',
                },
                usp3Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '16393388-0665-46c8-aee2-8a92dece5c1e',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-gas/',
            name: '6 Months Electricity and Gas',
            displayName: '6 Months Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'ae5198c8-ac90-4ba6-839e-625bf847d91a',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-warmth/',
            name: '6 Months Electricity and Warmth',
            displayName: '6 Months Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom en warmte',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '348eab98-6db0-46fd-b96c-24fcd089ba2e',
            url: '/library/commodity-offer-data-library-folder/6-months-gas/',
            name: '6 Months Gas',
            displayName: '6 Months Gas',
            fields: {
              data: {
                name: {
                  value: '6 maanden gas',
                },
                usp2Text: {
                  value: '24/7 grip op je energieverbruik met de Eneco app',
                },
                id: {
                  value: '6-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'd76a98a7-1418-4928-86c0-feb6ac86255f',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity/',
            name: '0 Year Electricity',
            displayName: '0 Year Electricity',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity',
                },
                usp3Text: {
                  value: 'Flexibel opzeggen zonder boete',
                },
                usp1Text: {
                  value: 'Betaal actuele uurprijzen',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '8cdb3c34-715d-49b5-8d3e-4186fbc582c4',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-gas/',
            name: '0 Year Electricity and Gas',
            displayName: '0 Year Electricity and Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch gas & stroom',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-gas',
                },
                usp3Text: {
                  value: 'Flexibel opzeggen zonder boete',
                },
                usp1Text: {
                  value: 'Betaal actuele uur- en dagprijzen',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: 'baa4659b-5265-4467-8dbe-eb41281a8dc0',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-warmth/',
            name: '0 Year Electricity and Warmth',
            displayName: '0 Year Electricity and Warmth',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom & stadswarmte',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betaal actuele uurprijzen voor stroom',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '51ab57be-1043-42d1-8a61-dda847785f89',
            url: '/library/commodity-offer-data-library-folder/0-year-gas/',
            name: '0 Year Gas',
            displayName: '0 Year Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch gas',
                },
                usp2Text: {
                  value: 'Voor meer vrijheid en controle',
                },
                id: {
                  value: '0-y-gas',
                },
                usp3Text: {
                  value: 'Altijd boeteloos opzeggen',
                },
                usp1Text: {
                  value: 'Actuele dagtarieven',
                },
              },
            },
          },
          {
            id: '8f827823-f398-43e0-a706-7a06d3b2259a',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-1-year-gas/',
            name: '1 Year Electricity and 1 Year Gas',
            displayName: '1 Year Electricity and 1 Year Gas',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom en gas',
                },
                usp2Text: {
                  value: 'Voor zekerheid en zorgeloosheid',
                },
                id: {
                  value: '1-y-electricity-1-y-gas',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom en gas',
                },
              },
            },
          },
          {
            id: '08f1293d-f957-4b21-93d5-8310fcd2462a',
            url: '/library/commodity-offer-data-library-folder/1-year-electricity-and-0-year-warmth/',
            name: '1 Year Electricity and 0 Year Warmth',
            displayName: '1 Year Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '1 jaar stroom',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '1-y-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: '1 jaar lang geen prijsstijgingen',
                },
              },
            },
          },
          {
            id: '2f9b8ec2-b45d-4fe8-9d8e-29af7d5404ec',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-0-year-gas/',
            name: '0 Year Electricity and 0 Year Gas',
            displayName: '0 Year Electricity and 0 Year Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom en gas',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-0-y-gas',
                },
                usp3Text: {
                  value: 'Flexibel opzeggen zonder boete',
                },
                usp1Text: {
                  value: 'Betaal actuele uur- en dagprijzen',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '9ea79de9-eb22-45ad-8505-520ab24db671',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-0-year-warmth/',
            name: '0 Year Electricity and 0 Year Warmth',
            displayName: '0 Year Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom',
                },
                usp2Text: {
                  value: 'Vind voordelige dagdelen in de app',
                },
                id: {
                  value: '0-y-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betaal actuele uurprijzen voor stroom',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: '108c390e-2e28-413a-a0b5-dcec31c81ce2',
            url: '/library/commodity-offer-data-library-folder/0-year-electricity-and-1-year-gas/',
            name: '0 Year Electricity and 1 Year Gas',
            displayName: '0 Year Electricity and 1 Year Gas',
            fields: {
              data: {
                name: {
                  value: 'Dynamisch stroom en vast 1 jaar gas',
                },
                usp2Text: {
                  value: 'Voor mix flexibiliteit en zekerheid',
                },
                id: {
                  value: '0-y-electricity-1-y-gas',
                },
                usp3Text: {
                  value: 'Stroom zonder boete opzeggen',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor gas',
                },
                tariffDescriptionText: {
                  value:
                    'Verwacht totaal, inclusief alle kosten. Na je aanmelding berekenen we je daadwerkelijke verbruik',
                },
              },
            },
          },
          {
            id: 'cdc5df2d-35cb-4986-8444-48b2e9b78f7d',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity-and-0-year-warmth/',
            name: '3 Months Electricity and 0 Year Warmth',
            displayName: '3 Months Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Hergebruik van warmte die er al is',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '7373796f-eb23-4ae1-8a0d-e4abca098ff6',
            url: '/library/commodity-offer-data-library-folder/3-months-electricity-and-3-months-gas/',
            name: '3 Months Electricity and 3 Months Gas',
            displayName: '3 Months Electricity and 3 Months Gas',
            fields: {
              data: {
                name: {
                  value: '3 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-m-electricity-3-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: 'Zekerheid voor een korte periode, daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: 'e8943cb4-95bb-4e36-8db4-ad473d7b1fed',
            url: '/library/commodity-offer-data-library-folder/3-year-electricity-and-0-year-warmth/',
            name: '3 Year Electricity and 0 Year Warmth',
            displayName: '3 Year Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '3-y-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: 'a00de5b0-e2cd-47af-92cb-0497a455882f',
            url: '/library/commodity-offer-data-library-folder/3-years-electricity-and-3-years-gas/',
            name: '3 Years Electricity and 3 Years Gas',
            displayName: '3 Years Electricity and 3 Years Gas',
            fields: {
              data: {
                name: {
                  value: '3 jaar stroom en gas',
                },
                usp2Text: {
                  value: 'Voor gemak en duidelijkheid',
                },
                id: {
                  value: '3-y-electricity-3-y-gas',
                },
                usp3Text: {
                  value: 'Na 3 jaar vrij opzeggen',
                },
                usp1Text: {
                  value: 'Vaste tarieven voor 3 jaar',
                },
              },
            },
          },
          {
            id: '087e786e-2a4f-49ac-8567-176a12b23f0d',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-0-year-warmth/',
            name: '6 Months Electricity and 0 Year Warmth',
            displayName: '6 Months Electricity and 0 Year Warmth',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom',
                },
                usp2Text: {
                  value: 'Duurzaam verwarmen en altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-0-y-warmth',
                },
                usp3Text: {
                  value: 'Betaalbaar en door de warmtewet nooit duurder dan gas',
                },
                usp1Text: {
                  value: 'Betrouwbaar met een leveringszekerheid van 99,9%',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1768',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '6 Months Electricity and 6 Motths Gas',
            displayName: '6 Months Electricity and 6 Motths Gas',
            fields: {
              data: {
                name: {
                  value: '6 maanden stroom en gas',
                },
                usp2Text: {
                  value: 'Altijd 100% groene stroom uit Nederland',
                },
                id: {
                  value: '6-m-electricity-6-m-gas',
                },
                usp3Text: {
                  value: 'We helpen je verduurzamen en besparen',
                },
                usp1Text: {
                  value: '6 maanden zeker van je tarief daarna vrij in keuze',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1768',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '1 Year Electricity And Gas + Eneco Voordeel-Momenten',
            displayName: '1 Year Electricity And Gas + Eneco Voordeel-Momenten',
            fields: {
              data: {
                name: {
                  value: '1 Year Electricity And Gas + Eneco Voordeel-Momenten',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom en gas',
                },
                id: {
                  value: 'TimeOfUse-1-y-electricity-1-y-gas',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp2Text: {
                  value: 'Dagelijks 30% korting op stroom tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur',
                },
              },
              unitPriceLabels: {
                electricityLowLabel: {
                  value: 'Stroom (voordeel)',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1769',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '1 Year Electricity And Warmth + Eneco Voordeel-Momenten',
            displayName: '1 Year Electricity And Warmth + Eneco Voordeel-Momenten',
            fields: {
              data: {
                name: {
                  value: '1 Year Electricity And Warmth + Eneco Voordeel-Momenten',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom en warmth',
                },
                id: {
                  value: 'TimeOfUse-1-y-electricity-1-y-warmth',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp2Text: {
                  value: 'Dagelijks 30% korting op stroom tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur',
                },
              },
              unitPriceLabels: {
                electricityLowLabel: {
                  value: 'Stroom (voordeel)',
                },
              },
            },
          },
          {
            id: '55e5253a-8fed-4112-813d-f1d6815a1770',
            url: '/library/commodity-offer-data-library-folder/6-months-electricity-and-6-motths-gas/',
            name: '1 Year Electricity + Eneco Voordeel-Momenten',
            displayName: '1 Year Electricity + Eneco Voordeel-Momenten',
            fields: {
              data: {
                name: {
                  value: '1 Year Electricity + Eneco Voordeel-Momenten',
                },
                usp1Text: {
                  value: 'Vast jaartarief voor stroom',
                },
                id: {
                  value: 'TimeOfUse-1-y-electricity',
                },
                usp3Text: {
                  value: 'Na 1 jaar vrij opzeggen',
                },
                usp2Text: {
                  value: 'Dagelijks 30% korting op stroom tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur',
                },
              },
              unitPriceLabels: {
                electricityLowLabel: {
                  value: 'Stroom (voordeel)',
                },
              },
            },
          },
        ],
      },
      giftData: {
        giftProductDataList: [
          {
            id: 'c616daa6-d2c1-4efb-90c8-595481469c32',
            url: 'https://www.dev-dxp.eneco.nl/library/incentive-product-data-library-folder/gamma-giftcard-150/',
            name: 'Gamma Giftcard 150',
            displayName: 'Gamma Giftcard 150',
            fields: {
              content: {
                title: {
                  value: 'Gamma cadeaukaart 150',
                },
                descriptionContent: {
                  value: 'Een gamma cadeaukaart is een super <a href="/">incentive</a>',
                },
              },
              gift: {
                giftId: {
                  value: 'Gamma-Giftcard-150',
                },
              },
            },
          },
          {
            id: 'ac9e512a-3396-4506-ab1d-a43564baf52a',
            url: 'https://www.dev-dxp.eneco.nl/library/incentive-product-data-library-folder/gamma-giftcard-50/',
            name: 'Gamma Giftcard 50',
            displayName: 'Gamma Giftcard 50',
            fields: {
              content: {
                title: {
                  value: 'Gamma cadeaukaart 150',
                },
                descriptionContent: {
                  value: 'Een gamma cadeaukaart is een super <a href="/">incentive</a>',
                },
              },
              gift: {
                giftId: {
                  value: 'Gamma-Giftcard-50',
                },
              },
            },
          },
        ],
        giftExplanationTriggerText: {
          value: 'meer info',
        },
      },
      promotionTexts: {
        cashbackOnYearNoteRibbonContent: {
          value: '{cashBackOnYearNote} Cashback On Year Note',
        },
        cashbackDirectRibbonContent: {
          value: '{cashBackDirect} Cashback Direct',
        },
        cashbackDirectLabelContent: {
          value: "Eenmalige Cashback <a href='...'>(meer info)</a>",
        },
        cashbackOnYearNoteLabelContent: {
          value: "Eenmalige Cashback <a href='...'>(meer info)</a>",
        },
        cashbackDirectExplanationContent: {
          value:
            'Quasi id hic dolorem dignissimos ut possimus expedita. Aliquid mollitia cum aut et facere. Sint aperiam tenetur at aperiam.',
        },
        cashbackOnYearNoteExplanationContent: {
          value:
            'Quasi id hic dolorem dignissimos ut possimus expedita. Aliquid mollitia cum aut et facere. Sint aperiam tenetur at aperiam.',
        },
        cashbackExplanationTriggerText: {
          value: 'meer info',
        },
        timeOfUseRibbonContent: {
          value: '30% korting op stroom tijdens de Eneco VoordeelMomenten',
        },
      },
      unitPriceLabels: {
        electricityHighLabel: {
          value: 'Stroom (normaal)',
        },
        electricityLowLabel: {
          value: 'Stroom (dal)',
        },
        electricityLabel: {
          value: 'Stroom',
        },
        redeliveryLabel: {
          value: 'Teruglevering',
        },
        gasLabel: {
          value: 'Gas',
        },
        warmthLabel: {
          value: 'Warmte',
        },
        waterColdLabel: {
          value: 'Water (koud)',
        },
        waterWarmLabel: {
          value: 'Water (warm)',
        },
      },
    },
    datasourceRequired: true,
  },
];

export default mockupData;
