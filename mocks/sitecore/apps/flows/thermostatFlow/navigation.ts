const mockupData = {
  uid: 'dba57033-b966-440a-849a-197b36e9c269',
  componentName: 'Navigation',
  dataSource: '{42985EEE-3AA9-4C33-9DF9-E484FE05ACD8}',
  params: {},
  fields: {
    flowCompletion: {
      redirectMessageContent: {
        value: 'Je wordt doorgestuurd.',
      },
      flowCompletionUrl: {
        value: '/energieproducten/thermostat/bestellen',
      },
    },
    header: {
      closeRedirectLink: {
        value: {
          href: '/energieproducten',
          text: 'Ga echt weg',
          linktype: 'external',
          url: '/energieproducten',
          anchor: '',
          target: '',
        },
      },
      previousStepText: {
        value: 'Ga terug',
      },
      cancelButtonText: {
        value: 'Nee, ga terug',
      },
      navigationSteps: {
        value: {
          enum: [
            {
              name: 'SUITABILITY',
              label: 'Geschiktheid',
            },
            {
              name: 'ACCOUNTSELECT',
              label: 'Account',
            },
            { name: 'LOGI<PERSON>', label: 'Inloggen' },
            { name: 'OPTIONS', label: 'Opties' },
          ],
        },
      },
      stopDialog: {
        value: {
          title: 'Weg!',
          content: 'Weg!<br class="t-last-br" />',
          triggerText: '',
          submitButtonText: 'next',
          cancelButtonText: 'cancel',
        },
      },
      helpDialog: {
        value: {
          title: '',
          content: '',
          triggerText: '',
          submitButtonText: 'next',
          cancelButtonText: 'cancel',
        },
      },
    },
  },
  datasourceRequired: true,
};

export default mockupData;
