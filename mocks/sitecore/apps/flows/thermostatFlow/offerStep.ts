import type { OfferStepRendering } from '@sitecore/types/manual/thermostatFlow/offerStep';

import navigationHeader from './navigation';
import image from '../../../components/shared/image';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockupData: [any, OfferStepRendering] = [
  navigationHeader,
  {
    uid: 'e39016cb-09c8-4855-ae5b-5e7df2f5c585',
    componentName: 'ThermostatFlowOfferStep',
    dataSource: '{DA8030AF-8A34-4F59-A21F-3F6E5AE19BA9}',
    params: {},
    fields: {
      content: {
        title: {
          value: 'Het is een match!',
        },
        text: {
          value:
            'Het lijkt erop dat de Energie Inzicht+ zijn werk goed kan doen in jouw huis. Hieronder zie je de details van het aanbod. Er komen geen installatiekosten bij, want je kan de P1 dongel zelf installeren. In de volgende stap kan je de bestelling afronden.',
        },
        errorNotification: {
          value: {
            variant: 'error',
            title: 'Er ging iets mis',
            content: 'Er is iets misgegaan met het ophalen van de productinformatie. Probeer het later nog eens.',
          },
        },
        nextStepText: { value: 'Volgende' },
      },

      productDetails: {
        image: image().fields.image,
        title: {
          value: 'Slimme thermostaat',
        },
        text: {
          value: 'Met Slimme thermostaat krijg je extra inzicht in je energieverbruik in de app.',
        },
        perMonthLabel: {
          value: 'eenmaling',
        },
        fixedCostsLabel: {
          value: 'Abonnement',
        },
        firstYearFreeLabel: {
          value: 'na 6 maanden per maand',
        },
        oneOffCostsLabel: {
          value: 'Slimme thermostaat',
        },
        discountLabel: {
          value: 'Toon klanten korting',
        },
      },
    },
    datasourceRequired: true,
  },
];

export default mockupData;
