import type { OverviewStepRendering } from '@sitecore/types/manual/thermostatFlow/overviewStep';

import navigationHeader from './navigation';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockupData: [any, OverviewStepRendering] = [
  navigationHeader,
  {
    uid: 'e39016cb-09c8-4855-ae5b-5e7df2f5c585',
    componentName: 'ThermostatFlowOverviewStep',
    dataSource: '{DA8030AF-8A34-4F59-A21F-3F6E5AE19BA9}',
    params: {},
    fields: {
      content: {
        title: {
          value: 'Samenvatting',
        },
        text: {
          value:
            'Je kiest voor Energie Inzicht+. Hiermee krijg je extra inzicht in je energieverbruik in de app. Controleer de gegevens goed en plaats je bestelling.',
        },
        nextStepText: { value: 'Bestelling afronden' },
      },
      notifications: {
        offerErrorNotification: {
          value: {
            variant: 'error',
            title: 'Er ging iets mis',
            content: 'Er is iets misgegaan met het ophalen van de productinformatie. Probeer het later nog eens.',
          },
        },
        orderErrorNotification: {
          value: {
            variant: 'error',
            title: 'Er ging iets mis',
            content: 'Het is niet gelukt om de bestelling te versturen. Probeer het later nog eens.',
          },
        },
      },
      personalDetails: {
        title: { value: 'Persoonlijke gegevens' },
        content: {
          value:
            "Dit zijn de gegevens die we van jou hebben. Als er iets niet klopt, kan je dit aanpassen in <a href='/mijn-eneco'>Mijn Eneco</a>.",
        },
        nameLabel: { value: 'Naam' },
        addressLabel: { value: 'Adres' },
        contactLabel: { value: 'Contactgegevens' },
        emailAddressLabel: { value: 'Emailadres' },
        phoneNumberLabel: { value: 'Telefoonnummer' },
        mobilePhoneNumberLabel: { value: 'Mobiel telefoonnummer' },
      },
      productDetails: {
        title: { value: 'Gekozen producten' },
        perMonthLabel: { value: 'per maand' },
        fixedCostsLabel: { value: 'Abonnement' },
        firstYearFreeLabel: { value: 'Eerste jaar gratis' },
        oneOffCostsLabel: { value: 'Eenmalige kosten' },
        thermostatInfoLabel: { value: 'Hardware' },
        discountLabel: { value: 'Korting' },
        discountInfoLabel: { value: 'Allen voor Toon klanten' },
        firstYearInfoText: { value: 'eerste 6  maanden gratis' },
        contractEndDateLabel: { value: '' },
        contractStartDateLabel: { value: '' },
        conditionContent: {
          value: `<p>Wanneer je klikt op onderstaande knop, ga je akkoord met de {conditions} en ga je een
      betalingsverplichting met Eneco aan. Tevens machtig je Eneco, totdat je deze weer intrekt, tot
      automatische incasso, vanaf de door jou opgegeven rekening, van de verschuldigde maand- en jaarbedragen
      voor energie en daarbij behorende producten en diensten.</p>
      <br/>
      <p>Tevens bevestig je het volgende:</p>
      <ul>
        <li>Je hebt een slimme meter met wifi bereik</li>
        <li>Je hebt geen zonnepanelen, stadswarmte of een Toon</li>
      </ul>
      `,
        },
        salesConditionsDialog: {
          value: {
            title: 'Verkoopvoorwaarden',
            content: 'De verkoopvoorwaarden die van toepassing zijn op deze bestelling vind je hieronder.&nbsp;',
            triggerText: 'voorwaarden',
            submitButtonText: '',
            cancelButtonText: '',
          },
        },
        privacyLink: {
          value: {
            url: '',
            anchor: '',
            text: 'Privacy statement',
            href: 'https://www.eneco.nl/privacy-statement/',
            linktype: 'internal',
            target: '',
            class: '',
            title: '',
            querystring: '',
            id: '',
          },
        },
      },
    },
    datasourceRequired: true,
  },
];

export default mockupData;
