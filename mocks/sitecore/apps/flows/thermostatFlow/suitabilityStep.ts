import { Illustrations } from '@sitecore/types/manual/Illustrations';
import type { SuitabilityStepRendering } from '@sitecore/types/manual/thermostatFlow/suitabilityStep';

import navigationHeader from './navigation';

const createCheckbox = ({
  name,
  illustration,
  label,
  reqMessage,
}: {
  name: string;
  illustration: Illustrations;
  label: string;
  reqMessage: string;
}) => ({
  id: 'e61198a4-090a-4604-b74b-339e716e5c61',
  url: 'https://www.acc.eneco.nl/energieproducten/thermostat/bestellen/geschiktheid/local-content/thermostat-flow-suitability-step',
  name,
  displayName: name,
  fields: {
    checkbox: {
      illustration: {
        value: illustration,
      },
      name: {
        value: name,
      },
      label: {
        value: label,
      },
      requiredMessageText: {
        value: reqMessage,
      },
    },
  },
});

const suitabilityStep: SuitabilityStepRendering = {
  uid: 'e39016cb-09c8-4855-ae5b-5e7df2f5c585',
  componentName: 'ThermostatFlowSuitabilityStep',
  dataSource: '{DA8030AF-8A34-4F59-A21F-3F6E5AE19BA9}',
  params: {},
  fields: {
    content: {
      title: {
        value: 'Wat geldt voor jou?',
      },
      text: {
        value:
          'Leuk dat je een Slimme thermostaat wilt aanschaffen. Om zeker te weten dat de Slimme thermostaat ook in jouw woning past check je onderstaande vereisten.',
      },
      suitableNotification: {
        value: {
          variant: 'success',
          title: 'Jouw woning is geschikt.',
          content: 'Je woning is geschikt voor Energie Inzicht+. In de volgende stap laten we jouw mogelijkheden zien.',
        },
      },
      nextStepText: { value: 'Bekijk aanbod' },
    },
    formfields: {
      requiredCheckedItemsList: [
        createCheckbox({
          label: 'Ik heb in mijn woning WiFi bereik',
          name: 'hasWifiAccess',
          illustration: 'WiFi',
          reqMessage:
            'De slimme meter moet in verbinding staan met het internet om Energie Inzicht+ te kunnen gebruiken.',
        }),
        createCheckbox({
          label: 'Ik heb stadswarmte',
          name: 'hasHeating',
          illustration: 'GasBoiler',
          reqMessage: 'We kunnen geen inzicht geven in je verbruik als je stadswarmte hebt.',
        }),
        createCheckbox({
          label: 'Ik heb Eneco App',
          name: 'hasEnecoApp',
          illustration: 'Smartphone',
          reqMessage: 'Toon en Energie Inzicht+ werken niet goed samen. Je kunt wel gebruik maken van de Toon app.',
        }),
        createCheckbox({
          label: 'Ik heb 230wk socket near CH boiler',
          name: 'hasSocketBoiler',
          illustration: 'ElectricalOutlet',
          reqMessage:
            'We kunnen geen inzicht geven in je verbruik als je zonnepanelen hebt, omdat het direct verbruik van zonne-energie niet geregistreerd word.',
        }),
        createCheckbox({
          label: 'Ik heb a wired Thermostaat',
          name: 'hasThermostatWired',
          illustration: 'Thermostat',
          reqMessage:
            'We kunnen geen inzicht geven in je verbruik als je zonnepanelen hebt, omdat het direct verbruik van zonne-energie niet geregistreerd word.',
        }),
      ],
      requiredUncheckedItemsList: [],
    },
    unsuitable: {
      multipleReasonsText: {
        value: 'Je kan je niet aanmelden om de volgende redenen:',
      },
      unsuitableNotification: {
        value: {
          variant: 'error',
          title: 'Jouw situatie is niet geschikt.',
          content: 'Je woning is helaas niet geschikt voor Energie Inzicht+.',
        },
      },
      signupContent: {
        value: `<p>We willen graag in de toekomst Energie Inzicht+ aan een breder publiek aanbieden. Wil je op de hoogte blijven van de ontwikkelingen? Schrijf je dan in voor onze nieuwsbrief.
        </p>
        <p>Door op de knop hieronder te klikken, geef je toestemming om maximaal 1 keer per maand een e-mail van ons te ontvangen. De email zal worden verstuurd naar {email}.</p>`,
      },
      emailSignupButtonText: {
        value: 'Hou mij op de hoogte',
      },
      emailDestinationCode: {
        value: '4F39E4D9-881A-4EA3-88F8-97758380CC9E',
      },
      emailSuccessNotification: {
        value: {
          variant: 'success',
          title: 'Bedankt voor je aanmelding',
          content: 'We hebben je aanmelding voor de nieuwsbrief ontvangen. Je ontvangt binnenkort een e-mail van ons.',
        },
      },
      emailErrorNotification: {
        value: {
          variant: 'error',
          title: 'Aanmelden niet gelukt',
          content: 'Er is iets misgegaan bij het aanmelden voor de nieuwsbrief. Probeer het later nog eens.',
        },
      },
    },
  },
  datasourceRequired: true,
};

const mockupData = [navigationHeader, suitabilityStep];

export default mockupData;
