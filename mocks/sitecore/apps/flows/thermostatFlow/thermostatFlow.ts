import { ComponentRendering } from '@sitecore/types/lib';

import accountSelectStep from './accountSelectStep';
import logInStep from './logInStep';
import navigation from './navigation';
import offerStep from './offerStep';
import overviewStep from './overviewStep';
import suitabilityStep from './suitabilityStep';

export const getThermostatFlowPlaceholder = (
  item: string,
  nextBasePath: string,
  appBasePath: string,
): ComponentRendering[] => {
  switch (true) {
    case item.includes(`${nextBasePath}${appBasePath}/geschiktheid/`):
      return suitabilityStep;
    case item.includes(`${nextBasePath}${appBasePath}/inloggen/`):
      return logInStep;
    case item.includes(`${nextBasePath}${appBasePath}/account/`):
      return accountSelectStep;
    case item.includes(`${nextBasePath}${appBasePath}/aanbod/`):
      return offerStep;
    case item.includes(`${nextBasePath}${appBasePath}/controle/`):
      return overviewStep;
    default:
      return [navigation];
  }
};
