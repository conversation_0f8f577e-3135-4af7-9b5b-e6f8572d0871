const mockupData = {
  uid: '7c29e25d-0765-4586-a37d-53a561754f4a',
  componentName: 'Navigation',
  dataSource: '{B5D4F036-1644-4C55-934B-DDDCA8564D5D}',
  params: {},
  fields: {
    header: {
      navigationSteps: {
        value: {
          enum: [
            {
              name: 'details',
              label: 'Details',
            },
          ],
        },
      },
      stopDialog: {
        value: {
          title: 'Weet je zeker dat je wilt stoppen?',
          content:
            '<p>Als je dit scherm sluit, worden je gegevens bewaard tot het einde van de dag. Daarna zul je opnieuw moeten beginnen met het invullen van je gegevens. Kom je er niet uit? Bel ons op\n<a href="tel:+31888955955">088 - 8 955 955</a>\n(je betaalt je gebruikelijke belkosten).</p>',
          triggerText: '',
          submitButtonText: 'Ja ik wil stoppen',
          cancelButtonText: 'Nee, ik wil verder gaan',
        },
      },
      helpDialog: {
        value: {
          title: 'Hoe kunnen we je helpen?',
          content:
            '<p>Mocht je er niet helemaal uitkomen, dan kun je altijd bellen met onze klantenservice om je contract af te sluiten. We zijn op maandag tot en met vrijdag van 08.00 tot 18.00 bereikbaar.</p>\n<p><a href="tel:+31888955955">088 - 8 955 955</a> (je betaalt je gebruikelijke belkosten).</p>',
          triggerText: '',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      previousStepText: {
        value: 'Vorige',
      },
    },
  },
  datasourceRequired: true,
};

export default mockupData;
