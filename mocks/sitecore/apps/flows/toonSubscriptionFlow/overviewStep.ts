import navigationHeader from './navigation';

const mockupData = [
  navigationHeader,
  {
    uid: 'c84ca02d-23f0-4cd4-9d7d-e7863b8b6329',
    componentName: 'ToonOverviewStep',
    dataSource: '{3B4112AF-F287-4DA8-BA6E-38F17F0CAC3D}',
    params: {},
    fields: {
      content: {
        disclaimerText: {
          value: 'Disclaimer text content',
        },
        title: {
          value: 'Titel',
        },
        text: {
          value: 'Dit is een text',
        },
        notification: {
          value: {
            title: '',
            content: '',
          },
        },
        nextStepText: {
          value: 'Volgende',
        },
      },
      costDetails: {
        oneOffCostsLabel: {
          value: 'Eenmalige kosten',
        },
        totalCostsLabel: {
          value: 'Totale kosten',
        },
        perMonthLabel: {
          value: 'per maand',
        },
      },
      productData: {
        ToonProductsList: [
          {
            id: '2ef9578f-2eb2-49c1-8d19-e412057b7692',
            url: '/library/toon-products-folder/toon-thermometer/',
            name: 'Toon thermometer',
            displayName: 'Toon thermometer',
            fields: {
              content: {
                productName: {
                  value: 'Toon',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Toon thermostaat',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'T2007',
                },
              },
            },
          },
          {
            id: '40b3c805-7360-4924-ab47-10518d87196c',
            url: '/library/toon-products-folder/toon-installation/',
            name: 'Toon installation',
            displayName: 'Toon installation',
            fields: {
              content: {
                productName: {
                  value: 'Toon installatie',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Installatie van Toon',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TINSTAL',
                },
              },
            },
          },
          {
            id: '6fdbf3bd-adce-4f19-b743-63aa8a227403',
            url: '/library/toon-products-folder/toon-substitution/',
            name: 'Toon substitution',
            displayName: 'Toon substitution',
            fields: {
              content: {
                productName: {
                  value: 'Toon Abonnenment',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Abonnement op Toon',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TLU01',
                },
              },
            },
          },
          {
            id: '6fdbf3bd-adce-4f19-b743-63aa8a227403',
            url: '/library/toon-products-folder/toon-substitution/',
            name: 'Toon substitution',
            displayName: 'Toon substitution',
            fields: {
              content: {
                productName: {
                  value: 'Toon Abonnenment 2 maanden gratis',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Abonnement op Toon',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TLU02',
                },
              },
            },
          },
          {
            id: '6fdbf3bd-adce-4f19-b743-63aa8a227403',
            url: '/library/toon-products-folder/toon-substitution/',
            name: 'Toon substitution',
            displayName: 'Toon substitution',
            fields: {
              content: {
                productName: {
                  value: 'Toon Abonnenment 3 maanden gratis',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Abonnement op Toon',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TLU03',
                },
              },
            },
          },
          {
            id: '6fdbf3bd-adce-4f19-b743-63aa8a227403',
            url: '/library/toon-products-folder/toon-substitution/',
            name: 'Toon substitution',
            displayName: 'Toon substitution',
            fields: {
              content: {
                productName: {
                  value: 'Toon Abonnenment 4 maanden gratis',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Abonnement op Toon',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TLU04',
                },
              },
            },
          },
          {
            id: 'bb0e3aa0-1836-4560-b930-4e7f2ef79909',
            url: '/library/toon-products-folder/zon-op-toon-combination/',
            name: 'Zon Op Toon combination',
            displayName: 'Zon Op Toon combination',
            fields: {
              content: {
                productName: {
                  value: 'Zon op Toon',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Combineer met je zonnepanelen',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TZON',
                },
              },
            },
          },
          {
            id: '07881c5a-682d-43f1-a609-72bf46860bdc',
            url: '/library/toon-products-folder/zon-op-toon/',
            name: 'Zon op Toon',
            displayName: 'Zon op Toon',
            fields: {
              content: {
                productName: {
                  value: 'Zon op Toon',
                },
                uspList: {
                  value: {
                    enum: [],
                  },
                },
                productShortDesciption: {
                  value: 'Combineer Toon met je zonnepanelen',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TZONS',
                },
              },
            },
          },
          {
            id: '854e8c22-05d0-4369-8dfc-5231b9c8e2d3',
            url: '/library/toon-products-folder/toon-thermostaat-plus-installatie/',
            name: 'Toon thermostaat plus installatie',
            displayName: 'Toon thermostaat plus installatie',
            fields: {
              content: {
                productName: {
                  value: 'Toon thermostaat',
                },
                uspList: {
                  value: {
                    enum: [
                      {
                        name: 'usp_1',
                        value: 'usp_1',
                        label: 'Slimme thermostaat die je overal bedient met je app',
                      },
                      {
                        name: 'usp_2',
                        value: 'usp_2',
                        label: 'Zie precies hoeveel stroom en gas je verbruikt en bespaart',
                      },
                      {
                        name: 'usp_3',
                        value: 'usp_3',
                        label: '',
                      },
                    ],
                  },
                },
                productShortDesciption: {
                  value: 'Installatie van Toon',
                },
              },
              productProperties: {
                marketingProductCode: {
                  value: 'TTHERMO',
                },
              },
            },
          },
        ],
      },
    },
    datasourceRequired: true,
  },
];

export default mockupData;
