import { ComponentRendering } from '@sitecore/types/lib';

import navigation from './navigation';
import overviewStep from './overviewStep';
import summaryStep from './summaryStep';
import addressStep from '../energyFlow/addressStep';
import contactDetailsStep from '../energyFlow/contactDetailsStep';
import personalDetailsStep from '../energyFlow/personalDetailsStep';

export const getToonSubscriptionFlowPlaceholder = (
  item: string,
  nextBasePath: string,
  appBasePath: string,
): ComponentRendering[] => {
  switch (true) {
    case item.includes(`${nextBasePath}${appBasePath}/overzicht/`):
      return overviewStep;
    case item.includes(`${nextBasePath}${appBasePath}/adresgegevens/`):
      return addressStep;
    case item.includes(`${nextBasePath}${appBasePath}/persoonlijke-gegevens/`):
      return personalDetailsStep;
    case item.includes(`${nextBasePath}${appBasePath}/contactgegevens/`):
      return contactDetailsStep;
    case item.includes(`${nextBasePath}${appBasePath}/controle-keuze/`):
      return summaryStep;
    default:
      return [navigation];
  }
};
