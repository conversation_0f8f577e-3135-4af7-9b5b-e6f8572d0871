import { HEMSMinimalNavigationBarRendering } from '@sitecore/types/HEMSMinimalNavigationBar';
import { SmartChargingOverrideLoadingTargetScheduleRendering } from '@sitecore/types/SmartChargingOverrideLoadingTargetSchedule';

export const smartChargingOverrideLoadingTargetScheduleComponent: SmartChargingOverrideLoadingTargetScheduleRendering =
  {
    uid: 'f5e2adc4-6380-4c35-8f67-ca79dc672ab6',
    componentName: 'SmartChargingOverrideLoadingTargetSchedule',
    dataSource: '{4337E813-BDDD-4CA4-AA07-3862E43B6DC6}',
    params: {},
    fields: {},
    datasourceRequired: true,
  };

const smartChargingOverrideLoadingTargetSchedule = [
  {
    uid: 'c0ed6791-dd45-4534-94f5-5b9163631f3f',
    componentName: 'HEMSMinimalNavigationBar',
    dataSource: '{1879CB45-86BF-477A-9B67-3C9B173BCE5C}',
    params: {},
    fields: {
      closeButtonAriaLabel: {
        value: '',
      },
      navigationHeaderBackAriaLabel: {
        value: '',
      },
      backButtonLink: {
        value: {
          href: '',
          text: '',
          anchor: '',
          linktype: 'internal',
          class: '',
          title: '',
          querystring: '',
          id: '{E9B1DE24-0224-4FCE-A4F5-75D00C8FF749}',
        },
      },
      closeButtonLink: {
        value: {
          href: '/?item=%2Fev',
          text: '',
          anchor: '',
          linktype: 'internal',
          class: '',
          title: '',
          querystring: '',
          id: '{E9B1DE24-0224-4FCE-A4F5-75D00C8FF749}',
        },
      },
      navigationHeaderText: {
        value: '',
      },
    },
    datasourceRequired: false,
  } as HEMSMinimalNavigationBarRendering,
  smartChargingOverrideLoadingTargetScheduleComponent,
];

export default smartChargingOverrideLoadingTargetSchedule;
