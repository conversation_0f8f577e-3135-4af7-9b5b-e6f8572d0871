import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';

export const energyMonitor: ConsumptionGraphicalOverviewRendering = {
  uid: '944952D0-D7C6-4699-8E15-44BCA6C43E8F',
  componentName: 'ConsumptionGraphicalOverview',
  dataSource: '599695E5-A05A-4F1C-B4F5-832D43494DDF',
  params: {},
  datasourceRequired: false,
  fields: {
    data: {
      addressFilterLabel: { value: 'Adres' },
      consumptionTypeLabel: { value: 'Verbruikstype' },
      periodLabel: { value: 'Periode' },
      title: { value: 'Verbruik' },
      consumptionTypeOptionsList: {
        value: {
          enum: [
            { name: 'Totaal', value: 'Total', label: 'Totaal' },
            { name: 'Elektriciteit', value: 'Electricity', label: 'Elektriciteit' },
            { name: 'Gas', value: 'Gas', label: 'Gas' },
            { name: 'Piekwaarden', value: 'PeakValues', label: 'Piekwaarden' },
            { name: 'G<PERSON>ïnjecteerd', value: 'Injection', label: 'G<PERSON>ïnjecteerd' },
          ],
        },
      },
      periodOptionsList: {
        value: {
          enum: [
            { name: 'Maand', value: 'Monthly', label: 'Maand' },
            { name: 'Jaar', value: 'Yearly', label: 'Jaar' },
          ],
        },
      },
      description: { value: 'Beschrijving' },
      graphTitle: { value: 'Titel' },
      disclaimerText: { value: 'Disclaimer' },
      disclaimerInfoDescription: { value: 'Disclaimer info' },
      noDigitalMeterText: { value: 'Geen digitale meter' },
    },
    meterReadingsTable: {
      addMeterReadingButtonText: { value: 'Toevoegen' },
      dateTableHeaderText: { value: 'Datum' },
      deleteText: { value: 'Verwijderen' },
      deleteValueText: { value: 'Verwijderen' },
      energyOptionsList: {
        value: {
          enum: [
            { name: 'Elektriciteit', value: 'Electricity', label: 'Elektriciteit' },
            { name: 'Gas', value: 'Gas', label: 'Gas' },
          ],
        },
      },
      meterReadingTableTitle: { value: 'Meterstanden' },
      originInformativeText: { value: 'Bron' },
      originMoreInfoPopoverContent: { value: 'Bron' },
      meterReadingElectricityTableText: { value: 'Meterstanden elec' },
      meterReadingGasTableText: { value: 'Meterstanden gas' },
      originOfficialText: { value: 'Bron' },
      originText: { value: 'Bron' },
      originMoreInfoText: { value: 'Bron' },
      originUserText: { value: 'Bron' },
      unableToDeleteValueText: { value: 'Verwijderen niet mogelijk' },
      originRlpText: { value: 'RLP waarde' },
    },
    legend: {
      title: { value: 'Totaalverbruik' },
      totalLabel: { value: 'Totaal' },
      electricityLabel: { value: 'Stroom' },
      fixedCostLabel: { value: 'Vaste kosten' },
      fixedCostDescription: { value: 'Vaste kosten omschrijving' },
      fixedCostInformationDescription: { value: 'Vaste kosten info' },
      peakValuesTitle: { value: 'Piekwaarden' },
      averagePeakValueLabel: { value: 'Gemiddelde piekwaarde' },
      capacityTariffLabel: { value: 'Capaciteitstarief' },
      gasLabel: { value: 'Gas' },
      injectionLabel: { value: 'Opwek' },
    },
    advanceCard: {
      recommendedAmountNotification: {
        value: {
          title: 'Aanbevolen bedrag',
          content: 'Aanbevolen bedrag',
          variant: 'info',
        },
      },
      title: { value: 'Aanbevolen bedrag' },
      acceptRecommendedAmountLink: { value: 'Aanbevolen bedrag' },
      currencySignLabel: { value: '€' },
      currentAmountTitle: { value: 'Huidige bedrag' },
      recommendedAmountTitle: { value: 'Aanbevolen bedrag' },
      eachMonthLabel: { value: 'Elke maand' },
      recommendedAmountTooHighDisclaimerLabel: { value: 'Aanbevolen bedrag te hoog' },
      recommendedAmountTooLowDisclaimerLabel: { value: 'Aanbevolen bedrag te laag' },
      editAdvanceAmountLink: { value: 'Aanbevolen bedrag' },
    },
  },
};
export default energyMonitor;
