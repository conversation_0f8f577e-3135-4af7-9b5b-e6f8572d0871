import { LayoutServiceData } from '@sitecore/types/lib';

import errorMessageContainer from './errorMessageContainer';
import onboardingFluvius from './fluvius/onboarding';
import onboardingFluviusReturn from './fluvius/onboardingReturn';
import insightsMenu from './insightsMenu';
import profile from './profile';
import accountAndSettings from './profile/accountAndSettings';
import profileLogin from './profile/accountAndSettings/change-email';
import profileLoginChangePassword from './profile/accountAndSettings/change-password';
import profileLoginDeleteAccount from './profile/accountAndSettings/delete-account';
import profileSettingsNotifications from './profile/accountAndSettings/notifications';
import smartChargingConnectionIssue from './profile/connection-error';
import profileSettingsConnections from './profile/connections';
import dongleInfo from './profile/connections/dongle/index';
import mandateInfoElectricity from './profile/connections/electricity';
import mandateInfoGas from './profile/connections/gas';
import smartChargingInfo from './profile/connections/smartcharging';
import energyProfileOverview from './profile/energy-profile-overview';
import profileFAQ from './profile/faq';
import profileFAQFunctionalities from './profile/faq/functionalities';
import profileFAQInstallation from './profile/faq/installation';
import profileFAQProblemSolving from './profile/faq/problem-solving';
import profileInvoices from './profile/invoices';
import profilePremium from './profile/premium';
import profileSmartInsightsDetails from './profile/premium/smart-insights/index';
import reportProblem from './profile/report-problem';
import termsAndInformation from './profile/terms-and-information';
import appVersion from './profile/terms-and-information/app-version';
import profileSettingsConditions from './profile/terms-and-information/conditions';
import profileSettingsPrivacy from './profile/terms-and-information/privacy';
import profileSettingsPrivacyRevoke from './profile/terms-and-information/privacy/revoke';
import removeAccountSuccess from './removeAccountSuccess';
import accountReset from '../../../../apps/accessmanagement/be/eneco/native/accountReset';
import appLandingPage from '../../../../apps/accessmanagement/be/eneco/native/appLandingPage';
import fluviusFlow from '../../../../apps/accessmanagement/be/eneco/native/fluviusFlow/fluviusFlow';
import privacyConsent from '../../../../apps/accessmanagement/be/eneco/native/privacyConsent';
import registration from '../../../../apps/accessmanagement/be/eneco/native/registration';
import welcomePage from '../../../../apps/accessmanagement/be/eneco/native/welcomePage';
import dashboardHeader from '../../../../apps/dashboard/be/eneco/native/dashboardHeader';
import dashboardServiceError from '../../../../apps/dashboard/be/eneco/native/dashboardServiceError';
import featuredBanner from '../../../../apps/dashboard/be/eneco/native/featuredBanner';
import generalInformationalBanner from '../../../../apps/dashboard/be/eneco/native/generalInformationalBanner';
import monthSummaryCard from '../../../../apps/dashboard/be/eneco/native/monthSummaryCard';
import motivationCard from '../../../../apps/dashboard/be/eneco/native/motivationCard';
import usageCards from '../../../../apps/dashboard/be/eneco/native/usageCards';
import woonprofielSuccessCard from '../../../../apps/dashboard/be/eneco/native/woonprofielSuccessCard';
import dongleOnboarding from '../../../../apps/dongle/dongleOnboarding';
import energyProfile from '../../../../apps/energyprofile/be/eneco/native/energyProfile';
import fluviusOnboardingButton from '../../../../apps/fluvius/fluviusOnboardingButton';
import CompatibilityCheck from '../../../../apps/hems/be/eneco/native/smartcharging/CompatibilityCheck';
import CompatibilityCheckCarFailure from '../../../../apps/hems/be/eneco/native/smartcharging/CompatibilityCheckCarFailure';
import CompatibilityCheckChargerFailure from '../../../../apps/hems/be/eneco/native/smartcharging/CompatibilityCheckChargerFailure';
import CompatibilityCheckFailure from '../../../../apps/hems/be/eneco/native/smartcharging/CompatibilityCheckFailure';
import CompatibilityCheckSuccess from '../../../../apps/hems/be/eneco/native/smartcharging/CompatibilityCheckSuccess';
import CompatibilityCheckIntro from '../../../../apps/hems/be/eneco/native/smartcharging/CompatibillityCheckIntro';
import smartChargingConnectionStatus from '../../../../apps/hems/be/eneco/native/smartcharging/smartChargingConnectionStatus';
import SmartChargingCurrentLoadingScheduleOverride from '../../../../apps/hems/be/eneco/native/smartcharging/SmartChargingCurrentLoadingSchedule';
import smartChargingDashboardCard from '../../../../apps/hems/be/eneco/native/smartcharging/smartChargingDashboardCard';
import smartChargingDefaultSchedule from '../../../../apps/hems/be/eneco/native/smartcharging/smartChargingDefaultSchedule';
import {
  SmartChargingInsights,
  IconLink,
} from '../../../../apps/hems/be/eneco/native/smartcharging/SmartChargingInsights';
import smartChargingLinksAndReward from '../../../../apps/hems/be/eneco/native/smartcharging/SmartChargingQuickLinksAndReward';
import smartChargingSchedulePreferences from '../../../../apps/hems/be/eneco/native/smartcharging/smartChargingSchedulePreferences';
import smartChargingSchedulingToggle from '../../../../apps/hems/be/eneco/native/smartcharging/smartChargingSchedulingToggle';
import smartChargingSessionDetails from '../../../../apps/hems/be/eneco/native/smartcharging/SmartChargingSessionDetails';
import smartChargingTutorial from '../../../../apps/hems/be/eneco/native/smartcharging/smartChargingTutorial';
import stopSmartChargingButton from '../../../../apps/hems/be/eneco/native/smartcharging/stopSmartChargingButton';
import stopSmartChargingNotification from '../../../../apps/hems/be/eneco/native/smartcharging/stopSmartChargingNotification';
import TermsAndConditions from '../../../../apps/hems/be/eneco/native/smartcharging/TermsAndConditions';
import productPurchaseFlow from '../../../../apps/launchpad/productPurchaseFlow';
import productPurchaseResultFailure from '../../../../apps/launchpad/productPurchaseResultFailure';
import productPurchaseResultSuccess from '../../../../apps/launchpad/productPurchaseResultSuccess';
import smartInsightsDetails from '../../../../apps/launchpad/smartInsightsDetails';
import smartInsightsProductPurchaseFlow from '../../../../apps/launchpad/smartInsightsProductPurchaseFlow';
import mandateWarning from '../../../../apps/mandate/be/eneco/native/mandateWarning';
import pushNotificationToggles from '../../../../apps/profile/be/eneco/native/pushNotificationToggles';
import RemoveAccount from '../../../../apps/profile/be/eneco/native/removeAccount';
import removeAccountButton from '../../../../apps/profile/be/eneco/native/removeAccountButton';
import reportProblemButton from '../../../../apps/profile/be/eneco/native/reportProblemButton';
import RevokePrivacy from '../../../../apps/profile/be/eneco/native/revokePrivacy';
import usage from '../../../../apps/usage/be/eneco/native/usage';
import { getBaseContext } from '../../../../common';
import { Mock } from '../../../../types/mock';

const topBar = (backgroundColor: string) => ({
  params: { anchor: { value: '' } },
  componentName: 'TopBar',
  fields: {
    backgroundColor,
  },
});

const mock: Mock = (item = '/', options = { locale: 'nl-BE', site: 'be-eneco-insights' }) => {
  const context = getBaseContext({
    domain: 'eneco',
    basePath: item,
    locale: options.locale,
    siteName: options.site,
    customerId: options.customerId,
  });

  const route: LayoutServiceData['sitecore']['route'] = {
    name: 'Eneco Insights App',
    layoutId: '4',
    fields: {},
    placeholders: {
      'jss-main': [],
      'jss-footer': [],
      'jss-meta': [],
      'jss-header': [],
    },
  };

  switch (true) {
    case item === '/':
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [appLandingPage];
      break;
    case item === '/welcome':
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [welcomePage];
      break;
    case item === '/registration':
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [registration];
      break;
    case item === '/reset-account':
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [accountReset];
      break;
    case item === '/privacy-consent':
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [privacyConsent];
      break;
    case item.startsWith('/dashboard'):
      route.layoutId = '7108c6ed-54b4-4633-b619-7027c9c7ef55'; // Dashboard
      route.placeholders['jss-top-fixed'] = [topBar('backgroundVarFour')];
      route.placeholders['jss-header'] = [dashboardHeader];
      route.placeholders['jss-main'] = [
        woonprofielSuccessCard,
        generalInformationalBanner,
        mandateWarning,
        dashboardServiceError,
        stopSmartChargingNotification,
        featuredBanner,
        smartChargingDashboardCard,
        smartChargingSessionDetails,
        monthSummaryCard,
        usageCards,
        motivationCard,
      ];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];
      break;
    case item.startsWith('/usage'):
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [mandateWarning, usage];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];
      break;
    case item.startsWith('/energyprofile'):
      route.layoutId = '************************************';
      route.placeholders['jss-main'] = [energyProfile];
      break;
    case item.startsWith('/launchpad/dongle'):
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1';
      route.placeholders['jss-main'] = [productPurchaseFlow];

      break;

    case item.startsWith('/launchpad/smart-insights'):
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1';
      route.placeholders['jss-main'] = [smartInsightsProductPurchaseFlow];

      break;

    case item.startsWith('/launchpad/smart-insights-details'):
      route.layoutId = '************************************';
      route.placeholders['jss-main'] = [...smartInsightsDetails];

      break;

    case item.startsWith('/launchpad/purchase-dongle-success'):
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1';
      route.placeholders['jss-main'] = [productPurchaseResultSuccess];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item.startsWith('/launchpad/purchase-dongle-failure'):
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1';
      route.placeholders['jss-main'] = [productPurchaseResultFailure];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/profile':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profile];
      route.placeholders['jss-main-bottom'] = [reportProblemButton];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/account':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Account' };
      route.placeholders['jss-main'] = [...accountAndSettings];
      route.placeholders['jss-main-bottom'] = [removeAccountButton];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/account/change-email':
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1'; // ContentSecondary
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'E-mailadres wijzigen' };
      route.placeholders['jss-main'] = [...profileLogin];
      route.placeholders['jss-main-bottom'] = [];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/profile/account/change-password':
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1'; // ContentSecondary
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Wachtwoord wijzigen' };
      route.placeholders['jss-main'] = [...profileLoginChangePassword];
      route.placeholders['jss-main-bottom'] = [];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/profile/account/delete-account':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Verwijder je account' };
      route.placeholders['jss-main'] = [...profileLoginDeleteAccount];
      route.placeholders['jss-main-bottom'] = [RemoveAccount];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profilev2/connections/dongle':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-main'] = [...dongleInfo];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/remove-account-success':
      route.fields!.browserTitle = { value: 'Bedankt voor het gebruik maken van de App' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundPrimary')];
      route.placeholders['jss-main'] = [...removeAccountSuccess];

      route.placeholders['jss-main-bottom'] = [
        {
          componentName: 'ButtonLink',
          fields: {
            buttonlink: {
              value: {
                href: '/',
                text: 'Naar registratie account',
                anchor: '',
                linktype: 'internal',
                class: '',
                title: '',
                querystring: '',
                id: '{E92F4C1E-1A20-4E82-AA4F-0C6F4E7E566B}',
              },
            },
          },
        },
      ];

      break;
    case item === '/profile/account/notifications':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Meldingen beheren' };
      route.placeholders['jss-main'] = [...profileSettingsNotifications, ...pushNotificationToggles];
      route.placeholders['jss-main-bottom'] = [];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/energy-profile-overview':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Mijn woonprofiel' };
      route.placeholders['jss-main'] = [...energyProfileOverview];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/fluviusonboarding':
      route.fields!.browserTitle = { value: 'Krijg 24/7 inzicht in je verbruik en statistieken' };
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...onboardingFluvius];
      route.placeholders['jss-main-bottom'] = [fluviusOnboardingButton];
      break;

    case item === '/fluviusreturn':
      route.fields!.browserTitle = { value: 'Direct aan de slag' };
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-main'] = [...onboardingFluviusReturn];
      route.placeholders['jss-bottom-fixed'] = [];
      route.placeholders['jss-main-bottom'] = [
        {
          componentName: 'ButtonLink',
          fields: {
            buttonLink: {
              value: {
                href: '/?item=%2Fdashboard',
                text: 'Volgende',
                anchor: '',
                linktype: 'internal',
                class: '',
                title: '',
                querystring: '',
                id: '{E92F4C1E-1A20-4E82-AA4F-0C6F4E7E566B}',
              },
            },
          },
        },
      ];
      break;

    case item === '/profile/connections':
      route.fields!.browserTitle = { value: 'Mijn verbindingen' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profileSettingsConnections];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/connections/gas':
      route.fields!.browserTitle = { value: 'Gas' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...mandateInfoGas];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/connections/electricity':
      route.fields!.browserTitle = { value: 'Elektriciteit' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...mandateInfoElectricity];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/connections/dongle':
      route.fields!.browserTitle = { value: 'Dongle' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...dongleInfo];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/connections/smartcharging':
      route.fields!.browserTitle = { value: 'Elektrische wagen' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...smartChargingInfo];
      route.placeholders['jss-main-bottom'] = [stopSmartChargingButton];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/settings/notifications':
      route.fields!.browserTitle = { value: 'Notifications' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profileSettingsNotifications, ...pushNotificationToggles];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/settings/connections/smartcharging-issue':
      route.fields!.browserTitle = { value: 'Connectieprobleem' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...smartChargingConnectionIssue];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/profile/invoices':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Facturen' };
      route.placeholders['jss-main'] = [...profileInvoices];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/premium':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Premium diensten' };
      route.placeholders['jss-main'] = [...profilePremium];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/premium/smart-insights':
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1'; // ContentSecondary
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profileSmartInsightsDetails];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/faq':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Veelgestelde vragen' };
      route.placeholders['jss-main'] = [...profileFAQ];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/faq/functionaliteiten':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Functionaliteiten' };
      route.placeholders['jss-main'] = [...profileFAQFunctionalities];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/faq/installatie':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Installatie' };
      route.placeholders['jss-main'] = [...profileFAQInstallation];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/faq/probleemoplossing':
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.fields!.browserTitle = { value: 'Probleemoplossing' };
      route.placeholders['jss-main'] = [...profileFAQProblemSolving];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/terms-and-information':
      route.fields!.browserTitle = { value: 'Voorwaarden en informatie' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...termsAndInformation];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/terms-and-information/privacy':
      route.fields!.browserTitle = { value: 'Privacy' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profileSettingsPrivacy];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/terms-and-information/privacy/revoke':
      route.fields!.browserTitle = { value: 'Privacy wijzigen' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profileSettingsPrivacyRevoke];
      route.placeholders['jss-main-bottom'] = [RevokePrivacy];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;

    case item === '/profile/terms-and-information/conditions':
      route.fields!.browserTitle = { value: 'Voorwaarden' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...profileSettingsConditions];

      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/terms-and-information/app-info':
      route.fields!.browserTitle = { value: 'App-info' };
      route.layoutId = '************************************'; // ContentSecondary V2
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...appVersion];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];

      break;
    case item === '/profile/report-problem':
      route.fields!.browserTitle = { value: 'Rapporteer een probleem' };
      route.layoutId = '************************************'; // ContentPrimary
      route.placeholders['jss-top-fixed'] = [topBar('backgroundSecondary')];
      route.placeholders['jss-main'] = [...reportProblem];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/ev':
      route.layoutId = '9f7018ad-a37a-49aa-ba6f-103f201d19fd';
      route.placeholders['jss-main'] = [smartChargingLinksAndReward, smartChargingSchedulePreferences, smartChargingSchedulingToggle];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];
      break;

    case item === '/ev/smartcharging-default-schedule':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...smartChargingDefaultSchedule];
      route.placeholders['jss-bottom-fixed'] = [];

      break;

    case item === '/ev/smartcharging-current-session-override':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...SmartChargingCurrentLoadingScheduleOverride];
      route.placeholders['jss-bottom-fixed'] = [];
      break;

    case item === '/ev/connections':
      route.fields!.browserTitle = { value: 'Verbindingen' };
      route.layoutId = 'c67edc63-278a-43bf-9812-8ff8a2a7dea1';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...smartChargingConnectionStatus];
      route.placeholders['jss-bottom-fixed'] = [insightsMenu];
      break;

    case item === '/smartcharging-compatibilitycheck-step1':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...CompatibilityCheckIntro];
      route.placeholders['jss-bottom-fixed'] = [];

      break;

    case item === '/smartcharging-compatibilitycheck-step1/smartcharging-compatibilitycheck-step2':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...CompatibilityCheck];
      route.placeholders['jss-bottom-fixed'] = [];
      break;
    case item === '/smartcharging-compatibilitycheck-step1/smartcharging-compatibilitycheck-step2/car-failure':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...CompatibilityCheckCarFailure];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item ===
      '/smartcharging-compatibilitycheck-step1/smartcharging-compatibilitycheck-step2/chargingstation-failure':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...CompatibilityCheckChargerFailure];
      route.placeholders['jss-bottom-fixed'] = [];
      break;
    case item === '/smartcharging-compatibilitycheck-step1/smartcharging-compatibilitycheck-step2/combination-failure':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...CompatibilityCheckFailure];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/smartcharging-compatibilitycheck-step1/smartcharging-compatibilitycheck-step2/success':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [...CompatibilityCheckSuccess];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/smartcharging-terms-and-conditions':
      route.layoutId = '************************************';
      route.placeholders['jss-main'] = [...TermsAndConditions];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/ev/smartcharging-insights':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [IconLink, SmartChargingInsights];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/smartcharging-tutorial':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [smartChargingTutorial];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/fluvius-flow':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [fluviusFlow];
      route.placeholders['jss-bottom-fixed'] = [];

      break;

    case item === '/p1-dongle-onboarding':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [dongleOnboarding];
      route.placeholders['jss-bottom-fixed'] = [];

      break;

    case item === '/error':
      route.layoutId = '************************************';
      route.placeholders['jss-top-fixed'] = [];
      route.placeholders['jss-main'] = [errorMessageContainer];
      route.placeholders['jss-bottom-fixed'] = [];

      break;
    case item === '/login':
    default: // Main
      route.layoutId = '4';
      route.placeholders['jss-main'] = [];

      break;
  }

  return {
    sitecore: {
      context,
      route,
    },
  };
};

export default mock;
