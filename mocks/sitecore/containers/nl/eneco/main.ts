import { LayoutServiceData, RouteData } from '@sitecore/types/lib';

import personalizedTitleTextCTA from '../../../../sitecore/components/shared/personalizedTitleTextCta';
import registration from '../../../apps/accessmanagement/nl/eneco/registrationAccountCreate';
import registrationReset from '../../../apps/accessmanagement/nl/eneco/registrationAccountReset';
import registrationFooter from '../../../apps/accessmanagement/nl/eneco/registrationFooter';
import registrationHeader from '../../../apps/accessmanagement/nl/eneco/registrationHeader';
import contactCustomerService from '../../../apps/content/nl/oxxio/contactCustomerService';
import contactMethods from '../../../apps/content/nl/oxxio/contactMethods';
import cookieWall from '../../../apps/cookiewall/nl/shared/cookieWall';
import dynamicPricing from '../../../apps/dynamicPricing/nl/multilabel/dynamicPricing';
import { getContractAdviceFlowPlaceholder } from '../../../apps/flows/contractAdviceFlow/contractAdviceFlow';
import { getContractExtensionFlowPlaceholder } from '../../../apps/flows/contractExtensionFlow/contractExtensionFlow';
import { getDecarbonisationLeadFlowPlaceholder } from '../../../apps/flows/decarbonisationLeadFlow/decarbonisationLeadFlow';
import { getDeceasedCancelationFlow } from '../../../apps/flows/deceasedCancelationFlow/deceaseCancelationFlow';
import { getDongleCancelationFlow } from '../../../apps/flows/dongleCancelationFlow/dongleCancelationFlow';
import { getDongleFlowPlaceholder } from '../../../apps/flows/dongleFlow/dongleFlow';
import { getDynamicEnergyFlowPlaceholder } from '../../../apps/flows/dynamicEnergyFlow/dynamicEnergyFlow';
import { getEnergyFlowPlaceholder, getFlowCompletionPageContent } from '../../../apps/flows/energyFlow/energyFlow';
import { getKetelComfortFlowPlaceholder } from '../../../apps/flows/ketelComfortFlow/ketelComfortFlow';
import { getKetleComfortCancelationFlow } from '../../../apps/flows/ketleComfortCancelationFlow/ketleComfortCancelationFlow';
import { getLegislationCompassFlowPlaceholder } from '../../../apps/flows/legislationCompassFlow/legislationCompassFlow';
import { getRentalDeviceFlowPlaceholder } from '../../../apps/flows/rentalDeviceFlow/rentalDeviceFlow';
import { getServiceGemakFlowPlaceholder } from '../../../apps/flows/serviceGemakFlow/serviceGemakFlow';
import { getSmeEnergyFlowPlaceholder } from '../../../apps/flows/smeEnergyFlow/smeEnergyFlowV2';
import { getThermostatFlowPlaceholder } from '../../../apps/flows/thermostatFlow/thermostatFlow';
import { getToonCancelationFlow } from '../../../apps/flows/toonCancelationFlow/toonCancelationFlow';
import { getToonSubscriptionFlowPlaceholder } from '../../../apps/flows/toonSubscriptionFlow/toonSubscriptionFlow';
import { getUrgentEnergyFlowPlaceholder } from '../../../apps/flows/urgentEnergyFlow/urgentEnergyFlow';
import { inspirationAdviceFilter } from '../../../apps/inspirationadvicefilter/nl/eneco/inspirationAdviceFilter';
import meterReadingsConfirmationStep from '../../../apps/meterreadings/nl/multilabel/meterReadingsConfirmationStep';
import meterReadingsErrorStep from '../../../apps/meterreadings/nl/multilabel/meterReadingsErrorStep';
import meterReadingsIntroductionStep from '../../../apps/meterreadings/nl/multilabel/meterReadingsIntroductionStep';
import meterReadingsMeterReadingsStep from '../../../apps/meterreadings/nl/multilabel/meterReadingsMeterReadingStep';
import meterReadingsReadingId from '../../../apps/meterreadings/nl/multilabel/meterReadingsReadingIdStep';
import meterReadingsThankYouStepAdhoc from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepAdhoc';
import meterReadingsThankYouStepCloseDown from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepCloseDown';
import MeterReadingsThankYouStepPeriodicEven from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicEven';
import MeterReadingsThankYouStepPeriodicGetBack from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicGetBack';
import MeterReadingsThankYouStepPeriodicGetBackTermAmount from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicGetBackTermAmount';
import MeterReadingsThankYouStepPeriodicNewTermAmount from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicNewTermAmount';
import MeterReadingsThankYouStepPeriodicPayBack from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicPayBack';
import MeterReadingsThankYouStepPeriodicPayBackLess from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicPayBackLess';
import MeterReadingsThankYouStepPeriodicPayBackMore from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicPayBackMore';
import MeterReadingsThankYouStepPeriodicPayBackTermAmount from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepPeriodicPayBackTermAmount';
import MeterReadingsThankYouStepRegistration from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepRegistration';
import MeterReadingsThankYouStepRelocationNewAddress from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepRelocationNewAddress';
import MeterReadingsThankYouStepRelocationOldAddress from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepRelocationOldAddress';
import meterReadingsThankYouStepTme from '../../../apps/meterreadings/nl/multilabel/meterReadingsThankYouStepTme';
import IdealPayment from '../../../apps/payment/shared/multilabel/IDealPayment';
import { shopWindow } from '../../../apps/shopwindow/shopWindow';
import { heatPumpSpecTable, linkCard, text, textAndTable, USP } from '../../../apps/shopwindow/shopWindowPDP';
import { getBaseContext } from '../../../common';
import { articleLineUp } from '../../../components/nl/eneco/articleLineUp';
import { articlePage } from '../../../components/nl/eneco/articlePage';
import { contentCardRebranded } from '../../../components/nl/eneco/contentCardRebranded';
import {
  contentCarousel,
  contentLineUp,
  contentStepsSlider,
  testimonialsCardCarousel,
  testimonialsCardLineUp,
} from '../../../components/nl/eneco/contentLineUp';
import { creditWarningBar } from '../../../components/nl/eneco/creditWarningBar';
import customerCareSearchTestHero from '../../../components/nl/eneco/customerCareSearchTestHero';
import { featurePreview } from '../../../components/nl/eneco/featurePreview';
import hero from '../../../components/nl/eneco/hero';
import heroCardRebranded from '../../../components/nl/eneco/heroCardRebranded';
import { priceDetailHero } from '../../../components/nl/eneco/priceDetailHero';
import weDoenHetNuTextImage from '../../../components/nl/eneco/road2zero/weDoenHetNuTextImage';
import { textImageRebranded } from '../../../components/nl/eneco/textImageRebranded';
import { loginCard } from '../../../components/nl/multilabel/BannerCard/loginCard';
import heroCardAddressFinder, { getHeroCardComponent } from '../../../components/shared/addressFinder';
import { oxxioCalculationToolForm as advancedCalculationTool } from '../../../components/shared/advancedCalculationTool';
import { offerCalculationToolForm } from '../../../components/shared/calculationTool';
import centered from '../../../components/shared/centered';
import chat from '../../../components/shared/chat';
import chatLinks from '../../../components/shared/chatLinks';
import { contactForm } from '../../../components/shared/contactForm';
import contentCategories from '../../../components/shared/contentCategories';
import { contractConfirmation } from '../../../components/shared/contractConfirmation';
import customerReview from '../../../components/shared/customerReview';
import EnergyStatisticsByRegion from '../../../components/shared/energyStatisticsByRegion';
import EnergyStatisticsForRegion from '../../../components/shared/energyStatisticsForRegion';
import { getFooter } from '../../../components/shared/footer';
import genericForm from '../../../components/shared/genericForm';
import { heatComparisonCalculatorForm } from '../../../components/shared/heatComparisonCalculator';
import heatDamageForm from '../../../components/shared/heatDamageForm';
import image from '../../../components/shared/image';
import metaData from '../../../components/shared/metaData';
import { getNavBar } from '../../../components/shared/navigationBar';
import { nextBestAction } from '../../../components/shared/nextBestAction';
import paginatedContent from '../../../components/shared/paginatedContent';
import personalizedVideo, { personalizedVideoScript } from '../../../components/shared/personalizedVideo';
import registerToNewsletterForm from '../../../components/shared/registerToNewsletterForm';
import revolutionRegisterToNewsletterForm from '../../../components/shared/revolutionRegisterToNewsletterForm';
import section from '../../../components/shared/section';
import { serviceSignupForm } from '../../../components/shared/serviceSignupForm';
import { splitview } from '../../../components/shared/splitview';
import { splitViewWithAddressFinder } from '../../../components/shared/splitviewWithAddressFinder';
import structuredDataSchema from '../../../components/shared/structuredDataSchema';
import titleTextCTA from '../../../components/shared/titleTextCTA';
import usabillaFeedback from '../../../components/shared/usabillaFeedback';
import { WarmthCheckAddressFinder } from '../../../components/shared/warmChecker';
import { warmOptimaalCampaignForm } from '../../../components/shared/warmOptimaalCampaignForm';
import warmthInterruptions from '../../../components/shared/warmthInterruptions';
import { Mock } from '../../../types/mock';

const setPageTitle = (route: RouteData, title: string) => {
  if (!route.fields) route.fields = {};
  route.fields['pageTitle'] = { value: title };
};

const flowNames = [
  'energy-flow',
  'dynamic-energy-flow',
  'urgent-energy-flow',
  'contract-extension-flow',
  'contract-advice-flow',
  'zon-op-toon-flow',
  'toon-subscription-flow',
  'toon-replacement-flow',
  'rental-devices-flow',
  'legislation-compass-flow',
  'sme-energy-flow-v2',
  'decarbonisation-lead-flow',
  'servicegemak-flow',
  'ketelcomfort-flow',
  'dongle-flow',
  'dongle-cancelation-flow',
  'thermostat-flow',
  'ketelcomfort-cancelation-flow',
  'toon-cancelation-flow',
  'deceased-flow',
];

const mock: Mock = (item, options = { locale: 'nl-NL', site: 'nl-eneco-main' }): LayoutServiceData => {
  const nextBasePath = '/';
  const flowName = flowNames.find(flowName => item.split('/').some(item => item === flowName)) || '';
  const appBasePath = flowName;
  const flowCompletionUrl = `/flow-complete`;
  const flowCancelationUrl = `/`;
  const basePath = '';

  const route: LayoutServiceData['sitecore']['route'] = {
    name: 'Eneco',
    displayName: 'Eneco',
    databaseName: 'web',
    placeholders: {
      'jss-navigationbar': [
        getNavBar({
          includeSubHeader: false,
          includeTopHeader: true,
          includeBreadcrumbs: true,
          label: 'eneco',
          businessUnit: 'nl',
          currentRoute: item,
        }),
      ],
      'jss-notifications': [],
      'jss-main': [],
      'jss-meta': [cookieWall(), metaData(), structuredDataSchema()],
      'jss-footer': [getFooter()],
    },
  };

  let context = getBaseContext({ domain: 'eneco', basePath: item, ...options });

  if (appBasePath !== '') {
    context = getBaseContext({ domain: 'eneco', basePath: `${nextBasePath}${appBasePath}/`, ...options });
    context.itemPath = item;

    route.fields = {
      frontEndRootPath: { value: `${nextBasePath}${appBasePath}` },
      allowFrontEndRouting: { value: true },
      flowName: { value: flowName },
      flowCompletionUrl: {
        // @ts-ignore TODO: remove this ignore once "fields" type is fixed "libs/sitecore/types/lib/index.ts"
        value: {
          // @ts-ignore TODO: remove this ignore once "fields" type is fixed "libs/sitecore/types/lib/index.ts"
          href: flowCompletionUrl,
          linktype: 'internal',
          url: '',
          anchor: '',
          target: '',
          text: '',
          class: '',
          title: '',
          queryString: '',
          id: '{9890890-9834234-f09898}',
        },
      },
      flowCancelationUrl: {
        // @ts-ignore TODO: remove this ignore once "fields" type is fixed "libs/sitecore/types/lib/index.ts"
        value: {
          // @ts-ignore TODO: remove this ignore once "fields" type is fixed "libs/sitecore/types/lib/index.ts"
          href: flowCancelationUrl,
          linktype: 'internal',
          url: '',
          anchor: '',
          target: '',
          text: '',
          class: '',
          title: '',
          queryString: '',
          id: '{9890890-9834234-f09899}',
        },
      },
    };
  }

  switch (true) {
    case item === '/':
      route.placeholders['jss-main'] = [getHeroCardComponent({ isSme: true })];
      break;
    case item.includes(`warmthinterruptions`):
      route.placeholders['jss-main'] = [heroCardAddressFinder, warmthInterruptions, section(centered(heatDamageForm))];
      break;
    case item.includes(`revolution-register-to-newsletter-form`):
      route.placeholders['jss-main'] = [section(centered(revolutionRegisterToNewsletterForm))];
      break;
    case item.includes(`register-to-newsletter-form`):
      route.placeholders['jss-main'] = [section(centered(registerToNewsletterForm))];
      break;
    case item.includes(`warm-optimaal-form`):
      route.placeholders['jss-main'] = [section(centered(warmOptimaalCampaignForm))];
      break;
    case item.includes(`energy-statistics`):
      route.placeholders['jss-main'] = [
        section(item.includes('by-region') ? EnergyStatisticsByRegion : EnergyStatisticsForRegion),
      ];
      break;
    case item.includes(`pagination`):
      route.placeholders['jss-main'] = [section(paginatedContent(splitview(titleTextCTA(), image())))];
      break;

    case item.includes(`generic-form`):
      route.placeholders['jss-main'] = [section(centered(genericForm))];
      break;

    case item.startsWith(`${basePath}/${appBasePath}/je-gegevens/controle-keuze${flowCompletionUrl}/`):
      route.placeholders['jss-main'] = [getFlowCompletionPageContent()];
      route.placeholders['jss-footer'] = [getFooter()];
      break;

    case item.includes('energy-flow-calculation-tool'):
      route.placeholders['jss-main'] = [offerCalculationToolForm];
      break;

    case item.includes('energy-flow-advanced-calculation-tool'):
      route.placeholders['jss-main'] = [advancedCalculationTool];
      break;

    case item.includes('next-best-action'):
      route.placeholders['jss-main'] = [nextBestAction];
      break;

    case item.startsWith(`${basePath}/energy-flow/`):
      route.placeholders['jss-app-energy-flow'] = getEnergyFlowPlaceholder(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];

      // example how to mock experiment info
      if (item === `${basePath}/energy-flow/`) {
        context.experimentInfo = {
          name: 'bestellen2 23F41E93FCAA4233B487142A8C29970E',
          id: '666705a9-245b-4a74-9c76-c56d7cd0fde1.1',
          variant: 'Original',
          path: '/energy-flow',
        };
      } else {
        delete context.experimentInfo;
      }

      break;
    case item.startsWith(`${basePath}/dynamic-energy-flow/`):
      route.placeholders['jss-app-dynamic-energy-flow'] = getDynamicEnergyFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/dongle-flow/`):
      route.placeholders['jss-app-dongle-flow'] = getDongleFlowPlaceholder(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/thermostat-flow/`):
      route.placeholders['jss-app-thermostat-flow'] = getThermostatFlowPlaceholder(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/servicegemak-flow/`):
      route.placeholders['jss-app-servicegemak-flow'] = getServiceGemakFlowPlaceholder(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;

    case item.startsWith(`${basePath}/ketelcomfort-flow/`):
      route.placeholders['jss-app-ketelcomfort-flow'] = getKetelComfortFlowPlaceholder(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/urgent-energy-flow/`):
      route.placeholders['jss-app-urgent-energy-flow'] = getUrgentEnergyFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/contract-extension-flow/`):
      route.placeholders['jss-app-contract-extension-flow'] = getContractExtensionFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/contract-advice-flow/`):
      route.placeholders['jss-app-contract-advice-flow'] = getContractAdviceFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;

    case item.startsWith(`${basePath}/toon-subscription-flow/`):
      route.placeholders['jss-app-toon-subscription-flow'] = getToonSubscriptionFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/rental-devices-flow/`):
      route.placeholders['jss-app-rental-devices-flow'] = getRentalDeviceFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/legislation-compass-flow/`):
      route.placeholders['jss-app-legislation-compass-flow'] = getLegislationCompassFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;

    case item.startsWith(`${basePath}/sme-energy-flow-v2/`):
      route.placeholders['jss-app-sme-energy-flow-v2'] = getSmeEnergyFlowPlaceholder(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/decarbonisation-lead-flow/`):
      route.placeholders['jss-app-decarbonisation-lead-flow'] = getDecarbonisationLeadFlowPlaceholder(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;

    case item.startsWith(`${basePath}/klantenservice/`):
      setPageTitle(route, 'Eneco Klantenservice');
      route.placeholders['jss-main'] = [
        customerCareSearchTestHero,
        section(loginCard),
        section(personalizedTitleTextCTA({ title: 'Hoe kunnen we je helpen, {firstName}?' })),
        section(chatLinks('eneco')),
        section(contentCategories),
        section(splitview(image(), titleTextCTA())),
        section(contactCustomerService),
        chat(),
      ];
      break;

    case item.startsWith(`${basePath}/klantenserviceV2/`):
      setPageTitle(route, 'Eneco Klantenservice');
      route.placeholders['jss-main'] = [
        customerCareSearchTestHero,
        section(loginCard),
        section(personalizedTitleTextCTA({ title: 'Hoe kunnen we je helpen, {firstName}?' })),
        section(chatLinks('eneco')),
        section(contentCategories),
        section(splitview(image(), titleTextCTA())),
        section(contactMethods),
        chat(),
      ];
      break;

    case item.startsWith(`${basePath}/contactformulier`):
      setPageTitle(route, 'Contact form');
      route.placeholders['jss-main'] = [
        customerCareSearchTestHero,
        section(centered(contactForm({ budgetManagerAvailable: true, hideInquiryTypeField: false }))),
        chat(),
      ];
      break;
    case item.startsWith(`${basePath}/ketelcomfort-cancelation-flow/`):
      route.placeholders['jss-app-ketelcomfort-cancelation-flow'] = getKetleComfortCancelationFlow(
        item,
        nextBasePath,
        appBasePath,
      );
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/dongle-cancelation-flow/`):
      route.placeholders['jss-app-dongle-cancelation-flow'] = getDongleCancelationFlow(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/toon-cancelation-flow/`):
      route.placeholders['jss-app-toon-cancelation-flow'] = getToonCancelationFlow(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;
    case item.startsWith(`${basePath}/deceased-flow/`):
      route.placeholders['jss-app-deceased-flow'] = getDeceasedCancelationFlow(item, nextBasePath, appBasePath);
      route.placeholders['jss-footer'] = [];
      break;

    case item.startsWith(`${basePath}/personalized-video`):
      route.placeholders['jss-main'] = [personalizedVideoScript, section(personalizedVideo)];
      break;

    case item.includes('bespaartips'):
      route.placeholders['jss-main'] = [inspirationAdviceFilter];
      break;

    case item.includes('lineup-page'):
      route.placeholders['jss-main'] = [
        contentLineUp,
        contentCarousel,
        testimonialsCardCarousel,
        testimonialsCardLineUp,
        contentStepsSlider,
      ];
      break;

    case item.includes('article-page'):
      route.placeholders['jss-main'] = [articlePage, articleLineUp];
      break;

    case item.includes('componenten'):
      route.placeholders['jss-main'] = [heroCardAddressFinder, splitViewWithAddressFinder, customerReview];
      break;

    case item.includes('road2zero'):
      route.placeholders['jss-main'] = [hero, weDoenHetNuTextImage];
      break;

    case item.includes('afm-credit-warning'):
      route.placeholders['jss-notifications'] = [creditWarningBar];
      break;

    case item.includes('shopwindow-overview'):
      setPageTitle(route, 'Eneco Warmtepompen');
      route.placeholders['jss-main'] = [hero, shopWindow];
      break;

    case item.includes('shopwindow-pdp'):
      setPageTitle(route, 'Eneco Warmtepompen PDP');
      route.placeholders['jss-main'] = [priceDetailHero, USP, text, heatPumpSpecTable, textAndTable, linkCard];
      break;

    case item.includes('dynamisch'):
      route.placeholders['jss-main'] = [dynamicPricing(), featurePreview];
      break;

    case item.includes('heat-comparison-calculator'):
      route.placeholders['jss-main'] = [heatComparisonCalculatorForm];
      break;

    case item.includes('service-signup-form'):
      route.placeholders['jss-main'] = [section(centered(serviceSignupForm))];
      break;

    case item.includes('usabillafeedback'):
      route.placeholders['jss-main'] = usabillaFeedback;
      break;

    case item.startsWith(`${basePath}/energieproducten/cv-onderhoud/afspraakV2`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-appointments-flow-v2'] = [];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/energieproducten/cv-onderhoud/afspraakV2/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/foutmelding`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsErrorStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsThankYouStepAdhoc];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/opzegging/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsThankYouStepCloseDown];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-volgt/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsThankYouStepAdhoc];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/tariefwissel/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsThankYouStepTme];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-we-staan-quitte/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicEven];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-geld-terug/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicGetBack];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-termijnbedrag-geld-terug/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicGetBackTermAmount];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-nieuw-termijnbedrag/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicNewTermAmount];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-bijbetalen/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicPayBack];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-bijbetalen-minder/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicPayBackLess];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-bijbetalen-meer/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicPayBackMore];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/jaarnota-termijnbedrag-bijbetalen/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepPeriodicPayBackTermAmount];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/aanmelding/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepRegistration];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/nieuwe-woning/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepRelocationNewAddress];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bedankt/oude-woning/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [MeterReadingsThankYouStepRelocationOldAddress];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/bevestigen`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsConfirmationStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/tapwater`):
    case item.startsWith(`${basePath}/meterstanden-doorgeven/koude`):
    case item.startsWith(`${basePath}/meterstanden-doorgeven/warmte`):
    case item.startsWith(`${basePath}/meterstanden-doorgeven/stroom`):
    case item.startsWith(`${basePath}/meterstanden-doorgeven/gas`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsMeterReadingsStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/jaarnota`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsIntroductionStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/eindnota`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsIntroductionStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/tariefwijziging`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsIntroductionStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/opnamekenmerk`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-meterreadings-flow'] = [meterReadingsReadingId];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/meterstanden-doorgeven/`):
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-meta'] = [chat()];

      route.placeholders['jss-app-meterreadings-flow'] = [];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/meterstanden-doorgeven/`;
      break;

    case item.startsWith(`${basePath}/account-aanmaken`):
      setPageTitle(route, `Account aanmaken`);
      route.placeholders['jss-navigationbar'] = [];
      route.placeholders['jss-header'] = [registrationHeader()];
      route.placeholders['jss-main'] = [registration('eneco')];
      route.placeholders['jss-footer'] = [registrationFooter()];
      break;

    case item.startsWith(`${basePath}/inloggegevens-wijzigen`):
      setPageTitle(route, `Inloggegevens wijzigen`);
      route.placeholders['jss-navigationbar'] = [];
      route.placeholders['jss-header'] = [registrationHeader()];
      route.placeholders['jss-main'] = [registrationReset('eneco')];
      route.placeholders['jss-footer'] = [registrationFooter()];
      break;

    case item.includes('contract-confirmation'):
      setPageTitle(route, 'Contract confirmation page');
      route.placeholders['jss-main'] = [contractConfirmation];
      break;

    case item.startsWith(`${basePath}/ideal`):
      setPageTitle(route, 'iDeal page');
      route.placeholders['jss-main'] = [section(centered(IdealPayment))];
      break;

    case item.startsWith(`${basePath}/hero-card-rebranded`):
      setPageTitle(route, 'Hero Card Rebranded');
      route.placeholders['jss-main'] = [section(centered(heroCardRebranded))];
      break;

    case item.startsWith(`${basePath}/warm-checker`):
      setPageTitle(route, 'Warm Checker');
      route.placeholders['jss-main'] = [section(centered(WarmthCheckAddressFinder))];
      break;

    case item.startsWith(`${basePath}/card-rebranded`):
      setPageTitle(route, 'Card Rebranded');
      route.placeholders['jss-main'] = [section(centered(contentCardRebranded))];
      break;

    case item.startsWith(`${basePath}/textimage-rebranded`):
      setPageTitle(route, 'TextImage Rebranded');
      route.placeholders['jss-main'] = [section(textImageRebranded)];
      break;

    default:
      // Current "404" response from Sitecore:
      return {
        sitecore: {
          context: {
            // eslint-disable-next-line no-loss-of-precision
            visitorIdentificationTimestamp: 637781993280964784,
            pageEditing: false,
            site: { name: 'website_eneco_main' },
            pageState: 'normal',
            language: 'nl-NL',
          },
          route: null,
        },
      } as unknown as LayoutServiceData; // todo: not sure yet if Sitecore is wrong or the jss type is wrong; but this is literally the Sitecore response on a 404
  }

  return {
    sitecore: {
      context,
      route,
    },
  };
};

export default mock;
