import chalk from 'chalk';
import { execSync } from 'child_process';
import { readFile, writeFile, rm, access } from 'fs/promises';
import inquirer from 'inquirer';

import { fetchDevSecrets } from './azure/fetch-dev-secrets.mjs';
import path from 'path';
import { fileURLToPath } from 'url';
main();

const infraRepoName = 'eneco-dxp-infra';

async function main() {
  cleanup();
  const [container, environment, developmentType] = await getConfig();
  console.log(
    `\nFetching latest env vars for ${chalk.cyan(container)} in the ${chalk.cyan(environment)} environment.\nUsing ${developmentType === 'mocked' ? chalk.cyan('mocked data') : chalk.cyan('integrated')} development.\n`,
  );

  const paths = getPaths(container, environment);

  pullFromGit(paths);

  const infraRepoValues = await fetchInfraEnvVariables({ paths, environment });
  const integratedDevelopmentSecretVars =
    developmentType === 'integrated' ? await fetchSecretsForEnv({ environment, container }) : {};
  const localDevelopmentVars =
    (await getLocalDevEnvVariables({
      container,
      developmentType,
    })) || {};

  const newEnvFileVars = {
    ...infraRepoValues,
    ...integratedDevelopmentSecretVars,
    ...localDevelopmentVars,
  };
  const isLocalDevelopmentDefined = Object.values(localDevelopmentVars)?.length > 0;
  if (Object.values(newEnvFileVars)?.length) {
    await updateEnvFile(
      newEnvFileVars,
      // if project has specified mocked/integrated values specified
      // we don't need to copy anything from the .env.example
      isLocalDevelopmentDefined,
    );
  }
  if (developmentType === 'integrated') {
    console.error(
      chalk.yellow(
        `Integrated development: please make sure you start the server with port ${container === 'nl-internal-agentportal' ? '3000' : '8888'}.\n`,
      ),
    );
  }

  cleanup();
}

/*
-----------------------
 Functions
-----------------------
*/

async function getConfig() {
  const availableContainers = getAvailableContainers();
  const args = process.argv;

  if (args.length < 4 || !availableContainers.includes(args[2])) {
    return getInputInteractively(availableContainers);
  }

  const container = args[2];
  const environment = deriveEnvFromArg(args[3]);
  const developmentType = args[4] || 'mocked';
  return [container, environment, developmentType];
}

function getAvailableContainers() {
  //These containers do not have env stored in workload files in the infra repo.
  const forbiddenContainers = ['nl-internal-eligibilityhub', 'nl-eneco-sandbox', 'be-eneco-insights'];
  return execSync(`nx show projects --with-target serve`, { stdio: [] })
    .toString()
    .trim()
    .split('\n')
    .filter(containerName => !forbiddenContainers.includes(containerName));
}

async function getInputInteractively(availableContainers) {
  try {
    const { container } = await inquirer.prompt([
      { choices: availableContainers, type: 'list', name: 'container', message: 'Select a container' },
    ]);

    const containersWithMockedDataOptions = [
      'nl-eneco-selfservice',
      'nl-eneco-grootzakelijk',
      'nl-eneco-zakelijk',
      'nl-eneco-main',
      'nl-oxxio-main',
      'nl-oxxio-selfservice',
      'nl-woonenergie-main',
      'nl-woonenergie-selfservice',
      'nl-internal-agentportal',
      'be-eneco-insights',
      'be-eneco-main',
    ];

    const developmentTypeOptions = [
      { name: 'Mocked data', value: 'mocked' },
      { name: 'Integrated (Fetch from API)', value: 'integrated' },
    ];

    const { developmentType } = containersWithMockedDataOptions.includes(container)
      ? await inquirer.prompt([
          {
            choices: developmentTypeOptions,
            type: 'list',
            name: 'developmentType',
            message: 'Select development type',
          },
        ])
      : { developmentType: 'mocked' };

    const availableEnvs = ['development', 'test', 'acceptance', 'production'];
    const { environment } =
      developmentType === 'integrated'
        ? await inquirer.prompt([
            { choices: availableEnvs, type: 'list', name: 'environment', message: 'Select an environment' },
          ])
        : { environment: availableEnvs[0] };

    return [container, environment, developmentType];
  } catch (e) {
    console.error(chalk.red(`Error: ${e.message}`));
    process.exit();
  }
}

function deriveEnvFromArg(arg) {
  switch (arg.toLowerCase()) {
    case 'development':
    case 'dev':
    case 'd':
      return 'development';
    case 'test':
    case 't':
      return 'test';
    case 'acceptance':
    case 'acc':
    case 'a':
      return 'acceptance';
    case 'production':
    case 'prod':
    case 'p':
      return 'production';
    default:
      console.log(`No recognizable environment specified, defaulting to development.`);
      return 'development';
  }
}

function getPaths(container, environment) {
  return {
    baseEnvPath: `workloads/web/base/components/environments/${environment}/Deployment.yaml`,
    baseContainerPath: `workloads/web/${container}/components/base/Deployment.yaml`,
    envContainerPath: `workloads/web/${container}/${environment}/Deployment.yaml`,
  };
}

/**
 * Only fetch the relevant workload files from the repository.
 *
 * {@link https://git-scm.com/docs/git-sparse-checkout git-sparse-checkout } reduces the working tree to the given paths.
 * By cloning with `blob:none`, we omit all files, and we only fetch the files after we set mode to `sparse-checkout`.
 * This makes the script run way faster, as only a few files are fetched from the infra repo.
 */
function pullFromGit(paths) {
  checkGitAvailability();
  try {
    execSync(
      `git clone -q --filter=blob:none --no-checkout --depth 1 --sparse **************:eneco-online/${infraRepoName}.git`,
    );
    execSync(
      `git sparse-checkout init --no-cone
git sparse-checkout set /${Object.values(paths).join(' /')}
git checkout master -q`,
      { cwd: infraRepoName },
    );
  } catch (e) {
    console.log(`\nError checking out ${infraRepoName}, please refer to above error message`);
    process.exit();
  }
}

function checkGitAvailability() {
  try {
    execSync('git --version');
  } catch (e) {
    console.error('Sorry, this script requires git.');
    process.exit();
  }
}

async function fetchInfraEnvVariables({ paths, environment }) {
  const { baseEnvPath, baseContainerPath, envContainerPath } = paths;
  try {
    const baseEnvData = fetchEnvVarsFromFile(baseEnvPath);
    const baseContainerData = fetchEnvVarsFromFile(baseContainerPath);
    const envContainerData = fetchEnvVarsFromFile(envContainerPath);
    const envData = await Promise.all([baseEnvData, baseContainerData, envContainerData]);
    return Object.assign(
      { SITECORE_API_HOST: getApiHost(environment), FE_SITECORE_MOCKS: '0', FE_DC_MOCKS: '0' },
      ...envData,
    );
  } catch (e) {
    console.error(chalk.red(`Error: ${e.message}`));
  }

  console.error(`Could not process the workload files. Environmental variables will not be updated.`);
}

async function getLocalDevEnvVariables({ container, developmentType }) {
  const { mockedDevelopmentVars, integratedDevelopmentVars } = (await getLocalDevConfig(container)) || {};
  return developmentType === 'integrated' ? integratedDevelopmentVars : mockedDevelopmentVars;
}

async function fetchEnvVarsFromFile(fileName) {
  const forbiddenKeys = ['FE_APPINSIGHTS_CONNECTIONSTRING', 'FE_STATIC_PREFIX', 'FE_AAD_REDIRURL'];
  const fileContents = await readFile(`${infraRepoName}/${fileName}`, { encoding: 'utf8' });
  return (
    fileContents
      .split('env:')[1]
      ?.trim()
      .split(' - ')
      .reduce(function (obj, envLine) {
        const propertyName = envLine.match(/name:\s*(\w+)/)?.at(1);
        const propertyValue = envLine.match(/value:\s*(".*"|[\w-]+)/)?.at(1);
        if (propertyName && propertyValue) {
          //Exclude values dependent on keyvault or that break local development
          if (!propertyValue.includes('$(') && !forbiddenKeys.includes(propertyName)) {
            obj[propertyName] = propertyValue;
          }
        }
        return obj;
      }, {}) ?? {}
  );
}

async function fetchSecretsForEnv({ environment, container }) {
  const [label, brand] = container.split('-');
  const labelAndBrand = `${label.toUpperCase()}-${brand.toUpperCase()}`;
  const secretKeys = [
    {
      vaultName: 'NEXTAUTH-SECRET',
      envName: 'NEXTAUTH_SECRET',
    },
    {
      vaultName: `OKTA-OAUTH2-CLIENT-SECRET-${labelAndBrand}-WEBSITE`,
      envName: 'OKTA_OAUTH2_CLIENT_SECRET',
    },
  ];

  // Return empty secrets for non-development environments
  if (!['development', 'acceptance', 'test'].includes(environment)) {
    return Object.fromEntries(secretKeys.map(({ envName }) => [envName, '']));
  }

  const devSecrets = await fetchDevSecrets(environment);

  // Map fetched secrets to the desired environment variable names
  return secretKeys.reduce((acc, { vaultName, envName }) => {
    if (devSecrets[vaultName]) {
      acc[envName] = devSecrets[vaultName];
    }
    return acc;
  }, {});
}

async function getLocalDevConfig(container) {
  const [label, brand, project] = container.split('-');

  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);

  const relativePath = `../containers/${label}/${brand}/${project}/localDev.config.mjs`;
  const absolutePath = path.resolve(__dirname, relativePath);

  try {
    await access(absolutePath);
    console.log(chalk.green(`LocalDev config file found.`));
    return await import(absolutePath);
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.log(
        chalk.yellow(
          `No localDev config file found at ${absolutePath}. The keys from .env.example will be used instead`,
        ),
      );
      return null;
    }
    console.error('Error reading mocked data env vars:', error);
    return null;
  }
}

/** Manually derive the SITECORE_API_HOST value,
    as the infra repo only refers to http://cd.dxp-sitecore.svc.cluster.local
 */
function getApiHost(environment) {
  switch (environment) {
    case 'development':
      return 'https://www.dev.eneco.nl';
    case 'test':
      return 'https://www.test.eneco.nl';
    case 'acceptance':
      return 'https://www.acc.eneco.nl';
    case 'production':
      return 'https://www.eneco.nl';
  }
}

async function updateEnvFile(newValues, skipOldValues) {
  const oldFileContents = skipOldValues ? '' : await getOldFileContents();
  const { updatedFileContents, updatedKeys, newKeys } = createUpdatedFileContents(newValues, oldFileContents);

  if (newKeys.length === 0 && updatedKeys.length === 0) {
    console.log(chalk.yellow(`No changes detected.`));
    return;
  } else {
    try {
      await writeFile('.env', updatedFileContents);
      logChanges(updatedKeys, newKeys);
      console.log(chalk.green(`.env file successfully updated.\n`));
    } catch (err) {
      console.error(chalk.red(`Error writing to .env file: ${err}\n`));
    }
  }
}

async function getOldFileContents() {
  let envFileContents = '';
  try {
    envFileContents = await readFile('.env.example', { encoding: 'utf8' });
  } catch (e) {
    console.error(
      chalk.red(`Error copying .env.example file: ${e}.
    Starting with a empty file.\n`),
    );
  }
  return envFileContents;
}

function createUpdatedFileContents(newValues, envFileContents) {
  //Keep track of changes for logging;
  const updatedKeys = [];
  const newKeys = [];
  Object.entries(newValues).forEach(([key, newValue]) => {
    const regex = new RegExp(`${key}=.*`);
    const lineWithKey = envFileContents.match(regex)?.at(0);
    const entry = `${key}=${newValue}`;

    if (lineWithKey) {
      const oldValue = lineWithKey.split('=')[1];
      if (trimQuotes(oldValue) !== trimQuotes(newValue)) {
        envFileContents = envFileContents.replace(regex, entry);
        updatedKeys.push({ key, value: newValue, oldValue });
      }
    } else {
      envFileContents += `\n${entry}`;
      newKeys.push({ key, value: newValue });
    }
  });
  return { updatedFileContents: envFileContents, updatedKeys, newKeys };
}

function trimQuotes(string) {
  return string.replace(/^"(.*)"$/, '$1');
}

function logChanges(updatedKeys, newKeys) {
  function drawLine() {
    console.log('-'.repeat(process.stdout.columns));
  }
  drawLine();
  if (updatedKeys.length > 0) {
    console.log(chalk.underline('Updated keys:'));
    console.log(
      updatedKeys
        .map(({ key, value, oldValue }) => `${key}: ${chalk.gray(oldValue || 'empty')} => ${chalk.cyan(value)}`)
        .join('\n'),
    );
  }
  if (newKeys.length > 0) {
    console.log(chalk.underline('\nNew keys:'));
    console.log(newKeys.map(({ key, value }) => `${key}: ${chalk.cyan(value)}`).join('\n'));
  }
  drawLine();
  console.log();
}

async function cleanup() {
  try {
    const clonedRepoName = 'eneco-dxp-infra';
    await access(clonedRepoName);
    await rm(clonedRepoName, { recursive: true });
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.log(chalk.grey('No eneco-dxp-infra folder to clean up.'));
    } else {
      console.error(
        chalk.red(`Error cleaning up: ${error.message}.
  Please remove the eneco-dxp-infra folder manually.\n`),
      );
    }
  }
}
