import React, { FC } from 'react';
import { render } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import { Session } from 'next-auth';
import { SitecoreContext } from '@sitecore/common';
import { layoutDataShim } from '@mocks/sitecore/layoutShim';
import { ApplicationProvider, CountryCode, Language, Locale } from '@common/application';
import { TrackingProvider } from '@tracking';
import { SparkyProvider } from '@sparky/providers';
import DigitalCoreProvider from '@dc/provider';
import { PlaceholderProvider } from '@sitecore/common';
import { RenderingProvider } from '@sitecore/common/core/Placeholder';
import { DC_Repositories_Base_Enumerations_BusinessUnit } from '@monorepo-types/dc';
import mockRouter from 'next-router-mock/async';
import { Mock } from '@mocks/sitecore/types/mock';
import { MandateProvider } from '@components/Mandate/MandateProvider';
import { Label } from '@dc/client/types';
import { fetchPlaceholder } from '@sitecore/client/browser';
import { PushNotificationsProvider } from '@native-components/providers/PushNotificationsProvider';
import { SmaNotificationContextProvider } from '@native-components/components/SmaNotification';
import { NotificationContextProvider } from '@components-next/Notification/Notification';
import NativeAppProvider from '@native-components/components/wrappers/NativeAppProvider/NativeAppProvider';
import { CustomerProfileProvider } from '@native-components/components/wrappers/CustomerProfileProvider/CustomerProfileProvider';

jest.mock('next/dist/client/router', () => {
  const mock = require('next-router-mock/async');

  // As next-router-mock removes trailing slashes,
  // we create a proxy that virtually adds the trailing slash.
  mock.memoryRouter = new Proxy(mock.memoryRouter, {
    get: (obj, prop) => {
      if (prop === 'asPath' || prop === 'pathname') {
        if (/\/?\?/.test(obj[prop])) return obj[prop].replace(/\/?\?/, '/?'); // Fix paths with search param(s)
        return obj[prop].endsWith('/') ? obj[prop] : `${obj[prop]}/`;
      }
      return obj[prop];
    },
  });

  return mock;
});

type Options = {
  mock: Mock;
  scope: string;
  path: string;
  locale?: Locale;
  userName?: string;
  customerId?: string;
  session?: Session;
};

/**
 * Use this helper to render (using testing-library) a feature or component for
 * which you don't need any switching of pages.
 *
 * @param AppComponent - component you want to render
 * @param mockOptions - options needed to mock URLs, content, etc
 */
const renderApp = (AppComponent: any, mockOptions: Options) => {
  mockRouter.setCurrentUrl(mockOptions.path);
  return render(<AppWithProviders mockOptions={mockOptions} AppComponent={AppComponent} />);
};

const AppWithProviders: FC<{ mockOptions: Options; AppComponent: any }> = ({ mockOptions, AppComponent }) => {
  const { scope, mock, customerId, session } = mockOptions;
  const [businessUnit, domain] = scope.split('-');
  const locale = (mockOptions.locale || 'nl-NL') as Locale;
  const language = locale.slice(0, 1) as Language;
  const countryCode = locale.slice(-2) as CountryCode;

  const mockFromPath = mock(mockOptions.path, { locale, site: scope, customerId });
  const sitecoreComponents = Object.entries(mockFromPath.sitecore.route.placeholders).flatMap(
    ([placeholder, components]) => components,
  );

  const appName = AppComponent.name;

  const appRendering = sitecoreComponents.find(({ componentName }) => componentName === appName);

  if (!appRendering) {
    throw new Error(
      `Component ${appName} not found in mock renderings. The exported AppComponent must match componentName`,
    );
  }

  const layoutData = layoutDataShim(locale, 200, mockFromPath.sitecore);
  const { accessToken, accountId, customerId: scCustomerId } = layoutData.sitecore.context;

  return (
    <SessionProvider
      session={
        (session ?? scCustomerId)
          ? ({
              idToken: accessToken,
              expires: 'someday',
              user: {
                customerId: scCustomerId,
              },
            } as Session)
          : undefined
      }>
      <ApplicationProvider
        i18n={{ locale, locales: [locale], language, languages: [language], countryCode }}
        path={mockOptions.path}>
        <TrackingProvider scope={scope}>
          <SparkyProvider locale={locale}>
            <MandateProvider>
              <SitecoreContext components={{}} layoutData={layoutData}>
                <DigitalCoreProvider
                  idToken={scCustomerId ? accessToken : null}
                  customerContext={{
                    customerId: scCustomerId,
                    accountId,
                    accessToken,
                  }}
                  label={domain as Label}
                  businessUnit={businessUnit.toUpperCase() as DC_Repositories_Base_Enumerations_BusinessUnit}>
                  <RenderingProvider rendering={appRendering}>
                    <PlaceholderProvider fetchPlaceholder={fetchPlaceholder}>
                      <NativeAppProvider>
                        <CustomerProfileProvider>
                          <SmaNotificationContextProvider>
                            <NotificationContextProvider>
                              <PushNotificationsProvider>
                                <AppComponent />
                              </PushNotificationsProvider>
                            </NotificationContextProvider>
                          </SmaNotificationContextProvider>
                        </CustomerProfileProvider>
                      </NativeAppProvider>
                    </PlaceholderProvider>
                  </RenderingProvider>
                </DigitalCoreProvider>
              </SitecoreContext>
            </MandateProvider>
          </SparkyProvider>
        </TrackingProvider>
      </ApplicationProvider>
    </SessionProvider>
  );
};

export default renderApp;
