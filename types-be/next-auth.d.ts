import { DefaultSession, Profile as Default<PERSON>ro<PERSON>le, DefaultUser } from 'next-auth';
import 'next-auth/jwt';

export interface CrmAccountWithRole {
  alias: string;
  crmAccountNumber: string;
  role: 'reader' | 'editor' | 'owner';
}
export interface Impersonation {
  type: 'None' | 'Email' | 'CrmAccountNumber';
  impersonatorSubject: string;
  actsAsIdentityProviderDisplayName: string;
  actsAsIdentityProviderId: string;
  role: 'reader' | 'editor';
  accounts: Array<CrmAccountWithRole>;
}

declare module 'next-auth' {
  interface User extends DefaultUser {
    customerId?: string;
    accountId?: number;
    roles: {
      owner?: CrmAccountWithRole[];
      editor?: CrmAccountWithRole[];
      reader?: CrmAccountWithRole[];
    };
    type: 'pageByPage' | string;
    access_token?: string;
    accessTokenExpires?: number;
  }

  interface Session extends DefaultSession {
    error?: string;
    idToken?: string;
    accessToken?: string;
    providerAccountId?: string;
    user: User;
  }

  interface Profile extends DefaultProfile {
    customerId?: number;
    owner_accounts: CrmAccountWithRole[];
    edit_accounts: CrmAccountWithRole[];
    read_accounts: CrmAccountWithRole[];
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    error?: string;
    id_token?: string;
    access_token?: string;
    okta_id?: number;
    customerId?: string;
    providerAccountId?: string;
    roles: {
      owner?: CrmAccountWithRole[];
      editor?: CrmAccountWithRole[];
      reader?: CrmAccountWithRole[];
    };
  }
}
